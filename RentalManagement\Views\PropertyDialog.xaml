<Window x:Class="RentalManagement.Views.PropertyDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:models="clr-namespace:RentalManagement.Models"
        mc:Ignorable="d"
        Title="{Binding Title}" 
        Height="600" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Title -->
        <TextBlock Grid.Row="0"
                   Text="{Binding Title}"
                   Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,24"/>

        <!-- Form Fields -->
        <StackPanel Grid.Row="1">
            <!-- Property Type -->
            <ComboBox SelectedValue="{Binding Type}"
                      materialDesign:HintAssist.Hint="نوع العقار *"
                      Style="{StaticResource MaterialDesignOutlinedComboBox}"
                      Margin="0,0,0,16">
                <ComboBoxItem Content="شقة">
                    <ComboBoxItem.Tag>
                        <models:PropertyType>Apartment</models:PropertyType>
                    </ComboBoxItem.Tag>
                </ComboBoxItem>
                <ComboBoxItem Content="محل">
                    <ComboBoxItem.Tag>
                        <models:PropertyType>Shop</models:PropertyType>
                    </ComboBoxItem.Tag>
                </ComboBoxItem>
            </ComboBox>

            <!-- Location -->
            <TextBox Text="{Binding Location, UpdateSourceTrigger=PropertyChanged}"
                     materialDesign:HintAssist.Hint="الموقع *"
                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                     materialDesign:ValidationAssist.UsePopup="True"
                     Margin="0,0,0,16"/>

            <!-- Rent Amount -->
            <TextBox Text="{Binding RentAmount, UpdateSourceTrigger=PropertyChanged}"
                     materialDesign:HintAssist.Hint="مبلغ الكراء *"
                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                     materialDesign:ValidationAssist.UsePopup="True"
                     Margin="0,0,0,16"/>

            <!-- Status -->
            <ComboBox SelectedValue="{Binding Status}"
                      materialDesign:HintAssist.Hint="حالة العقار"
                      Style="{StaticResource MaterialDesignOutlinedComboBox}"
                      Margin="0,0,0,16">
                <ComboBoxItem Content="متاح">
                    <ComboBoxItem.Tag>
                        <models:PropertyStatus>Available</models:PropertyStatus>
                    </ComboBoxItem.Tag>
                </ComboBoxItem>
                <ComboBoxItem Content="مكتراة">
                    <ComboBoxItem.Tag>
                        <models:PropertyStatus>Rented</models:PropertyStatus>
                    </ComboBoxItem.Tag>
                </ComboBoxItem>
            </ComboBox>

            <!-- Customer (only if rented) -->
            <ComboBox ItemsSource="{Binding Customers}"
                      SelectedItem="{Binding SelectedCustomer}"
                      DisplayMemberPath="FullName"
                      materialDesign:HintAssist.Hint="الزبون (في حالة الكراء)"
                      Style="{StaticResource MaterialDesignOutlinedComboBox}"
                      Margin="0,0,0,16"/>

            <!-- Description -->
            <TextBox Text="{Binding Description, UpdateSourceTrigger=PropertyChanged}"
                     materialDesign:HintAssist.Hint="الوصف"
                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                     AcceptsReturn="True"
                     TextWrapping="Wrap"
                     MinLines="3"
                     MaxLines="5"
                     VerticalScrollBarVisibility="Auto"/>
        </StackPanel>

        <!-- Error Message -->
        <TextBlock Grid.Row="2"
                   Text="{Binding ErrorMessage}"
                   Foreground="{StaticResource ErrorBrush}"
                   TextWrapping="Wrap"
                   Margin="0,16,0,0"
                   Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"/>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="3"
                    Orientation="Horizontal"
                    HorizontalAlignment="Left"
                    Margin="0,24,0,0">
            <Button Content="حفظ"
                    Command="{Binding SaveCommand}"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    IsDefault="True"
                    Margin="0,0,16,0"
                    Width="100"/>
            
            <Button Content="إلغاء"
                    Command="{Binding CancelCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    IsCancel="True"
                    Width="100"/>
        </StackPanel>
    </Grid>
</Window>
