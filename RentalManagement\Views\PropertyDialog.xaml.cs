using System.Windows;
using RentalManagement.ViewModels;

namespace RentalManagement.Views
{
    /// <summary>
    /// Interaction logic for PropertyDialog.xaml
    /// </summary>
    public partial class PropertyDialog : Window
    {
        public PropertyDialog(PropertyDialogViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
            
            // Subscribe to dialog closed event
            viewModel.DialogClosed += OnDialogClosed;
        }

        private void OnDialogClosed(object? sender, bool result)
        {
            DialogResult = result;
            Close();
        }
    }
}
