using Microsoft.EntityFrameworkCore;
using SimpleRentalApp.Data;
using SimpleRentalApp.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SimpleRentalApp.Services
{
    /// <summary>
    /// خدمة إدارة قوالب الرسائل للإشعارات
    /// </summary>
    public class NotificationTemplateService
    {
        private readonly RentalDbContext _context;

        public NotificationTemplateService(RentalDbContext context)
        {
            _context = context;
        }

        public NotificationTemplateService()
        {
            _context = new RentalDbContext();
        }

        /// <summary>
        /// الحصول على قالب الرسالة المناسب لنوع الإشعار
        /// </summary>
        public async Task<MessageTemplate?> GetTemplateForNotificationTypeAsync(NotificationType notificationType)
        {
            try
            {
                var template = await _context.MessageTemplates
                    .Where(t => t.Type == notificationType && t.IsActive)
                    .OrderBy(t => t.CreatedDate)
                    .FirstOrDefaultAsync();

                return template;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الحصول على قالب الرسالة: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// تطبيق قالب الرسالة على الإشعار مع استبدال المتغيرات
        /// </summary>
        public async Task<string> ApplyTemplateToNotificationAsync(AdvancedNotification notification)
        {
            try
            {
                var template = await GetTemplateForNotificationTypeAsync(notification.Type);
                
                if (template == null)
                {
                    // إذا لم يوجد قالب، استخدم الرسالة الافتراضية
                    return notification.Message ?? GenerateDefaultMessage(notification);
                }

                // تطبيق المتغيرات على القالب
                var message = await ReplaceVariablesInTemplate(template.Template, notification);
                return message;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق قالب الرسالة: {ex.Message}");
                return notification.Message ?? GenerateDefaultMessage(notification);
            }
        }

        /// <summary>
        /// استبدال المتغيرات في قالب الرسالة
        /// </summary>
        private async Task<string> ReplaceVariablesInTemplate(string template, AdvancedNotification notification)
        {
            try
            {
                // تحميل البيانات المطلوبة
                await LoadNotificationRelatedDataAsync(notification);

                var message = template;

                // المتغيرات الأساسية
                message = message.Replace("{اسم_المكتري}", notification.Customer?.FirstName + " " + notification.Customer?.LastName ?? "غير محدد");
                message = message.Replace("{اسم_المحل}", notification.Property?.Name ?? "غير محدد");
                message = message.Replace("{عنوان_المحل}", notification.Property?.Address ?? "غير محدد");
                message = message.Replace("{المبلغ}", notification.Amount.ToString("N0"));
                message = message.Replace("{تاريخ_الاستحقاق}", notification.DueDate.ToString("dd/MM/yyyy"));
                message = message.Replace("{رقم_الهاتف}", notification.Customer?.Phone ?? "غير محدد");

                // متغيرات خاصة بنوع الإشعار
                switch (notification.Type)
                {
                    case NotificationType.MonthlyRent:
                        if (notification.PaymentYear.HasValue && notification.PaymentMonth.HasValue)
                        {
                            var monthName = new DateTime(notification.PaymentYear.Value, notification.PaymentMonth.Value, 1).ToString("MMMM yyyy");
                            message = message.Replace("{شهر_الدفع}", monthName);
                            message = message.Replace("{سنة_الدفع}", notification.PaymentYear.Value.ToString());
                            message = message.Replace("{شهر_رقم}", notification.PaymentMonth.Value.ToString());
                        }
                        break;

                    case NotificationType.CleaningTax:
                        if (notification.TaxYear.HasValue)
                        {
                            message = message.Replace("{سنة_الضريبة}", notification.TaxYear.Value.ToString());
                            message = message.Replace("{موعد_انتهاء_الضريبة}", $"31/05/{notification.TaxYear.Value}");
                        }
                        break;

                    case NotificationType.RentOverdue:
                    case NotificationType.CleaningTaxOverdue:
                        if (notification.DaysOverdue.HasValue)
                        {
                            message = message.Replace("{ايام_التاخير}", notification.DaysOverdue.Value.ToString());
                        }
                        break;
                }

                // متغيرات التاريخ والوقت
                message = message.Replace("{التاريخ_الحالي}", DateTime.Now.ToString("dd/MM/yyyy"));
                message = message.Replace("{الوقت_الحالي}", DateTime.Now.ToString("HH:mm"));
                message = message.Replace("{اليوم}", DateTime.Now.ToString("dddd"));

                return message;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في استبدال المتغيرات: {ex.Message}");
                return template;
            }
        }

        /// <summary>
        /// تحميل البيانات المرتبطة بالإشعار
        /// </summary>
        private async Task LoadNotificationRelatedDataAsync(AdvancedNotification notification)
        {
            try
            {
                if (notification.Property == null && notification.PropertyId > 0)
                {
                    notification.Property = await _context.Properties
                        .FirstOrDefaultAsync(p => p.Id == notification.PropertyId);
                }

                if (notification.Customer == null && notification.CustomerId > 0)
                {
                    notification.Customer = await _context.Customers
                        .FirstOrDefaultAsync(c => c.Id == notification.CustomerId);
                }

                if (notification.Contract == null && notification.ContractId.HasValue)
                {
                    notification.Contract = await _context.Contracts
                        .Include(c => c.RentIncreases)
                        .FirstOrDefaultAsync(c => c.Id == notification.ContractId.Value);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل البيانات المرتبطة: {ex.Message}");
            }
        }

        /// <summary>
        /// ربط نوع الإشعار بنوع قالب الرسالة (تم إزالة هذه الوظيفة لأن النظام يستخدم NotificationType مباشرة)
        /// </summary>
        private NotificationType MapNotificationTypeToTemplateType(NotificationType notificationType)
        {
            // النظام يستخدم NotificationType مباشرة في MessageTemplate
            return notificationType;
        }

        /// <summary>
        /// إنشاء رسالة افتراضية في حالة عدم وجود قالب
        /// </summary>
        private string GenerateDefaultMessage(AdvancedNotification notification)
        {
            return notification.Type switch
            {
                NotificationType.MonthlyRent => $"تذكير: إيجار شهري مستحق بمبلغ {notification.Amount:N0} درهم.",
                NotificationType.CleaningTax => $"تذكير: ضريبة النظافة مستحقة بمبلغ {notification.Amount:N0} درهم.",
                NotificationType.RentOverdue => $"تنبيه: إيجار متأخر بمبلغ {notification.Amount:N0} درهم.",
                NotificationType.CleaningTaxOverdue => $"تنبيه: ضريبة النظافة متأخرة بمبلغ {notification.Amount:N0} درهم.",
                _ => $"إشعار: مبلغ {notification.Amount:N0} درهم مستحق."
            };
        }

        /// <summary>
        /// الحصول على قائمة المتغيرات المتاحة لنوع إشعار معين
        /// </summary>
        public List<TemplateVariable> GetAvailableVariables(NotificationType notificationType)
        {
            var commonVariables = new List<TemplateVariable>
            {
                new TemplateVariable("{اسم_المكتري}", "اسم المكتري الكامل"),
                new TemplateVariable("{اسم_المحل}", "اسم المحل"),
                new TemplateVariable("{عنوان_المحل}", "عنوان المحل"),
                new TemplateVariable("{المبلغ}", "المبلغ المستحق"),
                new TemplateVariable("{تاريخ_الاستحقاق}", "تاريخ الاستحقاق"),
                new TemplateVariable("{رقم_الهاتف}", "رقم هاتف المكتري"),
                new TemplateVariable("{التاريخ_الحالي}", "التاريخ الحالي"),
                new TemplateVariable("{الوقت_الحالي}", "الوقت الحالي"),
                new TemplateVariable("{اليوم}", "اسم اليوم")
            };

            var specificVariables = notificationType switch
            {
                NotificationType.MonthlyRent => new List<TemplateVariable>
                {
                    new TemplateVariable("{شهر_الدفع}", "شهر وسنة الدفع"),
                    new TemplateVariable("{سنة_الدفع}", "سنة الدفع"),
                    new TemplateVariable("{شهر_رقم}", "رقم الشهر")
                },
                NotificationType.CleaningTax => new List<TemplateVariable>
                {
                    new TemplateVariable("{سنة_الضريبة}", "سنة ضريبة النظافة"),
                    new TemplateVariable("{موعد_انتهاء_الضريبة}", "موعد انتهاء ضريبة النظافة")
                },
                NotificationType.RentOverdue => new List<TemplateVariable>
                {
                    new TemplateVariable("{ايام_التاخير}", "عدد أيام التأخير")
                },
                NotificationType.CleaningTaxOverdue => new List<TemplateVariable>
                {
                    new TemplateVariable("{ايام_التاخير}", "عدد أيام التأخير")
                },
                _ => new List<TemplateVariable>()
            };

            commonVariables.AddRange(specificVariables);
            return commonVariables;
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }

    /// <summary>
    /// متغير قالب الرسالة
    /// </summary>
    public class TemplateVariable
    {
        public string Variable { get; set; }
        public string Description { get; set; }

        public TemplateVariable(string variable, string description)
        {
            Variable = variable;
            Description = description;
        }
    }
}
