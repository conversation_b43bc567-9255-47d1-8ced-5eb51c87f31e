using Microsoft.EntityFrameworkCore;
using SimpleRentalApp.Data;
using SimpleRentalApp.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SimpleRentalApp.Services
{
    /// <summary>
    /// خدمة قوالب الإشعارات المحدثة للنظام الجديد
    /// </summary>
    public class NotificationTemplateService
    {
        private readonly RentalDbContext _context;

        public NotificationTemplateService(RentalDbContext context)
        {
            _context = context;
        }

        public NotificationTemplateService()
        {
            _context = new RentalDbContext();
        }

        /// <summary>
        /// الحصول على قالب الرسالة المناسب لنوع الإشعار
        /// </summary>
        public async Task<MessageTemplate?> GetTemplateForNotificationTypeAsync(WhatsAppNotificationType notificationType)
        {
            try
            {
                var template = await _context.MessageTemplates
                    .Where(t => t.Type == notificationType && t.IsActive)
                    .OrderBy(t => t.CreatedDate)
                    .FirstOrDefaultAsync();

                return template;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الحصول على القالب: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// تطبيق قالب الرسالة على إشعار الواتساب مع استبدال المتغيرات
        /// </summary>
        public async Task<string> ApplyTemplateToNotificationAsync(WhatsAppNotification notification)
        {
            try
            {
                var template = await GetTemplateForNotificationTypeAsync(notification.Type);
                
                if (template == null)
                {
                    return string.Empty; // سيتم استخدام الرسالة الافتراضية
                }

                // تحميل البيانات المطلوبة
                await LoadNotificationRelatedDataAsync(notification);

                // استبدال المتغيرات
                return await ReplaceVariablesInTemplate(template.Template, notification);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق القالب: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// تطبيق قالب مع بيانات مخصصة
        /// </summary>
        public async Task<string> ApplyTemplateAsync(MessageTemplate template, object data)
        {
            try
            {
                var message = template.Template;

                // استخدام reflection لاستبدال المتغيرات
                var properties = data.GetType().GetProperties();
                
                foreach (var property in properties)
                {
                    var value = property.GetValue(data)?.ToString() ?? "غير محدد";
                    var variableName = $"{{{property.Name}}}";
                    message = message.Replace(variableName, value);
                }

                return message;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق القالب المخصص: {ex.Message}");
                return template.Template;
            }
        }

        /// <summary>
        /// استبدال المتغيرات في قالب الرسالة
        /// </summary>
        private async Task<string> ReplaceVariablesInTemplate(string template, WhatsAppNotification notification)
        {
            try
            {
                var message = template;

                // المتغيرات الأساسية
                message = message.Replace("{اسم_المكتري}", notification.Customer?.FullName ?? "المكتري");
                message = message.Replace("{اسم_المحل}", notification.Property?.Name ?? "المحل");
                message = message.Replace("{عنوان_المحل}", notification.Property?.Address ?? "غير محدد");
                message = message.Replace("{المبلغ}", notification.Amount.ToString("N0"));
                message = message.Replace("{تاريخ_الاستحقاق}", notification.DueDate?.ToString("dd/MM/yyyy") ?? "غير محدد");
                message = message.Replace("{رقم_الهاتف}", notification.PhoneNumber);

                // متغيرات التاريخ والوقت
                message = message.Replace("{التاريخ_الحالي}", DateTime.Now.ToString("dd/MM/yyyy"));
                message = message.Replace("{الوقت_الحالي}", DateTime.Now.ToString("HH:mm"));
                message = message.Replace("{اليوم}", DateTime.Now.ToString("dddd"));

                // متغيرات خاصة بنوع الإشعار
                switch (notification.Type)
                {
                    case WhatsAppNotificationType.MonthlyRent:
                        if (notification.Month.HasValue && notification.Year.HasValue)
                        {
                           var monthName = new DateTime(notification.Year.Value, notification.Month.Value, 1)
							.ToString("MMMM yyyy", new CultureInfo("ar-MA"));
                            message = message.Replace("{شهر_الدفع}", monthName);
                            message = message.Replace("{سنة_الدفع}", notification.Year.Value.ToString());
                            message = message.Replace("{شهر_رقم}", notification.Month.Value.ToString());
                        }
                        break;

                    case WhatsAppNotificationType.CleaningTax:
                        if (notification.Year.HasValue)
                        {
                            message = message.Replace("{سنة_الضريبة}", notification.Year.Value.ToString());
                            message = message.Replace("{موعد_انتهاء_الضريبة}", $"31/05/{notification.Year.Value}");
                        }
                        break;

                    case WhatsAppNotificationType.RentOverdue:
                    case WhatsAppNotificationType.CleaningTaxOverdue:
                        if (notification.DueDate.HasValue)
                        {
                            var daysOverdue = (DateTime.Now.Date - notification.DueDate.Value.Date).Days;
                            message = message.Replace("{ايام_التاخير}", daysOverdue.ToString());
                        }
                        break;
                }

                return message;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في استبدال المتغيرات: {ex.Message}");
                return template;
            }
        }

        /// <summary>
        /// تحميل البيانات المرتبطة بالإشعار
        /// </summary>
        private async Task LoadNotificationRelatedDataAsync(WhatsAppNotification notification)
        {
            try
            {
                if (notification.Customer == null && notification.CustomerId > 0)
                {
                    notification.Customer = await _context.Customers
                        .FirstOrDefaultAsync(c => c.Id == notification.CustomerId);
                }

                if (notification.Property == null && notification.PropertyId > 0)
                {
                    notification.Property = await _context.Properties
                        .FirstOrDefaultAsync(p => p.Id == notification.PropertyId);
                }

                if (notification.Contract == null && notification.ContractId.HasValue)
                {
                    notification.Contract = await _context.Contracts
                        .FirstOrDefaultAsync(c => c.Id == notification.ContractId.Value);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل البيانات المرتبطة: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على قائمة المتغيرات المتاحة لنوع إشعار معين
        /// </summary>
        public List<TemplateVariable> GetAvailableVariables(WhatsAppNotificationType notificationType)
        {
            var commonVariables = new List<TemplateVariable>
            {
                new TemplateVariable("{اسم_المكتري}", "اسم المكتري الكامل"),
                new TemplateVariable("{اسم_المحل}", "اسم المحل"),
                new TemplateVariable("{عنوان_المحل}", "عنوان المحل"),
                new TemplateVariable("{المبلغ}", "المبلغ المستحق"),
                new TemplateVariable("{تاريخ_الاستحقاق}", "تاريخ الاستحقاق"),
                new TemplateVariable("{رقم_الهاتف}", "رقم هاتف المكتري"),
                new TemplateVariable("{التاريخ_الحالي}", "التاريخ الحالي"),
                new TemplateVariable("{الوقت_الحالي}", "الوقت الحالي"),
                new TemplateVariable("{اليوم}", "اسم اليوم")
            };

            var specificVariables = notificationType switch
            {
                WhatsAppNotificationType.MonthlyRent => new List<TemplateVariable>
                {
                    new TemplateVariable("{شهر_الدفع}", "شهر وسنة الدفع"),
                    new TemplateVariable("{سنة_الدفع}", "سنة الدفع"),
                    new TemplateVariable("{شهر_رقم}", "رقم الشهر")
                },
                WhatsAppNotificationType.CleaningTax => new List<TemplateVariable>
                {
                    new TemplateVariable("{سنة_الضريبة}", "سنة ضريبة النظافة"),
                    new TemplateVariable("{موعد_انتهاء_الضريبة}", "موعد انتهاء ضريبة النظافة")
                },
                WhatsAppNotificationType.RentOverdue => new List<TemplateVariable>
                {
                    new TemplateVariable("{ايام_التاخير}", "عدد أيام التأخير")
                },
                WhatsAppNotificationType.CleaningTaxOverdue => new List<TemplateVariable>
                {
                    new TemplateVariable("{ايام_التاخير}", "عدد أيام التأخير")
                },
                _ => new List<TemplateVariable>()
            };

            commonVariables.AddRange(specificVariables);
            return commonVariables;
        }

        /// <summary>
        /// إنشاء قوالب افتراضية للأنواع المفقودة
        /// </summary>
        public async Task<bool> CreateDefaultTemplatesAsync()
        {
            try
            {
                var templatesCreated = 0;

                // قالب الإيجار الشهري
                if (!await _context.MessageTemplates.AnyAsync(t => t.Type == WhatsAppNotificationType.MonthlyRent))
                {
                    var template = new MessageTemplate
                    {
                        Name = "قالب الإيجار الشهري الافتراضي",
                        Type = WhatsAppNotificationType.MonthlyRent,
                        Template = "السلام عليكم {اسم_المكتري}،\n\n" +
                                  "نذكركم بوجوب أداء كراء محل {اسم_المحل} لشهر {شهر_الدفع}.\n\n" +
                                  "📍 المحل: {اسم_المحل}\n" +
                                  "📍 العنوان: {عنوان_المحل}\n" +
                                  "💰 المبلغ المستحق: {المبلغ} درهم\n" +
                                  "📅 آخر أجل للدفع: {تاريخ_الاستحقاق}\n\n" +
                                  "شكراً لتعاونكم معنا.\n" +
                                  "فريق الإدارة",
                        IsActive = true,
                        CreatedDate = DateTime.Now,
                        UpdatedDate = DateTime.Now
                    };
                    
                    _context.MessageTemplates.Add(template);
                    templatesCreated++;
                }

                // قالب ضريبة النظافة
                if (!await _context.MessageTemplates.AnyAsync(t => t.Type == WhatsAppNotificationType.CleaningTax))
                {
                    var template = new MessageTemplate
                    {
                        Name = "قالب ضريبة النظافة الافتراضي",
                        Type = WhatsAppNotificationType.CleaningTax,
                        Template = "مرحباً {اسم_المكتري}،\n\n" +
                                  "المرجو تسديد ضريبة النظافة عن المحل {اسم_المحل} لسنة {سنة_الضريبة}.\n\n" +
                                  "📍 المحل: {اسم_المحل}\n" +
                                  "📍 العنوان: {عنوان_المحل}\n" +
                                  "💰 المبلغ المستحق: {المبلغ} درهم\n" +
                                  "📅 آخر أجل للدفع: {موعد_انتهاء_الضريبة}\n\n" +
                                  "ملاحظة: ضريبة النظافة إجبارية ويجب تسديدها قبل الموعد المحدد.\n\n" +
                                  "مع تحياتنا،\n" +
                                  "فريق الإدارة",
                        IsActive = true,
                        CreatedDate = DateTime.Now,
                        UpdatedDate = DateTime.Now
                    };
                    
                    _context.MessageTemplates.Add(template);
                    templatesCreated++;
                }

                if (templatesCreated > 0)
                {
                    await _context.SaveChangesAsync();
                }

                return templatesCreated > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء القوالب الافتراضية: {ex.Message}");
                return false;
            }
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }

    /// <summary>
    /// متغير القالب
    /// </summary>
    public class TemplateVariable
    {
        public string Variable { get; set; }
        public string Description { get; set; }

        public TemplateVariable(string variable, string description)
        {
            Variable = variable;
            Description = description;
        }
    }
}
