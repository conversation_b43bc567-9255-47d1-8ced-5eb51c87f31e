<Window x:Class="SimpleRentalApp.Windows.PrintReceiptWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="معاينة وطباعة الإيصال" 
        Height="800" Width="700"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        Background="#F5F5F5">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header with buttons -->
        <Border Grid.Row="0" 
                Background="#2196F3" 
                Padding="20">
            <StackPanel Orientation="Horizontal" 
                        HorizontalAlignment="Center">
                <Button Name="PreviewPrintButton"
                        Content="👁️ معاينة وطباعة"
                        Width="140"
                        Height="40"
                        FontSize="14"
                        FontWeight="Bold"
                        Background="#9C27B0"
                        Foreground="White"
                        BorderThickness="0"
                        Margin="0,0,15,0"
                        Click="PreviewPrintButton_Click"/>

                <Button Name="PrintButton"
                        Content="🖨️ طباعة مباشرة"
                        Width="130"
                        Height="40"
                        FontSize="14"
                        FontWeight="Bold"
                        Background="#2196F3"
                        Foreground="White"
                        BorderThickness="0"
                        Margin="0,0,15,0"
                        Click="PrintButton_Click"/>

                <Button Name="SaveAsPdfButton"
                        Content="📄 حفظ PDF"
                        Width="120"
                        Height="40"
                        FontSize="14"
                        FontWeight="Bold"
                        Background="#4CAF50"
                        Foreground="White"
                        BorderThickness="0"
                        Margin="0,0,15,0"
                        Click="SaveAsPdfButton_Click"/>

                <Button Name="CloseButton"
                        Content="❌ إغلاق"
                        Width="100"
                        Height="40"
                        FontSize="14"
                        Background="#F44336"
                        Foreground="White"
                        BorderThickness="0"
                        Click="CloseButton_Click"/>
            </StackPanel>
        </Border>

        <!-- Receipt Preview -->
        <ScrollViewer Grid.Row="1" 
                      Background="White"
                      Margin="20,38,20,2"
                      Padding="20"
                      VerticalScrollBarVisibility="Auto">
            <Border Name="ReceiptBorder"
                    Background="White"
                    BorderBrush="#E0E0E0"
                    BorderThickness="1"
                    Padding="40">

                <!-- Receipt Content -->
                <StackPanel Name="ReceiptContent">

                    <!-- Header -->
                    <Border Background="#2196F3" 
                            Padding="20"
                            Margin="0,0,0,30">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="🏢 إيصال كراء" 
                                       FontSize="28" 
                                       FontWeight="Bold"
                                       Foreground="White"
                                       HorizontalAlignment="Center"
                                       Margin="0,0,0,10"/>
                            <TextBlock Text="RENT RECEIPT" 
                                       FontSize="16" 
                                       Foreground="#E3F2FD"
                                       HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <!-- Receipt Number and Date -->
                    <Grid Margin="0,0,0,30">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="رقم الإيصال:" 
                                       FontSize="14" 
                                       FontWeight="Bold"
                                       Foreground="#666"/>
                            <TextBlock Name="ReceiptNumberText"
                                       Text="R2025-0001" 
                                       FontSize="18" 
                                       FontWeight="Bold"
                                       Foreground="#2196F3"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" 
                                    HorizontalAlignment="Left">
                            <TextBlock Text="تاريخ الإصدار:" 
                                       FontSize="14" 
                                       FontWeight="Bold"
                                       Foreground="#666"/>
                            <TextBlock Name="IssueDateText"
                                       Text="01/01/2025" 
                                       FontSize="16" 
                                       FontWeight="Bold"/>
                        </StackPanel>
                    </Grid>

                    <!-- Tenant and Property Info -->
                    <Border Background="#F8F9FA" 
                            BorderBrush="#E0E0E0"
                            BorderThickness="1"
                            Padding="20"
                            Margin="0,0,0,30">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0"
                                       Text="معلومات المستأجر والعقار"
                                       FontSize="16"
                                       FontWeight="Bold"
                                       Foreground="#2196F3"
                                       Margin="0,0,0,15"/>

                            <StackPanel Grid.Row="1" 
                                        Orientation="Horizontal"
                                        Margin="0,0,0,10">
                                <TextBlock Text="اسم المستأجر: " 
                                           FontWeight="Bold"
                                           Width="120"/>
                                <TextBlock Name="TenantNameText"
                                           Text="محمد أحمد"/>
                            </StackPanel>

                            <StackPanel Grid.Row="2" 
                                        Orientation="Horizontal"
                                        Margin="0,0,0,10">
                                <TextBlock Text="عنوان العقار: " 
                                           FontWeight="Bold"
                                           Width="120"/>
                                <TextBlock Name="PropertyAddressText"
                                           Text="شقة رقم 5، الطابق الثاني"
                                           TextWrapping="Wrap"/>
                            </StackPanel>

                            <StackPanel Grid.Row="3" 
                                        Orientation="Horizontal">
                                <TextBlock Text="رقم العقد: " 
                                           FontWeight="Bold"
                                           Width="120"/>
                                <TextBlock Name="ContractNumberText"
                                           Text="001"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- Payment Details -->
                    <Border Background="#E8F5E8" 
                            BorderBrush="#4CAF50"
                            BorderThickness="2"
                            Padding="20"
                            Margin="0,0,0,30">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0"
                                       Text="تفاصيل الدفع"
                                       FontSize="16"
                                       FontWeight="Bold"
                                       Foreground="#2E7D32"
                                       Margin="0,0,0,15"/>

                            <StackPanel Grid.Row="1" 
                                        Orientation="Horizontal"
                                        Margin="0,0,0,10">
                                <TextBlock Text="الفترة المؤداة: " 
                                           FontWeight="Bold"
                                           Width="120"/>
                                <TextBlock Name="PaymentPeriodText"
                                           Text="يناير 2025"/>
                            </StackPanel>

                            <StackPanel Grid.Row="2" 
                                        Orientation="Horizontal"
                                        Margin="0,0,0,10">
                                <TextBlock Text="تاريخ الأداء: " 
                                           FontWeight="Bold"
                                           Width="120"/>
                                <TextBlock Name="PaymentDateText"
                                           Text="01/01/2025"/>
                            </StackPanel>

                            <StackPanel Grid.Row="3" 
                                        Orientation="Horizontal"
                                        Margin="0,0,0,10">
                                <TextBlock Text="طريقة الدفع: " 
                                           FontWeight="Bold"
                                           Width="120"/>
                                <TextBlock Name="PaymentMethodText"
                                           Text="نقداً"/>
                            </StackPanel>

                            <StackPanel Grid.Row="4" 
                                        Orientation="Horizontal"
                                        Margin="0,0,0,15">
                                <TextBlock Text="المبلغ المؤدى: " 
                                           FontWeight="Bold"
                                           Width="120"/>
                                <TextBlock Name="AmountText"
                                           Text="5,000 درهم"
                                           FontSize="18"
                                           FontWeight="Bold"
                                           Foreground="#2E7D32"/>
                            </StackPanel>

                            <Border Grid.Row="5"
                                    Background="White"
                                    BorderBrush="#4CAF50"
                                    BorderThickness="1"
                                    Padding="15">
                                <StackPanel>
                                    <TextBlock Text="المبلغ بالحروف:"
                                               FontWeight="Bold"
                                               Margin="0,0,0,5"/>
                                    <TextBlock Name="AmountInWordsText"
                                               Text="خمسة آلاف درهم لا غير"
                                               FontStyle="Italic"
                                               TextWrapping="Wrap"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </Border>

                    <!-- Notes -->
                    <Border Name="NotesSection"
                            Background="#FFF3E0" 
                            BorderBrush="#FF9800"
                            BorderThickness="1"
                            Padding="20"
                            Margin="0,0,0,30"
                            Visibility="Collapsed">
                        <StackPanel>
                            <TextBlock Text="ملاحظات:"
                                       FontWeight="Bold"
                                       Foreground="#E65100"
                                       Margin="0,0,0,10"/>
                            <TextBlock Name="NotesText"
                                       TextWrapping="Wrap"/>
                        </StackPanel>
                    </Border>

                    <!-- Footer -->
                    <Border Background="#F5F5F5" 
                            Padding="20"
                            Margin="0,30,0,0">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="توقيع المستأجر:"
                                           FontWeight="Bold"
                                           Margin="0,0,0,30"/>
                                <Line X1="0" X2="150" Y1="0" Y2="0" 
                                      Stroke="Black" 
                                      StrokeThickness="1"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" 
                                        HorizontalAlignment="Left">
                                <TextBlock Text="ختم وتوقيع المؤجر:"
                                           FontWeight="Bold"
                                           Margin="0,0,0,30"/>
                                <Line X1="0" X2="150" Y1="0" Y2="0" 
                                      Stroke="Black" 
                                      StrokeThickness="1"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                </StackPanel>
            </Border>
        </ScrollViewer>

        <!-- Status Bar -->
        <Border Grid.Row="2" 
                Background="#E0E0E0" 
                Padding="10">
            <TextBlock Name="StatusText"
                       Text="جاهز للطباعة"
                       FontSize="12"
                       HorizontalAlignment="Center"/>
        </Border>
    </Grid>
</Window>
