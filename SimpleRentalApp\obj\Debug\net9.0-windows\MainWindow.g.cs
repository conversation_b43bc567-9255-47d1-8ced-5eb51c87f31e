﻿#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "8B1F78C25F239953617C491BA66ABE36788C6516"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleRentalApp {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 169 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserInfoTextBlock;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LogoutButton;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        
        #line 216 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DashboardBtn;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CustomersBtn;
        
        #line default
        #line hidden
        
        
        #line 229 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PropertiesBtn;
        
        #line default
        #line hidden
        
        
        #line 234 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PaymentsManagementBtn;
        
        #line default
        #line hidden
        
        
        #line 239 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ContractsManagementBtn;
        
        #line default
        #line hidden
        
        
        #line 244 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CleaningTaxManagementBtn;
        
        #line default
        #line hidden
        
        
        #line 249 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button WhatsAppNotificationsBtn;
        
        #line default
        #line hidden
        
        
        #line 254 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ReportsBtn;
        
        #line default
        #line hidden
        
        
        #line 259 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DataManagementBtn;
        
        #line default
        #line hidden
        
        
        #line 272 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer ContentArea;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleRentalApp;V1.0.0.0;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.UserInfoTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.LogoutButton = ((System.Windows.Controls.Button)(target));
            
            #line 191 "..\..\..\MainWindow.xaml"
            this.LogoutButton.Click += new System.Windows.RoutedEventHandler(this.LogoutButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 200 "..\..\..\MainWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.DashboardBtn = ((System.Windows.Controls.Button)(target));
            
            #line 223 "..\..\..\MainWindow.xaml"
            this.DashboardBtn.Click += new System.Windows.RoutedEventHandler(this.DashboardBtn_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.CustomersBtn = ((System.Windows.Controls.Button)(target));
            
            #line 228 "..\..\..\MainWindow.xaml"
            this.CustomersBtn.Click += new System.Windows.RoutedEventHandler(this.CustomersBtn_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.PropertiesBtn = ((System.Windows.Controls.Button)(target));
            
            #line 233 "..\..\..\MainWindow.xaml"
            this.PropertiesBtn.Click += new System.Windows.RoutedEventHandler(this.PropertiesBtn_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.PaymentsManagementBtn = ((System.Windows.Controls.Button)(target));
            
            #line 238 "..\..\..\MainWindow.xaml"
            this.PaymentsManagementBtn.Click += new System.Windows.RoutedEventHandler(this.PaymentsManagementBtn_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ContractsManagementBtn = ((System.Windows.Controls.Button)(target));
            
            #line 243 "..\..\..\MainWindow.xaml"
            this.ContractsManagementBtn.Click += new System.Windows.RoutedEventHandler(this.ContractsManagementBtn_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.CleaningTaxManagementBtn = ((System.Windows.Controls.Button)(target));
            
            #line 248 "..\..\..\MainWindow.xaml"
            this.CleaningTaxManagementBtn.Click += new System.Windows.RoutedEventHandler(this.CleaningTaxManagementBtn_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.WhatsAppNotificationsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 253 "..\..\..\MainWindow.xaml"
            this.WhatsAppNotificationsBtn.Click += new System.Windows.RoutedEventHandler(this.WhatsAppNotificationsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.ReportsBtn = ((System.Windows.Controls.Button)(target));
            
            #line 258 "..\..\..\MainWindow.xaml"
            this.ReportsBtn.Click += new System.Windows.RoutedEventHandler(this.ReportsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.DataManagementBtn = ((System.Windows.Controls.Button)(target));
            
            #line 263 "..\..\..\MainWindow.xaml"
            this.DataManagementBtn.Click += new System.Windows.RoutedEventHandler(this.DataManagementBtn_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.ContentArea = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

