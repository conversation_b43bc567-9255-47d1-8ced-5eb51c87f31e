<Window x:Class="SimpleRentalApp.Windows.CleaningTaxManagementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة ضريبة النظافة" 
        Height="700" 
        Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        Background="#F5F5F5">

    <Window.Resources>
        <!-- Styles for modern UI -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="DeleteButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#F44336"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#D32F2F"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="PrintButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#9C27B0"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#7B1FA2"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="RefreshButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#4CAF50"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#388E3C"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="HeaderTextBlock" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#333"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>

        <Style x:Key="ModernDataGrid" TargetType="DataGrid">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HorizontalGridLinesBrush" Value="#F0F0F0"/>
            <Setter Property="RowBackground" Value="White"/>
            <Setter Property="AlternatingRowBackground" Value="#FAFAFA"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
            <Setter Property="AutoGenerateColumns" Value="False"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="CanUserDeleteRows" Value="False"/>
            <Setter Property="IsReadOnly" Value="True"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="FontSize" Value="12"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" 
                Background="#2196F3" 
                CornerRadius="8,8,0,0" 
                Padding="20,15">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="🧹" FontSize="24" Margin="0,0,10,0"/>
                <TextBlock Text="إدارة ضريبة النظافة" 
                          FontSize="20" 
                          FontWeight="Bold" 
                          Foreground="White"/>
            </StackPanel>
        </Border>

        <!-- Year Selection and Controls -->
        <Border Grid.Row="1" 
                Background="White" 
                BorderBrush="#E0E0E0" 
                BorderThickness="1,0,1,1" 
                Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" 
                          Text="السنة الضريبية:" 
                          VerticalAlignment="Center" 
                          FontWeight="Bold" 
                          Margin="0,0,10,0"/>

                <ComboBox Grid.Column="1" 
                         Name="YearComboBox" 
                         Height="35" 
                         FontSize="12" 
                         VerticalContentAlignment="Center"/>

                <StackPanel Grid.Column="3"
                           Orientation="Horizontal">
                    <Button Name="RefreshButton"
                           Content="🔄 تحديث البيانات"
                           Style="{StaticResource RefreshButton}"/>
                    <Button Name="ExportButton"
                           Content="📊 تصدير البيانات"
                           Style="{StaticResource ModernButton}"
                           Click="ExportButton_Click"/>

                </StackPanel>
            </Grid>
        </Border>

        <!-- Statistics Panel -->
        <Border Grid.Row="2" 
                Background="White" 
                BorderBrush="#E0E0E0" 
                BorderThickness="1,0,1,1" 
                Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Total Properties -->
                <Border Grid.Column="0" 
                        Background="#E3F2FD" 
                        CornerRadius="6" 
                        Padding="15,10" 
                        Margin="0,0,10,0">
                    <StackPanel>
                        <TextBlock Text="إجمالي المحلات" 
                                  FontWeight="Bold" 
                                  FontSize="12" 
                                  Foreground="#1976D2"/>
                        <TextBlock Name="TotalPropertiesText" 
                                  Text="0" 
                                  FontSize="18" 
                                  FontWeight="Bold" 
                                  Foreground="#1976D2"/>
                    </StackPanel>
                </Border>

                <!-- Paid Properties -->
                <Border Grid.Column="1" 
                        Background="#E8F5E8" 
                        CornerRadius="6" 
                        Padding="15,10" 
                        Margin="0,0,10,0">
                    <StackPanel>
                        <TextBlock Text="محلات مدفوعة" 
                                  FontWeight="Bold" 
                                  FontSize="12" 
                                  Foreground="#388E3C"/>
                        <TextBlock Name="PaidPropertiesText" 
                                  Text="0" 
                                  FontSize="18" 
                                  FontWeight="Bold" 
                                  Foreground="#388E3C"/>
                    </StackPanel>
                </Border>

                <!-- Partial Properties -->
                <Border Grid.Column="2" 
                        Background="#FFF3E0" 
                        CornerRadius="6" 
                        Padding="15,10" 
                        Margin="0,0,10,0">
                    <StackPanel>
                        <TextBlock Text="محلات جزئية" 
                                  FontWeight="Bold" 
                                  FontSize="12" 
                                  Foreground="#F57C00"/>
                        <TextBlock Name="PartialPropertiesText" 
                                  Text="0" 
                                  FontSize="18" 
                                  FontWeight="Bold" 
                                  Foreground="#F57C00"/>
                    </StackPanel>
                </Border>

                <!-- Unpaid Properties -->
                <Border Grid.Column="3" 
                        Background="#FFEBEE" 
                        CornerRadius="6" 
                        Padding="15,10">
                    <StackPanel>
                        <TextBlock Text="محلات غير مدفوعة" 
                                  FontWeight="Bold" 
                                  FontSize="12" 
                                  Foreground="#D32F2F"/>
                        <TextBlock Name="UnpaidPropertiesText" 
                                  Text="0" 
                                  FontSize="18" 
                                  FontWeight="Bold" 
                                  Foreground="#D32F2F"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>

        <!-- Data Grid -->
        <Border Grid.Row="3" 
                Background="White" 
                BorderBrush="#E0E0E0" 
                BorderThickness="1,0,1,1" 
                CornerRadius="0,0,8,8">
            <DataGrid Name="PropertiesDataGrid" 
                     Style="{StaticResource ModernDataGrid}" 
                     Margin="1">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="اسم المحل"
                                       Binding="{Binding PropertyName}"
                                       Width="180"
                                       FontWeight="Bold">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Padding" Value="8,4"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <DataGridTextColumn Header="اسم المكتري"
                                       Binding="{Binding CustomerName}"
                                       Width="140">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Padding" Value="8,4"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <DataGridTextColumn Header="طريقة الدفع"
                                       Binding="{Binding PaymentOption}"
                                       Width="110">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Padding" Value="8,4"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="FontWeight" Value="Medium"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <DataGridTextColumn Header="الضريبة السنوية"
                                       Binding="{Binding AnnualTaxDisplay}"
                                       Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Padding" Value="8,4"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="TextAlignment" Value="Center"/>
                                <Setter Property="Foreground" Value="#1976D2"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <DataGridTextColumn Header="المبلغ المدفوع"
                                       Binding="{Binding PaidAmountDisplay}"
                                       Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Padding" Value="8,4"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="TextAlignment" Value="Center"/>
                                <Setter Property="Foreground" Value="#388E3C"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <DataGridTextColumn Header="المبلغ المتبقي"
                                       Binding="{Binding RemainingAmountDisplay}"
                                       Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="Padding" Value="8,4"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="TextAlignment" Value="Center"/>
                                <Setter Property="Foreground" Value="#F57C00"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <DataGridTemplateColumn Header="الحالة" Width="100">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border CornerRadius="12"
                                       Padding="8,4"
                                       Margin="4,2"
                                       HorizontalAlignment="Center">
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Status}" Value="مكتملة">
                                                    <Setter Property="Background" Value="#E8F5E8"/>
                                                    <Setter Property="BorderBrush" Value="#4CAF50"/>
                                                    <Setter Property="BorderThickness" Value="1"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Status}" Value="جزئية">
                                                    <Setter Property="Background" Value="#FFF3E0"/>
                                                    <Setter Property="BorderBrush" Value="#FF9800"/>
                                                    <Setter Property="BorderThickness" Value="1"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Status}" Value="غير مدفوعة">
                                                    <Setter Property="Background" Value="#FFEBEE"/>
                                                    <Setter Property="BorderBrush" Value="#F44336"/>
                                                    <Setter Property="BorderThickness" Value="1"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    <TextBlock Text="{Binding StatusDisplay}"
                                              FontSize="10"
                                              FontWeight="Bold"
                                              TextAlignment="Center">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Status}" Value="مكتملة">
                                                        <Setter Property="Foreground" Value="#2E7D32"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="جزئية">
                                                        <Setter Property="Foreground" Value="#E65100"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="غير مدفوعة">
                                                        <Setter Property="Foreground" Value="#C62828"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                </Border>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <DataGridTemplateColumn Header="الإجراءات" Width="220">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button Content="👁️"
                                           ToolTip="عرض التفاصيل"
                                           Style="{StaticResource ModernButton}"
                                           Click="ViewDetailsButton_Click"
                                           Tag="{Binding}"
                                           Margin="1"
                                           Padding="6,4"
                                           FontSize="10"
                                           Width="30"/>

                                    <Button Content="🖨️"
                                           ToolTip="طباعة الوصل"
                                           Style="{StaticResource PrintButton}"
                                           Click="PrintButton_Click"
                                           Tag="{Binding PropertyId}"
                                           Margin="1"
                                           Padding="6,4"
                                           FontSize="10"
                                           Width="30"/>

                                    <Button Content="➕"
                                           ToolTip="إضافة دفعة"
                                           Style="{StaticResource RefreshButton}"
                                           Click="AddPaymentForPropertyButton_Click"
                                           Tag="{Binding PropertyId}"
                                           Margin="1"
                                           Padding="6,4"
                                           FontSize="10"
                                           Width="30"/>

                                    <Button Content="🗑️"
                                           ToolTip="حذف الدفعات"
                                           Style="{StaticResource DeleteButton}"
                                           Click="DeleteButton_Click"
                                           Tag="{Binding PropertyId}"
                                           Margin="1"
                                           Padding="6,4"
                                           FontSize="10"
                                           Width="30"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- Status Bar -->
        <Border Grid.Row="4" 
                Background="#F5F5F5" 
                BorderBrush="#E0E0E0" 
                BorderThickness="0,1,0,0" 
                Padding="20,10">
            <TextBlock Name="StatusTextBlock" 
                      Text="جاهز" 
                      FontSize="12" 
                      Foreground="#666"/>
        </Border>
    </Grid>
</Window>
