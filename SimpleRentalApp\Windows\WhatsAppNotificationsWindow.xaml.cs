using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;
using SimpleRentalApp.Data;
using SimpleRentalApp.Models;
using SimpleRentalApp.Services;

namespace SimpleRentalApp.Windows
{
    public partial class WhatsAppNotificationsWindow : Window
    {
        private readonly RentalDbContext _context;
        private readonly WhatsAppNotificationService _notificationService;
        private List<NotificationViewModel> _allNotifications = new();
        private List<NotificationViewModel> _filteredNotifications = new();
        private bool _sortAscending = false;

        public WhatsAppNotificationsWindow()
        {
            InitializeComponent();
            _context = new RentalDbContext();
            _notificationService = new WhatsAppNotificationService();
            LoadNotifications();
        }

        private async void LoadNotifications()
        {
            try
            {
                StatusTextBlock.Text = "جاري تحميل البيانات...";
                
                // جلب جميع المكترين مع بياناتهم
                var customers = await _context.Customers
                    .Include(c => c.Properties)
                    .Where(c => c.Phone != null && c.Phone != "")
                    .ToListAsync();

                _allNotifications.Clear();

                foreach (var customer in customers)
                {
                    foreach (var property in customer.Properties)
                    {
                        // التحقق من حالة الكراء الشهري
                        var currentMonth = DateTime.Now.Month;
                        var currentYear = DateTime.Now.Year;
                        var hasRentPayment = await _context.Payments
                            .AnyAsync(p => p.CustomerId == customer.Id &&
                                         p.PropertyId == property.Id &&
                                         p.PaymentDate.HasValue &&
                                         p.PaymentDate.Value.Month == currentMonth &&
                                         p.PaymentDate.Value.Year == currentYear);

                        // التحقق من حالة ضريبة النظافة
                        var hasCleaningTaxPayment = await _context.Payments
                            .AnyAsync(p => p.PropertyId == property.Id &&
                                          p.PaymentDate.HasValue &&
                                          p.PaymentDate.Value.Year == currentYear &&
                                          p.CleaningTaxAmount > 0);

                        // جلب يوم الأداء من العقد
                        var contract = await _context.Contracts
                            .FirstOrDefaultAsync(c => c.CustomerId == customer.Id &&
                                                    c.PropertyId == property.Id &&
                                                    c.IsActive);

                        var paymentDay = contract?.PaymentDay ?? contract?.StartDate.Day ?? 1;
                        var dueDate = GetNextDueDate(property, paymentDay);

                        // التحقق من التأخير باستخدام القيمة المحددة
                        var daysLate = GetDaysLateFromInput();
                        var isRentLate = !hasRentPayment && DateTime.Now > dueDate.AddDays(daysLate);
                        var isCleaningLate = !hasCleaningTaxPayment && DateTime.Now > new DateTime(currentYear, 5, 31).AddDays(7);

                        var viewModel = new NotificationViewModel
                        {
                            CustomerId = customer.Id,
                            PropertyId = property.Id,
                            CustomerName = customer.FullName,
                            PropertyName = property.Name,
                            PhoneNumber = customer.Phone,
                            PaymentDayDisplay = paymentDay.ToString(),
                            DueDateDisplay = dueDate.ToString("dd/MM/yyyy"),
                            RentStatus = hasRentPayment ? "مدفوع" : "غير مدفوع",
                            CleaningStatus = hasCleaningTaxPayment ? "مؤدى" : "متأخر",
                            TypeDisplay = "-",
                            AmountDisplay = "-",
                            StatusDisplay = "-",
                            CreatedDateDisplay = "-",
                            CanSend = true,
                            IsLate = isRentLate || isCleaningLate
                        };

                        _allNotifications.Add(viewModel);
                    }
                }

                // جلب الإشعارات الموجودة
                var existingNotifications = await _context.WhatsAppNotifications
                    .Include(n => n.Customer)
                    .Include(n => n.Property)
                    .OrderByDescending(n => n.CreatedDate)
                    .ToListAsync();

                foreach (var notification in existingNotifications)
                {
                    var existingViewModel = _allNotifications.FirstOrDefault(vm => 
                        vm.CustomerId == notification.CustomerId && 
                        vm.PropertyId == notification.PropertyId);

                    if (existingViewModel != null)
                    {
                        existingViewModel.Id = notification.Id;
                        existingViewModel.TypeDisplay = GetTypeDisplay(notification.Type);
                        existingViewModel.AmountDisplay = $"{notification.Amount:N0} درهم";
                        existingViewModel.StatusDisplay = GetStatusDisplay(notification.Status);
                        existingViewModel.CreatedDateDisplay = notification.CreatedDate.ToString("dd/MM/yyyy HH:mm");
                        existingViewModel.CanSend = notification.Status == NotificationStatus.Pending || 
                                                   notification.Status == NotificationStatus.Failed;
                    }
                }

                ApplyFilters();
                UpdateStatusBar();
                StatusTextBlock.Text = "تم تحميل البيانات بنجاح";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "خطأ في التحميل";
            }
        }

        private DateTime GetNextDueDate(Property property, int paymentDay = 1)
        {
            var today = DateTime.Now;
            var dueDate = new DateTime(today.Year, today.Month, Math.Min(paymentDay, DateTime.DaysInMonth(today.Year, today.Month)));

            if (dueDate < today)
                dueDate = dueDate.AddMonths(1);

            return dueDate;
        }

        private int GetDaysLateFromInput()
        {
            if (int.TryParse(DaysLateTextBox.Text, out int days) && days > 0)
                return days;
            return 3; // افتراضي
        }

        private string GetTypeDisplay(NotificationType type)
        {
            return type switch
            {
                NotificationType.MonthlyRent => "كراء شهري",
                NotificationType.CleaningTax => "ضريبة النظافة",
                NotificationType.ContractExpiry => "انتهاء العقد",
                NotificationType.RentOverdue => "دفعة متأخرة",
                NotificationType.PaymentReminder => "إشعار متأخر",
                _ => "غير محدد"
            };
        }

        private string GetStatusDisplay(NotificationStatus status)
        {
            return status switch
            {
                NotificationStatus.Pending => "معلق",
                NotificationStatus.Sent => "تم الإرسال",
                NotificationStatus.Failed => "فشل",
                NotificationStatus.Cancelled => "ملغي",
                _ => "غير محدد"
            };
        }

        private void ApplyFilters()
        {
            _filteredNotifications = _allNotifications.ToList();

            // تصفية حسب النوع
            if (TypeFilterComboBox.SelectedItem is ComboBoxItem typeItem && typeItem.Tag != null)
            {
                var selectedType = typeItem.Tag.ToString();
                _filteredNotifications = _filteredNotifications
                    .Where(n => n.TypeDisplay.Contains(GetTypeDisplayFromTag(selectedType)))
                    .ToList();
            }

            // تصفية حسب الحالة
            if (StatusFilterComboBox.SelectedItem is ComboBoxItem statusItem && statusItem.Tag != null)
            {
                var selectedStatus = statusItem.Tag.ToString();
                _filteredNotifications = _filteredNotifications
                    .Where(n => n.StatusDisplay.Contains(GetStatusDisplayFromTag(selectedStatus)))
                    .ToList();
            }

            // Apply date range filter
            if (DateRangeFilterComboBox?.SelectedItem is ComboBoxItem dateItem && dateItem.Tag != null)
            {
                var dateFilter = dateItem.Tag.ToString();
                var today = DateTime.Today;

                _filteredNotifications = dateFilter switch
                {
                    "Today" => _filteredNotifications.Where(n => n.DueDate.Date == today).ToList(),
                    "ThisWeek" => _filteredNotifications.Where(n => n.DueDate.Date >= today &&
                                                                   n.DueDate.Date <= today.AddDays(7)).ToList(),
                    "ThisMonth" => _filteredNotifications.Where(n => n.DueDate.Month == today.Month &&
                                                                     n.DueDate.Year == today.Year).ToList(),
                    "Overdue" => _filteredNotifications.Where(n => n.DueDate.Date < today).ToList(),
                    "DueSoon" => _filteredNotifications.Where(n => n.DueDate.Date >= today &&
                                                                  n.DueDate.Date <= today.AddDays(7)).ToList(),
                    _ => _filteredNotifications
                };
            }

            // تصفية حسب البحث
            if (!string.IsNullOrWhiteSpace(SearchTextBox.Text) &&
                SearchTextBox.Text != "البحث بالاسم أو المحل..." &&
                SearchTextBox.Foreground != System.Windows.Media.Brushes.Gray)
            {
                var searchText = SearchTextBox.Text.ToLower();
                _filteredNotifications = _filteredNotifications
                    .Where(n => n.CustomerName.ToLower().Contains(searchText) ||
                               n.PropertyName.ToLower().Contains(searchText))
                    .ToList();
            }

            // Apply sorting
            if (SortComboBox?.SelectedItem is ComboBoxItem sortItem && sortItem.Tag != null)
            {
                var sortBy = sortItem.Tag.ToString();

                _filteredNotifications = sortBy switch
                {
                    "DueDate" => _sortAscending ?
                        _filteredNotifications.OrderBy(n => n.DueDate).ToList() :
                        _filteredNotifications.OrderByDescending(n => n.DueDate).ToList(),
                    "CreatedDate" => _sortAscending ?
                        _filteredNotifications.OrderBy(n => n.CreatedDate).ToList() :
                        _filteredNotifications.OrderByDescending(n => n.CreatedDate).ToList(),
                    "CustomerName" => _sortAscending ?
                        _filteredNotifications.OrderBy(n => n.CustomerName).ToList() :
                        _filteredNotifications.OrderByDescending(n => n.CustomerName).ToList(),
                    "Type" => _sortAscending ?
                        _filteredNotifications.OrderBy(n => n.TypeDisplay).ToList() :
                        _filteredNotifications.OrderByDescending(n => n.TypeDisplay).ToList(),
                    "Status" => _sortAscending ?
                        _filteredNotifications.OrderBy(n => n.StatusDisplay).ToList() :
                        _filteredNotifications.OrderByDescending(n => n.StatusDisplay).ToList(),
                    _ => _filteredNotifications.OrderByDescending(n => n.DueDate).ToList()
                };
            }
            else
            {
                // Default sorting by due date
                _filteredNotifications = _filteredNotifications.OrderByDescending(n => n.DueDate).ToList();
            }

            NotificationsDataGrid.ItemsSource = _filteredNotifications;
            UpdateStatusBar();
        }

        private string GetTypeDisplayFromTag(string tag)
        {
            return tag switch
            {
                "MonthlyRent" => "كراء شهري",
                "CleaningTax" => "ضريبة النظافة",
                "OverduePayment" => "دفعة متأخرة",
                "LateReminder" => "إشعار متأخر",
                _ => ""
            };
        }

        private string GetStatusDisplayFromTag(string tag)
        {
            return tag switch
            {
                "Pending" => "معلق",
                "Sent" => "تم الإرسال",
                "Failed" => "فشل",
                _ => ""
            };
        }

        private void UpdateStatusBar()
        {
            CountTextBlock.Text = $"{_filteredNotifications.Count} إشعار";
            
            var pendingCount = _filteredNotifications.Count(n => n.StatusDisplay == "معلق");
            var sentCount = _filteredNotifications.Count(n => n.StatusDisplay == "تم الإرسال");
            
            PendingCountTextBlock.Text = $"{pendingCount} معلق";
            SentCountTextBlock.Text = $"{sentCount} مرسل";
        }

        private async void GenerateMonthlyRentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                StatusTextBlock.Text = "جاري توليد إشعارات الكراء الشهري...";
                var notifications = await _notificationService.CreateMonthlyRentNotificationsAsync();
                
                MessageBox.Show($"تم توليد {notifications.Count} إشعار كراء شهري", "نجح التوليد",
                    MessageBoxButton.OK, MessageBoxImage.Information);
                
                LoadNotifications();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في توليد الإشعارات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void GenerateCleaningTaxButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                StatusTextBlock.Text = "جاري توليد إشعارات ضريبة النظافة...";
                var notifications = await _notificationService.CreateOverdueCleaningTaxNotificationsAsync();

                MessageBox.Show($"تم توليد {notifications.Count} إشعار ضريبة نظافة", "نجح التوليد",
                    MessageBoxButton.OK, MessageBoxImage.Information);

                LoadNotifications();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في توليد الإشعارات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void GenerateLateRemindersButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var daysLate = GetDaysLateFromInput();
                StatusTextBlock.Text = $"جاري توليد الإشعارات المتأخرة (بعد {daysLate} أيام)...";

                var rentNotifications = await _notificationService.CreateLateRentNotificationsAsync();
                var cleaningNotifications = await _notificationService.CreateLateCleaningTaxNotificationsAsync();

                var totalCount = rentNotifications.Count + cleaningNotifications.Count;

                MessageBox.Show($"تم توليد {totalCount} إشعار متأخر (بعد {daysLate} أيام تأخير)\n" +
                               $"• {rentNotifications.Count} إشعار كراء متأخر\n" +
                               $"• {cleaningNotifications.Count} إشعار ضريبة نظافة متأخر",
                               "نجح التوليد", MessageBoxButton.OK, MessageBoxImage.Information);

                LoadNotifications();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في توليد الإشعارات المتأخرة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        private void TypeFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_allNotifications.Any())
                ApplyFilters();
        }

        private void StatusFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_allNotifications.Any())
                ApplyFilters();
        }

        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (SearchTextBox.Text == "البحث بالاسم أو المحل...")
            {
                SearchTextBox.Text = "";
                SearchTextBox.Foreground = System.Windows.Media.Brushes.Black;
            }
        }

        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                SearchTextBox.Text = "البحث بالاسم أو المحل...";
                SearchTextBox.Foreground = System.Windows.Media.Brushes.Gray;
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_allNotifications.Any())
                ApplyFilters();
        }

        private async void SendRentNotificationButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int customerId)
            {
                try
                {
                    // إنشاء إشعار كراء فوري
                    var customer = await _context.Customers.FindAsync(customerId);
                    var property = await _context.Properties.FirstOrDefaultAsync(p => p.CustomerId == customerId);

                    if (customer != null && property != null)
                    {
                        await CreateAndSendRentNotification(customer, property);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في إرسال إشعار الكراء: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void SendCleaningNotificationButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int customerId)
            {
                try
                {
                    // إنشاء إشعار ضريبة نظافة فوري
                    var customer = await _context.Customers.FindAsync(customerId);
                    var property = await _context.Properties.FirstOrDefaultAsync(p => p.CustomerId == customerId);

                    if (customer != null && property != null)
                    {
                        await CreateAndSendCleaningNotification(customer, property);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في إرسال إشعار ضريبة النظافة: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void SendLateReminderButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int customerId)
            {
                try
                {
                    // إنشاء إشعار متأخر فوري
                    var customer = await _context.Customers.FindAsync(customerId);
                    var property = await _context.Properties.FirstOrDefaultAsync(p => p.CustomerId == customerId);

                    if (customer != null && property != null)
                    {
                        await CreateAndSendLateReminder(customer, property);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في إرسال الإشعار المتأخر: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void SendNotificationButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int notificationId)
            {
                try
                {
                    var notification = await _context.WhatsAppNotifications
                        .Include(n => n.Customer)
                        .Include(n => n.Property)
                        .FirstOrDefaultAsync(n => n.Id == notificationId);

                    if (notification != null)
                    {
                        await SendWhatsAppMessage(notification);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في إرسال الإشعار: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void PreviewNotificationButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int notificationId)
            {
                try
                {
                    var notification = await _context.WhatsAppNotifications.FindAsync(notificationId);
                    if (notification != null)
                    {
                        var previewDialog = new NotificationPreviewDialog(notification.Message, notification.PhoneNumber);
                        previewDialog.ShowDialog();
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في معاينة الإشعار: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void DeleteNotificationButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int notificationId)
            {
                var result = MessageBox.Show("هل أنت متأكد من حذف هذا الإشعار؟", "تأكيد الحذف",
                    MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        var notification = await _context.WhatsAppNotifications.FindAsync(notificationId);
                        if (notification != null)
                        {
                            _context.WhatsAppNotifications.Remove(notification);
                            await _context.SaveChangesAsync();
                            LoadNotifications();
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف الإشعار: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private async void SendAllButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var pendingNotifications = await _context.WhatsAppNotifications
                    .Include(n => n.Customer)
                    .Include(n => n.Property)
                    .Where(n => n.Status == NotificationStatus.Pending)
                    .ToListAsync();

                if (!pendingNotifications.Any())
                {
                    MessageBox.Show("لا توجد إشعارات معلقة للإرسال", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                var result = MessageBox.Show($"هل تريد إرسال {pendingNotifications.Count} إشعار؟", "تأكيد الإرسال",
                    MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    int successCount = 0;
                    foreach (var notification in pendingNotifications)
                    {
                        try
                        {
                            await SendWhatsAppMessage(notification);
                            successCount++;
                        }
                        catch
                        {
                            // تجاهل الأخطاء الفردية
                        }
                    }

                    MessageBox.Show($"تم إرسال {successCount} من {pendingNotifications.Count} إشعار", "نتيجة الإرسال",
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    LoadNotifications();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إرسال الإشعارات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void TemplatesButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var templatesWindow = new MessageTemplatesWindow();
                templatesWindow.Owner = this;
                templatesWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة القوالب: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task CreateAndSendRentNotification(Customer customer, Property property)
        {
            // جلب يوم الأداء من العقد
            var contract = await _context.Contracts
                .FirstOrDefaultAsync(c => c.CustomerId == customer.Id &&
                                        c.PropertyId == property.Id &&
                                        c.IsActive);

            var paymentDay = contract?.PaymentDay ?? contract?.StartDate.Day ?? 1;
            var currentMonth = DateTime.Now.ToString("MMMM yyyy", new CultureInfo("ar-SA"));
            var dueDate = GetNextDueDate(property, paymentDay);

            var notification = new WhatsAppNotification
            {
                CustomerId = customer.Id,
                PropertyId = property.Id,
                Type = NotificationType.MonthlyRent,
                PhoneNumber = customer.Phone,
                Amount = property.MonthlyRent,
                DueDate = dueDate,
                Month = currentMonth,
                Year = DateTime.Now.Year,
                ScheduledDate = DateTime.Now,
                Status = NotificationStatus.Pending,
                UniqueKey = $"MANUAL_RENT_{customer.Id}_{property.Id}_{DateTime.Now.Ticks}"
            };

            notification.Message = await _notificationService.GenerateMessageAsync(notification);

            _context.WhatsAppNotifications.Add(notification);
            await _context.SaveChangesAsync();

            await SendWhatsAppMessage(notification);
            LoadNotifications();
        }

        private async Task CreateAndSendCleaningNotification(Customer customer, Property property)
        {
            var currentYear = DateTime.Now.Year;
            var cleaningTaxAmount = property.MonthlyRent * 12 * 0.105m;
            var deadline = new DateTime(currentYear, 5, 31);

            var notification = new WhatsAppNotification
            {
                CustomerId = customer.Id,
                PropertyId = property.Id,
                Type = NotificationType.CleaningTax,
                PhoneNumber = customer.Phone,
                Amount = cleaningTaxAmount,
                DueDate = deadline,
                Year = currentYear,
                ScheduledDate = DateTime.Now,
                Status = NotificationStatus.Pending,
                UniqueKey = $"MANUAL_CLEANING_{customer.Id}_{property.Id}_{DateTime.Now.Ticks}"
            };

            notification.Message = await _notificationService.GenerateMessageAsync(notification);

            _context.WhatsAppNotifications.Add(notification);
            await _context.SaveChangesAsync();

            await SendWhatsAppMessage(notification);
            LoadNotifications();
        }

        private async Task CreateAndSendLateReminder(Customer customer, Property property)
        {
            // جلب يوم الأداء من العقد
            var contract = await _context.Contracts
                .FirstOrDefaultAsync(c => c.CustomerId == customer.Id &&
                                        c.PropertyId == property.Id &&
                                        c.IsActive);

            var paymentDay = contract?.PaymentDay ?? contract?.StartDate.Day ?? 1;
            var currentDate = DateTime.Now;
            var dueDate = GetNextDueDate(property, paymentDay);
            var daysLate = GetDaysLateFromInput();

            // تحديد نوع التأخير (كراء أو ضريبة نظافة)
            var currentYear = currentDate.Year;
            var hasRentPayment = await _context.Payments
                .AnyAsync(p => p.CustomerId == customer.Id &&
                              p.PropertyId == property.Id &&
                              p.PaymentDate.HasValue &&
                              p.PaymentDate.Value.Month == dueDate.Month &&
                              p.PaymentDate.Value.Year == dueDate.Year &&
                              p.RentAmount > 0);

            var hasCleaningTaxPayment = await _context.Payments
                .AnyAsync(p => p.PropertyId == property.Id &&
                              p.PaymentDate.HasValue &&
                              p.PaymentDate.Value.Year == currentYear &&
                              p.CleaningTaxAmount > 0);

            decimal amount;
            DateTime reminderDueDate;
            string reminderType;

            // تحديد نوع الإشعار المتأخر باستخدام القيمة المحددة
            if (!hasRentPayment && currentDate > dueDate.AddDays(daysLate))
            {
                amount = property.MonthlyRent;
                reminderDueDate = dueDate;
                reminderType = "كراء شهري";
            }
            else if (!hasCleaningTaxPayment && currentDate > new DateTime(currentYear, 5, 31).AddDays(7))
            {
                amount = property.MonthlyRent * 12 * 0.105m;
                reminderDueDate = new DateTime(currentYear, 5, 31);
                reminderType = "ضريبة النظافة";
            }
            else
            {
                MessageBox.Show($"لا توجد دفعات متأخرة لهذا المكتري (بعد {daysLate} أيام)", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var notification = new WhatsAppNotification
            {
                CustomerId = customer.Id,
                PropertyId = property.Id,
                Type = NotificationType.PaymentReminder,
                PhoneNumber = customer.Phone,
                Amount = amount,
                DueDate = reminderDueDate,
                Month = reminderDueDate.ToString("MMMM yyyy", new CultureInfo("ar-SA")),
                Year = reminderDueDate.Year,
                ScheduledDate = DateTime.Now,
                Status = NotificationStatus.Pending,
                UniqueKey = $"MANUAL_LATE_{customer.Id}_{property.Id}_{DateTime.Now.Ticks}"
            };

            notification.Message = await _notificationService.GenerateMessageAsync(notification);

            _context.WhatsAppNotifications.Add(notification);
            await _context.SaveChangesAsync();

            await SendWhatsAppMessage(notification);

            MessageBox.Show($"تم إرسال إشعار متأخر لـ {reminderType}", "تم بنجاح",
                MessageBoxButton.OK, MessageBoxImage.Information);

            LoadNotifications();
        }

        private async Task SendWhatsAppMessage(WhatsAppNotification notification)
        {
            try
            {
                // تنظيف رقم الهاتف
                var phoneNumber = CleanPhoneNumber(notification.PhoneNumber);
                if (string.IsNullOrEmpty(phoneNumber))
                {
                    notification.Status = NotificationStatus.Failed;
                    notification.ErrorMessage = "رقم الهاتف غير صحيح";
                    await _context.SaveChangesAsync();
                    return;
                }

                // إنشاء رابط الواتساب
                var encodedMessage = HttpUtility.UrlEncode(notification.Message);
                var whatsappUrl = $"https://wa.me/{phoneNumber.Replace("+", "")}?text={encodedMessage}";

                // فتح الواتساب
                Process.Start(new ProcessStartInfo
                {
                    FileName = whatsappUrl,
                    UseShellExecute = true
                });

                // تحديث حالة الإشعار
                notification.Status = NotificationStatus.Sent;
                notification.SentDate = DateTime.Now;
                notification.UpdatedDate = DateTime.Now;
                await _context.SaveChangesAsync();

                MessageBox.Show("تم فتح الواتساب! يرجى إرسال الرسالة يدوياً.", "تم بنجاح",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                notification.Status = NotificationStatus.Failed;
                notification.ErrorMessage = ex.Message;
                notification.UpdatedDate = DateTime.Now;
                await _context.SaveChangesAsync();
                throw;
            }
        }

        private string CleanPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber)) return "";

            phoneNumber = phoneNumber.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");

            if (phoneNumber.StartsWith("0"))
                phoneNumber = "+212" + phoneNumber.Substring(1);
            else if (!phoneNumber.StartsWith("+"))
                phoneNumber = "+212" + phoneNumber;

            return phoneNumber;
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            _notificationService?.Dispose();
            base.OnClosed(e);
        }

        // New Event Handlers for Enhanced Features

        private void DateRangeFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_allNotifications.Any())
                ApplyFilters();
        }

        private void SortComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_allNotifications.Any())
                ApplyFilters();
        }

        private void SortDirectionButton_Click(object sender, RoutedEventArgs e)
        {
            _sortAscending = !_sortAscending;
            SortDirectionButton.Content = _sortAscending ? "⬆️" : "⬇️";
            SortDirectionButton.ToolTip = _sortAscending ? "ترتيب تصاعدي" : "ترتيب تنازلي";
            if (_allNotifications.Any())
                ApplyFilters();
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadNotifications();
        }

        private void DeleteSelectedButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطبيق هذه الميزة قريباً", "قيد التطوير",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private async void EditDateButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int notificationId)
            {
                await EditNotificationDate(notificationId);
            }
        }

        private async Task EditNotificationDate(int notificationId)
        {
            try
            {
                var notification = _allNotifications.FirstOrDefault(n => n.Id == notificationId);
                if (notification == null)
                {
                    MessageBox.Show("لم يتم العثور على الإشعار", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var dialog = new EditDateDialog(notification.DueDate);
                if (dialog.ShowDialog() == true)
                {
                    var dbNotification = await _context.WhatsAppNotifications
                        .FirstOrDefaultAsync(n => n.Id == notificationId);

                    if (dbNotification != null)
                    {
                        dbNotification.DueDate = dialog.SelectedDate;
                        await _context.SaveChangesAsync();

                        LoadNotifications();

                        MessageBox.Show($"تم تحديث تاريخ الاستحقاق إلى {dialog.SelectedDate:dd/MM/yyyy}",
                            "تم التحديث", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث التاريخ: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    public class NotificationViewModel
    {
        public int Id { get; set; }
        public int CustomerId { get; set; }
        public int PropertyId { get; set; }
        public string CustomerName { get; set; } = "";
        public string PropertyName { get; set; } = "";
        public string PhoneNumber { get; set; } = "";
        public string PaymentDayDisplay { get; set; } = ""; // جديد: يوم الأداء
        public string DueDateDisplay { get; set; } = "";
        public string RentStatus { get; set; } = "";
        public string CleaningStatus { get; set; } = "";
        public string TypeDisplay { get; set; } = "";
        public string AmountDisplay { get; set; } = "";
        public string StatusDisplay { get; set; } = "";
        public string CreatedDateDisplay { get; set; } = "";
        public bool CanSend { get; set; }
        public bool IsLate { get; set; } // للتحقق من التأخير

        // Additional properties needed for filtering and sorting
        public DateTime DueDate { get; set; }
        public DateTime CreatedDate { get; set; }
        public string Type { get; set; } = "";
        public string Status { get; set; } = "";
    }
}
