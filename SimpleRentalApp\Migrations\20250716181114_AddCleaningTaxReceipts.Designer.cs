﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using SimpleRentalApp.Data;

#nullable disable

namespace SimpleRentalApp.Migrations
{
    [DbContext(typeof(RentalDbContext))]
    [Migration("20250716181114_AddCleaningTaxReceipts")]
    partial class AddCleaningTaxReceipts
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "9.0.6");

            modelBuilder.Entity("SimpleRentalApp.Models.CleaningTaxReceipt", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsPrinted")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("PaymentDate")
                        .HasColumnType("TEXT");

                    b.Property<int?>("PaymentId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("PaymentMethod")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("PrintedDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("PropertyId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ReceiptNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<int>("TaxYear")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("PaymentId");

                    b.HasIndex("PropertyId");

                    b.HasIndex("ReceiptNumber", "PropertyId", "TaxYear")
                        .IsUnique();

                    b.ToTable("CleaningTaxReceipts");
                });

            modelBuilder.Entity("SimpleRentalApp.Models.Contract", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("CustomerId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("InitialRentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int?>("PaymentDay")
                        .HasColumnType("INTEGER");

                    b.Property<int>("PropertyId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("RentIncreaseCount")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("RentIncreasePercentage")
                        .HasColumnType("decimal(5,2)");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("PropertyId");

                    b.ToTable("Contracts");
                });

            modelBuilder.Entity("SimpleRentalApp.Models.ContractAttachment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("ContractId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<long>("FileSize")
                        .HasColumnType("INTEGER");

                    b.Property<string>("FileType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("UploadDate")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ContractId");

                    b.ToTable("ContractAttachments");
                });

            modelBuilder.Entity("SimpleRentalApp.Models.ContractAugment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("ApplicationDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("AugmentType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("BaseRent")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("CalculatedAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("ContractId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Percentage")
                        .HasColumnType("decimal(5,2)");

                    b.HasKey("Id");

                    b.HasIndex("ContractId");

                    b.ToTable("ContractAugments");
                });

            modelBuilder.Entity("SimpleRentalApp.Models.Customer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("NationalId")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Customers");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Address = "حي الملز، الرياض",
                            CreatedDate = new DateTime(2025, 6, 16, 19, 11, 13, 913, DateTimeKind.Local).AddTicks(9048),
                            Email = "<EMAIL>",
                            FirstName = "أحمد",
                            LastName = "محمد علي",
                            NationalId = "1234567890",
                            Phone = "0501234567",
                            UpdatedDate = new DateTime(2025, 7, 16, 19, 11, 13, 913, DateTimeKind.Local).AddTicks(5727)
                        },
                        new
                        {
                            Id = 2,
                            Address = "حي النخيل، الرياض",
                            CreatedDate = new DateTime(2025, 6, 21, 19, 11, 13, 913, DateTimeKind.Local).AddTicks(9519),
                            Email = "<EMAIL>",
                            FirstName = "فاطمة",
                            LastName = "عبدالله",
                            NationalId = "0987654321",
                            Phone = "0509876543",
                            UpdatedDate = new DateTime(2025, 7, 16, 19, 11, 13, 913, DateTimeKind.Local).AddTicks(9509)
                        },
                        new
                        {
                            Id = 3,
                            Address = "حي العليا، الرياض",
                            CreatedDate = new DateTime(2025, 6, 26, 19, 11, 13, 913, DateTimeKind.Local).AddTicks(9529),
                            Email = "<EMAIL>",
                            FirstName = "محمد",
                            LastName = "أحمد",
                            NationalId = "1122334455",
                            Phone = "0551122334",
                            UpdatedDate = new DateTime(2025, 7, 16, 19, 11, 13, 913, DateTimeKind.Local).AddTicks(9525)
                        },
                        new
                        {
                            Id = 4,
                            Address = "حي الورود، الرياض",
                            CreatedDate = new DateTime(2025, 7, 1, 19, 11, 13, 913, DateTimeKind.Local).AddTicks(9538),
                            Email = "<EMAIL>",
                            FirstName = "سارة",
                            LastName = "خالد",
                            NationalId = "5566778899",
                            Phone = "0555566778",
                            UpdatedDate = new DateTime(2025, 7, 16, 19, 11, 13, 913, DateTimeKind.Local).AddTicks(9535)
                        },
                        new
                        {
                            Id = 5,
                            Address = "حي الصحافة، الرياض",
                            CreatedDate = new DateTime(2025, 7, 6, 19, 11, 13, 913, DateTimeKind.Local).AddTicks(9547),
                            Email = "<EMAIL>",
                            FirstName = "عبدالله",
                            LastName = "سعد",
                            NationalId = "9988776655",
                            Phone = "0559988776",
                            UpdatedDate = new DateTime(2025, 7, 16, 19, 11, 13, 913, DateTimeKind.Local).AddTicks(9544)
                        });
                });

            modelBuilder.Entity("SimpleRentalApp.Models.MessageTemplate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("AvailableVariables")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Template")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("Type")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("MessageTemplates");
                });

            modelBuilder.Entity("SimpleRentalApp.Models.NotificationSettings", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<bool>("AutoNotificationsEnabled")
                        .HasColumnType("INTEGER");

                    b.Property<int>("CleaningTaxLateDays")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("CleaningTaxNotificationsEnabled")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("DaysBetweenLateReminders")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("LateRemindersEnabled")
                        .HasColumnType("INTEGER");

                    b.Property<int>("MaxLateRemindersPerMonth")
                        .HasColumnType("INTEGER");

                    b.Property<int>("RentLateDays")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("RentNotificationsEnabled")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("NotificationSettings");
                });

            modelBuilder.Entity("SimpleRentalApp.Models.Payment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("CleaningTaxAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("CustomerId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("DueDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("PaymentDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("PaymentMethod")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("نقداً");

                    b.Property<int>("PropertyId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ReceiptNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("RentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("Status")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("TaxYear")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Type")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("PropertyId");

                    b.ToTable("Payments");
                });

            modelBuilder.Entity("SimpleRentalApp.Models.Property", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("Area")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("Bathrooms")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("CleaningTaxPaymentOption")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<int?>("CustomerId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("MonthlyRent")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int?>("Rooms")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Status")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Type")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.ToTable("Properties");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Address = "حي الملز، شارع الأمير محمد بن عبدالعزيز",
                            Area = 120m,
                            Bathrooms = 2,
                            CleaningTaxPaymentOption = 1m,
                            CreatedDate = new DateTime(2025, 6, 16, 19, 11, 13, 916, DateTimeKind.Local).AddTicks(5905),
                            CustomerId = 1,
                            Description = "شقة فاخرة مع إطلالة رائعة",
                            MonthlyRent = 3500m,
                            Name = "شقة الملز الفاخرة",
                            Rooms = 3,
                            Status = 2,
                            Type = 1,
                            UpdatedDate = new DateTime(2025, 7, 16, 19, 11, 13, 915, DateTimeKind.Local).AddTicks(2944)
                        },
                        new
                        {
                            Id = 2,
                            Address = "شارع التحلية، وسط الدار البيضاء",
                            Area = 80m,
                            CleaningTaxPaymentOption = 4m,
                            CreatedDate = new DateTime(2025, 6, 21, 19, 11, 13, 916, DateTimeKind.Local).AddTicks(6433),
                            CustomerId = 2,
                            Description = "محل تجاري في موقع ممتاز",
                            MonthlyRent = 7000m,
                            Name = "محل التحلية التجاري",
                            Status = 2,
                            Type = 2,
                            UpdatedDate = new DateTime(2025, 7, 16, 19, 11, 13, 916, DateTimeKind.Local).AddTicks(6360)
                        },
                        new
                        {
                            Id = 3,
                            Address = "حي النخيل، شارع الملك فهد",
                            Area = 150m,
                            Bathrooms = 3,
                            CleaningTaxPaymentOption = 2m,
                            CreatedDate = new DateTime(2025, 6, 26, 19, 11, 13, 916, DateTimeKind.Local).AddTicks(6445),
                            CustomerId = 3,
                            Description = "شقة عائلية واسعة",
                            MonthlyRent = 4200m,
                            Name = "شقة النخيل العائلية",
                            Rooms = 4,
                            Status = 2,
                            Type = 1,
                            UpdatedDate = new DateTime(2025, 7, 16, 19, 11, 13, 916, DateTimeKind.Local).AddTicks(6439)
                        },
                        new
                        {
                            Id = 4,
                            Address = "برج الفيصلية، حي العليا",
                            Area = 200m,
                            CleaningTaxPaymentOption = 12m,
                            CreatedDate = new DateTime(2025, 7, 1, 19, 11, 13, 916, DateTimeKind.Local).AddTicks(6454),
                            Description = "مكتب فاخر في برج الفيصلية",
                            MonthlyRent = 11000m,
                            Name = "مكتب برج الفيصلية",
                            Status = 1,
                            Type = 3,
                            UpdatedDate = new DateTime(2025, 7, 16, 19, 11, 13, 916, DateTimeKind.Local).AddTicks(6451)
                        },
                        new
                        {
                            Id = 5,
                            Address = "حي العليا، شارع العروبة",
                            Area = 130m,
                            Bathrooms = 2,
                            CleaningTaxPaymentOption = 3m,
                            CreatedDate = new DateTime(2025, 7, 6, 19, 11, 13, 916, DateTimeKind.Local).AddTicks(6464),
                            CustomerId = 4,
                            Description = "شقة حديثة مع جميع المرافق",
                            MonthlyRent = 5500m,
                            Name = "شقة العليا الحديثة",
                            Rooms = 3,
                            Status = 2,
                            Type = 1,
                            UpdatedDate = new DateTime(2025, 7, 16, 19, 11, 13, 916, DateTimeKind.Local).AddTicks(6460)
                        },
                        new
                        {
                            Id = 6,
                            Address = "حي الورود، شارع الأمير سلطان",
                            Area = 400m,
                            Bathrooms = 4,
                            CleaningTaxPaymentOption = 2m,
                            CreatedDate = new DateTime(2025, 7, 11, 19, 11, 13, 916, DateTimeKind.Local).AddTicks(6474),
                            Description = "فيلا فاخرة مع حديقة",
                            MonthlyRent = 16000m,
                            Name = "فيلا الورود الفاخرة",
                            Rooms = 6,
                            Status = 1,
                            Type = 4,
                            UpdatedDate = new DateTime(2025, 7, 16, 19, 11, 13, 916, DateTimeKind.Local).AddTicks(6470)
                        });
                });

            modelBuilder.Entity("SimpleRentalApp.Models.RentChangeLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("ChangeDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("ChangeReason")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("ChangedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int?>("ContractId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("NewRentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("OldRentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("PropertyId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("ContractId");

                    b.HasIndex("PropertyId");

                    b.ToTable("RentChangeLogs");
                });

            modelBuilder.Entity("SimpleRentalApp.Models.RentIncrease", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("ContractId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("IncreaseDate")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("IncreasePercentage")
                        .HasColumnType("decimal(5,2)");

                    b.Property<decimal>("NewAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PreviousAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ContractId");

                    b.ToTable("RentIncreases");
                });

            modelBuilder.Entity("SimpleRentalApp.Models.RentReceipt", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("ContractId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsPrinted")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("PaymentDate")
                        .HasColumnType("TEXT");

                    b.Property<int?>("PaymentId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("PaymentMethod")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("PaymentPeriod")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("PeriodEndDate")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("PeriodStartDate")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("PrintedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("ReceiptNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<bool>("ShowLegalNotices")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("ContractId");

                    b.HasIndex("PaymentId");

                    b.HasIndex("ReceiptNumber", "ContractId")
                        .IsUnique();

                    b.ToTable("RentReceipts");
                });

            modelBuilder.Entity("SimpleRentalApp.Models.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastLoginDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<int>("Role")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("Username")
                        .IsUnique();

                    b.ToTable("Users");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedDate = new DateTime(2024, 1, 1, 8, 0, 0, 0, DateTimeKind.Unspecified),
                            Email = "<EMAIL>",
                            FullName = "حفيظ عبدو",
                            IsActive = true,
                            PasswordHash = "hafidos159357",
                            Role = 1,
                            Username = "hafid"
                        },
                        new
                        {
                            Id = 2,
                            CreatedDate = new DateTime(2024, 1, 2, 9, 0, 0, 0, DateTimeKind.Unspecified),
                            Email = "<EMAIL>",
                            FullName = "أحمد المدير",
                            IsActive = true,
                            PasswordHash = "manager123",
                            Role = 2,
                            Username = "manager"
                        },
                        new
                        {
                            Id = 3,
                            CreatedDate = new DateTime(2024, 1, 3, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            Email = "<EMAIL>",
                            FullName = "فاطمة الموظفة",
                            IsActive = true,
                            PasswordHash = "employee123",
                            Role = 3,
                            Username = "employee"
                        });
                });

            modelBuilder.Entity("SimpleRentalApp.Models.WhatsAppNotification", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Amount")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("CustomerId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("DueDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsAutoGenerated")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Month")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("PropertyId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("ScheduledDate")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("SentDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("Status")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Type")
                        .HasColumnType("INTEGER");

                    b.Property<string>("UniqueKey")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("Year")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("PropertyId");

                    b.ToTable("WhatsAppNotifications");
                });

            modelBuilder.Entity("SimpleRentalApp.Models.CleaningTaxReceipt", b =>
                {
                    b.HasOne("SimpleRentalApp.Models.Payment", "Payment")
                        .WithMany()
                        .HasForeignKey("PaymentId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("SimpleRentalApp.Models.Property", "Property")
                        .WithMany()
                        .HasForeignKey("PropertyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Payment");

                    b.Navigation("Property");
                });

            modelBuilder.Entity("SimpleRentalApp.Models.Contract", b =>
                {
                    b.HasOne("SimpleRentalApp.Models.Customer", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("SimpleRentalApp.Models.Property", "Property")
                        .WithMany()
                        .HasForeignKey("PropertyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("Property");
                });

            modelBuilder.Entity("SimpleRentalApp.Models.ContractAttachment", b =>
                {
                    b.HasOne("SimpleRentalApp.Models.Contract", "Contract")
                        .WithMany("Attachments")
                        .HasForeignKey("ContractId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Contract");
                });

            modelBuilder.Entity("SimpleRentalApp.Models.ContractAugment", b =>
                {
                    b.HasOne("SimpleRentalApp.Models.Contract", "Contract")
                        .WithMany("Augments")
                        .HasForeignKey("ContractId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Contract");
                });

            modelBuilder.Entity("SimpleRentalApp.Models.Payment", b =>
                {
                    b.HasOne("SimpleRentalApp.Models.Customer", "Customer")
                        .WithMany("Payments")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("SimpleRentalApp.Models.Property", "Property")
                        .WithMany("Payments")
                        .HasForeignKey("PropertyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("Property");
                });

            modelBuilder.Entity("SimpleRentalApp.Models.Property", b =>
                {
                    b.HasOne("SimpleRentalApp.Models.Customer", "Customer")
                        .WithMany("Properties")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Customer");
                });

            modelBuilder.Entity("SimpleRentalApp.Models.RentChangeLog", b =>
                {
                    b.HasOne("SimpleRentalApp.Models.Contract", "Contract")
                        .WithMany()
                        .HasForeignKey("ContractId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("SimpleRentalApp.Models.Property", "Property")
                        .WithMany()
                        .HasForeignKey("PropertyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Contract");

                    b.Navigation("Property");
                });

            modelBuilder.Entity("SimpleRentalApp.Models.RentIncrease", b =>
                {
                    b.HasOne("SimpleRentalApp.Models.Contract", "Contract")
                        .WithMany("RentIncreases")
                        .HasForeignKey("ContractId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Contract");
                });

            modelBuilder.Entity("SimpleRentalApp.Models.RentReceipt", b =>
                {
                    b.HasOne("SimpleRentalApp.Models.Contract", "Contract")
                        .WithMany()
                        .HasForeignKey("ContractId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("SimpleRentalApp.Models.Payment", "Payment")
                        .WithMany("Receipts")
                        .HasForeignKey("PaymentId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Contract");

                    b.Navigation("Payment");
                });

            modelBuilder.Entity("SimpleRentalApp.Models.WhatsAppNotification", b =>
                {
                    b.HasOne("SimpleRentalApp.Models.Customer", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("SimpleRentalApp.Models.Property", "Property")
                        .WithMany()
                        .HasForeignKey("PropertyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("Property");
                });

            modelBuilder.Entity("SimpleRentalApp.Models.Contract", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("Augments");

                    b.Navigation("RentIncreases");
                });

            modelBuilder.Entity("SimpleRentalApp.Models.Customer", b =>
                {
                    b.Navigation("Payments");

                    b.Navigation("Properties");
                });

            modelBuilder.Entity("SimpleRentalApp.Models.Payment", b =>
                {
                    b.Navigation("Receipts");
                });

            modelBuilder.Entity("SimpleRentalApp.Models.Property", b =>
                {
                    b.Navigation("Payments");
                });
#pragma warning restore 612, 618
        }
    }
}
