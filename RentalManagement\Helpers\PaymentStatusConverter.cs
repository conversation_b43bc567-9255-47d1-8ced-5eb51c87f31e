using RentalManagement.Models;
using System.Globalization;
using System.Windows.Data;

namespace RentalManagement.Helpers
{
    public class PaymentStatusConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is PaymentStatus status)
            {
                return status switch
                {
                    PaymentStatus.Unpaid => "غير مدفوع",
                    PaymentStatus.Paid => "مدفوع",
                    PaymentStatus.Overdue => "متأخر",
                    _ => "غير محدد"
                };
            }
            return "غير محدد";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
