# نظام إدارة العقارات - Rental Management System

## نظرة عامة
تطبيق مكتبي شامل لإدارة العقارات المخصصة للكراء، مطور باستخدام C# و WPF مع تصميم عصري يدعم Material Design.

## المتطلبات التقنية
- **الإطار**: .NET 9.0
- **واجهة المستخدم**: WPF مع MaterialDesignThemes
- **قاعدة البيانات**: SQLite مع Entity Framework Core
- **نمط التصميم**: MVVM
- **طباعة PDF**: iTextSharp
- **التوافق**: Windows 7 فما فوق

## الميزات الرئيسية

### 🏠 لوحة التحكم
- عرض إحصائيات شاملة للعقارات والزبائن
- تنبيهات للأداءات المتأخرة
- نظرة عامة على الإيرادات الشهرية
- عرض الأنشطة الأخيرة

### 👥 إدارة الزبائن
- إضافة وتعديل وحذف الزبائن
- التحقق من صحة البيانات
- البحث والتصفية المتقدمة
- تخزين معلومات الاتصال والهوية

### 🏢 إدارة المحلات
- إدارة الشقق والمحلات التجارية
- تتبع حالة المحل (متاح/مكتراة)
- ربط المحلات بالمكترين
- تحديد مبالغ الكراء

### 💰 نظام الأداءات
- تسجيل وتتبع الأداءات
- تحديد حالة الدفع (مدفوع/غير مدفوع/متأخر)
- تنبيهات تلقائية للمتأخرات
- إنشاء إيصالات PDF قابلة للطباعة

### 🎨 التصميم والواجهة
- تصميم عصري باستخدام Material Design
- دعم الوضع الليلي والعادي
- واجهة باللغة العربية مع دعم RTL
- تصميم متجاوب وسهل الاستخدام

## هيكل المشروع

```
RentalManagement/
├── Models/                 # نماذج البيانات
│   ├── Customer.cs
│   ├── Property.cs
│   └── Payment.cs
├── ViewModels/            # نماذج العرض (MVVM)
│   ├── BaseViewModel.cs
│   ├── MainViewModel.cs
│   ├── DashboardViewModel.cs
│   ├── CustomersViewModel.cs
│   ├── PropertiesViewModel.cs
│   └── PaymentsViewModel.cs
├── Views/                 # واجهات المستخدم
│   ├── MainWindow.xaml
│   ├── DashboardView.xaml
│   ├── CustomersView.xaml
│   ├── PropertiesView.xaml
│   └── PaymentsView.xaml
├── Services/              # الخدمات
│   ├── DatabaseService.cs
│   ├── ThemeService.cs
│   └── ReceiptService.cs
├── Data/                  # طبقة البيانات
│   └── RentalDbContext.cs
├── Helpers/               # المساعدات والأدوات
│   ├── RelayCommand.cs
│   └── Converters/
└── Resources/             # الموارد والثيمات
    └── Themes.xaml
```

## التشغيل والتثبيت

### المتطلبات المسبقة
1. .NET 9.0 SDK
2. Visual Studio 2022 أو VS Code
3. Windows 7 أو أحدث

### خطوات التشغيل
1. استنساخ المشروع:
   ```bash
   git clone [repository-url]
   cd RentalManagement
   ```

2. استعادة الحزم:
   ```bash
   dotnet restore
   ```

3. بناء المشروع:
   ```bash
   dotnet build
   ```

4. تشغيل التطبيق:
   ```bash
   dotnet run
   ```

## قاعدة البيانات
- يتم إنشاء قاعدة البيانات تلقائياً عند أول تشغيل
- الموقع: `%LocalAppData%/RentalManagement/rental.db`
- يتم تحميل بيانات تجريبية للاختبار

## الميزات المتقدمة

### طباعة الإيصالات
- إنشاء إيصالات PDF احترافية
- تضمين جميع تفاصيل الأداء والزبون
- إمكانية الطباعة المباشرة أو الحفظ

### البحث والتصفية
- بحث متقدم في جميع الصفحات
- تصفية حسب الحالة والتاريخ والنوع
- نتائج فورية أثناء الكتابة

### إدارة الثيمات
- تبديل سهل بين الوضع الليلي والعادي
- حفظ تفضيلات المستخدم
- تطبيق الثيم على جميع النوافذ

## المساهمة
نرحب بالمساهمات! يرجى:
1. إنشاء Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## الترخيص
هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الدعم
للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء Issue في المستودع.

---

تم تطوير هذا المشروع باستخدام أفضل الممارسات في تطوير تطبيقات WPF مع التركيز على الأداء وسهولة الاستخدام.
