using Microsoft.EntityFrameworkCore;
using RentalManagement.Data;
using RentalManagement.Models;
using RentalManagement.Services;
using System.Collections.ObjectModel;

namespace RentalManagement.ViewModels
{
    public class DashboardViewModel : BaseViewModel
    {
        private int _totalProperties;
        private int _rentedProperties;
        private int _availableProperties;
        private int _totalCustomers;
        private decimal _totalMonthlyRevenue;
        private int _overduePayments;

        public DashboardViewModel()
        {
            Title = "لوحة التحكم";
            OverduePayments = new ObservableCollection<Payment>();
            RecentPayments = new ObservableCollection<Payment>();
            
            LoadDashboardData();
        }

        public int TotalProperties
        {
            get => _totalProperties;
            set => SetProperty(ref _totalProperties, value);
        }

        public int RentedProperties
        {
            get => _rentedProperties;
            set => SetProperty(ref _rentedProperties, value);
        }

        public int AvailableProperties
        {
            get => _availableProperties;
            set => SetProperty(ref _availableProperties, value);
        }

        public int TotalCustomers
        {
            get => _totalCustomers;
            set => SetProperty(ref _totalCustomers, value);
        }

        public decimal TotalMonthlyRevenue
        {
            get => _totalMonthlyRevenue;
            set => SetProperty(ref _totalMonthlyRevenue, value);
        }

        public int OverduePaymentsCount
        {
            get => _overduePayments;
            set => SetProperty(ref _overduePayments, value);
        }

        public ObservableCollection<Payment> OverduePayments { get; }
        public ObservableCollection<Payment> RecentPayments { get; }

        private async void LoadDashboardData()
        {
            try
            {
                IsBusy = true;
                ClearError();

                using var context = DatabaseService.Instance.CreateContext();

                // Load statistics
                TotalProperties = await context.Properties.CountAsync();
                RentedProperties = await context.Properties.CountAsync(p => p.Status == PropertyStatus.Rented);
                AvailableProperties = TotalProperties - RentedProperties;
                TotalCustomers = await context.Customers.CountAsync();

                // Calculate monthly revenue
                var currentMonth = DateTime.Now.Month;
                var currentYear = DateTime.Now.Year;
                TotalMonthlyRevenue = await context.Payments
                    .Where(p => p.Status == PaymentStatus.Paid && 
                               p.PaymentDate.HasValue &&
                               p.PaymentDate.Value.Month == currentMonth &&
                               p.PaymentDate.Value.Year == currentYear)
                    .SumAsync(p => p.Amount);

                // Load overdue payments
                var overduePaymentsList = await context.Payments
                    .Include(p => p.Customer)
                    .Include(p => p.Property)
                    .Where(p => p.Status == PaymentStatus.Unpaid && p.DueDate < DateTime.Now)
                    .OrderBy(p => p.DueDate)
                    .Take(10)
                    .ToListAsync();

                OverduePayments.Clear();
                foreach (var payment in overduePaymentsList)
                {
                    OverduePayments.Add(payment);
                }
                OverduePaymentsCount = overduePaymentsList.Count;

                // Load recent payments
                var recentPaymentsList = await context.Payments
                    .Include(p => p.Customer)
                    .Include(p => p.Property)
                    .Where(p => p.Status == PaymentStatus.Paid)
                    .OrderByDescending(p => p.PaymentDate)
                    .Take(10)
                    .ToListAsync();

                RecentPayments.Clear();
                foreach (var payment in recentPaymentsList)
                {
                    RecentPayments.Add(payment);
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في تحميل بيانات لوحة التحكم: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        public void RefreshData()
        {
            LoadDashboardData();
        }
    }
}
