@echo off
echo ========================================
echo    Publishing Rental Management App
echo ========================================
echo.

echo Cleaning previous builds...
if exist "bin\Release" rmdir /s /q "bin\Release"
if exist "publish" rmdir /s /q "publish"
if exist "dist" rmdir /s /q "dist"

echo.
echo Building application...
dotnet build --configuration Release

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Publishing application...
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output "publish"

if %ERRORLEVEL% neq 0 (
    echo Publish failed!
    pause
    exit /b 1
)

echo.
echo Creating distribution folder...
if not exist "dist" mkdir "dist"

echo.
echo Copying files...
copy "publish\SimpleRentalApp.exe" "dist\RentalManagement.exe"

echo.
echo Creating README file...
echo Rental Management Application > "dist\README.txt"
echo ================================ >> "dist\README.txt"
echo. >> "dist\README.txt"
echo This application is for managing rental properties. >> "dist\README.txt"
echo. >> "dist\README.txt"
echo How to run: >> "dist\README.txt"
echo 1. Double-click on RentalManagement.exe >> "dist\README.txt"
echo 2. Database will be created automatically on first run >> "dist\README.txt"
echo. >> "dist\README.txt"
echo Developer: Hafid Abdo >> "dist\README.txt"
echo Year: 2024 >> "dist\README.txt"

echo.
echo ========================================
echo Application published successfully!
echo ========================================
echo.
echo Files available in: dist folder
echo Application file: RentalManagement.exe
echo.
echo You can now distribute the dist folder or compress it to ZIP
echo.
pause
