# إنشاء حزمة تثبيت عربية لنظام تدبير الكراء
# Arabic Installation Package Creator for Rental Management System

param(
    [string]$OutputName = "RentalManagement_Arabic_v1.0.zip"
)

Write-Host "========================================" -ForegroundColor Green
Write-Host "    إنشاء حزمة التثبيت العربية" -ForegroundColor Green
Write-Host "    Creating Arabic Installation Package" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Check if portable folder exists
if (-not (Test-Path "portable")) {
    Write-Host "خطأ: مجلد portable غير موجود" -ForegroundColor Red
    Write-Host "Error: portable folder not found" -ForegroundColor Red
    exit 1
}

# Create temporary directory for Arabic package
$tempDir = "arabic_package_temp"
if (Test-Path $tempDir) {
    Remove-Item $tempDir -Recurse -Force
}
New-Item -ItemType Directory -Path $tempDir | Out-Null

Write-Host "[1/5] نسخ ملفات التطبيق..." -ForegroundColor Cyan
Write-Host "[1/5] Copying application files..." -ForegroundColor Cyan

# Copy portable files
Copy-Item "portable\*" -Destination $tempDir -Recurse

Write-Host "[2/5] إضافة الملفات العربية..." -ForegroundColor Cyan
Write-Host "[2/5] Adding Arabic files..." -ForegroundColor Cyan

# Copy Arabic documentation
Copy-Item "README_Arabic.txt" -Destination "$tempDir\اقرأني.txt"
Copy-Item "LICENSE_Arabic.txt" -Destination "$tempDir\الترخيص.txt"
Copy-Item "دليل_التثبيت_العربي.txt" -Destination "$tempDir\دليل_التثبيت.txt"

Write-Host "[3/5] إنشاء ملفات التشغيل العربية..." -ForegroundColor Cyan
Write-Host "[3/5] Creating Arabic launcher files..." -ForegroundColor Cyan

# Create Arabic batch file
$arabicBatchContent = @"
@echo off
chcp 65001 > nul
title نظام تدبير الكراء
echo ========================================
echo       نظام تدبير الكراء
echo    Rental Management System
echo ========================================
echo.
echo المطور: حفيظ عبدو
echo Developer: Hafid Abdo
echo الإصدار: 1.0.0
echo Version: 1.0.0
echo.
echo بدء تشغيل النظام...
echo Starting system...
echo.
start SimpleRentalApp.exe
"@

$arabicBatchContent | Out-File -FilePath "$tempDir\تشغيل_النظام.bat" -Encoding UTF8

# Create installation instructions
$installInstructions = @"
تعليمات التثبيت والتشغيل
=======================

مرحباً بك في نظام تدبير الكراء!

خطوات التشغيل:
===============
1. فك ضغط هذا الملف في أي مجلد تريده
2. تشغيل ملف "تشغيل_النظام.bat" أو "SimpleRentalApp.exe"
3. تسجيل الدخول بالبيانات التالية:
   - اسم المستخدم: hafid
   - كلمة المرور: hafidos159357

الملفات المهمة:
===============
• تشغيل_النظام.bat - لتشغيل البرنامج
• SimpleRentalApp.exe - الملف الرئيسي للبرنامج
• اقرأني.txt - دليل المستخدم الكامل
• الترخيص.txt - شروط الاستخدام
• دليل_التثبيت.txt - تعليمات مفصلة

متطلبات النظام:
===============
• Windows 10 أو أحدث
• 4 GB ذاكرة عشوائية
• 200 MB مساحة فارغة

الميزات:
========
✓ إدارة المحلات والعقارات
✓ إدارة عقود الإيجار
✓ تتبع المدفوعات
✓ إنشاء الإيصالات
✓ إدارة ضريبة النظافة
✓ إشعارات WhatsApp
✓ التقارير والإحصائيات
✓ النسخ الاحتياطي

للدعم الفني:
============
المطور: حفيظ عبدو
يرجى التواصل مع المطور للحصول على الدعم

شكراً لاستخدام نظام تدبير الكراء!
"@

$installInstructions | Out-File -FilePath "$tempDir\تعليمات_التشغيل.txt" -Encoding UTF8

Write-Host "[4/5] إنشاء ملف مضغوط..." -ForegroundColor Cyan
Write-Host "[4/5] Creating compressed file..." -ForegroundColor Cyan

# Create ZIP file
if (Test-Path $OutputName) {
    Remove-Item $OutputName -Force
}

Add-Type -AssemblyName System.IO.Compression.FileSystem
[System.IO.Compression.ZipFile]::CreateFromDirectory($tempDir, $OutputName)

Write-Host "[5/5] تنظيف الملفات المؤقتة..." -ForegroundColor Cyan
Write-Host "[5/5] Cleaning temporary files..." -ForegroundColor Cyan

# Cleanup
Remove-Item $tempDir -Recurse -Force

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "تم إنشاء الحزمة بنجاح!" -ForegroundColor Green
Write-Host "Package created successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "اسم الملف: $OutputName" -ForegroundColor Yellow
Write-Host "File name: $OutputName" -ForegroundColor Yellow

if (Test-Path $OutputName) {
    $fileSize = (Get-Item $OutputName).Length
    $fileSizeMB = [math]::Round($fileSize / 1MB, 2)
    Write-Host "حجم الملف: $fileSizeMB MB" -ForegroundColor Yellow
    Write-Host "File size: $fileSizeMB MB" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "محتويات الحزمة:" -ForegroundColor Cyan
Write-Host "Package contents:" -ForegroundColor Cyan
Write-Host "• جميع ملفات التطبيق" -ForegroundColor White
Write-Host "• دليل المستخدم العربي" -ForegroundColor White
Write-Host "• ملف تشغيل عربي" -ForegroundColor White
Write-Host "• تعليمات التثبيت" -ForegroundColor White
Write-Host "• اتفاقية الترخيص" -ForegroundColor White
Write-Host ""
Write-Host "الحزمة جاهزة للتوزيع!" -ForegroundColor Green
Write-Host "Package ready for distribution!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
