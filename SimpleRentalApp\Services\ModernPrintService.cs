using System;
using System.Printing;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using System.Windows.Markup;
using System.IO;
using System.Globalization;
using System.Windows.Controls.Primitives;
using SimpleRentalApp.Models;

namespace SimpleRentalApp.Services
{
    public class ModernPrintService
    {
        public void ShowPreview(RentReceipt receipt)
        {
            PrintReceiptWithPreview(receipt);
        }

        private static ModernPrintService? _instance;
        public static ModernPrintService Instance => _instance ??= new ModernPrintService();

        public ModernPrintService() { }

        public bool PrintReceipt(RentReceipt receipt)
        {
            try
            {
                // Configure print dialog first
                var printDialog = new PrintDialog();

                // Set A6 paper size and landscape orientation for printing
                var printTicket = printDialog.PrintTicket;
                printTicket.PageMediaSize = new PageMediaSize(PageMediaSizeName.ISOA6, 583, 413); // A6 Landscape (148x105mm in 1/96 inch)
                printTicket.PageOrientation = PageOrientation.Landscape;

                // Show print dialog
                if (printDialog.ShowDialog() == true)
                {
                    // Create the receipt visual element for printing (A6 Landscape)
                    var receiptVisual = CreateReceiptVisual(receipt, forPrint: true);

                    // Print the visual element
                    printDialog.PrintVisual(receiptVisual, $"توصيل كراء - {receipt.ReceiptNumber}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        private FrameworkElement CreateReceiptVisual(RentReceipt receipt, bool forPrint = false, Property? property = null, Customer? customer = null)
        {
            Grid container;

            if (forPrint)
            {
                // A6 Portrait for printing (105mm x 148mm) - optimized dimensions
                container = new Grid
                {
                    Width = 373, // A6 Portrait width (393 - 20 for margins)
                    Height = 543, // A6 Portrait height (563 - 20 for margins)
                    Background = Brushes.White,
                    FlowDirection = FlowDirection.RightToLeft,
                    UseLayoutRounding = true
                };
                container.Margin = new Thickness(8);
            }
            else
            {
                // Flexible size for preview
                container = new Grid
                {
                    Width = 400, // Comfortable preview width
                    Height = 550, // Comfortable preview height
                    Background = Brushes.White,
                    FlowDirection = FlowDirection.RightToLeft,
                    UseLayoutRounding = true
                };
                container.Margin = new Thickness(15);
            }

            // Create the receipt content with appropriate sizing
            var receiptContent = CreateReceiptContent(receipt, forPrint, property, customer);
            container.Children.Add(receiptContent);

            // Force layout update
            container.UpdateLayout();

            return container;
        }

        private UIElement CreateReceiptContent(RentReceipt receipt, bool forPrint = false, Property? property = null, Customer? customer = null)
        {
            var mainGrid = new Grid();

            if (forPrint)
            {
                // A6 Portrait layout - single column vertical structure
                mainGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) }); // Single column

                mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Header
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(8) }); // Spacing
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Tenant & Property info
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(6) }); // Spacing
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Payment details
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(6) }); // Spacing
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Amount
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(6) }); // Spacing
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Legal notices
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) }); // Flexible space
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Footer
            }
            else
            {
                // Preview layout - vertical arrangement
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(50) }); // Header
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(8) }); // Spacing
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(90) }); // Tenant info
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(8) }); // Spacing
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(80) }); // Payment details
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(8) }); // Spacing
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(45) }); // Amount
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(8) }); // Spacing
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // Legal notices
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(20) }); // Footer
            }

            // 1. Header
            var header = CreateHeader(receipt, forPrint);
            Grid.SetRow(header, 0);
            mainGrid.Children.Add(header);

            if (forPrint)
            {
                // 2. Tenant and Property info (single section)
                var tenantInfo = CreateInfoSection(" المكتري والمحل", GetTenantInfoForPdf(receipt, property, customer),
                    new SolidColorBrush(Color.FromRgb(248, 249, 250)), forPrint); // #F8F9FA
                Grid.SetRow(tenantInfo, 2);
                mainGrid.Children.Add(tenantInfo);

                // 3. Payment details
                var paymentInfo = CreateInfoSection("تفاصيل الأداء", GetPaymentDetailsForPdf(receipt),
                    new SolidColorBrush(Color.FromRgb(255, 255, 255)), forPrint); // #FFFFFF
                Grid.SetRow(paymentInfo, 4);
                mainGrid.Children.Add(paymentInfo);

                // 4. Amount section
                var amountSection = CreateAmountSection(receipt, forPrint);
                Grid.SetRow(amountSection, 6);
                mainGrid.Children.Add(amountSection);

                // 5. Legal notices (if enabled)
                if (receipt.ShowLegalNotices)
                {
                    var legalSection = CreateLegalSection(forPrint);
                    Grid.SetRow(legalSection, 8);
                    mainGrid.Children.Add(legalSection);
                }

                // 6. Footer (at the bottom)
                var footer = CreateFooter(receipt, forPrint);
                Grid.SetRow(footer, 9);
                mainGrid.Children.Add(footer);
            }
            else
            {
                // Preview layout - vertical arrangement

                // 2. Tenant info section
                var tenantInfo = CreateInfoSection(" المكتري والمحل", GetTenantInfo(receipt, property, customer),
                    new SolidColorBrush(Color.FromRgb(248, 249, 250)), forPrint);
                Grid.SetRow(tenantInfo, 2);
                mainGrid.Children.Add(tenantInfo);

                // 3. Payment details section
                var paymentInfo = CreateInfoSection("تفاصيل الأداء", GetPaymentDetails(receipt),
                    new SolidColorBrush(Color.FromRgb(255, 255, 255)), forPrint);
                Grid.SetRow(paymentInfo, 4);
                mainGrid.Children.Add(paymentInfo);

                // 4. Amount highlight
                var amountSection = CreateAmountSection(receipt, forPrint);
                Grid.SetRow(amountSection, 6);
                mainGrid.Children.Add(amountSection);

                // 5. Legal notices (if enabled)
                if (receipt.ShowLegalNotices)
                {
                    var legalSection = CreateLegalSection(forPrint);
                    Grid.SetRow(legalSection, 8);
                    mainGrid.Children.Add(legalSection);
                }

                // 6. Footer
                var footer = CreateFooter(receipt, forPrint);
                Grid.SetRow(footer, 9);
                mainGrid.Children.Add(footer);
            }

            return mainGrid;
        }

        private UIElement CreateHeader(RentReceipt receipt, bool forPrint = false)
        {
            if (forPrint)
            {
                // Professional Header matching PDF style
                var headerBorder = new Border
                {
                    Background = new SolidColorBrush(Color.FromRgb(25, 118, 210)), // #1976D2 - Blue
                    Padding = new Thickness(10)
                };

                var headerStack = new StackPanel
                {
                    Orientation = Orientation.Vertical,
                    HorizontalAlignment = HorizontalAlignment.Center
                };

                // Title
                var title = new TextBlock
                {
                    Text = "توصيل الكراء",
                    FontFamily = new FontFamily("Tahoma"),
                    FontSize = 18,
                    FontWeight = FontWeights.Bold,
                    Foreground = Brushes.White,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    TextAlignment = TextAlignment.Center,
                    FlowDirection = FlowDirection.RightToLeft
                };

                // Receipt number
                var receiptNumber = new TextBlock
                {
                    Text = $"رقم التوصيل: {receipt.ReceiptNumber}",
                    FontFamily = new FontFamily("Tahoma"),
                    FontSize = 12,
                    FontWeight = FontWeights.Bold,
                    Foreground = new SolidColorBrush(Color.FromRgb(227, 242, 253)), // #E3F2FD - Light blue
                    HorizontalAlignment = HorizontalAlignment.Center,
                    TextAlignment = TextAlignment.Center,
                    FlowDirection = FlowDirection.RightToLeft,
                    Margin = new Thickness(0, 4, 0, 0)
                };

                headerStack.Children.Add(title);
                headerStack.Children.Add(receiptNumber);
                headerBorder.Child = headerStack;

                return headerBorder;
            }
            else
            {
                // Same colorful header for preview
                var headerBorder = new Border
                {
                    Background = new SolidColorBrush(Color.FromRgb(44, 62, 80)),
                    CornerRadius = new CornerRadius(3),
                    Padding = new Thickness(8, 6, 8, 6)
                };

                var headerStack = new StackPanel
                {
                    Orientation = Orientation.Vertical,
                    HorizontalAlignment = HorizontalAlignment.Center
                };

                var title = new TextBlock
                {
                    Text = "توصيل الكراء",
                    FontFamily = new FontFamily("Tahoma"),
                    FontSize = 16,
                    FontWeight = FontWeights.Bold,
                    Foreground = Brushes.White,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    TextAlignment = TextAlignment.Center,
                    FlowDirection = FlowDirection.RightToLeft,
                    Margin = new Thickness(0, 0, 0, 4)
                };

                var receiptNumber = new TextBlock
                {
                    Text = $"رقم: {receipt.ReceiptNumber}",
                    FontFamily = new FontFamily("Tahoma"),
                    FontSize = 11,
                    Foreground = new SolidColorBrush(Color.FromRgb(189, 195, 199)),
                    HorizontalAlignment = HorizontalAlignment.Center,
                    TextAlignment = TextAlignment.Center,
                    FlowDirection = FlowDirection.RightToLeft
                };

                headerStack.Children.Add(title);
                headerStack.Children.Add(receiptNumber);
                headerBorder.Child = headerStack;

                return headerBorder;
            }
        }

        private UIElement CreateInfoSection(string title, string content, Brush backgroundColor, bool forPrint = false)
        {
            if (forPrint)
            {
                // Professional section with compact spacing
                var sectionBorder = new Border
                {
                    Background = backgroundColor,
                    BorderBrush = new SolidColorBrush(Color.FromRgb(25, 118, 210)), // #1976D2 - Blue border
                    BorderThickness = new Thickness(1),
                    Padding = new Thickness(8, 6, 8, 6), // Compact padding
                    FlowDirection = FlowDirection.RightToLeft
                };

                var sectionStack = new StackPanel
                {
                    FlowDirection = FlowDirection.RightToLeft
                };

                // Section title - centered but RTL with larger font
                var titleBlock = new TextBlock
                {
                    Text = title,
                    FontFamily = new FontFamily("Tahoma"),
                    FontSize = forPrint ? 13 : 14,
                    FontWeight = FontWeights.Bold,
                    Foreground = new SolidColorBrush(Color.FromRgb(25, 118, 210)), // #1976D2 - Blue
                    HorizontalAlignment = HorizontalAlignment.Center,
                    TextAlignment = TextAlignment.Center,
                    FlowDirection = FlowDirection.RightToLeft,
                    Margin = new Thickness(0, 0, 0, 4) // Reduced space after title
                };

                // Section content - left aligned with larger font for better readability
                var contentBlock = new TextBlock
                {
                    Text = content,
                    FontFamily = new FontFamily("Tahoma"),
                    FontSize = forPrint ? 12 : 13, // Larger font size
                    FontWeight = FontWeights.Medium, // Slightly bolder for better readability
                    Foreground = new SolidColorBrush(Color.FromRgb(33, 33, 33)), // #212121 - Darker for better contrast
                    TextWrapping = TextWrapping.Wrap,
                    TextAlignment = TextAlignment.Left,
                    FlowDirection = FlowDirection.RightToLeft,
                    HorizontalAlignment = HorizontalAlignment.Stretch,
                    LineHeight = forPrint ? 16 : 18, // Compact line spacing
                    Margin = new Thickness(0, 0, 0, 2) // Minimal margin at bottom
                };

                sectionStack.Children.Add(titleBlock);
                sectionStack.Children.Add(contentBlock);
                sectionBorder.Child = sectionStack;

                return sectionBorder;
            }
            else
            {
                // Compact section for preview
                var sectionBorder = new Border
                {
                    Background = backgroundColor,
                    BorderBrush = new SolidColorBrush(Color.FromRgb(25, 118, 210)), // Match print version
                    BorderThickness = new Thickness(1),
                    CornerRadius = new CornerRadius(2),
                    Padding = new Thickness(8, 6, 8, 6) // Compact padding
                };

                var sectionStack = new StackPanel
                {
                    FlowDirection = FlowDirection.RightToLeft
                };

                var titleBlock = new TextBlock
                {
                    Text = title,
                    FontFamily = new FontFamily("Tahoma"), // Match print version
                    FontSize = 14,
                    FontWeight = FontWeights.Bold,
                    Foreground = new SolidColorBrush(Color.FromRgb(25, 118, 210)), // Match print version
                    HorizontalAlignment = HorizontalAlignment.Center,
                    TextAlignment = TextAlignment.Center,
                    FlowDirection = FlowDirection.RightToLeft,
                    Margin = new Thickness(0, 0, 0, 4) // Reduced space after title
                };

                var contentBlock = new TextBlock
                {
                    Text = content,
                    FontFamily = new FontFamily("Tahoma"), // Match print version
                    FontSize = 13, // Larger font size
                    FontWeight = FontWeights.Medium, // Slightly bolder for better readability
                    Foreground = new SolidColorBrush(Color.FromRgb(33, 33, 33)), // Darker for better contrast
                    TextWrapping = TextWrapping.Wrap,
                    TextAlignment = TextAlignment.Left,
                    FlowDirection = FlowDirection.RightToLeft,
                    HorizontalAlignment = HorizontalAlignment.Stretch,
                    LineHeight = 18, // Compact line spacing
                    Margin = new Thickness(0, 0, 0, 2) // Minimal margin at bottom
                };

                sectionStack.Children.Add(titleBlock);
                sectionStack.Children.Add(contentBlock);
                sectionBorder.Child = sectionStack;

                return sectionBorder;
            }
        }

        private UIElement CreateAmountSection(RentReceipt receipt, bool forPrint = false)
        {
            if (forPrint)
            {
                // Professional amount box matching PDF style
                var amountBorder = new Border
                {
                    Background = new SolidColorBrush(Color.FromRgb(255, 243, 224)), // #FFF3E0 - Orange background
                    BorderBrush = new SolidColorBrush(Color.FromRgb(255, 152, 0)), // #FF9800 - Orange border
                    BorderThickness = new Thickness(2),
                    Padding = new Thickness(12)
                };

                var amountStack = new StackPanel
                {
                    HorizontalAlignment = HorizontalAlignment.Center
                };

                // Amount label
                var amountLabel = new TextBlock
                {
                    Text = "المبلغ المدفوع",
                    FontFamily = new FontFamily("Tahoma"),
                    FontSize = 14,
                    FontWeight = FontWeights.Bold,
                    Foreground = new SolidColorBrush(Color.FromRgb(230, 81, 0)), // #E65100
                    HorizontalAlignment = HorizontalAlignment.Center,
                    TextAlignment = TextAlignment.Center,
                    FlowDirection = FlowDirection.RightToLeft,
                    Margin = new Thickness(0, 0, 0, 4)
                };

                // Amount in numbers
                var amountNumber = new TextBlock
                {
                    Text = $"{receipt.Amount:F2} درهم",
                    FontFamily = new FontFamily("Tahoma"),
                    FontSize = 18,
                    FontWeight = FontWeights.Bold,
                    Foreground = new SolidColorBrush(Color.FromRgb(230, 81, 0)), // #E65100
                    HorizontalAlignment = HorizontalAlignment.Center,
                    TextAlignment = TextAlignment.Center,
                    FlowDirection = FlowDirection.RightToLeft,
                    Margin = new Thickness(0, 0, 0, 4)
                };

                // Amount in words
                var amountWords = new TextBlock
                {
                    Text = $"({receipt.AmountInWords})",
                    FontFamily = new FontFamily("Tahoma"),
                    FontSize = 10,
                    FontWeight = FontWeights.Bold,
                    Foreground = new SolidColorBrush(Color.FromRgb(191, 54, 12)), // #BF360C
                    HorizontalAlignment = HorizontalAlignment.Center,
                    TextAlignment = TextAlignment.Center,
                    FlowDirection = FlowDirection.RightToLeft,
                    TextWrapping = TextWrapping.Wrap
                };

                amountStack.Children.Add(amountLabel);
                amountStack.Children.Add(amountNumber);
                amountStack.Children.Add(amountWords);
                amountBorder.Child = amountStack;

                return amountBorder;
            }
            else
            {
                // Colorful amount box for preview
                var amountBorder = new Border
                {
                    Background = new SolidColorBrush(Color.FromRgb(254, 249, 231)),
                    BorderBrush = new SolidColorBrush(Color.FromRgb(230, 126, 34)),
                    BorderThickness = new Thickness(1),
                    CornerRadius = new CornerRadius(3),
                    Padding = new Thickness(10, 6, 10, 6)
                };

                var amountStack = new StackPanel
                {
                    HorizontalAlignment = HorizontalAlignment.Center
                };

                var amountNumber = new TextBlock
                {
                    Text = $"المبلغ: {receipt.AmountDisplay}",
                    FontFamily = new FontFamily("Arial"),
                    FontSize = 12,
                    FontWeight = FontWeights.Bold,
                    Foreground = new SolidColorBrush(Color.FromRgb(211, 84, 0)),
                    HorizontalAlignment = HorizontalAlignment.Center,
                    TextAlignment = TextAlignment.Center,
                    FlowDirection = FlowDirection.RightToLeft,
                    Margin = new Thickness(0, 0, 0, 3)
                };

                var amountWords = new TextBlock
                {
                    Text = receipt.AmountInWords,
                    FontFamily = new FontFamily("Arial"),
                    FontSize = 9,
                    Foreground = new SolidColorBrush(Color.FromRgb(230, 126, 34)),
                    HorizontalAlignment = HorizontalAlignment.Center,
                    TextAlignment = TextAlignment.Center,
                    FlowDirection = FlowDirection.RightToLeft,
                    TextWrapping = TextWrapping.Wrap
                };

                amountStack.Children.Add(amountNumber);
                amountStack.Children.Add(amountWords);
                amountBorder.Child = amountStack;

                return amountBorder;
            }
        }

        private UIElement CreateLegalSection(bool forPrint = false)
        {
            if (forPrint)
            {
                // Professional legal section with better spacing and content fitting
                var legalBorder = new Border
                {
                    Background = new SolidColorBrush(Color.FromRgb(236, 239, 241)), // #ECEFF1 - Light gray
                    BorderBrush = new SolidColorBrush(Color.FromRgb(144, 164, 174)), // #90A4AE - Blue gray
                    BorderThickness = new Thickness(1),
                    Padding = new Thickness(12, 10, 12, 10), // Better padding for content
                    FlowDirection = FlowDirection.RightToLeft
                };

                var legalStack = new StackPanel
                {
                    FlowDirection = FlowDirection.RightToLeft
                };

                // Legal title - centered but RTL
                var legalTitle = new TextBlock
                {
                    Text = "التحفظات القانونية",
                    FontFamily = new FontFamily("Tahoma"),
                    FontSize = forPrint ? 10 : 11,
                    FontWeight = FontWeights.Bold,
                    Foreground = new SolidColorBrush(Color.FromRgb(55, 71, 79)), // #37474F - Dark blue gray
                    HorizontalAlignment = HorizontalAlignment.Center,
                    TextAlignment = TextAlignment.Center,
                    FlowDirection = FlowDirection.RightToLeft,
                    Margin = new Thickness(0, 0, 0, 8) // More space after title
                };

                // Legal content - left aligned with compact spacing
                var legalContent = new TextBlock
                {
                    Text = GetLegalNoticesForPrint(),
                    FontFamily = new FontFamily("Tahoma"),
                    FontSize = forPrint ? 9 : 10,
                    FontWeight = FontWeights.Normal,
                    Foreground = new SolidColorBrush(Color.FromRgb(66, 66, 66)), // #424242 - Darker for better readability
                    TextWrapping = TextWrapping.Wrap,
                    TextAlignment = TextAlignment.Left,
                    FlowDirection = FlowDirection.RightToLeft,
                    HorizontalAlignment = HorizontalAlignment.Stretch,
                    LineHeight = forPrint ? 12 : 14, // Compact line spacing
                    Margin = new Thickness(0, 0, 0, 4) // Small margin at bottom
                };

                legalStack.Children.Add(legalTitle);
                legalStack.Children.Add(legalContent);
                legalBorder.Child = legalStack;

                return legalBorder;
            }
            else
            {
                // Improved legal section for preview with better content fitting
                var legalBorder = new Border
                {
                    Background = new SolidColorBrush(Color.FromRgb(236, 239, 241)), // Match print version
                    BorderBrush = new SolidColorBrush(Color.FromRgb(144, 164, 174)), // Match print version
                    BorderThickness = new Thickness(1),
                    CornerRadius = new CornerRadius(2),
                    Padding = new Thickness(12, 10, 12, 10), // Better padding
                    Margin = new Thickness(0, 3, 0, 0)
                };

                var legalStack = new StackPanel
                {
                    FlowDirection = FlowDirection.RightToLeft
                };

                var legalTitle = new TextBlock
                {
                    Text = "التحفظات القانونية",
                    FontFamily = new FontFamily("Tahoma"), // Match print version
                    FontSize = 11,
                    FontWeight = FontWeights.Bold,
                    Foreground = new SolidColorBrush(Color.FromRgb(55, 71, 79)), // Match print version
                    HorizontalAlignment = HorizontalAlignment.Center,
                    TextAlignment = TextAlignment.Center,
                    FlowDirection = FlowDirection.RightToLeft,
                    Margin = new Thickness(0, 0, 0, 8) // More space after title
                };

                var legalContent = new TextBlock
                {
                    Text = GetLegalNoticesForPrint(), // Use same content as print
                    FontFamily = new FontFamily("Tahoma"), // Match print version
                    FontSize = 10,
                    FontWeight = FontWeights.Normal,
                    Foreground = new SolidColorBrush(Color.FromRgb(66, 66, 66)), // Match print version
                    TextWrapping = TextWrapping.Wrap,
                    TextAlignment = TextAlignment.Left,
                    FlowDirection = FlowDirection.RightToLeft,
                    HorizontalAlignment = HorizontalAlignment.Stretch,
                    LineHeight = 14, // Compact line spacing
                    Margin = new Thickness(0, 0, 0, 4) // Small margin at bottom
                };

                legalStack.Children.Add(legalTitle);
                legalStack.Children.Add(legalContent);
                legalBorder.Child = legalStack;

                return legalBorder;
            }
        }

        private UIElement CreateFooter(RentReceipt receipt, bool forPrint = false)
        {
            if (forPrint)
            {
                // Simple footer without signatures
                var footerBorder = new Border
                {
                    Padding = new Thickness(6),
                    FlowDirection = FlowDirection.RightToLeft
                };

                var footerStack = new StackPanel
                {
                    FlowDirection = FlowDirection.RightToLeft
                };

                // System name only
                var systemBlock = new TextBlock
                {
                    Text = "",
                    FontFamily = new FontFamily("Tahoma"),
                    FontSize = 8,
                    Foreground = new SolidColorBrush(Color.FromRgb(102, 102, 102)), // #666666
                    HorizontalAlignment = HorizontalAlignment.Center,
                    TextAlignment = TextAlignment.Center,
                    FlowDirection = FlowDirection.RightToLeft
                };

                footerStack.Children.Add(systemBlock);
                footerBorder.Child = footerStack;

                return footerBorder;
            }
            else
            {
                // Colorful footer for preview
                var footerBorder = new Border
                {
                    Background = new SolidColorBrush(Color.FromRgb(236, 240, 241)),
                    Padding = new Thickness(6, 3, 6, 3)
                };

                var footerGrid = new Grid();

                var systemBlock = new TextBlock
                {
                    Text = "",
                    FontFamily = new FontFamily("Arial"),
                    FontSize = 7,
                    Foreground = new SolidColorBrush(Color.FromRgb(127, 140, 141)),
                    HorizontalAlignment = HorizontalAlignment.Center,
                    TextAlignment = TextAlignment.Center,
                    FlowDirection = FlowDirection.RightToLeft
                };

                footerGrid.Children.Add(systemBlock);
                footerBorder.Child = footerGrid;

                return footerBorder;
            }
        }

        private string GetTenantInfoForPdf(RentReceipt receipt, Property? property = null, Customer? customer = null)
        {
            var info = "";

            // Tenant information
            if (customer != null)
            {
                info += $"الاسم: {customer.FullName}\n";
            }
            else if (receipt.Contract?.Customer != null)
            {
                info += $"الاسم: {receipt.Contract.Customer.FullName}\n";
            }

            // Property information
            if (property != null)
            {
                if (!string.IsNullOrEmpty(property.Address))
                    info += $"عنوان المحل: {property.Address}\n";
                else
                    info += $"عنوان المحل: {property.Name}\n";

                info += $"الإيجار الشهري: {property.MonthlyRent:N0} درهم";
            }
            else if (receipt.Contract?.Property != null)
            {
                var prop = receipt.Contract.Property;
                if (!string.IsNullOrEmpty(prop.Address))
                    info += $"عنوان المحل: {prop.Address}\n";
                else
                    info += $"عنوان المحل: {prop.Name}\n";

                info += $"الإيجار الشهري: {prop.MonthlyRent:N0} درهم";
            }

            return info;
        }

        private string GetPaymentDetailsForPdf(RentReceipt receipt)
        {
            var details = $"تاريخ الأداء: {receipt.PaymentDate:dd/MM/yyyy}\n";

            if (receipt.PeriodStartDate.HasValue && receipt.PeriodEndDate.HasValue)
            {
                details += $"فترة الكراء: ";
                details += $"من {receipt.PeriodStartDate.Value:dd/MM/yyyy} ";
                details += $"إلى {receipt.PeriodEndDate.Value:dd/MM/yyyy}";
            }

            if (!string.IsNullOrWhiteSpace(receipt.Notes))
            {
                details += $"\n\nملاحظات:\n{receipt.Notes}";
            }

            return details;
        }

        // Keep original methods for backward compatibility
        private string GetTenantInfo(RentReceipt receipt, Property? property = null, Customer? customer = null)
        {
            return GetTenantInfoForPdf(receipt, property, customer);
        }

        private string GetPaymentDetails(RentReceipt receipt)
        {
            return GetPaymentDetailsForPdf(receipt);
        }

        private string GetLegalNotices()
        {
            return "⚖️ تحت جميع التحفظات القانونية\n\n" +
                   "تنبيهات: لا ينبغي للمكتري أن يرتحل:\n\n" +
                   "أولاً: إن لم يبين لرب الملك عن طريق توصيل من القابض بأنه أدى جميع ما عليه من الأداءات الشخصية والخاصة بالمنقول للعام الجاري.\n\n" +
                   "ثانياً: إن لم يدفع أو يقبض طلب الإفراغ كتابة في الآجال المقررة.\n\n" +
                   "ثالثاً: إن لم يجعل جميع الإصلاحات التي تلزم المكتري طبق العادة في ذلك أو طبق حالة المحل إن وجدت.";
        }

        private string GetLegalNoticesCompactForPdf()
        {
            return "⚖️ تحت جميع التحفظات القانونية\n\n" +
                   "تنبيهات: لا ينبغي للمكتري أن يرتحل:\n" +
                   "أولاً: إن لم يبين لرب الملك عن طريق توصيل من القابض بأنه أدى جميع ما عليه من الأداءات الشخصية والخاصة بالمنقول للعام الجاري.\n" +
                   "ثانياً: إن لم يدفع أو يقبض طلب الإفراغ كتابة في الآجال المقررة.\n" +
                   "ثالثاً: إن لم يجعل جميع الإصلاحات التي تلزم المكتري طبق العادة في ذلك أو طبق حالة المحل إن وجدت.";
        }

        private string GetLegalNoticesCompact()
        {
            return GetLegalNoticesCompactForPdf();
        }

        private string GetLegalNoticesForPrint()
        {
            return "• أداء جميع الأداءات الشخصية والخاصة بالمنقول للعام الجاري\n" +
                   "• دفع الكراء في الوقت المحدد له\n" +
                   "• القيام بجميع الإصلاحات التي تلزم المكتري طبق العادة\n" +
                   "• المحافظة على حالة المحل وعدم إحداث أضرار به\n" +
                   "• احترام الآجال المقررة لطلب الإفراغ كتابة\n" +
                   "هذا التوصيل صحيح ومعتبر قانونياً ويُعتبر إثباتاً لأداء الكراء المذكور.";
        }

        // Alternative method: Print with preview
        public bool PrintReceiptWithPreview(RentReceipt receipt, Property? property = null, Customer? customer = null)
        {
            try
            {
                // Create a separate visual for preview (flexible layout)
                var previewVisual = CreateReceiptVisual(receipt, forPrint: false, property, customer);

                // Create a preview window - comfortable size for preview
                var previewWindow = new Window
                {
                    Title = $"معاينة التوصيل - {receipt.ReceiptNumber}",
                    Width = 500,
                    Height = 700,
                    WindowStartupLocation = WindowStartupLocation.CenterScreen,
                    FlowDirection = FlowDirection.RightToLeft,
                    Background = new SolidColorBrush(Color.FromRgb(240, 240, 240))
                };

                var scrollViewer = new ScrollViewer
                {
                    Content = previewVisual,
                    HorizontalScrollBarVisibility = ScrollBarVisibility.Auto,
                    VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                    Padding = new Thickness(20, 20, 20, 20),
                    Background = new SolidColorBrush(Color.FromRgb(250, 250, 250)),
                    FlowDirection = FlowDirection.RightToLeft
                };

                // Add print button to preview
                var buttonPanel = new StackPanel
                {
                    Orientation = Orientation.Horizontal,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(10),
                    Background = new SolidColorBrush(Color.FromRgb(248, 249, 250))
                };

                var printButton = new Button
                {
                    Content = "🖨️ طباعة",
                    Padding = new Thickness(20, 10, 20, 10),
                    Margin = new Thickness(5),
                    FontFamily = new FontFamily("Tahoma"),
                    FontSize = 12,
                    Background = new SolidColorBrush(Color.FromRgb(33, 150, 243)),
                    Foreground = Brushes.White,
                    BorderThickness = new Thickness(0)
                };

                var closeButton = new Button
                {
                    Content = "❌ إغلاق",
                    Padding = new Thickness(20, 10, 20, 10),
                    Margin = new Thickness(5),
                    FontFamily = new FontFamily("Tahoma"),
                    FontSize = 12,
                    Background = new SolidColorBrush(Color.FromRgb(244, 67, 54)),
                    Foreground = Brushes.White,
                    BorderThickness = new Thickness(0)
                };

                printButton.Click += (s, e) =>
                {
                    // Create a new visual for printing (A6 Landscape layout)
                    var printVisual = CreateReceiptVisual(receipt, forPrint: true, property, customer);

                    var printDialog = new PrintDialog();
                    var printTicket = printDialog.PrintTicket;
                    printTicket.PageMediaSize = new PageMediaSize(PageMediaSizeName.ISOA6, 583, 413); // A6 Landscape
                    printTicket.PageOrientation = PageOrientation.Landscape;

                    if (printDialog.ShowDialog() == true)
                    {
                        printDialog.PrintVisual(printVisual, $"توصيل كراء - {receipt.ReceiptNumber}");
                        MessageBox.Show("تم الطباعة بنجاح! ✅", "نجحت الطباعة",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }

                    previewWindow.Close();
                };

                closeButton.Click += (s, e) => previewWindow.Close();

                buttonPanel.Children.Add(printButton);
                buttonPanel.Children.Add(closeButton);

                var mainPanel = new DockPanel();
                DockPanel.SetDock(buttonPanel, Dock.Bottom);
                mainPanel.Children.Add(buttonPanel);
                mainPanel.Children.Add(scrollViewer);

                previewWindow.Content = mainPanel;
                previewWindow.ShowDialog();

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في المعاينة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        public bool SaveReceiptAsPdf(RentReceipt receipt, string filePath)
        {
            try
            {
                // Create the receipt visual for PDF
                var receiptVisual = CreateReceiptVisual(receipt, forPrint: true);

                // For now, just save as XPS (WPF native format)
                // In a real implementation, you might want to use a PDF library
                var xpsPath = filePath.Replace(".pdf", ".xps");

                // Create a simple document and save
                var printDialog = new PrintDialog();

                // Print the visual to create the file
                printDialog.PrintVisual(receiptVisual, $"Receipt_{receipt.ReceiptNumber}");

                return true; // Return true for successful creation
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving receipt as PDF: {ex.Message}");
                return false;
            }
        }
    }
}
