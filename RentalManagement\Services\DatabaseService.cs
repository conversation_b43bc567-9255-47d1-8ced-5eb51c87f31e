using Microsoft.EntityFrameworkCore;
using RentalManagement.Data;
using System.IO;

namespace RentalManagement.Services
{
    public class DatabaseService
    {
        private static DatabaseService? _instance;
        private static readonly object _lock = new object();

        public static DatabaseService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                            _instance = new DatabaseService();
                    }
                }
                return _instance;
            }
        }

        private DatabaseService()
        {
            InitializeDatabase();
        }

        public void InitializeDatabase()
        {
            try
            {
                using var context = new RentalDbContext();
                
                // Create database if it doesn't exist
                context.Database.EnsureCreated();
                
                // Apply any pending migrations
                if (context.Database.GetPendingMigrations().Any())
                {
                    context.Database.Migrate();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تهيئة قاعدة البيانات: {ex.Message}", ex);
            }
        }

        public RentalDbContext CreateContext()
        {
            return new RentalDbContext();
        }

        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                using var context = new RentalDbContext();
                return await context.Database.CanConnectAsync();
            }
            catch
            {
                return false;
            }
        }

        public async Task BackupDatabaseAsync(string backupPath)
        {
            try
            {
                using var context = new RentalDbContext();
                var dbPath = context.Database.GetConnectionString()?.Replace("Data Source=", "");
                
                if (!string.IsNullOrEmpty(dbPath) && File.Exists(dbPath))
                {
                    await Task.Run(() => File.Copy(dbPath, backupPath, true));
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في نسخ قاعدة البيانات: {ex.Message}", ex);
            }
        }

        public async Task RestoreDatabaseAsync(string backupPath)
        {
            try
            {
                if (!File.Exists(backupPath))
                    throw new FileNotFoundException("ملف النسخة الاحتياطية غير موجود");

                using var context = new RentalDbContext();
                var dbPath = context.Database.GetConnectionString()?.Replace("Data Source=", "");
                
                if (!string.IsNullOrEmpty(dbPath))
                {
                    // Close all connections
                    context.Database.CloseConnection();
                    
                    await Task.Run(() => File.Copy(backupPath, dbPath, true));
                    
                    // Reinitialize
                    InitializeDatabase();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في استعادة قاعدة البيانات: {ex.Message}", ex);
            }
        }
    }
}
