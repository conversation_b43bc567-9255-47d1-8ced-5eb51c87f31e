using System;
using System.Globalization;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;
using SimpleRentalApp.Data;
using SimpleRentalApp.Models;

namespace SimpleRentalApp.Windows
{
    public partial class RentIncreaseDialog : Window
    {
        private readonly Contract _contract;
        private readonly RentalDbContext _context;

        public RentIncreaseDialog(Contract contract)
        {
            InitializeComponent();
            _contract = contract ?? throw new ArgumentNullException(nameof(contract));
            _context = new RentalDbContext();
            
            InitializeForm();
        }

        private void InitializeForm()
        {
            // عرض معلومات العقد
            ContractInfoText.Text = $"العقد رقم {_contract.Id} - {_contract.Property?.Name ?? "غير محدد"}";
            
            // عرض الإيجار الحالي
            CurrentRentText.Text = $"{_contract.CurrentRentAmount:N0} درهم";
            
            // تعيين التاريخ الافتراضي
            EffectiveDatePicker.SelectedDate = DateTime.Now.AddMonths(1);
            
            // تحديث المعاينة
            UpdateNewRentPreview();
        }

        private void IncreaseTypeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (IncreaseUnitText != null)
            {
                IncreaseUnitText.Text = IncreaseTypeComboBox.SelectedIndex == 0 ? "%" : "درهم";
                UpdateNewRentPreview();
            }
        }

        private void IncreaseValueTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateNewRentPreview();
        }

        private void UpdateNewRentPreview()
        {
            if (NewRentText == null || IncreaseValueTextBox == null || IncreaseTypeComboBox == null)
                return;

            try
            {
                if (decimal.TryParse(IncreaseValueTextBox.Text, out decimal increaseValue) && increaseValue > 0)
                {
                    decimal newRent;

                    if (IncreaseTypeComboBox.SelectedIndex == 0) // نسبة مئوية
                    {
                        newRent = _contract.CurrentRentAmount * (1 + increaseValue / 100);
                    }
                    else // مبلغ ثابت
                    {
                        newRent = _contract.CurrentRentAmount + increaseValue;
                    }

                    NewRentText.Text = $"{newRent:N0} درهم";
                    NewRentText.Foreground = System.Windows.Media.Brushes.Green;

                    // تحديث ضريبة النظافة
                    if (NewCleaningTaxText != null)
                    {
                        var newCleaningTax = newRent * 12 * 0.105m;
                        NewCleaningTaxText.Text = $"ضريبة النظافة: {newCleaningTax:N0} درهم سنوياً";
                        NewCleaningTaxText.Foreground = System.Windows.Media.Brushes.Green;
                    }
                }
                else
                {
                    NewRentText.Text = $"{_contract.CurrentRentAmount:N0} درهم";
                    NewRentText.Foreground = System.Windows.Media.Brushes.Gray;

                    if (NewCleaningTaxText != null)
                    {
                        var currentCleaningTax = _contract.CurrentRentAmount * 12 * 0.105m;
                        NewCleaningTaxText.Text = $"ضريبة النظافة: {currentCleaningTax:N0} درهم سنوياً";
                        NewCleaningTaxText.Foreground = System.Windows.Media.Brushes.Gray;
                    }
                }
            }
            catch
            {
                NewRentText.Text = $"{_contract.CurrentRentAmount:N0} درهم";
                NewRentText.Foreground = System.Windows.Media.Brushes.Gray;

                if (NewCleaningTaxText != null)
                {
                    var currentCleaningTax = _contract.CurrentRentAmount * 12 * 0.105m;
                    NewCleaningTaxText.Text = $"ضريبة النظافة: {currentCleaningTax:N0} درهم سنوياً";
                    NewCleaningTaxText.Foreground = System.Windows.Media.Brushes.Gray;
                }
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateInput())
                    return;

                decimal increaseValue = decimal.Parse(IncreaseValueTextBox.Text);
                bool isPercentage = IncreaseTypeComboBox.SelectedIndex == 0;
                DateTime effectiveDate = EffectiveDatePicker.SelectedDate ?? DateTime.Now;

                // حساب الإيجار الجديد
                decimal newRentAmount;
                if (isPercentage)
                {
                    newRentAmount = _contract.CurrentRentAmount * (1 + increaseValue / 100);
                }
                else
                {
                    newRentAmount = _contract.CurrentRentAmount + increaseValue;
                }

                // إنشاء سجل زيادة جديد
                var rentIncrease = new RentIncrease
                {
                    ContractId = _contract.Id,
                    PreviousAmount = _contract.CurrentRentAmount,
                    NewAmount = newRentAmount,
                    IncreasePercentage = isPercentage ? increaseValue :
                        ((newRentAmount - _contract.CurrentRentAmount) / _contract.CurrentRentAmount) * 100,
                    IncreaseDate = effectiveDate,
                    Reason = NotesTextBox.Text?.Trim() ?? "زيادة إيجار",
                    CreatedDate = DateTime.Now
                };

                // حفظ في قاعدة البيانات
                _context.RentIncreases.Add(rentIncrease);

                // تحديث العقد
                _contract.CurrentRentAmount = newRentAmount;
                _contract.RentIncreaseCount++;
                _contract.RentIncreasePercentage = rentIncrease.IncreasePercentage;
                _contract.UpdatedDate = DateTime.Now;

                // تحديث إيجار المحل في جدول Properties
                if (_contract.Property != null)
                {
                    var property = await _context.Properties.FindAsync(_contract.PropertyId);
                    if (property != null)
                    {
                        var oldRent = property.MonthlyRent;
                        property.MonthlyRent = newRentAmount;
                        property.UpdatedDate = DateTime.Now;

                        // إضافة سجل في تاريخ التغييرات
                        await LogRentChangeAsync(property.Id, oldRent, newRentAmount, rentIncrease.IncreaseDate,
                            $"زيادة إيجار العقد رقم {_contract.Id}");
                    }
                }

                await _context.SaveChangesAsync();

                // حساب ضريبة النظافة الجديدة
                var oldCleaningTax = _contract.CurrentRentAmount * 12 * 0.105m;
                var newCleaningTax = newRentAmount * 12 * 0.105m;
                var cleaningTaxIncrease = newCleaningTax - oldCleaningTax;

                MessageBox.Show(
                    $"تم حفظ زيادة الإيجار بنجاح!\n\n" +
                    $"📊 تفاصيل الزيادة:\n" +
                    $"• الإيجار السابق: {_contract.CurrentRentAmount:N0} درهم\n" +
                    $"• الإيجار الجديد: {newRentAmount:N0} درهم\n" +
                    $"• مقدار الزيادة: {rentIncrease.IncreaseAmount:N0} درهم ({rentIncrease.IncreasePercentage:F1}%)\n\n" +
                    $"🧹 تأثير على ضريبة النظافة:\n" +
                    $"• الضريبة السابقة: {oldCleaningTax:N0} درهم سنوياً\n" +
                    $"• الضريبة الجديدة: {newCleaningTax:N0} درهم سنوياً\n" +
                    $"• زيادة الضريبة: {cleaningTaxIncrease:N0} درهم سنوياً\n\n" +
                    $"✅ تم تحديث بيانات المحل وضريبة النظافة تلقائياً",
                    "تم الحفظ بنجاح",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ زيادة الإيجار: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            // التحقق من قيمة الزيادة
            if (string.IsNullOrWhiteSpace(IncreaseValueTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال قيمة الزيادة", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                IncreaseValueTextBox.Focus();
                return false;
            }

            if (!decimal.TryParse(IncreaseValueTextBox.Text, out decimal increaseValue) || increaseValue <= 0)
            {
                MessageBox.Show("يرجى إدخال قيمة زيادة صحيحة أكبر من صفر", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                IncreaseValueTextBox.Focus();
                return false;
            }

            // التحقق من النسبة المئوية
            if (IncreaseTypeComboBox.SelectedIndex == 0 && increaseValue > 100)
            {
                MessageBox.Show("النسبة المئوية يجب أن تكون أقل من أو تساوي 100%", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                IncreaseValueTextBox.Focus();
                return false;
            }

            // التحقق من التاريخ
            if (!EffectiveDatePicker.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى اختيار تاريخ سريان الزيادة", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                EffectiveDatePicker.Focus();
                return false;
            }

            if (EffectiveDatePicker.SelectedDate.Value < DateTime.Today)
            {
                var result = MessageBox.Show(
                    "تاريخ سريان الزيادة في الماضي. هل تريد المتابعة؟",
                    "تأكيد التاريخ",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);
                
                if (result == MessageBoxResult.No)
                {
                    EffectiveDatePicker.Focus();
                    return false;
                }
            }

            return true;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private async Task LogRentChangeAsync(int propertyId, decimal oldRent, decimal newRent, DateTime changeDate, string reason)
        {
            try
            {
                // إنشاء سجل تغيير الإيجار
                var rentChangeLog = new RentChangeLog
                {
                    PropertyId = propertyId,
                    ContractId = _contract.Id,
                    OldRentAmount = oldRent,
                    NewRentAmount = newRent,
                    ChangeDate = changeDate,
                    ChangeReason = reason,
                    Notes = $"تم تطبيق زيادة إيجار بنسبة {((newRent - oldRent) / oldRent * 100):F1}%. " +
                           $"ضريبة النظافة السابقة: {oldRent * 12 * 0.105m:N0} درهم، " +
                           $"ضريبة النظافة الجديدة: {newRent * 12 * 0.105m:N0} درهم",
                    ChangedBy = "النظام - زيادة إيجار",
                    CreatedDate = DateTime.Now
                };

                _context.RentChangeLogs.Add(rentChangeLog);
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ دون إيقاف العملية الرئيسية
                System.Diagnostics.Debug.WriteLine($"خطأ في تسجيل تغيير الإيجار: {ex.Message}");
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            base.OnClosed(e);
        }
    }
}
