; Rental Management System - NSIS Installer Script
; Developer: <PERSON><PERSON><PERSON>
; Version: 1.0.0

Unicode True

; Basic Variables
!define APPNAME "Rental Management System"
!define APPNAME_AR "Rental Managemeent"
!define COMPANYNAME "Hafid <PERSON>ou"
!define DESCRIPTION "Property and Rental Management System"
!define VERSIONMAJOR 1
!define VERSIONMINOR 0
!define VERSIONBUILD 0
!define HELPURL "aabdou.hafid@gmailcom"
!define UPDATEURL ""
!define ABOUTURL ""
!define INSTALLSIZE 150000

; Installation Settings
RequestExecutionLevel admin
InstallDir "$PROGRAMFILES64\RentalManagement"
Name "${APPNAME}"
outFile "RentalManagement_Setup_v1.0.exe"

; تضمين المكتبات المطلوبة
!include LogicLib.nsh
!include MUI2.nsh
!include FileFunc.nsh

; إعدادات Modern UI
!define MUI_ABORTWARNING
!define MUI_ICON "app_icon.ico"
!define MUI_UNICON "app_icon.ico"

; نصوص مخصصة للواجهة
!define MUI_WELCOMEPAGE_TITLE "Welcome to the installation wizard. ${APPNAME}"
!define MUI_WELCOMEPAGE_TEXT "This wizard will guide you through the installation. ${APPNAME}.$\r$\n$\r$\nIt is recommended that you close all other applications before proceeding..$\r$\n$\r$\nClick Next to continue.."

!define MUI_DIRECTORYPAGE_TEXT_TOP "It will be installed. ${APPNAME} In the next volume.$\r$\n$\r$\nTo install the program in a different folder, click Browse and choose another folder. Click Next to continue.."

!define MUI_FINISHPAGE_TITLE "Installation complete ${APPNAME}"
!define MUI_FINISHPAGE_TEXT "Installed ${APPNAME} Successfully on computer.$\r$\n$\r$\nClick Finish to close this wizard.."
!define MUI_FINISHPAGE_RUN "$INSTDIR\SimpleRentalApp.exe"
!define MUI_FINISHPAGE_RUN_TEXT "run ${APPNAME}"

; Installation Pages
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

; Uninstallation Pages
!define MUI_UNCONFIRMPAGE_TEXT_TOP "${APPNAME} will be uninstalled from your computer. Click Uninstall to continue."
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES

; Supported Languages
!insertmacro MUI_LANGUAGE "English"

; Custom Section Text
LangString DESC_SecMain ${LANG_ENGLISH} "Core application files"
LangString DESC_SecDesktop ${LANG_ENGLISH} "Create desktop shortcut"
LangString DESC_SecStartMenu ${LANG_ENGLISH} "Create Start menu shortcuts"

; Verify Administrator Rights
!macro VerifyUserIsAdmin
UserInfo::GetAccountType
pop $0
${If} $0 != "admin"
    messageBox mb_iconstop "This program requires administrator privileges to install!"
    setErrorLevel 740
    quit
${EndIf}
!macroend

; Initialization Function
function .onInit
    setShellVarContext all
    !insertmacro VerifyUserIsAdmin
functionEnd

; Main Installation Section
Section "Core Files" SecMain
    SectionIn RO
    setOutPath $INSTDIR

    ; Copy application files
    File /r "dist_new\*.*"

    ; Create uninstaller
    WriteUninstaller "$INSTDIR\uninstall.exe"

    ; Register program information in system
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\RentalManagement" "DisplayName" "${APPNAME}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\RentalManagement" "UninstallString" "$\"$INSTDIR\uninstall.exe$\""
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\RentalManagement" "QuietUninstallString" "$\"$INSTDIR\uninstall.exe$\" /S"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\RentalManagement" "InstallLocation" "$\"$INSTDIR$\""
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\RentalManagement" "DisplayIcon" "$\"$INSTDIR\SimpleRentalApp.exe$\""
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\RentalManagement" "Publisher" "${COMPANYNAME}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\RentalManagement" "HelpLink" "${HELPURL}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\RentalManagement" "URLUpdateInfo" "${UPDATEURL}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\RentalManagement" "URLInfoAbout" "${ABOUTURL}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\RentalManagement" "DisplayVersion" "${VERSIONMAJOR}.${VERSIONMINOR}.${VERSIONBUILD}"
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\RentalManagement" "VersionMajor" ${VERSIONMAJOR}
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\RentalManagement" "VersionMinor" ${VERSIONMINOR}
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\RentalManagement" "NoModify" 1
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\RentalManagement" "NoRepair" 1
    
    ; Calculate and register installation size
    ${GetSize} "$INSTDIR" "/S=0K" $0 $1 $2
    IntFmt $0 "0x%08X" $0
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\RentalManagement" "EstimatedSize" "$0"

    ; Create data directory
    CreateDirectory "$APPDATA\TadbirAlKira"
    
SectionEnd

; Start Menu Shortcuts Section
Section "Start Menu Shortcuts" SecStartMenu
    CreateDirectory "$SMPROGRAMS\${APPNAME}"
    CreateShortCut "$SMPROGRAMS\${APPNAME}\${APPNAME}.lnk" "$INSTDIR\SimpleRentalApp.exe" "" "$INSTDIR\SimpleRentalApp.exe" 0
    CreateShortCut "$SMPROGRAMS\${APPNAME}\Uninstall.lnk" "$INSTDIR\uninstall.exe" "" "$INSTDIR\uninstall.exe" 0
    CreateShortCut "$SMPROGRAMS\${APPNAME}\User Guide.lnk" "$INSTDIR\USER_GUIDE.txt"
SectionEnd

; Desktop Shortcut Section
Section "Desktop Shortcut" SecDesktop
    CreateShortCut "$DESKTOP\${APPNAME}.lnk" "$INSTDIR\SimpleRentalApp.exe" "" "$INSTDIR\SimpleRentalApp.exe" 0
SectionEnd

; Section Descriptions
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
    !insertmacro MUI_DESCRIPTION_TEXT ${SecMain} $(DESC_SecMain)
    !insertmacro MUI_DESCRIPTION_TEXT ${SecStartMenu} $(DESC_SecStartMenu)
    !insertmacro MUI_DESCRIPTION_TEXT ${SecDesktop} $(DESC_SecDesktop)
!insertmacro MUI_FUNCTION_DESCRIPTION_END

; Uninstall Section
Section "Uninstall"
    ; Delete Start menu shortcuts
    Delete "$SMPROGRAMS\${APPNAME}\${APPNAME}.lnk"
    Delete "$SMPROGRAMS\${APPNAME}\Uninstall.lnk"
    Delete "$SMPROGRAMS\${APPNAME}\User Guide.lnk"
    RmDir "$SMPROGRAMS\${APPNAME}"

    ; Delete desktop shortcut
    Delete "$DESKTOP\${APPNAME}.lnk"

    ; Delete application files
    RmDir /r "$INSTDIR"

    ; Delete registry information
    DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\RentalManagement"

    ; Ask user about data removal
    MessageBox MB_YESNO "Do you want to delete application data? This will remove all your rental data." IDNO skip_data_removal
    RmDir /r "$APPDATA\TadbirAlKira"
    skip_data_removal:

SectionEnd
