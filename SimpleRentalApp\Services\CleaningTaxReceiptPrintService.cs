using System;
using System.IO;
using System.IO.Packaging;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using System.Windows.Shapes;
using System.Windows.Xps;
using System.Windows.Xps.Packaging;
using System.Printing;
using SimpleRentalApp.Models;

namespace SimpleRentalApp.Services;

public class CleaningTaxPrintService
{
    private static CleaningTaxPrintService? _instance;
    public static CleaningTaxPrintService Instance => _instance ??= new CleaningTaxPrintService();

    private readonly CleaningTaxCalculationService _calculationService;

    public CleaningTaxPrintService()
    {
        _calculationService = new CleaningTaxCalculationService();
    }

    /// <summary>
    /// طباعة وصل ضريبة النظافة من Payment مع الحساب التدريجي الصحيح
    /// </summary>
    public async Task<bool> PrintReceiptFromPaymentAsync(Payment payment)
    {
        try
        {
            // إنشاء بيانات الوصل مع الحساب التدريجي
            var receiptData = await _calculationService.CreateReceiptDataAsync(payment);

            // Configure print dialog first
            var printDialog = new PrintDialog();

            // Set A6 paper size and portrait orientation for printing
            var printTicket = printDialog.PrintTicket;
            printTicket.PageMediaSize = new PageMediaSize(PageMediaSizeName.ISOA6, 413, 583); // A6 Portrait
            printTicket.PageOrientation = PageOrientation.Portrait;

            // Show print dialog
            if (printDialog.ShowDialog() == true)
            {
                // Create the receipt visual element for printing
                var receiptVisual = CreateReceiptVisualFromData(receiptData, forPrint: true);

                // Print the visual element
                printDialog.PrintVisual(receiptVisual, $"وصل ضريبة النظافة - {receiptData.ReceiptNumber}");
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            throw new Exception($"خطأ في طباعة الوصل: {ex.Message}");
        }
    }

    public bool PrintReceipt(CleaningTaxReceipt receipt)
    {
        try
        {
            // Configure print dialog first
            var printDialog = new PrintDialog();

            // Set A6 paper size and portrait orientation for printing (like rent receipts)
            var printTicket = printDialog.PrintTicket;
            printTicket.PageMediaSize = new PageMediaSize(PageMediaSizeName.ISOA6, 413, 583); // A6 Portrait (105x148mm in 1/96 inch)
            printTicket.PageOrientation = PageOrientation.Portrait;

            // Show print dialog
            if (printDialog.ShowDialog() == true)
            {
                // Create the receipt visual element for printing (A6 Landscape)
                var receiptVisual = CreateReceiptVisual(receipt, forPrint: true);

                // Print the visual element
                printDialog.PrintVisual(receiptVisual, $"وصل ضريبة النظافة - {receipt.ReceiptNumber}");
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            throw new Exception($"خطأ في طباعة الوصل: {ex.Message}");
        }
    }

    public bool PrintReceiptWithPreview(CleaningTaxReceipt receipt)
    {
        try
        {
            // Create a separate visual for preview (flexible layout)
            var previewVisual = CreateReceiptVisual(receipt, forPrint: false);

            // Create a preview window - comfortable size for preview
            var previewWindow = new Window
            {
                Title = $"معاينة وصل ضريبة النظافة - {receipt.ReceiptNumber}",
                Width = 500,
                Height = 700,
                WindowStartupLocation = WindowStartupLocation.CenterScreen,
                FlowDirection = FlowDirection.RightToLeft,
                Background = new SolidColorBrush(Color.FromRgb(240, 240, 240))
            };

            // Create main grid
            var mainGrid = new Grid();
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });

            // Header with buttons
            var headerPanel = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(46, 125, 50)),
                Padding = new Thickness(20, 15, 20, 15)
            };

            var headerGrid = new Grid();
            headerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            headerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            var titleText = new TextBlock
            {
                Text = "🧹 معاينة وصل ضريبة النظافة",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = Brushes.White,
                VerticalAlignment = VerticalAlignment.Center
            };
            Grid.SetColumn(titleText, 0);
            headerGrid.Children.Add(titleText);

            var buttonsPanel = new StackPanel { Orientation = Orientation.Horizontal };

            var printButton = new Button
            {
                Content = "🖨️ طباعة",
                Background = new SolidColorBrush(Color.FromRgb(76, 175, 80)),
                Foreground = Brushes.White,
                Padding = new Thickness(15, 8, 15, 8),
                Margin = new Thickness(0, 0, 10, 0),
                BorderThickness = new Thickness(0, 0, 0, 0),
                FontWeight = FontWeights.Bold,
                Cursor = System.Windows.Input.Cursors.Hand
            };

            var closeButton = new Button
            {
                Content = "❌ إغلاق",
                Background = new SolidColorBrush(Color.FromRgb(244, 67, 54)),
                Foreground = Brushes.White,
                Padding = new Thickness(15, 8, 15, 8),
                BorderThickness = new Thickness(0, 0, 0, 0),
                FontWeight = FontWeights.Bold,
                Cursor = System.Windows.Input.Cursors.Hand
            };

            buttonsPanel.Children.Add(printButton);
            buttonsPanel.Children.Add(closeButton);
            Grid.SetColumn(buttonsPanel, 1);
            headerGrid.Children.Add(buttonsPanel);

            headerPanel.Child = headerGrid;
            Grid.SetRow(headerPanel, 0);
            mainGrid.Children.Add(headerPanel);

            // Preview content
            var scrollViewer = new ScrollViewer
            {
                Background = Brushes.White,
                Margin = new Thickness(20),
                Padding = new Thickness(20),
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto
            };

            scrollViewer.Content = previewVisual;
            Grid.SetRow(scrollViewer, 1);
            mainGrid.Children.Add(scrollViewer);

            previewWindow.Content = mainGrid;

            // Button events
            closeButton.Click += (s, e) => previewWindow.Close();

            printButton.Click += (s, e) =>
            {
                // Create a new visual for printing (A6 Portrait layout)
                var printVisual = CreateReceiptVisual(receipt, forPrint: true);

                var printDialog = new PrintDialog();
                var printTicket = printDialog.PrintTicket;
                printTicket.PageMediaSize = new PageMediaSize(PageMediaSizeName.ISOA6, 413, 583); // A6 Portrait
                printTicket.PageOrientation = PageOrientation.Portrait;

                if (printDialog.ShowDialog() == true)
                {
                    printDialog.PrintVisual(printVisual, $"وصل ضريبة النظافة - {receipt.ReceiptNumber}");
                    MessageBox.Show("تم طباعة وصل ضريبة النظافة بنجاح! ✅", "نجحت الطباعة",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }

                previewWindow.Close();
            };

            // Show preview window
            previewWindow.ShowDialog();
            return true;
        }
        catch (Exception ex)
        {
            throw new Exception($"خطأ في معاينة الوصل: {ex.Message}");
        }
    }

    private FrameworkElement CreateReceiptVisual(CleaningTaxReceipt receipt, bool forPrint)
    {
        var isA6Size = forPrint;
        var containerWidth = isA6Size ? 370 : 450; // A6 portrait width optimized
        var fontSize = isA6Size ? 9 : 10;

        var container = new Border
        {
            Width = containerWidth,
            Background = Brushes.White,
            BorderBrush = isA6Size ? Brushes.Transparent : new SolidColorBrush(Color.FromRgb(224, 224, 224)),
            BorderThickness = new Thickness(isA6Size ? 0 : 1),
            CornerRadius = new CornerRadius(isA6Size ? 0 : 8),
            Padding = new Thickness(isA6Size ? 8 : 15),
            FlowDirection = FlowDirection.RightToLeft
        };

        var content = new StackPanel();

        // Header
        var header = new Border
        {
            Background = new SolidColorBrush(Color.FromRgb(46, 125, 50)),
            CornerRadius = new CornerRadius(isA6Size ? 0 : 6, isA6Size ? 0 : 6, 0, 0),
            Padding = new Thickness(15, isA6Size ? 6 : 12, 15, isA6Size ? 6 : 12),
            Margin = new Thickness(0, 0, 0, isA6Size ? 6 : 12)
        };

        var headerStack = new StackPanel();

        var titleText = new TextBlock
        {
            Text = "🧹 وصل دفع ضريبة النظافة",
            FontSize = isA6Size ? 11 : 14,
            FontWeight = FontWeights.Bold,
            Foreground = Brushes.White,
            TextAlignment = TextAlignment.Center,
            FontFamily = new FontFamily("Tahoma"),
            FlowDirection = FlowDirection.RightToLeft
        };

        var receiptNumberText = new TextBlock
        {
            Text = $"رقم الوصل: {receipt.ReceiptNumber}",
            FontSize = isA6Size ? 9 : fontSize,
            FontWeight = FontWeights.Bold,
            Foreground = Brushes.White,
            TextAlignment = TextAlignment.Center,
            FontFamily = new FontFamily("Tahoma"),
            FlowDirection = FlowDirection.RightToLeft,
            Margin = new Thickness(0, 2, 0, 0)
        };

        headerStack.Children.Add(titleText);
        headerStack.Children.Add(receiptNumberText);
        header.Child = headerStack;
        content.Children.Add(header);

        // Vertical layout for both A6 and preview (like rent receipts)
        content.Children.Add(CreateCustomerPropertySection(receipt, isA6Size));
        content.Children.Add(CreatePaymentDetailsSection(receipt, isA6Size));

        // Legal notice and footer
        content.Children.Add(CreateLegalNoticeSection(receipt, isA6Size));
        content.Children.Add(CreateFooterSection(receipt, isA6Size));

        container.Child = content;
        return container;
    }

    private FrameworkElement CreateCustomerPropertySection(CleaningTaxReceipt receipt, bool isA6Size)
    {
        var section = new Border
        {
            Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
            BorderBrush = new SolidColorBrush(Color.FromRgb(224, 224, 224)),
            BorderThickness = new Thickness(1),
            CornerRadius = new CornerRadius(isA6Size ? 0 : 4),
            Padding = new Thickness(isA6Size ? 6 : 12),
            Margin = new Thickness(0, 0, 0, isA6Size ? 4 : 10),
            FlowDirection = FlowDirection.RightToLeft
        };

        var grid = new Grid();
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

        var fontSize = isA6Size ? 8 : 9;
        var labelFontSize = isA6Size ? 8 : 9;

        AddGridRow(grid, 0, "المكتري:", receipt.CustomerName, labelFontSize, fontSize);
        AddGridRow(grid, 1, "المحل:", receipt.PropertyName, labelFontSize, fontSize);
        AddGridRow(grid, 2, "العنوان:", receipt.PropertyAddress, labelFontSize, fontSize);

        section.Child = grid;
        return section;
    }

    private FrameworkElement CreatePaymentDetailsSection(CleaningTaxReceipt receipt, bool isA6Size)
    {
        var section = new Border
        {
            Background = new SolidColorBrush(Color.FromRgb(232, 245, 233)),
            BorderBrush = new SolidColorBrush(Color.FromRgb(76, 175, 80)),
            BorderThickness = new Thickness(1),
            CornerRadius = new CornerRadius(isA6Size ? 0 : 4),
            Padding = new Thickness(isA6Size ? 6 : 12),
            Margin = new Thickness(0, 0, 0, isA6Size ? 4 : 10),
            FlowDirection = FlowDirection.RightToLeft
        };

        var grid = new Grid();
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

        var fontSize = isA6Size ? 8 : 9;
        var labelFontSize = isA6Size ? 8 : 9;

        // Calculate payment type and remaining amount
        var annualTax = (receipt.Property?.MonthlyRent ?? 0) * 12 * 0.105m;
        var paymentType = GetPaymentType(receipt, receipt.Amount, annualTax);
        var remainingAmount = Math.Max(0, annualTax - receipt.Amount);

        AddGridRow(grid, 0, "تاريخ الدفع:", receipt.PaymentDateDisplay, labelFontSize, fontSize);
        AddGridRow(grid, 1, "السنة الضريبية:", receipt.TaxYear.ToString(), labelFontSize, fontSize);
        AddGridRow(grid, 2, "المبلغ:", receipt.AmountDisplay, labelFontSize, fontSize, true);
        AddGridRow(grid, 3, "بالحروف:", receipt.AmountInWords, labelFontSize, fontSize);
        AddGridRow(grid, 4, "نوع الدفعة:", paymentType, labelFontSize, fontSize);
        AddGridRow(grid, 5, "المتبقي من السنة:", remainingAmount > 0 ? $"{remainingAmount:N0} درهم" : "مكتملة", labelFontSize, fontSize);

        if (!string.IsNullOrWhiteSpace(receipt.Notes))
        {
            AddGridRow(grid, 6, "ملاحظات:", receipt.Notes, labelFontSize, fontSize);
        }

        section.Child = grid;
        return section;
    }

    private string GetPaymentType(CleaningTaxReceipt receipt, decimal paidAmount, decimal annualTax)
    {
        if (annualTax == 0) return "غير محدد";

        // Get payment type from property's cleaning tax payment option
        var property = receipt.Property;
        if (property != null && !string.IsNullOrEmpty(property.CleaningTaxPaymentOptionDisplay))
        {
            // Check if the paid amount matches the expected amount for the property's payment option
            var expectedAmount = property.CleaningTaxPerPayment;
            if (Math.Abs(paidAmount - expectedAmount) < 1) // Allow small rounding differences
            {
                return property.CleaningTaxPaymentOptionDisplay;
            }
        }

        // Fallback to percentage-based calculation
        var percentage = (paidAmount / annualTax) * 100;

        if (percentage >= 95) return "دفعة كاملة (سنوية)";
        if (percentage >= 45 && percentage <= 55) return "نصف سنوية";
        if (percentage >= 20 && percentage <= 30) return "ربع سنوية";
        if (percentage >= 5 && percentage <= 15) return "شهرية";

        return "دفعة جزئية";
    }

    private void AddGridRow(Grid grid, int row, string label, string value, double labelFontSize, double valueFontSize, bool highlight = false)
    {
        grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

        var labelText = new TextBlock
        {
            Text = label,
            FontSize = labelFontSize,
            FontWeight = FontWeights.Bold,
            FontFamily = new FontFamily("Tahoma"),
            Margin = new Thickness(0, 0, 5, 1),
            VerticalAlignment = VerticalAlignment.Top,
            FlowDirection = FlowDirection.RightToLeft
        };
        Grid.SetRow(labelText, row);
        Grid.SetColumn(labelText, 0);
        grid.Children.Add(labelText);

        var valueText = new TextBlock
        {
            Text = value,
            FontSize = valueFontSize,
            FontFamily = new FontFamily("Tahoma"),
            TextWrapping = TextWrapping.Wrap,
            Margin = new Thickness(0, 0, 0, 1),
            VerticalAlignment = VerticalAlignment.Top,
            FontWeight = highlight ? FontWeights.Bold : FontWeights.Normal,
            Foreground = highlight ? new SolidColorBrush(Color.FromRgb(46, 125, 50)) : Brushes.Black,
            FlowDirection = FlowDirection.RightToLeft
        };
        Grid.SetRow(valueText, row);
        Grid.SetColumn(valueText, 1);
        grid.Children.Add(valueText);
    }

    private FrameworkElement CreateLegalNoticeSection(CleaningTaxReceipt receipt, bool isA6Size)
    {
        // Calculate remaining amount to determine notice color and text
        var annualTax = (receipt.Property?.MonthlyRent ?? 0) * 12 * 0.105m;
        var remainingAmount = Math.Max(0, annualTax - receipt.Amount);
        var isFullyPaid = remainingAmount <= 0;

        var section = new Border
        {
            Background = isFullyPaid ?
                new SolidColorBrush(Color.FromRgb(232, 245, 233)) : // Light green for fully paid
                new SolidColorBrush(Color.FromRgb(255, 248, 225)),   // Light orange for partial
            BorderBrush = isFullyPaid ?
                new SolidColorBrush(Color.FromRgb(76, 175, 80)) :    // Green for fully paid
                new SolidColorBrush(Color.FromRgb(255, 193, 7)),     // Orange for partial
            BorderThickness = new Thickness(1),
            CornerRadius = new CornerRadius(isA6Size ? 0 : 4),
            Padding = new Thickness(isA6Size ? 4 : 10),
            Margin = new Thickness(0, 0, 0, isA6Size ? 4 : 10),
            FlowDirection = FlowDirection.RightToLeft
        };

        var stack = new StackPanel();

        var warningText = new TextBlock
        {
            Text = "⚠️ إشعار هام",
            FontSize = isA6Size ? 8 : 9,
            FontWeight = FontWeights.Bold,
            FontFamily = new FontFamily("Tahoma"),
            Margin = new Thickness(0, 0, 0, 1),
            FlowDirection = FlowDirection.RightToLeft
        };

        var noticeText = new TextBlock
        {
            FontSize = isA6Size ? 7 : 8,
            FontFamily = new FontFamily("Tahoma"),
            TextWrapping = TextWrapping.Wrap,
            Margin = new Thickness(0, 0, 0, 1),
            FlowDirection = FlowDirection.RightToLeft
        };

        if (isFullyPaid)
        {
            noticeText.Text = $"تم دفع ضريبة النظافة كاملة لسنة {receipt.TaxYear}";
        }
        else
        {
            var paidPercentage = ((annualTax - remainingAmount) / annualTax) * 100;
            noticeText.Text = $"تم دفع ضريبة النظافة بشكل جزئي لسنة {receipt.TaxYear} ({paidPercentage:F0}% من المبلغ الإجمالي)";
        }

        var deadlineText = new TextBlock
        {
            Text = "الموعد النهائي: 31 مايو من كل سنة",
            FontSize = isA6Size ? 7 : 8,
            FontWeight = FontWeights.Bold,
            FontFamily = new FontFamily("Tahoma"),
            Foreground = new SolidColorBrush(Color.FromRgb(211, 47, 47)),
            FlowDirection = FlowDirection.RightToLeft
        };

        stack.Children.Add(warningText);
        stack.Children.Add(noticeText);
        stack.Children.Add(deadlineText);

        section.Child = stack;
        return section;
    }

    private FrameworkElement CreateFooterSection(CleaningTaxReceipt receipt, bool isA6Size)
    {
        var section = new Border
        {
            BorderBrush = new SolidColorBrush(Color.FromRgb(224, 224, 224)),
            BorderThickness = new Thickness(0, 1, 0, 0),
            Padding = new Thickness(0, isA6Size ? 4 : 10, 0, 0),
            FlowDirection = FlowDirection.RightToLeft
        };

        var grid = new Grid();
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

        // Signature area
        var signatureStack = new StackPanel();
        var signatureLabel = new TextBlock
        {
            Text = "توقيع المؤجر",
            FontSize = isA6Size ? 7 : 8,
            FontWeight = FontWeights.Bold,
            FontFamily = new FontFamily("Tahoma"),
            TextAlignment = TextAlignment.Center,
            FlowDirection = FlowDirection.RightToLeft
        };

        var signatureLine = new Border
        {
            Height = isA6Size ? 12 : 20,
            BorderBrush = new SolidColorBrush(Color.FromRgb(221, 221, 221)),
            BorderThickness = new Thickness(0, 0, 0, 1),
            Margin = new Thickness(isA6Size ? 3 : 10, isA6Size ? 3 : 8, isA6Size ? 3 : 10, 0)
        };

        signatureStack.Children.Add(signatureLabel);
        signatureStack.Children.Add(signatureLine);
        Grid.SetColumn(signatureStack, 0);
        grid.Children.Add(signatureStack);

        // Date area
        var stampStack = new StackPanel();
        var stampLabel = new TextBlock
        {
            Text = "ختم المؤجر",
            FontSize = isA6Size ? 6 : 8,
            FontWeight = FontWeights.Bold,
            FontFamily = new FontFamily("Tahoma"),
            TextAlignment = TextAlignment.Center
        };

        var stampLine = new Border
        {
            Height = isA6Size ? 15 : 20,
            BorderBrush = new SolidColorBrush(Color.FromRgb(221, 221, 221)),
            BorderThickness = new Thickness(0, 0, 0, 1),
            Margin = new Thickness(isA6Size ? 5 : 10, isA6Size ? 5 : 8, isA6Size ? 5 : 10, 0)
        };

        stampStack.Children.Add(stampLabel);
        stampStack.Children.Add(stampLine);
        Grid.SetColumn(stampStack, 1);
        grid.Children.Add(stampStack);

        section.Child = grid;
        return section;
    }

    // Legacy method for compatibility - will be removed
    public async Task PrintReceiptAsync(dynamic receiptData)
    {
        // This method is deprecated - use PrintReceipt or PrintReceiptWithPreview instead
        throw new NotImplementedException("استخدم PrintReceipt أو PrintReceiptWithPreview بدلاً من ذلك");
    }

    /// <summary>
    /// إنشاء عنصر بصري للوصل من بيانات CleaningTaxReceiptData
    /// </summary>
    private FrameworkElement CreateReceiptVisualFromData(CleaningTaxReceiptData receiptData, bool forPrint = false)
    {
        bool isA6Size = forPrint;
        double pageWidth = isA6Size ? 413 : 400;  // A6 width in 1/96 inch
        double pageHeight = isA6Size ? 583 : 550; // A6 height in 1/96 inch

        var border = new Border
        {
            Width = pageWidth,
            Height = pageHeight,
            Background = new SolidColorBrush(Colors.White),
            BorderBrush = new SolidColorBrush(Color.FromRgb(200, 200, 200)),
            BorderThickness = new Thickness(isA6Size ? 1 : 2),
            Padding = new Thickness(isA6Size ? 10 : 15), // تحسين الهوامش
            FlowDirection = FlowDirection.RightToLeft
        };

        var mainStack = new StackPanel
        {
            Orientation = Orientation.Vertical,
            HorizontalAlignment = HorizontalAlignment.Stretch
        };

        // Header
        var headerStack = new StackPanel
        {
            Orientation = Orientation.Vertical,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, isA6Size ? 8 : 15)
        };

        // تم حذف اللوغو حسب الطلب

        var titleText = new TextBlock
        {
            Text = "وصل دفع ضريبة النظافة",
            FontSize = isA6Size ? 14 : 18, // تحسين حجم الخط
            FontWeight = FontWeights.Bold,
            FontFamily = new FontFamily("Segoe UI"), // خط أوضح
            TextAlignment = TextAlignment.Center,
            Foreground = new SolidColorBrush(Color.FromRgb(25, 118, 210)) // لون أزرق احترافي
        };

        var receiptNumberText = new TextBlock
        {
            Text = $"رقم الوصل: {receiptData.ReceiptNumber}",
            FontSize = isA6Size ? 10 : 12, // تحسين حجم الخط
            FontFamily = new FontFamily("Segoe UI"),
            FontWeight = FontWeights.Medium,
            TextAlignment = TextAlignment.Center,
            Margin = new Thickness(0, isA6Size ? 3 : 5, 0, 0),
            Foreground = new SolidColorBrush(Color.FromRgb(85, 85, 85))
        };

        var dateText = new TextBlock
        {
            Text = $"التاريخ: {receiptData.PaymentDateDisplay}",
            FontSize = isA6Size ? 10 : 12, // تحسين حجم الخط
            FontFamily = new FontFamily("Segoe UI"),
            FontWeight = FontWeights.Medium,
            TextAlignment = TextAlignment.Center,
            Margin = new Thickness(0, isA6Size ? 2 : 3, 0, 0),
            Foreground = new SolidColorBrush(Color.FromRgb(85, 85, 85))
        };

        headerStack.Children.Add(titleText);
        headerStack.Children.Add(receiptNumberText);
        headerStack.Children.Add(dateText);

        // Tenant and Property Info
        var tenantPropertyBorder = new Border
        {
            Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
            BorderBrush = new SolidColorBrush(Color.FromRgb(221, 221, 221)),
            BorderThickness = new Thickness(1),
            CornerRadius = new CornerRadius(4),
            Padding = new Thickness(isA6Size ? 6 : 10),
            Margin = new Thickness(0, 0, 0, isA6Size ? 6 : 10)
        };

        var tenantPropertyStack = new StackPanel();

        var tenantPropertyTitle = new TextBlock
        {
            Text = "الكاري والمحل",
            FontSize = isA6Size ? 11 : 13, // تحسين حجم الخط
            FontWeight = FontWeights.Bold,
            FontFamily = new FontFamily("Segoe UI"), // خط أوضح
            Margin = new Thickness(0, 0, 0, isA6Size ? 4 : 6),
            Foreground = new SolidColorBrush(Color.FromRgb(25, 118, 210)) // لون أزرق احترافي
        };

        var tenantText = new TextBlock
        {
            Text = $"الكاري: {receiptData.CustomerName}",
            FontSize = isA6Size ? 10 : 11, // تحسين حجم الخط
            FontFamily = new FontFamily("Segoe UI"),
            FontWeight = FontWeights.Medium,
            Margin = new Thickness(0, 0, 0, isA6Size ? 2 : 3),
            TextAlignment = TextAlignment.Left,
            Foreground = new SolidColorBrush(Color.FromRgb(60, 60, 60))
        };

        var propertyText = new TextBlock
        {
            Text = $"المحل: {receiptData.PropertyName}",
            FontSize = isA6Size ? 10 : 11,
            FontFamily = new FontFamily("Segoe UI"),
            FontWeight = FontWeights.Medium,
            Margin = new Thickness(0, 0, 0, isA6Size ? 2 : 3),
            TextAlignment = TextAlignment.Left,
            Foreground = new SolidColorBrush(Color.FromRgb(60, 60, 60))
        };

        var addressText = new TextBlock
        {
            Text = $"العنوان: {receiptData.PropertyAddress}",
            FontSize = isA6Size ? 10 : 11,
            FontFamily = new FontFamily("Segoe UI"),
            FontWeight = FontWeights.Medium,
            TextAlignment = TextAlignment.Left,
            Foreground = new SolidColorBrush(Color.FromRgb(60, 60, 60))
        };

        tenantPropertyStack.Children.Add(tenantPropertyTitle);
        tenantPropertyStack.Children.Add(tenantText);
        tenantPropertyStack.Children.Add(propertyText);
        tenantPropertyStack.Children.Add(addressText);
        tenantPropertyBorder.Child = tenantPropertyStack;

        // Payment Details with Progressive Calculation
        var paymentDetailsBorder = new Border
        {
            Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
            BorderBrush = new SolidColorBrush(Color.FromRgb(221, 221, 221)),
            BorderThickness = new Thickness(1),
            CornerRadius = new CornerRadius(4),
            Padding = new Thickness(isA6Size ? 6 : 10),
            Margin = new Thickness(0, 0, 0, isA6Size ? 6 : 10)
        };

        var paymentDetailsStack = new StackPanel();

        var paymentDetailsTitle = new TextBlock
        {
            Text = "تفاصيل الدفعة",
            FontSize = isA6Size ? 9 : 11,
            FontWeight = FontWeights.Bold,
            FontFamily = new FontFamily("Tahoma"),
            Margin = new Thickness(0, 0, 0, isA6Size ? 3 : 5),
            Foreground = new SolidColorBrush(Color.FromRgb(51, 51, 51))
        };

        var taxYearText = new TextBlock
        {
            Text = $"السنة الضريبية: {receiptData.TaxYear}",
            FontSize = isA6Size ? 8 : 9,
            FontFamily = new FontFamily("Tahoma"),
            Margin = new Thickness(0, 0, 0, isA6Size ? 1 : 2),
            TextAlignment = TextAlignment.Left
        };

        var sequenceText = new TextBlock
        {
            Text = $"الدفعة رقم: {receiptData.PaymentSequence}",
            FontSize = isA6Size ? 8 : 9,
            FontFamily = new FontFamily("Tahoma"),
            Margin = new Thickness(0, 0, 0, isA6Size ? 1 : 2),
            TextAlignment = TextAlignment.Left
        };

        var remainingBeforeText = new TextBlock
        {
            Text = $"المبلغ المتبقي قبل هذه الدفعة: {receiptData.RemainingBeforeDisplay}",
            FontSize = isA6Size ? 8 : 9,
            FontFamily = new FontFamily("Tahoma"),
            Margin = new Thickness(0, 0, 0, isA6Size ? 1 : 2),
            TextAlignment = TextAlignment.Left,
            Foreground = new SolidColorBrush(Color.FromRgb(255, 87, 34))
        };

        var currentPaymentText = new TextBlock
        {
            Text = $"مبلغ هذه الدفعة: {receiptData.PaymentAmountDisplay}",
            FontSize = isA6Size ? 8 : 9,
            FontFamily = new FontFamily("Tahoma"),
            Margin = new Thickness(0, 0, 0, isA6Size ? 1 : 2),
            TextAlignment = TextAlignment.Left,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush(Color.FromRgb(76, 175, 80))
        };

        var remainingAfterText = new TextBlock
        {
            Text = $"المبلغ المتبقي بعد هذه الدفعة: {receiptData.RemainingAfterDisplay}",
            FontSize = isA6Size ? 8 : 9,
            FontFamily = new FontFamily("Tahoma"),
            Margin = new Thickness(0, 0, 0, isA6Size ? 1 : 2),
            TextAlignment = TextAlignment.Left,
            Foreground = new SolidColorBrush(Color.FromRgb(33, 150, 243))
        };

        paymentDetailsStack.Children.Add(paymentDetailsTitle);
        paymentDetailsStack.Children.Add(taxYearText);
        paymentDetailsStack.Children.Add(sequenceText);
        paymentDetailsStack.Children.Add(remainingBeforeText);
        paymentDetailsStack.Children.Add(currentPaymentText);
        paymentDetailsStack.Children.Add(remainingAfterText);
        paymentDetailsBorder.Child = paymentDetailsStack;

        // Amount in Words
        var amountInWordsBorder = new Border
        {
            Background = new SolidColorBrush(Color.FromRgb(255, 248, 225)),
            BorderBrush = new SolidColorBrush(Color.FromRgb(255, 193, 7)),
            BorderThickness = new Thickness(1),
            CornerRadius = new CornerRadius(4),
            Padding = new Thickness(isA6Size ? 6 : 10),
            Margin = new Thickness(0, 0, 0, isA6Size ? 6 : 10)
        };

        var amountInWordsText = new TextBlock
        {
            Text = $"المبلغ بالحروف: {receiptData.PaymentAmountInWords}",
            FontSize = isA6Size ? 8 : 9,
            FontFamily = new FontFamily("Tahoma"),
            FontWeight = FontWeights.Bold,
            TextWrapping = TextWrapping.Wrap,
            TextAlignment = TextAlignment.Center,
            Foreground = new SolidColorBrush(Color.FromRgb(51, 51, 51))
        };

        amountInWordsBorder.Child = amountInWordsText;

        // Payment Status
        var statusBorder = new Border
        {
            Background = receiptData.RemainingAmountAfterPayment == 0 ?
                new SolidColorBrush(Color.FromRgb(232, 245, 233)) :
                new SolidColorBrush(Color.FromRgb(255, 243, 224)),
            BorderBrush = receiptData.RemainingAmountAfterPayment == 0 ?
                new SolidColorBrush(Color.FromRgb(76, 175, 80)) :
                new SolidColorBrush(Color.FromRgb(255, 152, 0)),
            BorderThickness = new Thickness(1),
            CornerRadius = new CornerRadius(4),
            Padding = new Thickness(isA6Size ? 6 : 10),
            Margin = new Thickness(0, 0, 0, isA6Size ? 6 : 10)
        };

        var statusText = new TextBlock
        {
            Text = receiptData.PaymentStatusText,
            FontSize = isA6Size ? 8 : 9,
            FontFamily = new FontFamily("Tahoma"),
            FontWeight = FontWeights.Bold,
            TextAlignment = TextAlignment.Center,
            Foreground = receiptData.RemainingAmountAfterPayment == 0 ?
                new SolidColorBrush(Color.FromRgb(27, 94, 32)) :
                new SolidColorBrush(Color.FromRgb(230, 81, 0))
        };

        statusBorder.Child = statusText;

        // Legal Notice with Deadline Warning
        var legalNoticeBorder = new Border
        {
            Background = new SolidColorBrush(Color.FromRgb(248, 248, 248)),
            BorderBrush = new SolidColorBrush(Color.FromRgb(200, 200, 200)),
            BorderThickness = new Thickness(1),
            CornerRadius = new CornerRadius(3),
            Padding = new Thickness(isA6Size ? 6 : 8),
            Margin = new Thickness(0, isA6Size ? 6 : 10, 0, 0)
        };

        var legalNoticeStack = new StackPanel();

        var legalNoticeText = new TextBlock
        {
            Text = "هذا الوصل يثبت دفع ضريبة النظافة للسنة المحددة",
            FontSize = isA6Size ? 7 : 8,
            FontFamily = new FontFamily("Segoe UI"),
            TextAlignment = TextAlignment.Center,
            Margin = new Thickness(0, 0, 0, isA6Size ? 2 : 3),
            Foreground = new SolidColorBrush(Color.FromRgb(100, 100, 100))
        };

        var deadlineWarning = new TextBlock
        {
            Text = "⚠️ تنبيه: آخر أجل لدفع ضريبة النظافة هو 31 ماي من كل سنة",
            FontSize = isA6Size ? 8 : 9,
            FontFamily = new FontFamily("Segoe UI"),
            FontWeight = FontWeights.Bold,
            TextAlignment = TextAlignment.Center,
            Foreground = new SolidColorBrush(Color.FromRgb(211, 47, 47)), // أحمر للتنبيه
            TextWrapping = TextWrapping.Wrap
        };

        legalNoticeStack.Children.Add(legalNoticeText);
        legalNoticeStack.Children.Add(deadlineWarning);
        legalNoticeBorder.Child = legalNoticeStack;

        // Add all elements to main stack
        mainStack.Children.Add(headerStack);
        mainStack.Children.Add(tenantPropertyBorder);
        mainStack.Children.Add(paymentDetailsBorder);
        mainStack.Children.Add(amountInWordsBorder);
        mainStack.Children.Add(statusBorder);
        mainStack.Children.Add(legalNoticeBorder);

        border.Child = mainStack;
        return border;
    }

    /// <summary>
    /// حفظ الوصل كملف PDF
    /// </summary>
    /// <param name="receiptData">بيانات الوصل</param>
    /// <param name="filePath">مسار الملف</param>
    public async Task SaveReceiptAsPdfAsync(CleaningTaxReceiptData receiptData, string filePath)
    {
        await Task.Run(() =>
        {
            try
            {
                // Create the receipt visual
                var receiptVisual = CreateReceiptVisualFromData(receiptData, true);

                // Create print dialog for configuration
                var printDialog = new PrintDialog();

                // Set up for A6 size
                printDialog.PrintTicket.PageMediaSize = new PageMediaSize(PageMediaSizeName.ISOA6);

                // Create XPS document
                var xpsPath = filePath.Replace(".pdf", ".xps");
                var package = Package.Open(xpsPath, FileMode.Create);
                var xpsDocument = new XpsDocument(package);
                var writer = XpsDocument.CreateXpsDocumentWriter(xpsDocument);

                // Print to XPS
                writer.Write(receiptVisual);

                xpsDocument.Close();
                package.Close();

                // For now, we'll save as XPS and rename to PDF
                // In a real implementation, you would use a PDF library like iTextSharp
                if (File.Exists(xpsPath))
                {
                    if (File.Exists(filePath))
                        File.Delete(filePath);
                    File.Move(xpsPath, filePath);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ الوصل كـ PDF: {ex.Message}");
            }
        });
    }
}
