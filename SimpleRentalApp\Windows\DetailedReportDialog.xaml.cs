using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using SimpleRentalApp.Models;
using SimpleRentalApp.Services;
using Microsoft.Win32;
using System.IO;
using System.Text.Json;

namespace SimpleRentalApp.Windows
{
    public partial class DetailedReportDialog : Window
    {
        private readonly Property _property;
        private readonly List<PaymentDisplayInfo> _allPayments;
        private readonly int _selectedYear;

        public DetailedReportDialog(Property property, List<PaymentDisplayInfo> allPayments, int selectedYear)
        {
            InitializeComponent();
            _property = property;
            _allPayments = allPayments;
            _selectedYear = selectedYear;
            
            LoadReportData();
        }

        private async void LoadReportData()
        {
            try
            {
                // Update header
                ReportTitleText.Text = $"التقرير المفصل - {_property.Name}";
                ReportSubtitleText.Text = $"تقرير شامل لضريبة النظافة للسنة {_selectedYear}";

                // Load property summary
                await LoadPropertySummary();
                
                // Load financial summary
                await LoadFinancialSummary();
                
                // Load payment statistics
                LoadPaymentStatistics();
                
                // Load payment timeline
                LoadPaymentTimeline();
                
                // Generate recommendations
                GenerateRecommendations();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadPropertySummary()
        {
            PropertyNameText.Text = _property.Name;
            PropertyAddressText.Text = _property.Address;
            MonthlyRentText.Text = $"{_property.MonthlyRent:N0} درهم";
            ReportDateText.Text = DateTime.Now.ToString("dd/MM/yyyy");
            SelectedYearText.Text = _selectedYear.ToString();

            // Load customer info
            if (_property.CustomerId.HasValue)
            {
                var customer = await DatabaseService.Instance.GetCustomerByIdAsync(_property.CustomerId.Value);
                CustomerNameText.Text = customer?.FullName ?? "غير محدد";
            }
            else
            {
                CustomerNameText.Text = "غير محدد";
            }
        }

        private async Task LoadFinancialSummary()
        {
            var calculationService = new CleaningTaxCalculationService();
            var status = await calculationService.GetPaymentStatusAsync(_property.Id, _selectedYear);
            
            AnnualTaxText.Text = $"{status.AnnualTax:N0} درهم";
            PaidAmountText.Text = $"{status.TotalPaid:N0} درهم";
            RemainingAmountText.Text = $"{status.RemainingAmount:N0} درهم";
            
            var completionPercentage = status.AnnualTax > 0 ? (status.TotalPaid / status.AnnualTax) * 100 : 0;
            CompletionPercentageText.Text = $"{completionPercentage:F1}%";
        }

        private void LoadPaymentStatistics()
        {
            var yearPayments = _allPayments.Where(p => p.Payment.PaymentDate?.Year == _selectedYear).ToList();
            
            if (yearPayments.Any())
            {
                PaymentCountText.Text = $"{yearPayments.Count} دفعة";
                
                var averagePayment = yearPayments.Average(p => p.Payment.CleaningTaxAmount);
                AveragePaymentText.Text = $"{averagePayment:N0} درهم";
                
                var largestPayment = yearPayments.Max(p => p.Payment.CleaningTaxAmount);
                LargestPaymentText.Text = $"{largestPayment:N0} درهم";
                
                var smallestPayment = yearPayments.Min(p => p.Payment.CleaningTaxAmount);
                SmallestPaymentText.Text = $"{smallestPayment:N0} درهم";
                
                var firstPayment = yearPayments.OrderBy(p => p.Payment.PaymentDate).FirstOrDefault();
                FirstPaymentText.Text = firstPayment?.PaymentDateDisplay ?? "لا توجد دفعات";
                
                var lastPayment = yearPayments.OrderByDescending(p => p.Payment.PaymentDate).FirstOrDefault();
                LastPaymentText.Text = lastPayment?.PaymentDateDisplay ?? "لا توجد دفعات";
            }
            else
            {
                PaymentCountText.Text = "0 دفعة";
                AveragePaymentText.Text = "0 درهم";
                LargestPaymentText.Text = "0 درهم";
                SmallestPaymentText.Text = "0 درهم";
                FirstPaymentText.Text = "لا توجد دفعات";
                LastPaymentText.Text = "لا توجد دفعات";
            }
        }

        private void LoadPaymentTimeline()
        {
            var yearPayments = _allPayments
                .Where(p => p.Payment.PaymentDate?.Year == _selectedYear)
                .OrderBy(p => p.Payment.PaymentDate)
                .ToList();
            
            PaymentTimelineDataGrid.ItemsSource = yearPayments;
        }

        private void GenerateRecommendations()
        {
            var recommendations = new List<string>();
            var yearPayments = _allPayments.Where(p => p.Payment.PaymentDate?.Year == _selectedYear).ToList();
            var annualTax = _property.MonthlyRent * 12 * 0.105m;
            var totalPaid = yearPayments.Sum(p => p.Payment.CleaningTaxAmount);
            var remainingAmount = annualTax - totalPaid;
            
            // Check payment status
            if (remainingAmount <= 0)
            {
                recommendations.Add("✅ تم دفع ضريبة النظافة بالكامل للسنة المحددة.");
            }
            else
            {
                var deadline = new DateTime(_selectedYear, 5, 31);
                if (DateTime.Now > deadline)
                {
                    recommendations.Add($"⚠️ تحذير: تأخر في دفع ضريبة النظافة. المبلغ المتبقي: {remainingAmount:N0} درهم");
                    recommendations.Add($"📅 الموعد النهائي كان: {deadline:dd/MM/yyyy}");
                }
                else
                {
                    var daysRemaining = (deadline - DateTime.Now).Days;
                    if (daysRemaining <= 30)
                    {
                        recommendations.Add($"⏰ تنبيه: يتبقى {daysRemaining} يوم على الموعد النهائي لدفع ضريبة النظافة");
                    }
                    recommendations.Add($"💰 المبلغ المتبقي: {remainingAmount:N0} درهم");
                }
            }

            // Payment pattern analysis
            if (yearPayments.Count > 1)
            {
                var paymentIntervals = new List<int>();
                for (int i = 1; i < yearPayments.Count; i++)
                {
                    var interval = (yearPayments[i].Payment.PaymentDate.Value - yearPayments[i-1].Payment.PaymentDate.Value).Days;
                    paymentIntervals.Add(interval);
                }
                
                var averageInterval = paymentIntervals.Average();
                if (averageInterval <= 35)
                {
                    recommendations.Add("📈 نمط دفع منتظم: يتم الدفع بانتظام شهرياً تقريباً");
                }
                else if (averageInterval <= 95)
                {
                    recommendations.Add("📊 نمط دفع ربع سنوي: يتم الدفع كل 3 أشهر تقريباً");
                }
                else
                {
                    recommendations.Add("📉 نمط دفع غير منتظم: فترات متباعدة بين الدفعات");
                }
            }

            // Amount analysis
            if (yearPayments.Any())
            {
                var amounts = yearPayments.Select(p => p.Payment.CleaningTaxAmount).ToList();
                var variance = amounts.Max() - amounts.Min();
                
                if (variance < 100)
                {
                    recommendations.Add("💯 ثبات في المبالغ: مبالغ الدفعات متقاربة ومنتظمة");
                }
                else
                {
                    recommendations.Add("📊 تنوع في المبالغ: مبالغ الدفعات متفاوتة");
                }
            }

            // Future recommendations
            if (remainingAmount > 0)
            {
                var monthsRemaining = Math.Max(1, (new DateTime(_selectedYear, 5, 31) - DateTime.Now).Days / 30);
                var suggestedMonthlyAmount = remainingAmount / monthsRemaining;
                recommendations.Add($"💡 اقتراح: دفع {suggestedMonthlyAmount:N0} درهم شهرياً لتغطية المبلغ المتبقي");
            }

            RecommendationsText.Text = string.Join("\n\n", recommendations);
        }

        private void PrintReportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    printDialog.PrintVisual(ReportContentPanel, $"تقرير ضريبة النظافة - {_property.Name}");
                    MessageBox.Show("تم طباعة التقرير بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportReportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "JSON files (*.json)|*.json|All files (*.*)|*.*",
                    FileName = $"تقرير_ضريبة_النظافة_{_property.Name}_{_selectedYear}.json"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    var reportData = new
                    {
                        Property = _property,
                        Year = _selectedYear,
                        Payments = _allPayments.Where(p => p.Payment.PaymentDate?.Year == _selectedYear),
                        GeneratedDate = DateTime.Now,
                        Summary = new
                        {
                            AnnualTax = AnnualTaxText.Text,
                            PaidAmount = PaidAmountText.Text,
                            RemainingAmount = RemainingAmountText.Text,
                            CompletionPercentage = CompletionPercentageText.Text
                        },
                        Statistics = new
                        {
                            PaymentCount = PaymentCountText.Text,
                            AveragePayment = AveragePaymentText.Text,
                            LargestPayment = LargestPaymentText.Text,
                            SmallestPayment = SmallestPaymentText.Text,
                            FirstPayment = FirstPaymentText.Text,
                            LastPayment = LastPaymentText.Text
                        },
                        Recommendations = RecommendationsText.Text
                    };

                    var json = JsonSerializer.Serialize(reportData, new JsonSerializerOptions 
                    { 
                        WriteIndented = true,
                        Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                    });
                    
                    File.WriteAllText(saveDialog.FileName, json);
                    MessageBox.Show("تم تصدير التقرير بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
