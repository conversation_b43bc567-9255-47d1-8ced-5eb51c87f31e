# إنشاء أيقونة بسيطة لتطبيق تدبير الكراء
# Create simple icon for Rental Management Application

Add-Type -AssemblyName System.Drawing

Write-Host "Creating simple application icon..." -ForegroundColor Green

# Create a 256x256 bitmap
$size = 256
$bitmap = New-Object System.Drawing.Bitmap($size, $size)
$graphics = [System.Drawing.Graphics]::FromImage($bitmap)

# Set high quality rendering
$graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
$graphics.TextRenderingHint = [System.Drawing.Text.TextRenderingHint]::AntiAlias

# Create gradient background
$brush = New-Object System.Drawing.Drawing2D.LinearGradientBrush(
    (New-Object System.Drawing.Point(0, 0)),
    (New-Object System.Drawing.Point($size, $size)),
    [System.Drawing.Color]::FromArgb(52, 152, 219),  # Blue
    [System.Drawing.Color]::FromArgb(41, 128, 185)   # Darker Blue
)

# Fill background
$graphics.FillRectangle($brush, 0, 0, $size, $size)

# Create building shape
$whiteBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)

# Main building
$building = New-Object System.Drawing.Rectangle(80, 100, 96, 80)
$graphics.FillRectangle($whiteBrush, $building)

# Roof
$roofPoints = @(
    (New-Object System.Drawing.Point(128, 60)),   # Top
    (New-Object System.Drawing.Point(70, 100)),   # Left
    (New-Object System.Drawing.Point(186, 100))   # Right
)
$graphics.FillPolygon($whiteBrush, $roofPoints)

# Windows
$windowBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(52, 152, 219))
$window1 = New-Object System.Drawing.Rectangle(95, 120, 20, 20)
$window2 = New-Object System.Drawing.Rectangle(140, 120, 20, 20)
$window3 = New-Object System.Drawing.Rectangle(95, 150, 20, 20)
$window4 = New-Object System.Drawing.Rectangle(140, 150, 20, 20)

$graphics.FillRectangle($windowBrush, $window1)
$graphics.FillRectangle($windowBrush, $window2)
$graphics.FillRectangle($windowBrush, $window3)
$graphics.FillRectangle($windowBrush, $window4)

# Door
$door = New-Object System.Drawing.Rectangle(118, 160, 20, 20)
$graphics.FillRectangle($windowBrush, $door)

# Add text
$font = New-Object System.Drawing.Font("Arial", 20, [System.Drawing.FontStyle]::Bold)
$textBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
$text = "RENT"
$textSize = $graphics.MeasureString($text, $font)
$textX = ($size - $textSize.Width) / 2
$textY = 200
$graphics.DrawString($text, $font, $textBrush, $textX, $textY)

# Clean up graphics
$graphics.Dispose()

# Save as PNG
$pngPath = "rental_icon.png"
$bitmap.Save($pngPath, [System.Drawing.Imaging.ImageFormat]::Png)
Write-Host "PNG icon created: $pngPath" -ForegroundColor Cyan

# Try to create ICO
try {
    $icoPath = "rental_icon.ico"
    $bitmap.Save($icoPath, [System.Drawing.Imaging.ImageFormat]::Icon)
    Write-Host "ICO icon created: $icoPath" -ForegroundColor Cyan
}
catch {
    Write-Host "Could not create ICO directly. PNG created successfully." -ForegroundColor Yellow
}

# Clean up
$bitmap.Dispose()
$brush.Dispose()
$whiteBrush.Dispose()
$windowBrush.Dispose()
$font.Dispose()
$textBrush.Dispose()

Write-Host "Icon creation completed!" -ForegroundColor Green
