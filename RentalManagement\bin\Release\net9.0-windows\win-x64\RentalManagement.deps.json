{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {}, ".NETCoreApp,Version=v9.0/win-x64": {"RentalManagement/1.0.0": {"dependencies": {"MaterialDesignThemes": "5.2.1", "Microsoft.EntityFrameworkCore.Sqlite": "9.0.6", "iTextSharp": "********", "runtimepack.Microsoft.NETCore.App.Runtime.win-x64": "9.0.6", "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64": "9.0.6"}, "runtime": {"RentalManagement.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/9.0.6": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "1*******", "fileVersion": "14.0.625.26613"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "9.0.625.26613"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Formats.Tar.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.InteropServices.JavaScript.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "netstandard.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "native": {"Microsoft.DiaSymReader.Native.amd64.dll": {"fileVersion": "14.42.34436.0"}, "System.IO.Compression.Native.dll": {"fileVersion": "9.0.625.26613"}, "clretwrc.dll": {"fileVersion": "9.0.625.26613"}, "clrgc.dll": {"fileVersion": "9.0.625.26613"}, "clrgcexp.dll": {"fileVersion": "9.0.625.26613"}, "clrjit.dll": {"fileVersion": "9.0.625.26613"}, "coreclr.dll": {"fileVersion": "9.0.625.26613"}, "createdump.exe": {"fileVersion": "9.0.625.26613"}, "hostfxr.dll": {"fileVersion": "9.0.625.26613"}, "hostpolicy.dll": {"fileVersion": "9.0.625.26613"}, "mscordaccore.dll": {"fileVersion": "9.0.625.26613"}, "mscordaccore_amd64_amd64_9.0.625.26613.dll": {"fileVersion": "9.0.625.26613"}, "mscordbi.dll": {"fileVersion": "9.0.625.26613"}, "mscorrc.dll": {"fileVersion": "9.0.625.26613"}, "msquic.dll": {"fileVersion": "*******"}}}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/9.0.6": {"runtime": {"Accessibility.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26605"}, "DirectWriteForwarder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}, "Microsoft.Win32.Registry.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "PresentationCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}, "PresentationFramework-SystemCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}, "PresentationFramework-SystemData.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}, "PresentationFramework-SystemDrawing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}, "PresentationFramework-SystemXml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}, "PresentationFramework-SystemXmlLinq.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}, "PresentationFramework.Aero.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}, "PresentationFramework.Aero2.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}, "PresentationFramework.AeroLite.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}, "PresentationFramework.Classic.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}, "PresentationFramework.Fluent.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}, "PresentationFramework.Luna.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}, "PresentationFramework.Royale.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}, "PresentationFramework.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}, "PresentationUI.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}, "ReachFramework.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}, "System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Diagnostics.EventLog.Messages.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Formats.Nrbf.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Printing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}, "System.Resources.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Threading.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Windows.Controls.Ribbon.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}, "System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}, "System.Windows.Input.Manipulations.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}, "System.Windows.Presentation.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}, "System.Xaml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}, "UIAutomationClient.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}, "UIAutomationClientSideProviders.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}, "UIAutomationProvider.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}, "UIAutomationTypes.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}, "native": {"D3DCompiler_47_cor3.dll": {"fileVersion": "10.0.22621.3233"}, "PenImc_cor3.dll": {"fileVersion": "9.0.625.26701"}, "PresentationNative_cor3.dll": {"fileVersion": "9.0.25.16803"}, "vcruntime140_cor3.dll": {"fileVersion": "14.44.34918.1"}, "wpfgfx_cor3.dll": {"fileVersion": "9.0.625.26701"}}}, "BouncyCastle.Cryptography/2.4.0": {"runtime": {"lib/net6.0/BouncyCastle.Cryptography.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.4.0.33771"}}}, "iTextSharp/********": {"dependencies": {"BouncyCastle.Cryptography": "2.4.0"}, "runtime": {"lib/net461/itextsharp.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MaterialDesignColors/5.2.1": {"runtime": {"lib/net8.0/MaterialDesignColors.dll": {"assemblyVersion": "5.2.1.0", "fileVersion": "5.2.1.0"}}}, "MaterialDesignThemes/5.2.1": {"dependencies": {"MaterialDesignColors": "5.2.1", "Microsoft.Xaml.Behaviors.Wpf": "1.1.39"}, "runtime": {"lib/net8.0/MaterialDesignThemes.Wpf.dll": {"assemblyVersion": "5.2.1.0", "fileVersion": "5.2.1.0"}}}, "Microsoft.Data.Sqlite.Core/9.0.6": {"dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "runtime": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26607"}}}, "Microsoft.EntityFrameworkCore/9.0.6": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.6", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26607"}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.6": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26607"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.6": {}, "Microsoft.EntityFrameworkCore.Relational/9.0.6": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26607"}}}, "Microsoft.EntityFrameworkCore.Sqlite/9.0.6": {"dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.10", "SQLitePCLRaw.core": "2.1.10", "System.Text.Json": "9.0.6"}}, "Microsoft.EntityFrameworkCore.Sqlite.Core/9.0.6": {"dependencies": {"Microsoft.Data.Sqlite.Core": "9.0.6", "Microsoft.EntityFrameworkCore.Relational": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "SQLitePCLRaw.core": "2.1.10", "System.Text.Json": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26607"}}}, "Microsoft.Extensions.Caching.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Caching.Memory/9.0.6": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyModel/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Options/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Primitives/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"runtime": {"lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.39.4716"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.10", "SQLitePCLRaw.provider.e_sqlite3": "2.1.10"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "SQLitePCLRaw.core/2.1.10": {"dependencies": {"System.Memory": "4.5.3"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"native": {"runtimes/win-x64/native/e_sqlite3.dll": {"fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "runtime": {"lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "System.Memory/4.5.3": {}, "System.Text.Json/9.0.6": {}}}, "libraries": {"RentalManagement/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/9.0.6": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/9.0.6": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "BouncyCastle.Cryptography/2.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-SwXsAV3sMvAU/Nn31pbjhWurYSjJ+/giI/0n6tCrYoupEK34iIHCuk3STAd9fx8yudM85KkLSVdn951vTng/vQ==", "path": "bouncycastle.cryptography/2.4.0", "hashPath": "bouncycastle.cryptography.2.4.0.nupkg.sha512"}, "iTextSharp/********": {"type": "package", "serviceable": true, "sha512": "sha512-/cvCNv8AJ+XuD99u4NfHSSxkBJKTdvP36wcfQF9V1Cjzi1ycyFSa4vJ64a3DhFVUTKVO60WF8OmAqngQDTHgYA==", "path": "itextsharp/********", "hashPath": "itextsharp.********.nupkg.sha512"}, "MaterialDesignColors/5.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-D0HW6E2/kzsnEWCh1KDG/K09Fpkvs9mR3n91Y8YSOsEAoQmGZbVAj58ssyAxGTiIPj2zB4ZVnwxkizwO35/v8A==", "path": "materialdesigncolors/5.2.1", "hashPath": "materialdesigncolors.5.2.1.nupkg.sha512"}, "MaterialDesignThemes/5.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-x8JDqNHJcTLLxIoVts3w7AbSq5Zo0FXTw89XqPN7+n0EKqLXFwWsywiUn08HDyTGAmZVJqbQsWKxKWCI8qfWsQ==", "path": "materialdesignthemes/5.2.1", "hashPath": "materialdesignthemes.5.2.1.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-3auiudiViGzj1TidUdjuDqtP3+f6PBk4xdw6r9sBaTtkYoGc3AZn0cP8LgYZaLRnJBqY5bXRLB+qhjoB+iATzA==", "path": "microsoft.data.sqlite.core/9.0.6", "hashPath": "microsoft.data.sqlite.core.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-r5hzM6Bhw4X3z28l5vmsaCPjk9VsQP4zaaY01THh1SAYjgTMVadYIvpNkCfmrv/Klks6aIf2A9eY7cpGZab/hg==", "path": "microsoft.entityframeworkcore/9.0.6", "hashPath": "microsoft.entityframeworkcore.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-7MkhPK8emb8hfOx/mFVvHuIHxQ+mH2YdlK4sFUXgsGlvR0A44vsmd2wcHavZOTTzaKhN+aFUVy3zmkztKmTo+A==", "path": "microsoft.entityframeworkcore.abstractions/9.0.6", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-VKggHNQC5FCn3/vooaIM/4aEjGmrmWm78IrdRLz9lLV0Rm9bVHEr/jiWApDkU0U9ec2xGAilvQqJ5mMX7QC2cw==", "path": "microsoft.entityframeworkcore.analyzers/9.0.6", "hashPath": "microsoft.entityframeworkcore.analyzers.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Ht6OT17sYnO31Dx+hX72YHrc5kZt53g5napaw0FpyIekXCvb+gUVvufEG55Fa7taFm8ccy0Vzs+JVNR9NL0JlA==", "path": "microsoft.entityframeworkcore.relational/9.0.6", "hashPath": "microsoft.entityframeworkcore.relational.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-bVSdfFrqIo3ZeQfWYYfnVVanP1GWghkdw+MnEmZJz7jUwtdPQpBKHr0BW9dMizPamzU+SMA1Qu4nXuRTlKVAGQ==", "path": "microsoft.entityframeworkcore.sqlite/9.0.6", "hashPath": "microsoft.entityframeworkcore.sqlite.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite.Core/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-xP+SvMDR/GZCDNXFw7z4WYbO2sYpECvht3+lqejg+Md8vLtURwTBvdsOUAnY4jBGmNFqHeh87hZSmUGmuxyqMA==", "path": "microsoft.entityframeworkcore.sqlite.core/9.0.6", "hashPath": "microsoft.entityframeworkcore.sqlite.core.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-bL/xQsVNrdVkzjP5yjX4ndkQ03H3+Bk3qPpl+AMCEJR2RkfgAYmoQ/xXffPV7is64+QHShnhA12YAaFmNbfM+A==", "path": "microsoft.extensions.caching.abstractions/9.0.6", "hashPath": "microsoft.extensions.caching.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-qPW2d798tBPZcRmrlaBJqyChf2+0odDdE+0Lxvrr0ywkSNl1oNMK8AKrOfDwyXyjuLCv0ua7p6nrUExCeXhCcg==", "path": "microsoft.extensions.caching.memory/9.0.6", "hashPath": "microsoft.extensions.caching.memory.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-3GgMIi2jP8g1fBW93Z9b9Unamc0SIsgyhiCmC91gq4loTixK9vQMuxxUsfJ1kRGwn+/FqLKwOHqmn0oYWn3Fvw==", "path": "microsoft.extensions.configuration.abstractions/9.0.6", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-vS65HMo5RS10DD543fknsyVDxihMcVxVn3/hNaILgBxWYnOLxWIeCIO9X0QFuCvPRNjClvXe9Aj8KaQNx7vFkQ==", "path": "microsoft.extensions.dependencyinjection/9.0.6", "hashPath": "microsoft.extensions.dependencyinjection.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-0Zn6nR/6g+90MxskZyOOMPQvnPnrrGu6bytPwkV+azDcTtCSuQ1+GJUrg8Klmnrjk1i6zMpw2lXijl+tw7Q3kA==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.6", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-grVU1ixgMHp+kuhIgvEzhE73jXRY6XmxNBPWrotmbjB9AvJvkwHnIzm1JlOsPpyixFgnzreh/bFBMJAjveX+fQ==", "path": "microsoft.extensions.dependencymodel/9.0.6", "hashPath": "microsoft.extensions.dependencymodel.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-XBzjitTFaQhF8EbJ645vblZezV1p52ePTxKHoVkRidHF11Xkjxg94qr0Rvp2qyxK2vBJ4OIZ41NB15YUyxTGMQ==", "path": "microsoft.extensions.logging/9.0.6", "hashPath": "microsoft.extensions.logging.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-LFnyBNK7WtFmKdnHu3v0HOYQ8BcjYuy0jdC9pgCJ/rbLKoJEG9/dBzSKMEeeWDbDeoWS0TIxOC8a9CM5ufca3A==", "path": "microsoft.extensions.logging.abstractions/9.0.6", "hashPath": "microsoft.extensions.logging.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-wUPhNM1zsI58Dy10xRdF2+pnsisiUuETg5ZBncyAEEUm/CQ9Q1vmivyUWH8RDbAlqyixf2dJNQ2XZb7HsKUEQw==", "path": "microsoft.extensions.options/9.0.6", "hashPath": "microsoft.extensions.options.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-BHniU24QV67qp1pJknqYSofAPYGmijGI8D+ci9yfw33iuFdyOeB9lWTg78ThyYLyQwZw3s0vZ36VMb0MqbUuLw==", "path": "microsoft.extensions.primitives/9.0.6", "hashPath": "microsoft.extensions.primitives.9.0.6.nupkg.sha512"}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"type": "package", "serviceable": true, "sha512": "sha512-8PZKqw9QOcu42xk8puY4P1+EXHL9YGOR9b7qhaYx5cILHul456H073tj99vyPcCt0W0781T9RwHqkx507ZyUpQ==", "path": "microsoft.xaml.behaviors.wpf/1.1.39", "hashPath": "microsoft.xaml.behaviors.wpf.1.1.39.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-UxWuisvZ3uVcVOLJQv7urM/JiQH+v3TmaJc1BLKl5Dxfm/nTzTUrqswCqg/INiYLi61AXnHo1M1JPmPqqLnAdg==", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.core/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-Ii8JCbC7oiVclaE/mbDEK000EFIJ+ShRPwAvvV89GOZhQ+ZLtlnSWl6ksCNMKu/VGXA4Nfi2B7LhN/QFN9oBcw==", "path": "sqlitepclraw.core/2.1.10", "hashPath": "sqlitepclraw.core.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-mAr69tDbnf3QJpRy2nJz8Qdpebdil00fvycyByR58Cn9eARvR+UiG2Vzsp+4q1tV3ikwiYIjlXCQFc12GfebbA==", "path": "sqlitepclraw.lib.e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-uZVTi02C1SxqzgT0HqTWatIbWGb40iIkfc3FpFCpE/r7g6K0PqzDUeefL6P6HPhDtc6BacN3yQysfzP7ks+wSQ==", "path": "sqlitepclraw.provider.e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.provider.e_sqlite3.2.1.10.nupkg.sha512"}, "System.Memory/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "path": "system.memory/4.5.3", "hashPath": "system.memory.4.5.3.nupkg.sha512"}, "System.Text.Json/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-h+ZtYTyTnTh5Ju6mHCKb3FPGx4ylJZgm9W7Y2psUnkhQRPMOIxX+TCN0ZgaR/+Yea+93XHWAaMzYTar1/EHIPg==", "path": "system.text.json/9.0.6", "hashPath": "system.text.json.9.0.6.nupkg.sha512"}}, "runtimes": {"win-x64": ["win", "any", "base"]}}