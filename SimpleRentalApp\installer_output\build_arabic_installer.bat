@echo off
chcp 65001 > nul
echo ========================================
echo    إنشاء ملف التثبيت العربي
echo    Building Arabic Installer
echo ========================================
echo.

echo [1/4] التحقق من وجود NSIS...
echo [1/4] Checking for NSIS...

set "NSIS_PATH="
if exist "%ProgramFiles%\NSIS\makensis.exe" (
    set "NSIS_PATH=%ProgramFiles%\NSIS\makensis.exe"
) else if exist "%ProgramFiles(x86)%\NSIS\makensis.exe" (
    set "NSIS_PATH=%ProgramFiles(x86)%\NSIS\makensis.exe"
) else (
    echo خطأ: لم يتم العثور على NSIS
    echo Error: NSIS not found
    echo.
    echo يرجى تحميل وتثبيت NSIS من:
    echo Please download and install NSIS from:
    echo https://nsis.sourceforge.io/Download
    echo.
    pause
    exit /b 1
)

echo تم العثور على NSIS في: %NSIS_PATH%
echo Found NSIS at: %NSIS_PATH%
echo.

echo [2/4] التحقق من الملفات المطلوبة...
echo [2/4] Checking required files...

if not exist "app\SimpleRentalApp.exe" (
    echo خطأ: ملف التطبيق غير موجود
    echo Error: Application file not found
    echo المسار المطلوب: app\SimpleRentalApp.exe
    echo Required path: app\SimpleRentalApp.exe
    pause
    exit /b 1
)

if not exist "README_Arabic.txt" (
    echo خطأ: ملف README العربي غير موجود
    echo Error: Arabic README file not found
    pause
    exit /b 1
)

if not exist "installer_arabic.nsi" (
    echo خطأ: ملف NSIS العربي غير موجود
    echo Error: Arabic NSIS script not found
    pause
    exit /b 1
)

echo جميع الملفات المطلوبة موجودة
echo All required files found
echo.

echo [3/4] بناء ملف التثبيت...
echo [3/4] Building installer...

"%NSIS_PATH%" installer_arabic.nsi

if errorlevel 1 (
    echo خطأ في بناء ملف التثبيت
    echo Error building installer
    pause
    exit /b 1
)

echo [4/4] التحقق من ملف التثبيت المنشأ...
echo [4/4] Verifying created installer...

if exist "تثبيت_نظام_تدبير_الكراء.exe" (
    echo ========================================
    echo تم إنشاء ملف التثبيت بنجاح!
    echo Installer created successfully!
    echo.
    echo اسم الملف: تثبيت_نظام_تدبير_الكراء.exe
    echo File name: تثبيت_نظام_تدبير_الكراء.exe
    echo.
    echo حجم الملف:
    echo File size:
    for %%A in ("تثبيت_نظام_تدبير_الكراء.exe") do echo %%~zA bytes
    echo ========================================
    echo.
    echo الملف جاهز للتوزيع!
    echo File ready for distribution!
) else (
    echo خطأ: لم يتم إنشاء ملف التثبيت
    echo Error: Installer file was not created
    pause
    exit /b 1
)

echo.
echo ملاحظات مهمة:
echo Important notes:
echo.
echo 1. تأكد من اختبار ملف التثبيت قبل التوزيع
echo    Test the installer before distribution
echo.
echo 2. ملف التثبيت يدعم اللغة العربية بالكامل
echo    The installer fully supports Arabic language
echo.
echo 3. سيتم إنشاء اختصارات باللغة العربية
echo    Arabic shortcuts will be created
echo.
pause
