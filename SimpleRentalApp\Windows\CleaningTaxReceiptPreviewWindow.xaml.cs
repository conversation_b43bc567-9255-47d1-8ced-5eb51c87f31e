using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using SimpleRentalApp.Models;
using SimpleRentalApp.Services;

namespace SimpleRentalApp.Windows
{
    public partial class CleaningTaxReceiptPreviewWindow : Window
    {
        private readonly Payment _payment;
        private CleaningTaxReceiptData? _receiptData;
        private FrameworkElement? _receiptVisual;

        public CleaningTaxReceiptPreviewWindow(Payment payment)
        {
            InitializeComponent();
            _payment = payment;
            LoadReceiptPreview();

            // Initialize zoom level
            _currentZoom = 1.0;
        }

        private double _currentZoom = 1.0;

        private async void LoadReceiptPreview()
        {
            try
            {
                // إنشاء بيانات الوصل مع الحساب التدريجي
                var calculationService = new CleaningTaxCalculationService();
                _receiptData = await calculationService.CreateReceiptDataAsync(_payment);

                // إنشاء العنصر البصري للوصل
                var printService = CleaningTaxPrintService.Instance;
                _receiptVisual = await CreatePreviewReceiptVisual(_receiptData);

                // عرض الوصل في المعاينة
                ReceiptContentPresenter.Content = _receiptVisual;

                // تحديث عنوان النافذة
                Title = $"معاينة وصل ضريبة النظافة - {_receiptData.ReceiptNumber}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل معاينة الوصل: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                Close();
            }
        }

        private async Task<FrameworkElement> CreatePreviewReceiptVisual(CleaningTaxReceiptData receiptData)
        {
            // إنشاء وصل محسن للمعاينة والطباعة
            var border = new Border
            {
                Width = 413,  // A6 width in 1/96 inch
                Height = 583, // A6 height in 1/96 inch
                Background = System.Windows.Media.Brushes.White,
                BorderBrush = System.Windows.Media.Brushes.LightGray,
                BorderThickness = new Thickness(1),
                Padding = new Thickness(15),
                FlowDirection = FlowDirection.RightToLeft
            };

            var mainStack = new StackPanel
            {
                Orientation = Orientation.Vertical,
                HorizontalAlignment = HorizontalAlignment.Stretch
            };

            // Header with Logo Area
            var headerStack = new StackPanel
            {
                Orientation = Orientation.Vertical,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 15)
            };

            // تم حذف اللوغو حسب الطلب

            var titleText = new TextBlock
            {
                Text = "وصل دفع ضريبة النظافة",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                FontFamily = new System.Windows.Media.FontFamily("Segoe UI"),
                TextAlignment = TextAlignment.Center,
                Foreground = System.Windows.Media.Brushes.DarkBlue
            };

            var receiptNumberText = new TextBlock
            {
                Text = $"رقم الوصل: {receiptData.ReceiptNumber}",
                FontSize = 12,
                FontFamily = new System.Windows.Media.FontFamily("Segoe UI"),
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 5, 0, 0),
                Foreground = System.Windows.Media.Brushes.Gray
            };

            var dateText = new TextBlock
            {
                Text = $"التاريخ: {receiptData.PaymentDateDisplay}",
                FontSize = 12,
                FontFamily = new System.Windows.Media.FontFamily("Segoe UI"),
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 2, 0, 0),
                Foreground = System.Windows.Media.Brushes.Gray
            };

            headerStack.Children.Add(titleText);
            headerStack.Children.Add(receiptNumberText);
            headerStack.Children.Add(dateText);

            // Tenant and Property Info
            var tenantPropertyBorder = CreateInfoSection("الكاري والمحل", new[]
            {
                $"الكاري: {receiptData.CustomerName}",
                $"المحل: {receiptData.PropertyName}",
                $"العنوان: {receiptData.PropertyAddress}"
            });

            // Payment Details with Progressive Calculation
            var paymentDetailsBorder = CreateInfoSection("تفاصيل الدفعة", new[]
            {
                $"السنة الضريبية: {receiptData.TaxYear}",
                $"الدفعة رقم: {receiptData.PaymentSequence}",
                $"المبلغ المتبقي قبل هذه الدفعة: {receiptData.RemainingBeforeDisplay}",
                $"مبلغ هذه الدفعة: {receiptData.PaymentAmountDisplay}",
                $"المبلغ المتبقي بعد هذه الدفعة: {receiptData.RemainingAfterDisplay}"
            });

            // Amount in Words
            var amountInWordsBorder = new Border
            {
                Background = System.Windows.Media.Brushes.LightYellow,
                BorderBrush = System.Windows.Media.Brushes.Orange,
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(4),
                Padding = new Thickness(10),
                Margin = new Thickness(0, 0, 0, 10)
            };

            var amountInWordsText = new TextBlock
            {
                Text = $"المبلغ بالحروف: {receiptData.PaymentAmountInWords}",
                FontSize = 12,
                FontFamily = new System.Windows.Media.FontFamily("Segoe UI"),
                FontWeight = FontWeights.Bold,
                TextWrapping = TextWrapping.Wrap,
                TextAlignment = TextAlignment.Center,
                Foreground = System.Windows.Media.Brushes.DarkOrange
            };

            amountInWordsBorder.Child = amountInWordsText;

            // Payment Status
            var statusBorder = new Border
            {
                Background = receiptData.RemainingAmountAfterPayment == 0 ? 
                    System.Windows.Media.Brushes.LightGreen : 
                    System.Windows.Media.Brushes.LightCoral,
                BorderBrush = receiptData.RemainingAmountAfterPayment == 0 ? 
                    System.Windows.Media.Brushes.Green : 
                    System.Windows.Media.Brushes.Red,
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(4),
                Padding = new Thickness(10),
                Margin = new Thickness(0, 0, 0, 10)
            };

            var statusText = new TextBlock
            {
                Text = receiptData.PaymentStatusText,
                FontSize = 12,
                FontFamily = new System.Windows.Media.FontFamily("Segoe UI"),
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center,
                Foreground = System.Windows.Media.Brushes.DarkRed
            };

            statusBorder.Child = statusText;

            // Legal Notice with Deadline Warning
            var legalNoticeBorder = new Border
            {
                Background = System.Windows.Media.Brushes.LightGray,
                BorderBrush = System.Windows.Media.Brushes.Gray,
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(4),
                Padding = new Thickness(10),
                Margin = new Thickness(0, 10, 0, 0)
            };

            var legalNoticeStack = new StackPanel();

            var legalNoticeText = new TextBlock
            {
                Text = "هذا الوصل يثبت دفع ضريبة النظافة للسنة المحددة",
                FontSize = 10,
                FontFamily = new System.Windows.Media.FontFamily("Segoe UI"),
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 5),
                Foreground = System.Windows.Media.Brushes.Gray
            };

            var deadlineWarning = new TextBlock
            {
                Text = "⚠️ تنبيه: آخر أجل لدفع ضريبة النظافة هو 31 ماي من كل سنة",
                FontSize = 11,
                FontFamily = new System.Windows.Media.FontFamily("Segoe UI"),
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center,
                Foreground = System.Windows.Media.Brushes.DarkRed,
                TextWrapping = TextWrapping.Wrap
            };

            legalNoticeStack.Children.Add(legalNoticeText);
            legalNoticeStack.Children.Add(deadlineWarning);
            legalNoticeBorder.Child = legalNoticeStack;

            // Add all elements to main stack
            mainStack.Children.Add(headerStack);
            mainStack.Children.Add(tenantPropertyBorder);
            mainStack.Children.Add(paymentDetailsBorder);
            mainStack.Children.Add(amountInWordsBorder);
            mainStack.Children.Add(statusBorder);
            mainStack.Children.Add(legalNoticeBorder);

            border.Child = mainStack;
            return border;
        }

        private Border CreateInfoSection(string title, string[] items)
        {
            var border = new Border
            {
                Background = System.Windows.Media.Brushes.AliceBlue,
                BorderBrush = System.Windows.Media.Brushes.LightBlue,
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(4),
                Padding = new Thickness(10),
                Margin = new Thickness(0, 0, 0, 10)
            };

            var stack = new StackPanel();

            var titleText = new TextBlock
            {
                Text = title,
                FontSize = 13,
                FontWeight = FontWeights.Bold,
                FontFamily = new System.Windows.Media.FontFamily("Segoe UI"),
                Margin = new Thickness(0, 0, 0, 5),
                Foreground = System.Windows.Media.Brushes.DarkBlue
            };

            stack.Children.Add(titleText);

            foreach (var item in items)
            {
                var itemText = new TextBlock
                {
                    Text = item,
                    FontSize = 11,
                    FontFamily = new System.Windows.Media.FontFamily("Segoe UI"),
                    Margin = new Thickness(0, 0, 0, 2),
                    TextAlignment = TextAlignment.Left
                };
                stack.Children.Add(itemText);
            }

            border.Child = stack;
            return border;
        }

        private async void PrintFromPreviewButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var printService = CleaningTaxPrintService.Instance;
                var success = await printService.PrintReceiptFromPaymentAsync(_payment);
                
                if (success)
                {
                    MessageBox.Show("تم طباعة الوصل بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة الوصل: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ZoomInButton_Click(object sender, RoutedEventArgs e)
        {
            // تكبير بسيط عن طريق تغيير حجم المحتوى
            if (_receiptVisual != null)
            {
                var transform = _receiptVisual.LayoutTransform as ScaleTransform ?? new ScaleTransform();
                transform.ScaleX = Math.Min(transform.ScaleX * 1.2, 3.0);
                transform.ScaleY = Math.Min(transform.ScaleY * 1.2, 3.0);
                _receiptVisual.LayoutTransform = transform;
            }
        }

        private void ZoomOutButton_Click(object sender, RoutedEventArgs e)
        {
            // تصغير بسيط عن طريق تغيير حجم المحتوى
            if (_receiptVisual != null)
            {
                var transform = _receiptVisual.LayoutTransform as ScaleTransform ?? new ScaleTransform();
                transform.ScaleX = Math.Max(transform.ScaleX * 0.8, 0.5);
                transform.ScaleY = Math.Max(transform.ScaleY * 0.8, 0.5);
                _receiptVisual.LayoutTransform = transform;
            }
        }

        private void ResetZoomButton_Click(object sender, RoutedEventArgs e)
        {
            // إعادة تعيين الحجم الأصلي
            if (_receiptVisual != null)
            {
                _receiptVisual.LayoutTransform = new ScaleTransform(1.0, 1.0);
            }
        }

        private void ClosePreviewButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private async void ExportPdfButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_receiptData == null)
                {
                    MessageBox.Show("لا توجد بيانات وصل للتصدير", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var saveDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Filter = "PDF Files (*.pdf)|*.pdf",
                    FileName = $"وصل_ضريبة_النظافة_{_receiptData.ReceiptNumber}_{DateTime.Now:yyyyMMdd}.pdf",
                    DefaultExt = "pdf"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    // Use the existing print service to save as PDF
                    var printService = new CleaningTaxPrintService();
                    await printService.SaveReceiptAsPdfAsync(_receiptData, saveDialog.FileName);

                    MessageBox.Show($"تم حفظ الوصل بنجاح في:\n{saveDialog.FileName}", "نجح التصدير",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير الوصل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
