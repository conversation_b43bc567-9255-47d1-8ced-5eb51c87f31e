using System;
using System.ComponentModel.DataAnnotations;

namespace SimpleRentalApp.Models
{
    /// <summary>
    /// نموذج الإشعارات المتطورة للواتساب
    /// </summary>
    public class AdvancedNotification
    {
        public int Id { get; set; }

        [Required]
        public int PropertyId { get; set; }
        public Property Property { get; set; } = null!;

        [Required]
        public int CustomerId { get; set; }
        public Customer Customer { get; set; } = null!;

        public int? ContractId { get; set; }
        public Contract? Contract { get; set; }

        [Required]
        public NotificationType Type { get; set; }

        [Required]
        public NotificationStatus Status { get; set; } = NotificationStatus.Pending;

        [Required]
        public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;

        [Required]
        public DateTime DueDate { get; set; }

        public DateTime? SentDate { get; set; }

        [Required]
        public decimal Amount { get; set; }

        [StringLength(1000)]
        public string Message { get; set; } = string.Empty;

        [StringLength(500)]
        public string Notes { get; set; } = string.Empty;

        public int RetryCount { get; set; } = 0;
        public int MaxRetries { get; set; } = 3;

        public DateTime? LastRetryDate { get; set; }
        public DateTime? LastSentDate { get; set; }
        public int? AttemptCount { get; set; } = 0;

        [StringLength(200)]
        public string ErrorMessage { get; set; } = string.Empty;

        // Metadata for different notification types
        public int? PaymentMonth { get; set; }  // للإيجار الشهري
        public int? PaymentYear { get; set; }   // للإيجار والضريبة
        public int? TaxYear { get; set; }       // لضريبة النظافة
        public int? DaysOverdue { get; set; }   // للتأخير

        [Required]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime UpdatedDate { get; set; } = DateTime.Now;

        [StringLength(100)]
        public string CreatedBy { get; set; } = "النظام";

        // Computed Properties
        public bool IsOverdue => DueDate < DateTime.Now && Status != NotificationStatus.Sent;
        public bool CanRetry => RetryCount < MaxRetries && Status == NotificationStatus.Failed;
        public bool CanSend => Status == NotificationStatus.Pending || Status == NotificationStatus.Failed;
        public string StatusDisplay => GetStatusDisplay();
        public string TypeDisplay => GetTypeDisplay();
        public string PriorityDisplay => GetPriorityDisplay();
        public string AmountDisplay => $"{Amount:N0} درهم";
        public string DueDateDisplay => DueDate.ToString("dd/MM/yyyy");
        public string SentDateDisplay => SentDate?.ToString("dd/MM/yyyy HH:mm") ?? "-";

        private string GetStatusDisplay()
        {
            return Status switch
            {
                NotificationStatus.Pending => "بانتظار الإرسال",
                NotificationStatus.Sent => "تم الإرسال",
                NotificationStatus.Failed => "فشل الإرسال",
                NotificationStatus.Cancelled => "ملغي",
                NotificationStatus.Scheduled => "مجدول",
                _ => "غير محدد"
            };
        }

        private string GetTypeDisplay()
        {
            return Type switch
            {
                NotificationType.MonthlyRent => "إيجار شهري",
                NotificationType.CleaningTax => "ضريبة النظافة",
                NotificationType.RentOverdue => "تأخير إيجار",
                NotificationType.CleaningTaxOverdue => "تأخير ضريبة النظافة",
                NotificationType.ContractExpiry => "انتهاء العقد",
                NotificationType.PaymentReminder => "تذكير دفع",
                _ => "غير محدد"
            };
        }

        private string GetPriorityDisplay()
        {
            return Priority switch
            {
                NotificationPriority.Low => "منخفضة",
                NotificationPriority.Normal => "عادية",
                NotificationPriority.High => "عالية",
                NotificationPriority.Urgent => "عاجلة",
                _ => "عادية"
            };
        }
    }

    /// <summary>
    /// أنواع الإشعارات
    /// </summary>
    public enum NotificationType
    {
        MonthlyRent = 1,           // إيجار شهري
        CleaningTax = 2,           // ضريبة النظافة
        RentOverdue = 3,           // تأخير إيجار
        CleaningTaxOverdue = 4,    // تأخير ضريبة النظافة
        ContractExpiry = 5,        // انتهاء العقد
        PaymentReminder = 6        // تذكير دفع عام
    }

    /// <summary>
    /// حالات الإشعار
    /// </summary>
    public enum NotificationStatus
    {
        Pending = 1,    // بانتظار الإرسال
        Sent = 2,       // تم الإرسال
        Failed = 3,     // فشل الإرسال
        Cancelled = 4,  // ملغي
        Scheduled = 5   // مجدول
    }

    /// <summary>
    /// أولويات الإشعار
    /// </summary>
    public enum NotificationPriority
    {
        Low = 1,      // منخفضة
        Normal = 2,   // عادية
        High = 3,     // عالية
        Urgent = 4    // عاجلة
    }

    /// <summary>
    /// نموذج عرض الإشعارات في الواجهة
    /// </summary>
    public class NotificationDisplayInfo
    {
        public int Id { get; set; }
        public string PropertyName { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string TypeDisplay { get; set; } = string.Empty;
        public string StatusDisplay { get; set; } = string.Empty;
        public string PriorityDisplay { get; set; } = string.Empty;
        public string AmountDisplay { get; set; } = string.Empty;
        public string DueDateDisplay { get; set; } = string.Empty;
        public string SentDateDisplay { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Notes { get; set; } = string.Empty;
        public bool IsOverdue { get; set; }
        public bool CanRetry { get; set; }
        public bool CanSend { get; set; }
        public int RetryCount { get; set; }
        public int MaxRetries { get; set; }
        public int AttemptCount { get; set; }
        public DateTime? LastSentDate { get; set; }
        public decimal Amount { get; set; }
        public bool IsSelected { get; set; } = false;
        public NotificationType Type { get; set; }
        public NotificationStatus Status { get; set; }
        public NotificationPriority Priority { get; set; }
        public DateTime DueDate { get; set; }
        public DateTime? SentDate { get; set; }
        public AdvancedNotification Notification { get; set; } = null!;
    }
}
