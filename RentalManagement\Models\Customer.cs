using System.ComponentModel.DataAnnotations;

namespace RentalManagement.Models
{
    public class Customer
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "الاسم الكامل مطلوب")]
        [StringLength(100, ErrorMessage = "الاسم لا يجب أن يتجاوز 100 حرف")]
        public string FullName { get; set; } = string.Empty;

        [Required(ErrorMessage = "رقم البطاقة الوطنية مطلوب")]
        [StringLength(20, ErrorMessage = "رقم البطاقة لا يجب أن يتجاوز 20 حرف")]
        public string NationalId { get; set; } = string.Empty;

        [Required(ErrorMessage = "رقم الهاتف مطلوب")]
        [Phone(ErrorMessage = "رقم الهاتف غير صحيح")]
        [StringLength(15, ErrorMessage = "رقم الهاتف لا يجب أن يتجاوز 15 رقم")]
        public string PhoneNumber { get; set; } = string.Empty;

        [StringLength(200, ErrorMessage = "العنوان لا يجب أن يتجاوز 200 حرف")]
        public string Address { get; set; } = string.Empty;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();
        public virtual ICollection<Property> RentedProperties { get; set; } = new List<Property>();
    }
}
