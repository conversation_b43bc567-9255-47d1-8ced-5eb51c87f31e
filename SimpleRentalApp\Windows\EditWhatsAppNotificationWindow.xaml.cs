using Microsoft.EntityFrameworkCore;
using SimpleRentalApp.Data;
using SimpleRentalApp.Models;
using SimpleRentalApp.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace SimpleRentalApp.Windows
{
    /// <summary>
    /// نافذة تعديل إشعار الواتساب
    /// </summary>
    public partial class EditWhatsAppNotificationWindow : Window
    {
        private readonly RentalDbContext _context;
        private readonly NotificationTemplateService _templateService;
        private readonly WhatsAppNotification _notification;
        private List<MessageTemplate> _templates;

        public bool IsModified { get; private set; } = false;

        public EditWhatsAppNotificationWindow(WhatsAppNotification notification)
        {
            InitializeComponent();
            
            _context = new RentalDbContext();
            _templateService = new NotificationTemplateService(_context);
            _notification = notification;
            
            InitializeWindow();
        }

        private async void InitializeWindow()
        {
            try
            {
                // تحميل البيانات المرتبطة
                await LoadRelatedDataAsync();
                
                // تحميل القوالب
                await LoadTemplatesAsync();
                
                // تحميل بيانات الإشعار
                LoadNotificationData();
                
                // تحديث المعاينة
                UpdatePreview();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل البيانات المرتبطة بالإشعار
        /// </summary>
        private async Task LoadRelatedDataAsync()
        {
            try
            {
                // تحميل بيانات المكتري والمحل
                if (_notification.Customer == null && _notification.CustomerId > 0)
                {
                    _notification.Customer = await _context.Customers
                        .FirstOrDefaultAsync(c => c.Id == _notification.CustomerId);
                }

                if (_notification.Property == null && _notification.PropertyId > 0)
                {
                    _notification.Property = await _context.Properties
                        .FirstOrDefaultAsync(p => p.Id == _notification.PropertyId);
                }

                if (_notification.Contract == null && _notification.ContractId.HasValue)
                {
                    _notification.Contract = await _context.Contracts
                        .FirstOrDefaultAsync(c => c.Id == _notification.ContractId.Value);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل البيانات المرتبطة: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل قوالب الرسائل
        /// </summary>
        private async Task LoadTemplatesAsync()
        {
            try
            {
                _templates = await _context.MessageTemplates
                    .Where(t => t.Type == _notification.Type && t.IsActive)
                    .OrderBy(t => t.Name)
                    .ToListAsync();

                TemplateComboBox.Items.Clear();
                TemplateComboBox.Items.Add(new ComboBoxItem { Content = "-- اختر قالب --", Tag = null });

                foreach (var template in _templates)
                {
                    TemplateComboBox.Items.Add(new ComboBoxItem 
                    { 
                        Content = template.Name, 
                        Tag = template 
                    });
                }

                TemplateComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل القوالب: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل بيانات الإشعار في النموذج
        /// </summary>
        private void LoadNotificationData()
        {
            try
            {
                // البيانات الأساسية (للقراءة فقط)
                CustomerNameTextBox.Text = _notification.Customer?.FullName ?? "غير محدد";
                PropertyNameTextBox.Text = _notification.Property?.Name ?? "غير محدد";

                // البيانات القابلة للتعديل
                PhoneNumberTextBox.Text = _notification.PhoneNumber;
                AmountTextBox.Text = _notification.Amount.ToString("F2");
                DueDatePicker.SelectedDate = _notification.DueDate;

                // رسالة فارغة في البداية
                MessageTextBox.Text = "";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل بيانات الإشعار: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث معاينة الرسالة
        /// </summary>
        private async void UpdatePreview()
        {
            try
            {
                string previewText = "";

                if (!string.IsNullOrWhiteSpace(MessageTextBox.Text))
                {
                    // استخدام النص المكتوب يدوياً
                    previewText = MessageTextBox.Text;
                }
                else if (TemplateComboBox.SelectedItem is ComboBoxItem selectedItem && 
                         selectedItem.Tag is MessageTemplate template)
                {
                    // استخدام القالب المحدد
                    previewText = await ApplyTemplateVariables(template.Template);
                }
                else
                {
                    previewText = "اختر قالب أو اكتب رسالة يدوياً...";
                }

                // تحديث المعاينة
                PreviewTextBlock.Text = previewText;

                // تحديث عداد الأحرف
                UpdateCharacterCount(previewText);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث المعاينة: {ex.Message}");
                PreviewTextBlock.Text = "خطأ في تحديث المعاينة";
            }
        }

        /// <summary>
        /// تطبيق متغيرات القالب
        /// </summary>
        private async Task<string> ApplyTemplateVariables(string template)
        {
            try
            {
                var message = template;

                // المتغيرات الأساسية
                message = message.Replace("{اسم_المكتري}", _notification.Customer?.FullName ?? "المكتري");
                message = message.Replace("{اسم_المحل}", _notification.Property?.Name ?? "المحل");
                message = message.Replace("{عنوان_المحل}", _notification.Property?.Address ?? "غير محدد");
                message = message.Replace("{المبلغ}", AmountTextBox.Text);
                message = message.Replace("{تاريخ_الاستحقاق}", DueDatePicker.SelectedDate?.ToString("dd/MM/yyyy") ?? "غير محدد");
                message = message.Replace("{رقم_الهاتف}", PhoneNumberTextBox.Text);

                // متغيرات التاريخ والوقت
                message = message.Replace("{التاريخ_الحالي}", DateTime.Now.ToString("dd/MM/yyyy"));
                message = message.Replace("{الوقت_الحالي}", DateTime.Now.ToString("HH:mm"));
                message = message.Replace("{اليوم}", DateTime.Now.ToString("dddd"));

                // متغيرات خاصة بنوع الإشعار
                switch (_notification.Type)
                {
                    case WhatsAppNotificationType.MonthlyRent:
                        if (_notification.Month.HasValue && _notification.Year.HasValue)
                        {
                            var monthName = new DateTime(_notification.Year.Value, _notification.Month.Value, 1).ToString("MMMM yyyy");
                            message = message.Replace("{شهر_الدفع}", monthName);
                            message = message.Replace("{سنة_الدفع}", _notification.Year.Value.ToString());
                            message = message.Replace("{شهر_رقم}", _notification.Month.Value.ToString());
                        }
                        break;

                    case WhatsAppNotificationType.CleaningTax:
                        if (_notification.Year.HasValue)
                        {
                            message = message.Replace("{سنة_الضريبة}", _notification.Year.Value.ToString());
                            message = message.Replace("{موعد_انتهاء_الضريبة}", $"31/05/{_notification.Year.Value}");
                        }
                        break;
                }

                return message;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق متغيرات القالب: {ex.Message}");
                return template;
            }
        }

        /// <summary>
        /// تحديث عداد الأحرف
        /// </summary>
        private void UpdateCharacterCount(string text)
        {
            try
            {
                var characterCount = text.Length;
                CharacterCountTextBlock.Text = characterCount.ToString();

                // تحديد طول الرسالة
                string lengthDescription;
                if (characterCount <= 160)
                {
                    lengthDescription = "قصيرة (رسالة واحدة)";
                }
                else if (characterCount <= 320)
                {
                    lengthDescription = "متوسطة (رسالتان)";
                }
                else if (characterCount <= 480)
                {
                    lengthDescription = "طويلة (3 رسائل)";
                }
                else
                {
                    lengthDescription = "طويلة جداً (4+ رسائل)";
                }

                MessageLengthTextBlock.Text = lengthDescription;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث عداد الأحرف: {ex.Message}");
            }
        }

        // Event Handlers
        private void PhoneNumberTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdatePreview();
        }

        private void AmountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdatePreview();
        }

        private void DueDatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdatePreview();
        }

        private void TemplateComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (TemplateComboBox.SelectedItem is ComboBoxItem selectedItem && 
                selectedItem.Tag is MessageTemplate template)
            {
                MessageTextBox.Text = ""; // مسح النص اليدوي عند اختيار قالب
            }
            UpdatePreview();
        }

        private void MessageTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(MessageTextBox.Text))
            {
                TemplateComboBox.SelectedIndex = 0; // إلغاء تحديد القالب عند الكتابة يدوياً
            }
            UpdatePreview();
        }

        private async void RefreshTemplatesBtn_Click(object sender, RoutedEventArgs e)
        {
            await LoadTemplatesAsync();
            MessageBox.Show("تم تحديث قائمة القوالب!", "تحديث",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private async void SaveBtn_Click(object sender, RoutedEventArgs e)
        {
            await SaveChangesAsync();
        }

        private void CancelBtn_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void Window_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter && (Keyboard.Modifiers & ModifierKeys.Control) == ModifierKeys.Control)
            {
                SaveBtn_Click(sender, e);
            }
            else if (e.Key == Key.Escape)
            {
                CancelBtn_Click(sender, e);
            }
        }

        /// <summary>
        /// حفظ التعديلات
        /// </summary>
        private async Task SaveChangesAsync()
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateInput())
                    return;

                // تحديث بيانات الإشعار
                _notification.PhoneNumber = PhoneNumberTextBox.Text.Trim();
                
                if (decimal.TryParse(AmountTextBox.Text, out decimal amount))
                {
                    _notification.Amount = amount;
                }

                _notification.DueDate = DueDatePicker.SelectedDate;

                // حفظ في قاعدة البيانات
                _context.WhatsAppNotifications.Update(_notification);
                await _context.SaveChangesAsync();

                IsModified = true;

                // إشعار النجاح
                MessageBox.Show("تم حفظ التعديلات بنجاح! ✅", "تم الحفظ",
                    MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التعديلات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات المدخلة
        /// </summary>
        private bool ValidateInput()
        {
            // التحقق من رقم الهاتف
            if (string.IsNullOrWhiteSpace(PhoneNumberTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الهاتف!", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                PhoneNumberTextBox.Focus();
                return false;
            }

            // التحقق من المبلغ
            if (!decimal.TryParse(AmountTextBox.Text, out decimal amount) || amount <= 0)
            {
                MessageBox.Show("يرجى إدخال مبلغ صحيح!", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                AmountTextBox.Focus();
                return false;
            }

            return true;
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            _templateService?.Dispose();
            base.OnClosed(e);
        }
    }
}
