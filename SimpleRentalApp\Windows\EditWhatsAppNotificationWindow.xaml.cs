using Microsoft.EntityFrameworkCore;
using SimpleRentalApp.Data;
using SimpleRentalApp.Models;
using SimpleRentalApp.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace SimpleRentalApp.Windows
{
    /// <summary>
    /// نافذة تعديل إشعار الواتساب
    /// </summary>
    public partial class EditWhatsAppNotificationWindow : Window
    {
        private readonly RentalDbContext _context;
        private readonly NotificationTemplateService _templateService;
        private readonly WhatsAppNotification _notification;
        private List<MessageTemplate> _templates;

        public bool IsModified { get; private set; } = false;

        public EditWhatsAppNotificationWindow(WhatsAppNotification notification)
        {
            InitializeComponent();
            
            _context = new RentalDbContext();
            _templateService = new NotificationTemplateService(_context);
            _notification = notification;
            
            InitializeWindow();
        }

        private async void InitializeWindow()
        {
            try
            {
                // تحميل البيانات المرتبطة
                await LoadRelatedDataAsync();
                
                // تحميل القوالب
                await LoadTemplatesAsync();
                
                // تحميل بيانات الإشعار
                LoadNotificationData();
                
                // تحديث المعاينة
                UpdatePreview();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل البيانات المرتبطة بالإشعار
        /// </summary>
        private async Task LoadRelatedDataAsync()
        {
            try
            {
                // تحميل بيانات المكتري والمحل
                if (_notification.Customer == null && _notification.CustomerId > 0)
                {
                    _notification.Customer = await _context.Customers
                        .FirstOrDefaultAsync(c => c.Id == _notification.CustomerId);
                }

                if (_notification.Property == null && _notification.PropertyId > 0)
                {
                    _notification.Property = await _context.Properties
                        .FirstOrDefaultAsync(p => p.Id == _notification.PropertyId);
                }

                if (_notification.Contract == null && _notification.ContractId.HasValue)
                {
                    _notification.Contract = await _context.Contracts
                        .FirstOrDefaultAsync(c => c.Id == _notification.ContractId.Value);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل البيانات المرتبطة: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل قوالب الرسائل
        /// </summary>
        private async Task LoadTemplatesAsync()
        {
            try
            {
                _templates = await _context.MessageTemplates
                    .Where(t => t.Type == _notification.Type && t.IsActive)
                    .OrderBy(t => t.Name)
                    .ToListAsync();

                TemplateComboBox.Items.Clear();
                TemplateComboBox.Items.Add(new ComboBoxItem { Content = "-- اختر قالب --", Tag = null });

                foreach (var template in _templates)
                {
                    TemplateComboBox.Items.Add(new ComboBoxItem 
                    { 
                        Content = template.Name, 
                        Tag = template 
                    });
                }

                TemplateComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل القوالب: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل بيانات الإشعار في النموذج
        /// </summary>
        private void LoadNotificationData()
        {
            try
            {
                // البيانات الأساسية (للقراءة فقط)
                CustomerNameTextBox.Text = _notification.Customer?.FullName ?? "غير محدد";
                PropertyNameTextBox.Text = _notification.Property?.Name ?? "غير محدد";

                // البيانات القابلة للتعديل
                PhoneNumberTextBox.Text = _notification.PhoneNumber;
                AmountTextBox.Text = _notification.Amount.ToString("F2");
                DueDatePicker.SelectedDate = _notification.DueDate;

                // تحميل الرسالة المحفوظة أو القالب المرتبط
                LoadSavedMessageOrTemplate();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل بيانات الإشعار: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل الرسالة المحفوظة أو القالب المرتبط
        /// </summary>
        private void LoadSavedMessageOrTemplate()
        {
            try
            {
                // إذا كان هناك رسالة محفوظة مسبقاً، اعرضها
                if (!string.IsNullOrWhiteSpace(_notification.MessageContent))
                {
                    MessageTextBox.Text = _notification.MessageContent;
                }

                // إذا كان هناك قالب مرتبط، حدده في ComboBox
                if (_notification.TemplateId.HasValue && _notification.Template != null)
                {
                    // البحث عن القالب في ComboBox وتحديده
                    foreach (ComboBoxItem item in TemplateComboBox.Items)
                    {
                        if (item.Tag is MessageTemplate template && template.Id == _notification.TemplateId.Value)
                        {
                            TemplateComboBox.SelectedItem = item;
                            break;
                        }
                    }

                    // إذا لم تكن هناك رسالة مخصصة، اعرض القالب
                    if (string.IsNullOrWhiteSpace(MessageTextBox.Text))
                    {
                        MessageTextBox.Text = ""; // سيتم ملؤها من القالب في UpdatePreview
                    }
                }

                // إذا لم تكن هناك رسالة ولا قالب، اتركها فارغة
                if (string.IsNullOrWhiteSpace(MessageTextBox.Text) && !_notification.TemplateId.HasValue)
                {
                    MessageTextBox.Text = "";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الرسالة المحفوظة: {ex.Message}");
                MessageTextBox.Text = "";
            }
        }

        /// <summary>
        /// تحديث معاينة الرسالة
        /// </summary>
        private async void UpdatePreview()
        {
            try
            {
                string previewText = "";

                if (!string.IsNullOrWhiteSpace(MessageTextBox.Text))
                {
                    // استخدام النص المكتوب يدوياً
                    previewText = MessageTextBox.Text;
                }
                else if (TemplateComboBox.SelectedItem is ComboBoxItem selectedItem &&
                         selectedItem.Tag is MessageTemplate template)
                {
                    // استخدام القالب المحدد مع تطبيق المتغيرات المحدثة
                    previewText = await ApplyTemplateVariables(template.Template);
                }
                else
                {
                    // إذا لم يتم اختيار أي شيء، اعرض رسالة افتراضية
                    previewText = await GenerateDefaultMessageAsync();
                }

                // تحديث المعاينة
                PreviewTextBlock.Text = previewText;

                // تحديث عداد الأحرف
                UpdateCharacterCount(previewText);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث المعاينة: {ex.Message}");
                PreviewTextBlock.Text = "خطأ في تحديث المعاينة";
            }
        }

        /// <summary>
        /// تطبيق متغيرات القالب
        /// </summary>
        private async Task<string> ApplyTemplateVariables(string template)
        {
            try
            {
                var message = template;

                // المتغيرات الأساسية
                message = message.Replace("{اسم_المكتري}", _notification.Customer?.FullName ?? "المكتري");
                message = message.Replace("{اسم_المحل}", _notification.Property?.Name ?? "المحل");
                message = message.Replace("{عنوان_المحل}", _notification.Property?.Address ?? "غير محدد");
                message = message.Replace("{المبلغ}", AmountTextBox.Text);
                message = message.Replace("{تاريخ_الاستحقاق}", DueDatePicker.SelectedDate?.ToString("dd/MM/yyyy") ?? "غير محدد");
                message = message.Replace("{رقم_الهاتف}", PhoneNumberTextBox.Text);

                // متغيرات التاريخ والوقت
                message = message.Replace("{التاريخ_الحالي}", DateTime.Now.ToString("dd/MM/yyyy"));
                message = message.Replace("{الوقت_الحالي}", DateTime.Now.ToString("HH:mm"));
                message = message.Replace("{اليوم}", DateTime.Now.ToString("dddd"));

                // متغيرات خاصة بنوع الإشعار
                switch (_notification.Type)
                {
                    case WhatsAppNotificationType.MonthlyRent:
                        if (_notification.Month.HasValue && _notification.Year.HasValue)
                        {
                            var monthName = new DateTime(_notification.Year.Value, _notification.Month.Value, 1).ToString("MMMM yyyy");
                            message = message.Replace("{شهر_الدفع}", monthName);
                            message = message.Replace("{سنة_الدفع}", _notification.Year.Value.ToString());
                            message = message.Replace("{شهر_رقم}", _notification.Month.Value.ToString());
                        }
                        break;

                    case WhatsAppNotificationType.CleaningTax:
                        if (_notification.Year.HasValue)
                        {
                            message = message.Replace("{سنة_الضريبة}", _notification.Year.Value.ToString());
                            message = message.Replace("{موعد_انتهاء_الضريبة}", $"31/05/{_notification.Year.Value}");
                        }
                        break;
                }

                return message;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق متغيرات القالب: {ex.Message}");
                return template;
            }
        }

        /// <summary>
        /// تحديث عداد الأحرف
        /// </summary>
        private void UpdateCharacterCount(string text)
        {
            try
            {
                var characterCount = text.Length;
                CharacterCountTextBlock.Text = characterCount.ToString();

                // تحديد طول الرسالة
                string lengthDescription;
                if (characterCount <= 160)
                {
                    lengthDescription = "قصيرة (رسالة واحدة)";
                }
                else if (characterCount <= 320)
                {
                    lengthDescription = "متوسطة (رسالتان)";
                }
                else if (characterCount <= 480)
                {
                    lengthDescription = "طويلة (3 رسائل)";
                }
                else
                {
                    lengthDescription = "طويلة جداً (4+ رسائل)";
                }

                MessageLengthTextBlock.Text = lengthDescription;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث عداد الأحرف: {ex.Message}");
            }
        }

        // Event Handlers
        private void PhoneNumberTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdatePreview();
        }

        private void AmountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdatePreview();
        }

        private void DueDatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdatePreview();
        }

        private void TemplateComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // عند اختيار قالب، امسح النص اليدوي لتجنب التضارب
            if (TemplateComboBox.SelectedItem is ComboBoxItem selectedItem &&
                selectedItem.Tag is MessageTemplate template)
            {
                // مسح النص اليدوي مؤقتاً لإظهار القالب
                if (!string.IsNullOrWhiteSpace(MessageTextBox.Text))
                {
                    var result = MessageBox.Show(
                        "لديك نص مكتوب يدوياً. هل تريد استبداله بالقالب المختار؟",
                        "تأكيد الاستبدال",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        MessageTextBox.Text = "";
                    }
                    else
                    {
                        // إذا رفض المستخدم، أعد تحديد "-- اختر قالب --"
                        TemplateComboBox.SelectedIndex = 0;
                        return;
                    }
                }
                else
                {
                    MessageTextBox.Text = "";
                }
            }
            UpdatePreview();
        }

        private void MessageTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // عند الكتابة يدوياً، إلغاء تحديد القالب
            if (!string.IsNullOrWhiteSpace(MessageTextBox.Text) &&
                TemplateComboBox.SelectedIndex > 0)
            {
                TemplateComboBox.SelectedIndex = 0;
            }
            UpdatePreview();
        }

        private async void RefreshTemplatesBtn_Click(object sender, RoutedEventArgs e)
        {
            await LoadTemplatesAsync();
            MessageBox.Show("تم تحديث قائمة القوالب!", "تحديث",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private async void SaveBtn_Click(object sender, RoutedEventArgs e)
        {
            await SaveChangesAsync();
        }

        private void CancelBtn_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void Window_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter && (Keyboard.Modifiers & ModifierKeys.Control) == ModifierKeys.Control)
            {
                SaveBtn_Click(sender, e);
            }
            else if (e.Key == Key.Escape)
            {
                CancelBtn_Click(sender, e);
            }
        }

        /// <summary>
        /// حفظ التعديلات
        /// </summary>
        private async Task SaveChangesAsync()
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateInput())
                    return;

                // تحديث بيانات الإشعار الأساسية
                _notification.PhoneNumber = PhoneNumberTextBox.Text.Trim();

                if (decimal.TryParse(AmountTextBox.Text, out decimal amount))
                {
                    _notification.Amount = amount;
                }

                _notification.DueDate = DueDatePicker.SelectedDate;

                // تحديث القالب والرسالة
                await UpdateTemplateAndMessageAsync();

                // حفظ في قاعدة البيانات
                _context.WhatsAppNotifications.Update(_notification);
                await _context.SaveChangesAsync();

                IsModified = true;

                // إشعار النجاح
                MessageBox.Show("تم حفظ التعديلات بنجاح! ✅\n\nسيتم استخدام الرسالة المحدثة عند الإرسال.", "تم الحفظ",
                    MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التعديلات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديث القالب والرسالة المخصصة
        /// </summary>
        private async Task UpdateTemplateAndMessageAsync()
        {
            try
            {
                // إذا كان هناك نص مكتوب يدوياً، استخدمه
                if (!string.IsNullOrWhiteSpace(MessageTextBox.Text))
                {
                    _notification.MessageContent = MessageTextBox.Text.Trim();
                    _notification.TemplateId = null; // إلغاء ربط القالب لأن المستخدم كتب رسالة مخصصة

                    System.Diagnostics.Debug.WriteLine($"حفظ رسالة مخصصة: {_notification.MessageContent.Substring(0, Math.Min(50, _notification.MessageContent.Length))}...");
                }
                // إذا تم اختيار قالب، استخدمه
                else if (TemplateComboBox.SelectedItem is ComboBoxItem selectedItem &&
                         selectedItem.Tag is MessageTemplate template)
                {
                    _notification.TemplateId = template.Id;

                    // تطبيق القالب مع المتغيرات المحدثة
                    var processedMessage = await ApplyTemplateVariables(template.Template);
                    _notification.MessageContent = processedMessage;

                    System.Diagnostics.Debug.WriteLine($"حفظ قالب معرف {template.Id}: {template.Name}");
                    System.Diagnostics.Debug.WriteLine($"محتوى الرسالة: {_notification.MessageContent.Substring(0, Math.Min(50, _notification.MessageContent.Length))}...");
                }
                // إذا لم يتم اختيار أي شيء، استخدم المعاينة الحالية أو أنشئ رسالة افتراضية
                else
                {
                    // استخدم النص من المعاينة إذا كان متاحاً
                    if (!string.IsNullOrWhiteSpace(PreviewTextBlock.Text) &&
                        PreviewTextBlock.Text != "اختر قالب أو اكتب رسالة يدوياً..." &&
                        PreviewTextBlock.Text != "خطأ في تحديث المعاينة")
                    {
                        _notification.MessageContent = PreviewTextBlock.Text;
                    }
                    else if (string.IsNullOrWhiteSpace(_notification.MessageContent))
                    {
                        _notification.MessageContent = await GenerateDefaultMessageAsync();
                    }

                    _notification.TemplateId = null;
                    System.Diagnostics.Debug.WriteLine($"حفظ رسالة افتراضية: {_notification.MessageContent.Substring(0, Math.Min(50, _notification.MessageContent.Length))}...");
                }

                // التأكد من أن الرسالة ليست فارغة
                if (string.IsNullOrWhiteSpace(_notification.MessageContent))
                {
                    _notification.MessageContent = await GenerateDefaultMessageAsync();
                    System.Diagnostics.Debug.WriteLine("تم إنشاء رسالة افتراضية لأن المحتوى كان فارغاً");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث القالب والرسالة: {ex.Message}");

                // في حالة الخطأ، أنشئ رسالة افتراضية
                _notification.MessageContent = await GenerateDefaultMessageAsync();
            }
        }

        /// <summary>
        /// توليد رسالة افتراضية
        /// </summary>
        private async Task<string> GenerateDefaultMessageAsync()
        {
            try
            {
                var customerName = _notification.Customer?.FullName ?? "المكتري";
                var propertyName = _notification.Property?.Name ?? "المحل";
                var amount = AmountTextBox.Text;
                var dueDate = DueDatePicker.SelectedDate?.ToString("dd/MM/yyyy") ?? "غير محدد";

                return _notification.Type switch
                {
                    WhatsAppNotificationType.MonthlyRent =>
                        $"السلام عليكم {customerName}،\n\n" +
                        $"نذكركم بوجوب أداء كراء محل {propertyName}.\n\n" +
                        $"💰 المبلغ المستحق: {amount} درهم\n" +
                        $"📅 آخر أجل للدفع: {dueDate}\n\n" +
                        $"شكراً لتعاونكم معنا.",

                    WhatsAppNotificationType.CleaningTax =>
                        $"مرحباً {customerName}،\n\n" +
                        $"المرجو تسديد ضريبة النظافة عن المحل {propertyName}.\n\n" +
                        $"💰 المبلغ المستحق: {amount} درهم\n" +
                        $"📅 آخر أجل للدفع: {dueDate}\n\n" +
                        $"مع تحياتنا.",

                    WhatsAppNotificationType.RentOverdue =>
                        $"تنبيه: {customerName}،\n\n" +
                        $"لديكم مبلغ متأخر عن محل {propertyName}.\n\n" +
                        $"💰 المبلغ المستحق: {amount} درهم\n" +
                        $"📅 تاريخ الاستحقاق: {dueDate}\n\n" +
                        $"يرجى التسديد في أقرب وقت.",

                    WhatsAppNotificationType.CleaningTaxOverdue =>
                        $"تنبيه: {customerName}،\n\n" +
                        $"لديكم ضريبة نظافة متأخرة عن محل {propertyName}.\n\n" +
                        $"💰 المبلغ المستحق: {amount} درهم\n" +
                        $"📅 تاريخ الاستحقاق: {dueDate}\n\n" +
                        $"يرجى التسديد فوراً لتجنب الغرامات.",

                    _ => $"إشعار من إدارة العقارات:\n\n" +
                         $"عزيزي {customerName}،\n" +
                         $"بخصوص محل {propertyName}\n" +
                         $"المبلغ: {amount} درهم\n" +
                         $"التاريخ: {dueDate}"
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في توليد الرسالة الافتراضية: {ex.Message}");
                return "رسالة إشعار من إدارة العقارات";
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات المدخلة
        /// </summary>
        private bool ValidateInput()
        {
            // التحقق من رقم الهاتف
            if (string.IsNullOrWhiteSpace(PhoneNumberTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الهاتف!", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                PhoneNumberTextBox.Focus();
                return false;
            }

            // التحقق من المبلغ
            if (!decimal.TryParse(AmountTextBox.Text, out decimal amount) || amount <= 0)
            {
                MessageBox.Show("يرجى إدخال مبلغ صحيح!", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                AmountTextBox.Focus();
                return false;
            }

            return true;
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            _templateService?.Dispose();
            base.OnClosed(e);
        }
    }
}
