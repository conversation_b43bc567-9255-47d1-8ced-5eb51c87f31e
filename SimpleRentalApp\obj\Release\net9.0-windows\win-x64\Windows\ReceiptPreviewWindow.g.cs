﻿#pragma checksum "..\..\..\..\..\Windows\ReceiptPreviewWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "1E2DDD74173332CAB6BF216CFE525E540E911115"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleRentalApp.Windows {
    
    
    /// <summary>
    /// ReceiptPreviewWindow
    /// </summary>
    public partial class ReceiptPreviewWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 64 "..\..\..\..\..\Windows\ReceiptPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ReceiptNumberText;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\..\..\Windows\ReceiptPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ReceiptContent;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\..\..\Windows\ReceiptPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PreviewReceiptNumber;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\..\Windows\ReceiptPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PreviewDate;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\..\Windows\ReceiptPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PreviewCustomerName;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\..\Windows\ReceiptPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PreviewPropertyAddress;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\..\Windows\ReceiptPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PreviewPaymentPeriod;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\..\Windows\ReceiptPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PreviewAmount;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\..\Windows\ReceiptPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PreviewAmountInWords;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\..\Windows\ReceiptPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel NotesSection;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\..\..\Windows\ReceiptPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PreviewNotes;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\..\..\Windows\ReceiptPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintBtn;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\..\Windows\ReceiptPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseBtn;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleRentalApp;V1.0.0.0;component/windows/receiptpreviewwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\ReceiptPreviewWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ReceiptNumberText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.ReceiptContent = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 3:
            this.PreviewReceiptNumber = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.PreviewDate = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.PreviewCustomerName = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.PreviewPropertyAddress = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.PreviewPaymentPeriod = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.PreviewAmount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.PreviewAmountInWords = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.NotesSection = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 11:
            this.PreviewNotes = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.PrintBtn = ((System.Windows.Controls.Button)(target));
            
            #line 166 "..\..\..\..\..\Windows\ReceiptPreviewWindow.xaml"
            this.PrintBtn.Click += new System.Windows.RoutedEventHandler(this.PrintBtn_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.CloseBtn = ((System.Windows.Controls.Button)(target));
            
            #line 169 "..\..\..\..\..\Windows\ReceiptPreviewWindow.xaml"
            this.CloseBtn.Click += new System.Windows.RoutedEventHandler(this.CloseBtn_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

