using System;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Win32;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using SimpleRentalApp.Models;

namespace SimpleRentalApp.Services;

public class CleaningTaxPdfService
{
    private static CleaningTaxPdfService? _instance;
    public static CleaningTaxPdfService Instance => _instance ??= new CleaningTaxPdfService();

    public CleaningTaxPdfService() { }

    public async Task<bool> ExportReceiptToPdfAsync(CleaningTaxReceipt receipt)
    {
        try
        {
            // Configure QuestPDF license (Community license is free)
            QuestPDF.Settings.License = LicenseType.Community;

            // Show save dialog
            var saveDialog = new SaveFileDialog
            {
                Title = "حفظ وصل ضريبة النظافة كـ PDF",
                Filter = "PDF Files (*.pdf)|*.pdf",
                FileName = $"وصل_ضريبة_النظافة_{receipt.ReceiptNumber}_{DateTime.Now:yyyyMMdd}.pdf",
                DefaultExt = "pdf"
            };

            if (saveDialog.ShowDialog() != true)
                return false;

            await CreatePdfReceiptAsync(receipt, saveDialog.FileName);

            MessageBox.Show($"تم حفظ وصل ضريبة النظافة بنجاح في:\n{saveDialog.FileName}", "نجح الحفظ",
                MessageBoxButton.OK, MessageBoxImage.Information);

            return true;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في حفظ PDF: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
            return false;
        }
    }

    private async Task CreatePdfReceiptAsync(CleaningTaxReceipt receipt, string filePath)
    {
        await Task.Run(() =>
        {
            Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(PageSizes.A6.Portrait());
                    page.Margin(15);
                    page.DefaultTextStyle(x => x.FontFamily("Arial").FontSize(8).DirectionFromRightToLeft());

                    page.Content().Column(column =>
                    {
                        // Header
                        column.Item().Background(Colors.Green.Darken2).Padding(10).Text(text =>
                        {
                            text.AlignCenter();
                            text.Span("🧹 وصل دفع ضريبة النظافة").FontSize(12).Bold().FontColor(Colors.White);
                            text.EmptyLine();
                            text.Span($"رقم الوصل: {receipt.ReceiptNumber}").FontSize(10).Bold().FontColor(Colors.White);
                        });

                        column.Item().PaddingVertical(8);

                        // Main content in two columns
                        // Customer and Property Info
                        column.Item().Background(Colors.Grey.Lighten4).Padding(8).Column(customerColumn =>
                        {
                            customerColumn.Item().Text("معلومات المكتري والمحل").FontSize(9).Bold().FontColor(Colors.Green.Darken2);
                            customerColumn.Item().PaddingVertical(2);

                            customerColumn.Item().Text(text =>
                            {
                                text.Span("المكتري: ").Bold();
                                text.Span(receipt.CustomerName);
                            });

                            customerColumn.Item().Text(text =>
                            {
                                text.Span("المحل: ").Bold();
                                text.Span(receipt.PropertyName);
                            });

                            customerColumn.Item().Text(text =>
                            {
                                text.Span("العنوان: ").Bold();
                                text.Span(receipt.PropertyAddress);
                            });
                        });

                        column.Item().PaddingVertical(4);

                        // Payment Details
                        column.Item().Background(Colors.Green.Lighten4).Padding(8).Column(paymentColumn =>
                        {
                            paymentColumn.Item().Text("تفاصيل الدفعة").FontSize(9).Bold().FontColor(Colors.Green.Darken2);
                            paymentColumn.Item().PaddingVertical(2);

                            paymentColumn.Item().Text(text =>
                            {
                                text.Span("تاريخ الدفع: ").Bold();
                                text.Span(receipt.PaymentDateDisplay);
                            });

                            paymentColumn.Item().Text(text =>
                            {
                                text.Span("السنة الضريبية: ").Bold();
                                text.Span(receipt.TaxYear.ToString());
                            });

                            paymentColumn.Item().Text(text =>
                            {
                                text.Span("المبلغ: ").Bold().FontColor(Colors.Green.Darken2);
                                text.Span(receipt.AmountDisplay).Bold().FontColor(Colors.Green.Darken2);
                            });

                            paymentColumn.Item().Text(text =>
                            {
                                text.Span("بالحروف: ").Bold();
                                text.Span(receipt.AmountInWords);
                            });

                            // Calculate payment type and remaining amount
                            var annualTax = (receipt.Property?.MonthlyRent ?? 0) * 12 * 0.105m;
                            var paymentType = GetPaymentType(receipt, receipt.Amount, annualTax);
                            var remainingAmount = Math.Max(0, annualTax - receipt.Amount);

                            paymentColumn.Item().Text(text =>
                            {
                                text.Span("نوع الدفعة: ").Bold();
                                text.Span(paymentType);
                            });

                            paymentColumn.Item().Text(text =>
                            {
                                text.Span("المتبقي من السنة: ").Bold();
                                text.Span(remainingAmount > 0 ? $"{remainingAmount:N0} درهم" : "مكتملة");
                            });

                            if (!string.IsNullOrWhiteSpace(receipt.Notes))
                            {
                                paymentColumn.Item().Text(text =>
                                {
                                    text.Span("ملاحظات: ").Bold();
                                    text.Span(receipt.Notes);
                                });
                            }
                        });

                        column.Item().PaddingVertical(6);

                        // Legal Notice
                        var annualTax = (receipt.Property?.MonthlyRent ?? 0) * 12 * 0.105m;
                        var remainingAmount = Math.Max(0, annualTax - receipt.Amount);
                        var isFullyPaid = remainingAmount <= 0;

                        column.Item()
                            .Background(isFullyPaid ? Colors.Green.Lighten4 : Colors.Orange.Lighten4)
                            .Border(1)
                            .BorderColor(isFullyPaid ? Colors.Green.Medium : Colors.Orange.Medium)
                            .Padding(6)
                            .Text(text =>
                            {
                                text.AlignCenter();
                                text.Span("⚠️ إشعار هام").Bold().FontSize(8);
                                text.EmptyLine();

                                if (isFullyPaid)
                                {
                                    text.Span($"تم دفع ضريبة النظافة كاملة لسنة {receipt.TaxYear}").FontSize(7);
                                }
                                else
                                {
                                    var paidPercentage = ((annualTax - remainingAmount) / annualTax) * 100;
                                    text.Span($"تم دفع ضريبة النظافة بشكل جزئي لسنة {receipt.TaxYear} ({paidPercentage:F0}% من المبلغ الإجمالي)").FontSize(7);
                                }

                                text.EmptyLine();
                                text.Span("الموعد النهائي: 31 مايو من كل سنة").Bold().FontSize(7).FontColor(Colors.Red.Darken1);
                            });

                        column.Item().PaddingVertical(6);

                        // Footer
                        column.Item().BorderTop(1).BorderColor(Colors.Grey.Medium).PaddingTop(6).Row(footerRow =>
                        {
                            footerRow.RelativeItem().Column(signatureColumn =>
                            {
                                signatureColumn.Item().Text("توقيع المؤجر").Bold().FontSize(7).AlignCenter();
                                signatureColumn.Item().PaddingVertical(8).BorderBottom(1).BorderColor(Colors.Grey.Medium);
                            });

                            footerRow.RelativeItem().Column(stampColumn =>
                            {
                                stampColumn.Item().Text("ختم المؤجر").FontSize(7).Bold().AlignCenter();
                                stampColumn.Item().PaddingVertical(8).BorderBottom(1).BorderColor(Colors.Grey.Medium);
                            });
                        });
                    });
                });
            }).GeneratePdf(filePath);
        });
    }

    private string GetPaymentType(CleaningTaxReceipt receipt, decimal paidAmount, decimal annualTax)
    {
        if (annualTax == 0) return "غير محدد";

        // Get payment type from property's cleaning tax payment option
        var property = receipt.Property;
        if (property != null && !string.IsNullOrEmpty(property.CleaningTaxPaymentOptionDisplay))
        {
            // Check if the paid amount matches the expected amount for the property's payment option
            var expectedAmount = property.CleaningTaxPerPayment;
            if (Math.Abs(paidAmount - expectedAmount) < 1) // Allow small rounding differences
            {
                return property.CleaningTaxPaymentOptionDisplay;
            }
        }

        // Fallback to percentage-based calculation
        var percentage = (paidAmount / annualTax) * 100;

        if (percentage >= 95) return "دفعة كاملة (سنوية)";
        if (percentage >= 45 && percentage <= 55) return "نصف سنوية";
        if (percentage >= 20 && percentage <= 30) return "ربع سنوية";
        if (percentage >= 5 && percentage <= 15) return "شهرية";

        return "دفعة جزئية";
    }
}
