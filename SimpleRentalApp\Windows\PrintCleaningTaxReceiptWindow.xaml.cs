using System;
using System.Windows;
using System.Windows.Media;
using SimpleRentalApp.Models;
using SimpleRentalApp.Services;

namespace SimpleRentalApp.Windows;

public partial class PrintCleaningTaxReceiptWindow : Window
{
    private readonly CleaningTaxReceipt _receipt;
    private readonly bool _pdfMode;

    public PrintCleaningTaxReceiptWindow(CleaningTaxReceipt receipt, bool pdfMode = false)
    {
        InitializeComponent();
        _receipt = receipt;
        _pdfMode = pdfMode;
        LoadReceiptData();

        if (_pdfMode)
        {
            // Hide print buttons and show only PDF save
            PrintButton.Visibility = Visibility.Collapsed;
            PreviewPrintButton.Visibility = Visibility.Collapsed;
            SaveAsPdfButton.Content = "💾 حفظ PDF";
        }
    }

    private void LoadReceiptData()
    {
        try
        {
            // Header
            ReceiptNumberText.Text = $"رقم الوصل: {_receipt.ReceiptNumber}";

            // Customer and property info
            CustomerNameText.Text = _receipt.CustomerName;
            PropertyNameText.Text = _receipt.PropertyName;
            PropertyAddressText.Text = _receipt.PropertyAddress;

            // Payment details
            PaymentDateText.Text = _receipt.PaymentDateDisplay;
            TaxYearText.Text = _receipt.TaxYear.ToString();
            AmountText.Text = _receipt.AmountDisplay;
            AmountInWordsText.Text = _receipt.AmountInWords;

            // Calculate payment type and remaining amount
            var annualTax = (_receipt.Property?.MonthlyRent ?? 0) * 12 * 0.105m;
            var paymentType = GetPaymentType(_receipt.Amount, annualTax);
            var remainingAmount = Math.Max(0, annualTax - _receipt.Amount);

            PaymentTypeText.Text = paymentType;
            RemainingAmountText.Text = remainingAmount > 0 ? $"{remainingAmount:N0} درهم" : "مكتملة";

            // Set payment status notice and colors
            SetPaymentStatusNotice(remainingAmount, annualTax);

            // Notes (show only if not empty)
            if (!string.IsNullOrWhiteSpace(_receipt.Notes))
            {
                NotesSection.Visibility = Visibility.Visible;
                NotesText.Text = _receipt.Notes;
            }
            else
            {
                NotesSection.Visibility = Visibility.Collapsed;
            }

            StatusText.Text = $"وصل ضريبة النظافة رقم {_receipt.ReceiptNumber} - جاهز للطباعة";
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل بيانات الوصل: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private string GetPaymentType(decimal paidAmount, decimal annualTax)
    {
        if (annualTax == 0) return "غير محدد";

        // Get payment type from property's cleaning tax payment option
        var property = _receipt.Property;
        if (property != null && !string.IsNullOrEmpty(property.CleaningTaxPaymentOptionDisplay))
        {
            // Check if the paid amount matches the expected amount for the property's payment option
            var expectedAmount = property.CleaningTaxPerPayment;
            if (Math.Abs(paidAmount - expectedAmount) < 1) // Allow small rounding differences
            {
                return property.CleaningTaxPaymentOptionDisplay;
            }
        }

        // Fallback to percentage-based calculation
        var percentage = (paidAmount / annualTax) * 100;

        if (percentage >= 95) return "دفعة كاملة (سنوية)";
        if (percentage >= 45 && percentage <= 55) return "نصف سنوية";
        if (percentage >= 20 && percentage <= 30) return "ربع سنوية";
        if (percentage >= 5 && percentage <= 15) return "شهرية";

        return "دفعة جزئية";
    }

    private void SetPaymentStatusNotice(decimal remainingAmount, decimal annualTax)
    {
        if (remainingAmount <= 0)
        {
            // Fully paid
            PaymentStatusNotice.Text = $"تم دفع ضريبة النظافة كاملة لسنة {_receipt.TaxYear}";
            LegalNoticeBorder.Background = new SolidColorBrush(System.Windows.Media.Color.FromRgb(232, 245, 233)); // Light green
            LegalNoticeBorder.BorderBrush = new SolidColorBrush(System.Windows.Media.Color.FromRgb(76, 175, 80)); // Green
            RemainingAmountText.Foreground = new SolidColorBrush(System.Windows.Media.Color.FromRgb(76, 175, 80)); // Green
        }
        else
        {
            // Partially paid
            var paidPercentage = ((annualTax - remainingAmount) / annualTax) * 100;
            PaymentStatusNotice.Text = $"تم دفع ضريبة النظافة بشكل جزئي لسنة {_receipt.TaxYear} ({paidPercentage:F0}% من المبلغ الإجمالي)";
            LegalNoticeBorder.Background = new SolidColorBrush(System.Windows.Media.Color.FromRgb(255, 243, 205)); // Light orange
            LegalNoticeBorder.BorderBrush = new SolidColorBrush(System.Windows.Media.Color.FromRgb(255, 193, 7)); // Orange
            RemainingAmountText.Foreground = new SolidColorBrush(System.Windows.Media.Color.FromRgb(244, 67, 54)); // Red
        }
    }

    private async void PreviewPrintButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            StatusText.Text = "جاري فتح المعاينة...";

            // Use the cleaning tax print service with preview
            var success = CleaningTaxPrintService.Instance.PrintReceiptWithPreview(_receipt);

            if (success)
            {
                // Mark as printed in database
                await DatabaseService.Instance.MarkCleaningTaxReceiptAsPrintedAsync(_receipt.Id);

                StatusText.Text = "تم الطباعة بنجاح ✅";
            }
            else
            {
                StatusText.Text = "جاهز للطباعة";
            }
        }
        catch (Exception ex)
        {
            StatusText.Text = "فشل في المعاينة ❌";
            MessageBox.Show($"خطأ في المعاينة: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void PrintButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            StatusText.Text = "جاري الطباعة...";

            // Use the cleaning tax print service
            var success = CleaningTaxPrintService.Instance.PrintReceipt(_receipt);

            if (success)
            {
                // Mark as printed in database
                await DatabaseService.Instance.MarkCleaningTaxReceiptAsPrintedAsync(_receipt.Id);

                StatusText.Text = "تم الطباعة بنجاح ✅";

                MessageBox.Show("تم طباعة وصل ضريبة النظافة بنجاح! ✅", "نجحت الطباعة",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                StatusText.Text = "تم إلغاء الطباعة";
            }
        }
        catch (Exception ex)
        {
            StatusText.Text = "فشل في الطباعة ❌";
            MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void SaveAsPdfButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            StatusText.Text = "جاري إنشاء PDF...";

            // Use the cleaning tax PDF service
            var success = await CleaningTaxPdfService.Instance.ExportReceiptToPdfAsync(_receipt);

            if (success)
            {
                StatusText.Text = "تم حفظ PDF بنجاح ✅";

                // Mark as printed in database
                await DatabaseService.Instance.MarkCleaningTaxReceiptAsPrintedAsync(_receipt.Id);
            }
            else
            {
                StatusText.Text = "تم إلغاء العملية";
            }
        }
        catch (Exception ex)
        {
            StatusText.Text = "فشل في الحفظ ❌";
            MessageBox.Show($"خطأ في حفظ PDF: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }
}
