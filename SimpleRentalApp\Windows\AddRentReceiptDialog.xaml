<Window x:Class="SimpleRentalApp.Windows.AddRentReceiptDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة توصيل كراء جديد" Height="600" Width="500"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        ResizeMode="NoResize">

    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="6"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Success Button Style -->
        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#4CAF50"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#45A049"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Cancel Button Style -->
        <Style x:Key="CancelButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#757575"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#616161"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Modern ComboBox Style -->
        <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>

        <!-- Modern DatePicker Style -->
        <Style x:Key="ModernDatePickerStyle" TargetType="DatePicker">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>
    </Window.Resources>

    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="White" Padding="20,15" 
                BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
            <StackPanel>
                <TextBlock Text="📄 إضافة توصيل كراء جديد" 
                           FontSize="20" FontWeight="Bold" 
                           Foreground="#1976D2" Margin="0,0,0,5"/>
                <TextBlock Name="PropertyInfoText" Text="المحل: غير محدد" 
                           FontSize="14" Foreground="#666"/>
            </StackPanel>
        </Border>

        <!-- Form Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <StackPanel>
                
                <!-- Receipt Number -->
                <StackPanel Margin="0,0,0,15">
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                        <TextBlock Text="رقم التوصيل:" FontWeight="Medium"/>
                        <TextBlock Text="(مستقل لكل محل)" FontSize="11" Foreground="Gray" Margin="5,0,0,0"/>
                    </StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBox Name="ReceiptNumberTextBox" Grid.Column="0" Padding="10" FontSize="14"
                                 TextChanged="ReceiptNumberTextBox_TextChanged"
                                 ToolTip="يمكنك تعديل رقم التوصيل يدوياً. الترقيم مستقل لكل محل."/>
                        <Button Grid.Column="1" Content="🔄" Margin="5,0,0,0" Padding="8"
                                ToolTip="إنشاء رقم تلقائي جديد لهذا المحل" Click="GenerateReceiptNumber_Click"/>
                    </Grid>
                    <TextBlock Name="ReceiptNumberHintText" FontSize="11" Foreground="Gray" Margin="0,3,0,0"
                               Text="الرقم التالي المتاح لهذا المحل سيتم إنشاؤه تلقائياً"/>

                    <!-- Generate New Number Button -->
                    <Button Name="GenerateNewNumberBtn" Content="📝 إنشاء رقم جديد تلقائياً"
                            Margin="0,5,0,0" Padding="8,4" FontSize="12"
                            Background="#E3F2FD" Foreground="#1976D2" BorderBrush="#BBDEFB"
                            ToolTip="إنشاء رقم توصيل جديد بناءً على آخر رقم مستخدم لهذا المحل"
                            Click="GenerateNewNumberBtn_Click"/>
                </StackPanel>

                <!-- Payment Date -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="تاريخ الأداء:" FontWeight="Medium" Margin="0,0,0,5"/>
                    <DatePicker Name="PaymentDatePicker" Style="{StaticResource ModernDatePickerStyle}"/>
                </StackPanel>

                <!-- Amount -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="المبلغ (درهم):" FontWeight="Medium" Margin="0,0,0,5"/>
                    <TextBox Name="AmountTextBox" Padding="10" FontSize="14"/>
                </StackPanel>

                <!-- Payment Period -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="الفترة المؤداة:" FontWeight="Medium" Margin="0,0,0,5"/>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="من:" FontSize="12" Margin="0,0,0,3"/>
                            <DatePicker Name="PeriodStartDatePicker" Style="{StaticResource ModernDatePickerStyle}" FontSize="13"/>
                        </StackPanel>

                        <TextBlock Grid.Column="1" Text="إلى" VerticalAlignment="Center"
                                   Margin="10,15,10,0" FontWeight="Medium"/>

                        <StackPanel Grid.Column="2">
                            <TextBlock Text="إلى:" FontSize="12" Margin="0,0,0,3"/>
                            <DatePicker Name="PeriodEndDatePicker" Style="{StaticResource ModernDatePickerStyle}" FontSize="13"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>

                <!-- Notes -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="ملاحظات (اختيارية):" FontWeight="Medium" Margin="0,0,0,5"/>
                    <TextBox Name="NotesTextBox" Padding="10" FontSize="14"
                             Height="80" TextWrapping="Wrap" AcceptsReturn="True"/>
                </StackPanel>

            </StackPanel>
        </ScrollViewer>

        <!-- Action Buttons -->
        <Border Grid.Row="2" Background="White" Padding="20,15" 
                BorderBrush="#E0E0E0" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Name="SaveBtn" Content="💾 حفظ التوصيل" 
                        Style="{StaticResource SuccessButtonStyle}"
                        Click="SaveBtn_Click" Padding="20,10"/>
                <Button Name="CancelBtn" Content="❌ إلغاء" 
                        Style="{StaticResource CancelButtonStyle}"
                        Click="CancelBtn_Click" Padding="20,10"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
