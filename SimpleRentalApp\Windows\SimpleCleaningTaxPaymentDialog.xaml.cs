using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Microsoft.EntityFrameworkCore;
using SimpleRentalApp.Data;
using SimpleRentalApp.Models;
using SimpleRentalApp.Services;
using SimpleRentalApp.Utils;

namespace SimpleRentalApp.Windows
{
    public partial class SimpleCleaningTaxPaymentDialog : Window
    {
        private readonly Property _property;
        private readonly int _selectedYear;
        private readonly RentalDbContext _context;
        private Customer? _customer;

        public new bool DialogResult { get; private set; }

        public SimpleCleaningTaxPaymentDialog(Property property, int selectedYear)
        {
            InitializeComponent();
            _property = property;
            _selectedYear = selectedYear;
            _context = new RentalDbContext();
            InitializeDialog();
        }

        private async void InitializeDialog()
        {
            try
            {
                await LoadPropertyData();
                SetupPaymentMethods();
                SetupEventHandlers();
                SetDefaultValues();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadPropertyData()
        {
            // جلب المكتري من العقد (أولوية للعقد النشط، ثم أحدث عقد)
            try
            {
                System.Diagnostics.Debug.WriteLine($"=== SimpleCleaningTaxPaymentDialog LoadPropertyData START ===");
                System.Diagnostics.Debug.WriteLine($"Property ID: {_property.Id}, Property Name: {_property.Name}");

                Contract? selectedContract = null;

                // أولاً: البحث عن عقد نشط (IsActive = true)
                var activeContracts = await _context.Contracts
                    .Include(c => c.Customer)
                    .Where(c => c.PropertyId == _property.Id && c.IsActive)
                    .ToListAsync();

                System.Diagnostics.Debug.WriteLine($"Found {activeContracts.Count} active contracts for property {_property.Id}");
                selectedContract = activeContracts
                    .OrderByDescending(c => c.StartDate)
                    .FirstOrDefault();

                // ثانياً: إذا لم يوجد عقد نشط، البحث عن عقد ضمن التواريخ الصحيحة
                if (selectedContract == null)
                {
                    System.Diagnostics.Debug.WriteLine("No active contract found, searching for contracts within date range...");
                    var dateRangeContracts = await _context.Contracts
                        .Include(c => c.Customer)
                        .Where(c => c.PropertyId == _property.Id &&
                               c.StartDate <= DateTime.Now &&
                               c.EndDate >= DateTime.Now)
                        .ToListAsync();

                    selectedContract = dateRangeContracts
                        .OrderByDescending(c => c.StartDate)
                        .FirstOrDefault();
                }

                // ثالثاً: إذا لم يوجد، جلب أحدث عقد للمحل
                if (selectedContract == null)
                {
                    System.Diagnostics.Debug.WriteLine("No contract within date range found, searching for any contract...");
                    var allContracts = await _context.Contracts
                        .Include(c => c.Customer)
                        .Where(c => c.PropertyId == _property.Id)
                        .ToListAsync();

                    System.Diagnostics.Debug.WriteLine($"Found {allContracts.Count} total contracts for property");
                    selectedContract = allContracts
                        .OrderByDescending(c => c.StartDate)
                        .FirstOrDefault();
                }

                if (selectedContract != null && selectedContract.Customer != null)
                {
                    _customer = selectedContract.Customer;
                    System.Diagnostics.Debug.WriteLine($"SUCCESS: Found customer from contract - Customer: {_customer.FullName}, Contract ID: {selectedContract.Id}, IsActive: {selectedContract.IsActive}");
                }
                else
                {
                    _customer = null;
                    System.Diagnostics.Debug.WriteLine($"WARNING: No valid contract or customer found for property {_property.Name}");

                    // كحل أخير، جرب جلب المكتري من المحل مباشرة
                    if (_property.CustomerId.HasValue)
                    {
                        System.Diagnostics.Debug.WriteLine($"Trying fallback: Getting customer from property.CustomerId = {_property.CustomerId.Value}");
                        _customer = await DatabaseService.Instance.GetCustomerByIdAsync(_property.CustomerId.Value);
                        if (_customer != null)
                        {
                            System.Diagnostics.Debug.WriteLine($"FALLBACK SUCCESS: Customer from Property: {_customer.FullName}");
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"=== SimpleCleaningTaxPaymentDialog LoadPropertyData END - Final Customer: {_customer?.FullName ?? "NULL"} ===");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading customer from contract: {ex.Message}");
                _customer = null;

                // كحل أخير في حالة الخطأ، جرب جلب المكتري من المحل
                try
                {
                    if (_property.CustomerId.HasValue)
                    {
                        _customer = await DatabaseService.Instance.GetCustomerByIdAsync(_property.CustomerId.Value);
                    }
                }
                catch (Exception fallbackEx)
                {
                    System.Diagnostics.Debug.WriteLine($"Fallback customer loading also failed: {fallbackEx.Message}");
                }
            }

            // Update UI
            PropertyNameText.Text = _property.Name;
            CustomerNameText.Text = _customer?.FullName ?? "غير محدد";
            YearText.Text = _selectedYear.ToString();
        }

        private void SetupPaymentMethods()
        {
            var paymentMethods = new List<string>
            {
                "نقداً",
                "شيك",
                "تحويل بنكي",
                "بطاقة ائتمان",
                "أخرى"
            };

            PaymentMethodComboBox.ItemsSource = paymentMethods;
            PaymentMethodComboBox.SelectedIndex = 0;
        }

        private void SetupEventHandlers()
        {
            // Add text changed event for amount
            AmountTextBox.TextChanged += (s, e) => UpdateAmountInWords();
        }

        private void SetDefaultValues()
        {
            PaymentDatePicker.SelectedDate = DateTime.Now;

            // Set default amount based on property's cleaning tax
            var annualTax = _property.MonthlyRent * 12 * 0.105m;
            AmountTextBox.Text = annualTax.ToString("F2");
            UpdateAmountInWords();

            // Generate default receipt number
            GenerateReceiptNumber();

            // Update hint text
            UpdateReceiptNumberHint();
        }

        private async void GenerateReceiptNumber()
        {
            try
            {
                // Use tax year from the selected year in the parent window
                var taxYear = _selectedYear;
                var nextNumber = await GetNextReceiptNumberForProperty(_property.Id, taxYear);
                ReceiptNumberTextBox.Text = nextNumber;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error generating receipt number: {ex.Message}");
                var currentYear = DateTime.Now.Year;
                ReceiptNumberTextBox.Text = $"TSC{currentYear}-001";
            }
        }

        /// <summary>
        /// Gets the next sequential receipt number for a specific property and tax year
        /// Each property has its own sequential numbering per tax year
        /// </summary>
        /// <param name="propertyId">Property ID</param>
        /// <param name="taxYear">Tax year for the payment</param>
        /// <returns>Next receipt number in format TSC{taxYear}-{number}</returns>
        private async Task<string> GetNextReceiptNumberForProperty(int propertyId, int taxYear)
        {
            try
            {
                // Get all cleaning tax receipts for this specific property and tax year
                var allReceipts = await DatabaseService.Instance.GetCleaningTaxReceiptsAsync();
                var propertyReceipts = allReceipts
                    .Where(r => r.PropertyId == propertyId && r.TaxYear == taxYear)
                    .ToList();

                // Also check payments for backward compatibility
                var allPayments = await DatabaseService.Instance.GetPaymentsAsync();
                var propertyPayments = allPayments
                    .Where(p => p.PropertyId == propertyId &&
                               ((p.TaxYear.HasValue && p.TaxYear.Value == taxYear) ||
                                (!p.TaxYear.HasValue && p.PaymentDate?.Year == taxYear)) &&
                               !string.IsNullOrEmpty(p.ReceiptNumber))
                    .ToList();

                var maxReceiptNumber = 0;
                var yearPrefix = $"TSC{taxYear}-";

                // Check existing cleaning tax receipts
                foreach (var receipt in propertyReceipts)
                {
                    if (!string.IsNullOrEmpty(receipt.ReceiptNumber) &&
                        receipt.ReceiptNumber.StartsWith(yearPrefix))
                    {
                        var numberPart = receipt.ReceiptNumber.Substring(yearPrefix.Length);
                        if (int.TryParse(numberPart, out int receiptNum))
                        {
                            maxReceiptNumber = Math.Max(maxReceiptNumber, receiptNum);
                        }
                    }
                }

                // Check existing payments for backward compatibility
                foreach (var payment in propertyPayments)
                {
                    if (!string.IsNullOrEmpty(payment.ReceiptNumber) &&
                        payment.ReceiptNumber.StartsWith(yearPrefix))
                    {
                        var numberPart = payment.ReceiptNumber.Substring(yearPrefix.Length);
                        if (int.TryParse(numberPart, out int receiptNum))
                        {
                            maxReceiptNumber = Math.Max(maxReceiptNumber, receiptNum);
                        }
                    }
                }

                var nextNumber = maxReceiptNumber + 1;
                return $"TSC{taxYear}-{nextNumber:D3}";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting next receipt number: {ex.Message}");
                return $"TSC{taxYear}-001";
            }
        }

        /// <summary>
        /// Check if receipt number already exists for this property and tax year
        /// </summary>
        private async Task<bool> IsReceiptNumberDuplicate(string receiptNumber, int propertyId, int taxYear)
        {
            try
            {
                // Check in cleaning tax receipts
                var allReceipts = await DatabaseService.Instance.GetCleaningTaxReceiptsAsync();
                var duplicateInReceipts = allReceipts.Any(r =>
                    r.PropertyId == propertyId &&
                    r.TaxYear == taxYear &&
                    r.ReceiptNumber.Equals(receiptNumber, StringComparison.OrdinalIgnoreCase));

                if (duplicateInReceipts)
                    return true;

                // Check in payments for backward compatibility
                var allPayments = await DatabaseService.Instance.GetPaymentsAsync();
                var duplicateInPayments = allPayments.Any(p =>
                    p.PropertyId == propertyId &&
                    ((p.TaxYear.HasValue && p.TaxYear.Value == taxYear) ||
                     (!p.TaxYear.HasValue && p.PaymentDate?.Year == taxYear)) &&
                    !string.IsNullOrEmpty(p.ReceiptNumber) &&
                    p.ReceiptNumber.Equals(receiptNumber, StringComparison.OrdinalIgnoreCase));

                return duplicateInPayments;
            }
            catch
            {
                return false; // If we can't check, assume it's not duplicate
            }
        }

        /// <summary>
        /// Validates that the receipt number is unique globally (not just for this property)
        /// </summary>
        /// <param name="receiptNumber">Receipt number to validate</param>
        /// <returns>True if unique, false if duplicate</returns>
        private async Task<bool> IsReceiptNumberUniqueAsync(string receiptNumber)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(receiptNumber))
                    return false;

                var allPayments = await DatabaseService.Instance.GetPaymentsAsync();

                return !allPayments.Any(p =>
                    !string.IsNullOrEmpty(p.ReceiptNumber) &&
                    p.ReceiptNumber.Equals(receiptNumber.Trim(), StringComparison.OrdinalIgnoreCase));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error validating receipt number: {ex.Message}");
                return false;
            }
        }

        private void UpdateAmountInWords()
        {
            try
            {
                if (decimal.TryParse(AmountTextBox.Text, out decimal amount) && amount > 0)
                {
                    AmountInWordsTextBox.Text = NumberToArabicWords.ConvertCurrencyToWords(amount);
                }
                else
                {
                    AmountInWordsTextBox.Text = "";
                }
            }
            catch
            {
                AmountInWordsTextBox.Text = "";
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                // التحقق من وجود المكتري من العقد
                if (_customer == null)
                {
                    MessageBox.Show("لا يمكن إضافة دفعة ضريبة النظافة لهذا المحل.\n\nالأسباب المحتملة:\n• لا يوجد عقد نشط للمحل\n• لا يوجد مكتري مرتبط بالعقد\n• لا يوجد مكتري مسجل للمحل\n\nيرجى التحقق من:\n1. وجود عقد نشط للمحل\n2. ربط مكتري بالعقد\n3. تسجيل مكتري للمحل",
                        "لا يمكن العثور على مكتري", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Use the tax year from the selected year in the management window
                var taxYear = _selectedYear;

                // Validate and prepare receipt number
                var receiptNumber = ReceiptNumberTextBox.Text?.Trim();
                if (string.IsNullOrEmpty(receiptNumber))
                {
                    // Generate automatic receipt number if empty
                    receiptNumber = await GetNextReceiptNumberForProperty(_property.Id, taxYear);
                }
                else
                {
                    // Validate format if manually entered
                    if (!receiptNumber.StartsWith($"TSC{taxYear}-"))
                    {
                        MessageBox.Show($"رقم التوصيل يجب أن يكون بالصيغة: TSC{taxYear}-XXX\nمثال: TSC{taxYear}-001",
                            "صيغة خاطئة", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }

                    // Check if receipt number already exists for this property and tax year
                    if (await IsReceiptNumberDuplicate(receiptNumber, _property.Id, taxYear))
                    {
                        var result = MessageBox.Show(
                            $"⚠️ رقم التوصيل '{receiptNumber}' موجود مسبقاً لمحل '{_property.Name}' في السنة الضريبية {taxYear}.\n\n" +
                            "هل تريد إنشاء رقم جديد تلقائياً؟\n\n" +
                            "• اختر 'نعم' لإنشاء رقم جديد تلقائياً\n" +
                            "• اختر 'لا' للعودة وتعديل الرقم يدوياً",
                            "رقم مكرر لنفس المحل والسنة",
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Warning);

                        if (result == MessageBoxResult.Yes)
                        {
                            try
                            {
                                receiptNumber = await GetNextReceiptNumberForProperty(_property.Id, taxYear);
                                ReceiptNumberTextBox.Text = receiptNumber;
                                ReceiptNumberHintText.Text = $"✅ تم إنشاء رقم جديد: {receiptNumber}";
                            }
                            catch (Exception ex)
                            {
                                MessageBox.Show($"خطأ في إنشاء رقم جديد: {ex.Message}", "خطأ",
                                    MessageBoxButton.OK, MessageBoxImage.Error);
                                return;
                            }
                        }
                        else
                        {
                            ReceiptNumberTextBox.Focus();
                            ReceiptNumberTextBox.SelectAll();
                            return;
                        }
                    }
                }

                var payment = new Payment
                {
                    PropertyId = _property.Id,
                    CustomerId = _customer.Id,
                    Type = PaymentType.CleaningTax,
                    RentAmount = 0, // لا يوجد إيجار في دفعة ضريبة النظافة
                    CleaningTaxAmount = decimal.Parse(AmountTextBox.Text),
                    TaxYear = taxYear, // حفظ السنة الضريبية
                    DueDate = PaymentDatePicker.SelectedDate ?? DateTime.Now,
                    PaymentDate = PaymentDatePicker.SelectedDate ?? DateTime.Now,
                    PaymentMethod = PaymentMethodComboBox.SelectedItem?.ToString() ?? "نقداً",
                    Notes = NotesTextBox.Text?.Trim() ?? "",
                    ReceiptNumber = receiptNumber,
                    Status = PaymentStatus.Paid,
                    CreatedDate = DateTime.Now,
                    UpdatedDate = DateTime.Now
                };

                await DatabaseService.Instance.AddPaymentAsync(payment);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                var errorMessage = "خطأ في حفظ الدفعة:\n\n";

                if (ex.InnerException != null)
                {
                    errorMessage += $"التفاصيل: {ex.InnerException.Message}\n\n";
                }
                else
                {
                    errorMessage += $"الخطأ: {ex.Message}\n\n";
                }

                errorMessage += "يرجى التحقق من:\n";
                errorMessage += "• وجود مكتري مرتبط بالمحل\n";
                errorMessage += "• صحة البيانات المدخلة\n";
                errorMessage += "• الاتصال بقاعدة البيانات";

                MessageBox.Show(errorMessage, "خطأ في حفظ الدفعة",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            var validationErrors = new List<string>();

            // Validate payment date
            if (!PaymentDatePicker.SelectedDate.HasValue)
            {
                validationErrors.Add("• يرجى اختيار تاريخ الدفع");
            }
            else if (PaymentDatePicker.SelectedDate.Value > DateTime.Now)
            {
                validationErrors.Add("• تاريخ الدفع لا يمكن أن يكون في المستقبل");
            }

            // Validate amount
            if (string.IsNullOrWhiteSpace(AmountTextBox.Text))
            {
                validationErrors.Add("• يرجى إدخال مبلغ الدفعة");
            }
            else if (!decimal.TryParse(AmountTextBox.Text, out decimal amount))
            {
                validationErrors.Add("• المبلغ المدخل غير صحيح");
            }
            else if (amount <= 0)
            {
                validationErrors.Add("• المبلغ يجب أن يكون أكبر من صفر");
            }

            // Validate payment method
            if (PaymentMethodComboBox.SelectedItem == null)
            {
                validationErrors.Add("• يرجى اختيار طريقة الدفع");
            }

            // Show validation errors if any
            if (validationErrors.Count > 0)
            {
                var errorMessage = "يرجى تصحيح الأخطاء التالية:\n\n" + string.Join("\n", validationErrors);
                MessageBox.Show(errorMessage, "تحقق من البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        private async Task<string> GenerateUniqueReceiptNumber()
        {
            try
            {
                var currentYear = DateTime.Now.Year;
                var baseNumber = $"CT{currentYear}";

                // Get existing receipt numbers for this year
                var existingPayments = await DatabaseService.Instance.GetPaymentsAsync();
                var existingNumbers = existingPayments
                    .Where(p => !string.IsNullOrEmpty(p.ReceiptNumber) && p.ReceiptNumber.StartsWith(baseNumber))
                    .Select(p => p.ReceiptNumber)
                    .ToList();

                // Find next available number
                int counter = 1;
                string newReceiptNumber;
                do
                {
                    newReceiptNumber = $"{baseNumber}-{counter:D4}";
                    counter++;
                } while (existingNumbers.Contains(newReceiptNumber) && counter < 10000);

                return newReceiptNumber;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error generating unique receipt number: {ex.Message}");
                return $"CT{DateTime.Now:yyyyMMddHHmm}";
            }
        }

        private async Task<bool> IsReceiptNumberDuplicate(string receiptNumber)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(receiptNumber))
                    return false;

                var existingPayments = await DatabaseService.Instance.GetPaymentsAsync();
                return existingPayments.Any(p =>
                    !string.IsNullOrEmpty(p.ReceiptNumber) &&
                    p.ReceiptNumber.Equals(receiptNumber.Trim(), StringComparison.OrdinalIgnoreCase));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking receipt number duplicate: {ex.Message}");
                return false; // في حالة الخطأ، نسمح بالمتابعة
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            base.OnClosed(e);
        }

        private void ReceiptNumberTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateReceiptNumberHint();
        }

        private async void UpdateReceiptNumberHint()
        {
            try
            {
                var nextNumber = await GetNextReceiptNumberForProperty(_property.Id, _selectedYear);
                var currentText = ReceiptNumberTextBox.Text.Trim();

                if (string.IsNullOrEmpty(currentText) || currentText == nextNumber)
                {
                    ReceiptNumberHintText.Text = $"الرقم التالي المتاح لمحل '{_property.Name}' للسنة {_selectedYear}: {nextNumber}";
                }
                else
                {
                    ReceiptNumberHintText.Text = $"رقم مخصص. الرقم التالي المتاح: {nextNumber}";
                }
            }
            catch
            {
                ReceiptNumberHintText.Text = "الرقم التالي المتاح لهذا المحل سيتم إنشاؤه تلقائياً";
            }
        }

        private async void GenerateReceiptNumber_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var newReceiptNumber = await GetNextReceiptNumberForProperty(_property.Id, _selectedYear);
                ReceiptNumberTextBox.Text = newReceiptNumber;
                ReceiptNumberTextBox.Focus();
                ReceiptNumberTextBox.SelectAll();

                // Show confirmation
                ReceiptNumberHintText.Text = $"تم إنشاء رقم جديد لمحل '{_property.Name}': {newReceiptNumber}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء رقم التوصيل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void GenerateNewNumberBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    $"هل تريد إنشاء رقم توصيل جديد تلقائياً لمحل '{_property.Name}' للسنة الضريبية {_selectedYear}؟\n\nسيتم استبدال الرقم الحالي بالرقم التالي المتاح.",
                    "إنشاء رقم جديد",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    var newReceiptNumber = await GetNextReceiptNumberForProperty(_property.Id, _selectedYear);
                    ReceiptNumberTextBox.Text = newReceiptNumber;
                    ReceiptNumberTextBox.Focus();
                    ReceiptNumberTextBox.SelectAll();

                    // Show success message
                    ReceiptNumberHintText.Text = $"✅ تم إنشاء رقم جديد: {newReceiptNumber}";
                    ReceiptNumberHintText.Foreground = new SolidColorBrush(Colors.Green);

                    // Reset color after 3 seconds
                    var timer = new System.Windows.Threading.DispatcherTimer();
                    timer.Interval = TimeSpan.FromSeconds(3);
                    timer.Tick += (s, args) =>
                    {
                        ReceiptNumberHintText.Foreground = new SolidColorBrush(Colors.Gray);
                        timer.Stop();
                    };
                    timer.Start();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء رقم التوصيل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
