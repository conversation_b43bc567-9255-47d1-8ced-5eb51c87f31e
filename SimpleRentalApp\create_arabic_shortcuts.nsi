; Create Arabic shortcuts after installation
; إنشاء اختصارات عربية بعد التثبيت

Unicode True

; Variables
!define APPNAME "Rental Management System"
!define APPNAME_AR "تدبير الكراء"

; Function to create Arabic shortcuts
Function CreateArabicShortcuts
    ; Create Arabic Start Menu folder
    CreateDirectory "$SMPROGRAMS\${APPNAME_AR}"
    
    ; Create Arabic shortcuts
    CreateShortCut "$SMPROGRAMS\${APPNAME_AR}\${APPNAME_AR}.lnk" "$INSTDIR\SimpleRentalApp.exe" "" "$INSTDIR\SimpleRentalApp.exe" 0
    CreateShortCut "$SMPROGRAMS\${APPNAME_AR}\إلغاء التثبيت.lnk" "$INSTDIR\uninstall.exe" "" "$INSTDIR\uninstall.exe" 0
    CreateShortCut "$SMPROGRAMS\${APPNAME_AR}\دليل المستخدم.lnk" "$INSTDIR\USER_GUIDE.txt"
    
    ; Create Arabic desktop shortcut
    CreateShortCut "$DESKTOP\${APPNAME_AR}.lnk" "$INSTDIR\SimpleRentalApp.exe" "" "$INSTDIR\SimpleRentalApp.exe" 0
    
    ; Create info file about Arabic shortcuts
    FileOpen $0 "$INSTDIR\arabic_shortcuts_info.txt" w
    FileWrite $0 "Arabic Shortcuts Created$\r$\n"
    FileWrite $0 "========================$\r$\n"
    FileWrite $0 "Start Menu: $SMPROGRAMS\${APPNAME_AR}$\r$\n"
    FileWrite $0 "Desktop: $DESKTOP\${APPNAME_AR}.lnk$\r$\n"
    FileWrite $0 "Created: $$(Date)$\r$\n"
    FileClose $0
FunctionEnd

; Function to remove Arabic shortcuts
Function RemoveArabicShortcuts
    ; Remove Arabic Start Menu shortcuts
    Delete "$SMPROGRAMS\${APPNAME_AR}\${APPNAME_AR}.lnk"
    Delete "$SMPROGRAMS\${APPNAME_AR}\إلغاء التثبيت.lnk"
    Delete "$SMPROGRAMS\${APPNAME_AR}\دليل المستخدم.lnk"
    RmDir "$SMPROGRAMS\${APPNAME_AR}"
    
    ; Remove Arabic desktop shortcut
    Delete "$DESKTOP\${APPNAME_AR}.lnk"
    
    ; Remove info file
    Delete "$INSTDIR\arabic_shortcuts_info.txt"
FunctionEnd
