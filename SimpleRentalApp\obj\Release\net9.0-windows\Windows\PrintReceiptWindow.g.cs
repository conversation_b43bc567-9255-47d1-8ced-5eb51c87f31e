﻿#pragma checksum "..\..\..\..\Windows\PrintReceiptWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "3AE97BBBB8DBCFABA48CE5093F479B46EC4D3245"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleRentalApp.Windows {
    
    
    /// <summary>
    /// PrintReceiptWindow
    /// </summary>
    public partial class PrintReceiptWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 23 "..\..\..\..\Windows\PrintReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PreviewPrintButton;
        
        #line default
        #line hidden
        
        
        #line 35 "..\..\..\..\Windows\PrintReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintButton;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\..\Windows\PrintReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveAsPdfButton;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\Windows\PrintReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\Windows\PrintReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border ReceiptBorder;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\..\Windows\PrintReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ReceiptContent;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\Windows\PrintReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ReceiptNumberText;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\Windows\PrintReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock IssueDateText;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\..\Windows\PrintReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TenantNameText;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\..\Windows\PrintReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PropertyAddressText;
        
        #line default
        #line hidden
        
        
        #line 183 "..\..\..\..\Windows\PrintReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ContractNumberText;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\..\Windows\PrintReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaymentPeriodText;
        
        #line default
        #line hidden
        
        
        #line 228 "..\..\..\..\Windows\PrintReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaymentDateText;
        
        #line default
        #line hidden
        
        
        #line 238 "..\..\..\..\Windows\PrintReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaymentMethodText;
        
        #line default
        #line hidden
        
        
        #line 248 "..\..\..\..\Windows\PrintReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AmountText;
        
        #line default
        #line hidden
        
        
        #line 264 "..\..\..\..\Windows\PrintReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AmountInWordsText;
        
        #line default
        #line hidden
        
        
        #line 274 "..\..\..\..\Windows\PrintReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border NotesSection;
        
        #line default
        #line hidden
        
        
        #line 286 "..\..\..\..\Windows\PrintReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NotesText;
        
        #line default
        #line hidden
        
        
        #line 330 "..\..\..\..\Windows\PrintReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleRentalApp;V1.0.0.0;component/windows/printreceiptwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\PrintReceiptWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.PreviewPrintButton = ((System.Windows.Controls.Button)(target));
            
            #line 33 "..\..\..\..\Windows\PrintReceiptWindow.xaml"
            this.PreviewPrintButton.Click += new System.Windows.RoutedEventHandler(this.PreviewPrintButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.PrintButton = ((System.Windows.Controls.Button)(target));
            
            #line 45 "..\..\..\..\Windows\PrintReceiptWindow.xaml"
            this.PrintButton.Click += new System.Windows.RoutedEventHandler(this.PrintButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.SaveAsPdfButton = ((System.Windows.Controls.Button)(target));
            
            #line 57 "..\..\..\..\Windows\PrintReceiptWindow.xaml"
            this.SaveAsPdfButton.Click += new System.Windows.RoutedEventHandler(this.SaveAsPdfButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 67 "..\..\..\..\Windows\PrintReceiptWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ReceiptBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 6:
            this.ReceiptContent = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 7:
            this.ReceiptNumberText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.IssueDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.TenantNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.PropertyAddressText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.ContractNumberText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.PaymentPeriodText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.PaymentDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.PaymentMethodText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.AmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.AmountInWordsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.NotesSection = ((System.Windows.Controls.Border)(target));
            return;
            case 18:
            this.NotesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

