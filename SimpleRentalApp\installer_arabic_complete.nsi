; نظام تدبير الكراء - مثبت NSIS
; المطور: حفيظ عبدو
; دعم كامل للغة العربية مع Unicode + UTF-8

; تفعيل دعم Unicode
Unicode true

; تعريف المتغيرات
!define APP_NAME "نظام تدبير الكراء"
!define APP_NAME_EN "RentalManagement"
!define APP_VERSION "1.0.0"
!define APP_PUBLISHER "حفيظ عبدو"
!define APP_DESCRIPTION "تطبيق إدارة العقارات والكراء"
!define APP_EXE "SimpleRentalApp.exe"
!define SOURCE_DIR "bin\Release\net9.0-windows\win-x64"
!define DESKTOP_INSTALLER_DIR "C:\Users\<USER>\Desktop\Installer"

; تضمين المكتبات المطلوبة
!include "MUI2.nsh"
!include "FileFunc.nsh"
!include "LogicLib.nsh"

; الإعدادات العامة
Name "${APP_NAME}"
OutFile "${DESKTOP_INSTALLER_DIR}\تثبيت_نظام_تدبير_الكراء_v${APP_VERSION}.exe"
InstallDir "$PROGRAMFILES64\${APP_NAME_EN}"
InstallDirRegKey HKLM "Software\${APP_NAME_EN}" "InstallDir"
RequestExecutionLevel admin

; إعدادات واجهة المستخدم الحديثة
!define MUI_ABORTWARNING
!define MUI_ICON "app_icon.ico"
!define MUI_UNICON "app_icon.ico"

; تخصيص صفحة الترحيب
!define MUI_WELCOMEPAGE_TITLE "مرحباً بك في مثبت ${APP_NAME}"
!define MUI_WELCOMEPAGE_TEXT "سيقوم هذا المعالج بتثبيت ${APP_NAME} على جهازك.$\r$\n$\r$\nهذا التطبيق يساعدك على إدارة العقارات والمكترين والعقود والمدفوعات بكفاءة.$\r$\n$\r$\nانقر التالي للمتابعة."

; تخصيص صفحة الانتهاء
!define MUI_FINISHPAGE_TITLE "اكتمل تثبيت ${APP_NAME}"
!define MUI_FINISHPAGE_TEXT "تم تثبيت ${APP_NAME} بنجاح على جهازك.$\r$\n$\r$\nيمكنك الآن البدء في إدارة عقاراتك.$\r$\n$\r$\nانقر إنهاء لإغلاق المثبت."
!define MUI_FINISHPAGE_RUN "$INSTDIR\${APP_EXE}"
!define MUI_FINISHPAGE_RUN_TEXT "تشغيل ${APP_NAME} الآن"

; صفحات المثبت
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE_Arabic.txt"
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

; صفحات إلغاء التثبيت
!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

; اللغات
!insertmacro MUI_LANGUAGE "Arabic"
!insertmacro MUI_LANGUAGE "English"

; معلومات الإصدار
VIProductVersion "${APP_VERSION}.0"
VIAddVersionKey /LANG=${LANG_ARABIC} "ProductName" "${APP_NAME}"
VIAddVersionKey /LANG=${LANG_ARABIC} "Comments" "${APP_DESCRIPTION}"
VIAddVersionKey /LANG=${LANG_ARABIC} "CompanyName" "${APP_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_ARABIC} "LegalCopyright" "© 2025 ${APP_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_ARABIC} "FileDescription" "${APP_DESCRIPTION}"
VIAddVersionKey /LANG=${LANG_ARABIC} "FileVersion" "${APP_VERSION}"
VIAddVersionKey /LANG=${LANG_ARABIC} "ProductVersion" "${APP_VERSION}"

VIAddVersionKey /LANG=${LANG_ENGLISH} "ProductName" "Rental Management System"
VIAddVersionKey /LANG=${LANG_ENGLISH} "Comments" "Property Rental Management Application"
VIAddVersionKey /LANG=${LANG_ENGLISH} "CompanyName" "${APP_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "LegalCopyright" "© 2025 ${APP_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "FileDescription" "Property Rental Management Application"
VIAddVersionKey /LANG=${LANG_ENGLISH} "FileVersion" "${APP_VERSION}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "ProductVersion" "${APP_VERSION}"

; قسم التثبيت الرئيسي
Section "التطبيق الرئيسي" SecMain
  SectionIn RO
  
  ; تعيين مجلد الإخراج
  SetOutPath "$INSTDIR"
  
  ; عرض رسالة التقدم
  DetailPrint "جاري نسخ ملفات التطبيق..."
  
  ; نسخ الملف التنفيذي الرئيسي
  File "${SOURCE_DIR}\${APP_EXE}"
  
  ; نسخ جميع ملفات DLL المطلوبة
  File "${SOURCE_DIR}\*.dll"
  
  ; نسخ ملفات التكوين
  File "${SOURCE_DIR}\*.json"
  
  ; نسخ الأيقونة (إذا كانت موجودة)
  IfFileExists "${SOURCE_DIR}\app_icon.ico" 0 +2
  File "${SOURCE_DIR}\app_icon.ico"
  
  ; نسخ مجلدات اللغات
  SetOutPath "$INSTDIR"
  IfFileExists "${SOURCE_DIR}\cs" 0 +2
  File /r "${SOURCE_DIR}\cs"
  IfFileExists "${SOURCE_DIR}\de" 0 +2
  File /r "${SOURCE_DIR}\de"
  IfFileExists "${SOURCE_DIR}\es" 0 +2
  File /r "${SOURCE_DIR}\es"
  IfFileExists "${SOURCE_DIR}\fr" 0 +2
  File /r "${SOURCE_DIR}\fr"
  IfFileExists "${SOURCE_DIR}\it" 0 +2
  File /r "${SOURCE_DIR}\it"
  IfFileExists "${SOURCE_DIR}\ja" 0 +2
  File /r "${SOURCE_DIR}\ja"
  IfFileExists "${SOURCE_DIR}\ko" 0 +2
  File /r "${SOURCE_DIR}\ko"
  IfFileExists "${SOURCE_DIR}\pl" 0 +2
  File /r "${SOURCE_DIR}\pl"
  IfFileExists "${SOURCE_DIR}\pt-BR" 0 +2
  File /r "${SOURCE_DIR}\pt-BR"
  IfFileExists "${SOURCE_DIR}\ru" 0 +2
  File /r "${SOURCE_DIR}\ru"
  IfFileExists "${SOURCE_DIR}\tr" 0 +2
  File /r "${SOURCE_DIR}\tr"
  IfFileExists "${SOURCE_DIR}\zh-Hans" 0 +2
  File /r "${SOURCE_DIR}\zh-Hans"
  IfFileExists "${SOURCE_DIR}\zh-Hant" 0 +2
  File /r "${SOURCE_DIR}\zh-Hant"
  
  ; نسخ الوثائق
  File "README_Arabic.txt"
  File "LICENSE_Arabic.txt"
  
  ; عرض رسالة التقدم
  DetailPrint "جاري إنشاء الاختصارات..."
  
  ; إنشاء مجلد في قائمة البداية
  CreateDirectory "$SMPROGRAMS\${APP_NAME}"
  CreateShortCut "$SMPROGRAMS\${APP_NAME}\${APP_NAME}.lnk" "$INSTDIR\${APP_EXE}" "" "$INSTDIR\${APP_EXE}" 0 SW_SHOWNORMAL "" "${APP_DESCRIPTION}"
  CreateShortCut "$SMPROGRAMS\${APP_NAME}\إلغاء التثبيت.lnk" "$INSTDIR\Uninstall.exe" "" "$INSTDIR\Uninstall.exe" 0 SW_SHOWNORMAL "" "إلغاء تثبيت ${APP_NAME}"

  ; إنشاء اختصار على سطح المكتب
  CreateShortCut "$DESKTOP\${APP_NAME}.lnk" "$INSTDIR\${APP_EXE}" "" "$INSTDIR\${APP_EXE}" 0 SW_SHOWNORMAL "" "${APP_DESCRIPTION}"
  
  ; عرض رسالة التقدم
  DetailPrint "جاري تسجيل التطبيق في النظام..."
  
  ; كتابة معلومات إلغاء التثبيت في السجل
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME_EN}" "DisplayName" "${APP_NAME}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME_EN}" "UninstallString" "$INSTDIR\Uninstall.exe"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME_EN}" "InstallLocation" "$INSTDIR"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME_EN}" "DisplayIcon" "$INSTDIR\${APP_EXE}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME_EN}" "Publisher" "${APP_PUBLISHER}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME_EN}" "DisplayVersion" "${APP_VERSION}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME_EN}" "Comments" "${APP_DESCRIPTION}"
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME_EN}" "NoModify" 1
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME_EN}" "NoRepair" 1
  
  ; حساب حجم التثبيت
  ${GetSize} "$INSTDIR" "/S=0K" $0 $1 $2
  IntFmt $0 "0x%08X" $0
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME_EN}" "EstimatedSize" "$0"
  
  ; إنشاء أداة إلغاء التثبيت
  DetailPrint "جاري إنشاء أداة إلغاء التثبيت..."
  WriteUninstaller "$INSTDIR\Uninstall.exe"
  
  ; حفظ مجلد التثبيت
  WriteRegStr HKLM "Software\${APP_NAME_EN}" "InstallDir" "$INSTDIR"
  
  ; رسالة اكتمال التثبيت
  DetailPrint "تم تثبيت ${APP_NAME} بنجاح!"
SectionEnd

; وصف الأقسام
LangString DESC_SecMain ${LANG_ARABIC} "الملفات الأساسية للتطبيق"
LangString DESC_SecMain ${LANG_ENGLISH} "Essential application files"
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
  !insertmacro MUI_DESCRIPTION_TEXT ${SecMain} $(DESC_SecMain)
!insertmacro MUI_FUNCTION_DESCRIPTION_END

; قسم إلغاء التثبيت
Section "Uninstall"
  ; رسائل التقدم باللغة العربية
  DetailPrint "جاري حذف ملفات التطبيق..."

  ; حذف الملف التنفيذي الرئيسي
  Delete "$INSTDIR\${APP_EXE}"

  ; حذف جميع ملفات DLL
  Delete "$INSTDIR\*.dll"

  ; حذف ملفات التكوين
  Delete "$INSTDIR\*.json"
  Delete "$INSTDIR\*.pdb"

  ; حذف الأيقونة (إذا كانت موجودة)
  Delete "$INSTDIR\app_icon.ico"

  ; حذف الوثائق
  Delete "$INSTDIR\README_Arabic.txt"
  Delete "$INSTDIR\LICENSE_Arabic.txt"

  ; حذف أداة إلغاء التثبيت
  Delete "$INSTDIR\Uninstall.exe"

  DetailPrint "جاري حذف الاختصارات..."

  ; حذف الاختصارات
  Delete "$SMPROGRAMS\${APP_NAME}\${APP_NAME}.lnk"
  Delete "$SMPROGRAMS\${APP_NAME}\إلغاء التثبيت.lnk"
  Delete "$DESKTOP\${APP_NAME}.lnk"

  DetailPrint "جاري حذف المجلدات..."

  ; حذف مجلد الخطوط (إذا كان موجوداً)
  RMDir /r "$INSTDIR\LatoFont"

  ; حذف مجلدات اللغات
  RMDir /r "$INSTDIR\cs"
  RMDir /r "$INSTDIR\de"
  RMDir /r "$INSTDIR\es"
  RMDir /r "$INSTDIR\fr"
  RMDir /r "$INSTDIR\it"
  RMDir /r "$INSTDIR\ja"
  RMDir /r "$INSTDIR\ko"
  RMDir /r "$INSTDIR\pl"
  RMDir /r "$INSTDIR\pt-BR"
  RMDir /r "$INSTDIR\ru"
  RMDir /r "$INSTDIR\tr"
  RMDir /r "$INSTDIR\zh-Hans"
  RMDir /r "$INSTDIR\zh-Hant"

  ; حذف المجلدات
  RMDir "$SMPROGRAMS\${APP_NAME}"
  RMDir "$INSTDIR"

  DetailPrint "جاري إزالة التسجيل من النظام..."

  ; حذف مفاتيح السجل
  DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME_EN}"
  DeleteRegKey HKLM "Software\${APP_NAME_EN}"

  DetailPrint "تم إلغاء تثبيت ${APP_NAME} بنجاح!"
SectionEnd

; دالة تشغيل عند بداية التثبيت
Function .onInit
  ; عرض رسالة ترحيب
  MessageBox MB_YESNO "مرحباً بك في مثبت ${APP_NAME}!$\r$\n$\r$\nسيتم تثبيت نظام إدارة العقارات والكراء على جهازك.$\r$\n$\r$\nهل تريد المتابعة؟" IDYES +2
  Abort
FunctionEnd

; دالة تشغيل عند انتهاء التثبيت بنجاح
Function .onInstSuccess
  ; عرض رسالة نجاح التثبيت
  MessageBox MB_YESNO "تم تثبيت ${APP_NAME} بنجاح!$\r$\n$\r$\nهل تريد تشغيل التطبيق الآن؟" IDNO +2
  ExecShell "open" "$INSTDIR\${APP_EXE}"
FunctionEnd
