using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;
using SimpleRentalApp.Services.CloudSync.Security;
using Newtonsoft.Json;
using Azure.Identity;

namespace SimpleRentalApp.Services.CloudSync.Providers
{
    /// <summary>
    /// خدمة مزامنة Azure SQL Database
    /// </summary>
    public class AzureSqlSyncService : ISyncService
    {
        private SqlConnection? _connection;
        private EncryptionService? _encryptionService;
        private SyncConfiguration? _config;
        private bool _isConnected;
        private DateTime? _lastSyncTime;

        public string ServiceName => "Azure SQL Database";
        public bool IsConnected => _isConnected;
        public DateTime? LastSyncTime => _lastSyncTime;

        public event EventHandler<ConnectionStatusChangedEventArgs>? ConnectionStatusChanged;
        public event EventHandler<SyncProgressEventArgs>? SyncProgress;
        public event EventHandler<SyncCompletedEventArgs>? SyncCompleted;
        public event EventHandler<SyncErrorEventArgs>? SyncError;

        /// <summary>
        /// تهيئة الاتصال بـ Azure SQL Database
        /// </summary>
        public async Task<bool> InitializeAsync(SyncConfiguration config)
        {
            try
            {
                _config = config ?? throw new ArgumentNullException(nameof(config));
                
                if (_config.EnableEncryption && !string.IsNullOrEmpty(_config.EncryptionKey))
                {
                    _encryptionService = new EncryptionService(_config.EncryptionKey);
                }

                // إنشاء اتصال Azure SQL
                _connection = new SqlConnection(_config.ConnectionString);
                
                // اختبار الاتصال
                var testResult = await TestConnectionAsync();
                if (testResult)
                {
                    _isConnected = true;
                    OnConnectionStatusChanged(ConnectionStatus.Disconnected, ConnectionStatus.Connected, "تم الاتصال بنجاح");
                    
                    // إنشاء الجداول إذا لم تكن موجودة
                    await CreateTablesIfNotExistAsync();
                }

                return testResult;
            }
            catch (Exception ex)
            {
                OnSyncError(ex, "فشل في تهيئة الاتصال بـ Azure SQL Database");
                return false;
            }
        }

        /// <summary>
        /// اختبار الاتصال
        /// </summary>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                if (_connection == null) return false;

                await _connection.OpenAsync();
                
                using var command = new SqlCommand("SELECT 1", _connection);
                await command.ExecuteScalarAsync();
                
                await _connection.CloseAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// مزامنة البيانات إلى السحابة
        /// </summary>
        public async Task<SyncResult> SyncToCloudAsync(SyncData data)
        {
            var result = new SyncResult();
            var startTime = DateTime.UtcNow;

            try
            {
                if (_connection == null || !_isConnected)
                {
                    result.Message = "غير متصل بالخدمة السحابية";
                    return result;
                }

                OnSyncProgress(1, 0, "بدء المزامنة", data.EntityType);

                // تشفير البيانات إذا كان مفعل
                var jsonData = data.JsonData;
                if (_encryptionService != null)
                {
                    jsonData = await _encryptionService.EncryptAsync(data.JsonData);
                }

                await _connection.OpenAsync();

                // تنفيذ العملية حسب النوع
                switch (data.Operation)
                {
                    case SyncOperation.Create:
                        await InsertSyncDataAsync(data, jsonData);
                        break;
                    case SyncOperation.Update:
                        await UpdateSyncDataAsync(data, jsonData);
                        break;
                    case SyncOperation.Delete:
                        await DeleteSyncDataAsync(data);
                        break;
                }

                await _connection.CloseAsync();

                OnSyncProgress(1, 1, "اكتملت المزامنة", data.EntityType);

                result.Success = true;
                result.SyncedData.Add(data);
                result.SuccessfulRecords = 1;
                result.TotalRecords = 1;
                result.Message = "تمت المزامنة بنجاح";
                _lastSyncTime = DateTime.UtcNow;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"فشل في المزامنة: {ex.Message}";
                result.FailedRecords = 1;
                result.TotalRecords = 1;
                OnSyncError(ex, "فشل في مزامنة البيانات إلى السحابة", data.EntityType, data.EntityId);
            }
            finally
            {
                if (_connection?.State == ConnectionState.Open)
                    await _connection.CloseAsync();
                    
                result.Duration = DateTime.UtcNow - startTime;
                OnSyncCompleted(result);
            }

            return result;
        }

        /// <summary>
        /// مزامنة البيانات من السحابة
        /// </summary>
        public async Task<SyncResult> SyncFromCloudAsync(DateTime? lastSyncTime = null)
        {
            var result = new SyncResult();
            var startTime = DateTime.UtcNow;

            try
            {
                if (_connection == null || !_isConnected)
                {
                    result.Message = "غير متصل بالخدمة السحابية";
                    return result;
                }

                var syncTime = lastSyncTime ?? _lastSyncTime ?? DateTime.UtcNow.AddDays(-30);
                
                OnSyncProgress(0, 0, "جاري جلب البيانات من السحابة", "");

                await _connection.OpenAsync();

                // جلب البيانات المحدثة
                var query = @"
                    SELECT EntityType, EntityId, JsonData, Operation, Timestamp, UserId, DeviceId, Hash, Metadata
                    FROM SyncData 
                    WHERE Timestamp > @LastSyncTime 
                    ORDER BY Timestamp ASC";

                using var command = new SqlCommand(query, _connection);
                command.Parameters.AddWithValue("@LastSyncTime", syncTime);

                using var reader = await command.ExecuteReaderAsync();
                var syncedData = new List<SyncData>();

                while (await reader.ReadAsync())
                {
                    try
                    {
                        // فك تشفير البيانات إذا كان مفعل
                        var jsonData = reader.GetString("JsonData");
                        if (_encryptionService != null)
                        {
                            jsonData = await _encryptionService.DecryptAsync(jsonData);
                        }

                        var syncData = new SyncData
                        {
                            EntityType = reader.GetString("EntityType"),
                            EntityId = reader.GetString("EntityId"),
                            JsonData = jsonData,
                            Operation = Enum.Parse<SyncOperation>(reader.GetString("Operation")),
                            Timestamp = reader.GetDateTime("Timestamp"),
                            UserId = reader.IsDBNull("UserId") ? "" : reader.GetString("UserId"),
                            DeviceId = reader.IsDBNull("DeviceId") ? "" : reader.GetString("DeviceId"),
                            Hash = reader.IsDBNull("Hash") ? "" : reader.GetString("Hash"),
                            Metadata = reader.IsDBNull("Metadata") || string.IsNullOrEmpty(reader.GetString("Metadata"))
                                ? new Dictionary<string, object>() 
                                : JsonConvert.DeserializeObject<Dictionary<string, object>>(reader.GetString("Metadata")) ?? new()
                        };

                        syncedData.Add(syncData);
                        result.SuccessfulRecords++;
                        
                        OnSyncProgress(result.TotalRecords, result.SuccessfulRecords, "معالجة البيانات", syncData.EntityType);
                    }
                    catch (Exception ex)
                    {
                        result.FailedRecords++;
                        OnSyncError(ex, "فشل في معالجة عنصر البيانات");
                    }
                }

                await _connection.CloseAsync();

                result.SyncedData = syncedData;
                result.TotalRecords = syncedData.Count;
                result.Success = true;
                result.Message = $"تم جلب {result.SuccessfulRecords} عنصر من السحابة";
                _lastSyncTime = DateTime.UtcNow;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"فشل في جلب البيانات من السحابة: {ex.Message}";
                OnSyncError(ex, "فشل في مزامنة البيانات من السحابة");
            }
            finally
            {
                if (_connection?.State == ConnectionState.Open)
                    await _connection.CloseAsync();
                    
                result.Duration = DateTime.UtcNow - startTime;
                OnSyncCompleted(result);
            }

            return result;
        }

        /// <summary>
        /// حل التعارضات
        /// </summary>
        public async Task<ConflictResolutionResult> ResolveConflictsAsync(List<SyncConflict> conflicts)
        {
            var result = new ConflictResolutionResult();

            try
            {
                foreach (var conflict in conflicts)
                {
                    switch (_config?.ConflictResolution)
                    {
                        case ConflictResolutionStrategy.ServerWins:
                            // استخدام البيانات من السيرفر
                            await SyncToCloudAsync(conflict.RemoteData);
                            conflict.IsResolved = true;
                            conflict.Resolution = "تم اختيار بيانات السيرفر";
                            break;

                        case ConflictResolutionStrategy.ClientWins:
                            // استخدام البيانات المحلية
                            await SyncToCloudAsync(conflict.LocalData);
                            conflict.IsResolved = true;
                            conflict.Resolution = "تم اختيار البيانات المحلية";
                            break;

                        case ConflictResolutionStrategy.Manual:
                            // يتطلب تدخل يدوي
                            result.UnresolvedConflicts.Add(conflict);
                            continue;

                        default:
                            result.UnresolvedConflicts.Add(conflict);
                            continue;
                    }

                    conflict.ResolvedAt = DateTime.UtcNow;
                    result.ResolvedConflicts.Add(conflict);
                }

                result.Success = result.ResolvedConflicts.Any();
                result.Message = $"تم حل {result.ResolvedConflicts.Count} تعارض من أصل {conflicts.Count}";
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"فشل في حل التعارضات: {ex.Message}";
                OnSyncError(ex, "فشل في حل التعارضات");
            }

            return result;
        }

        /// <summary>
        /// حذف البيانات من السحابة
        /// </summary>
        public async Task<bool> DeleteFromCloudAsync(string entityType, string entityId)
        {
            try
            {
                if (_connection == null || !_isConnected) return false;

                await _connection.OpenAsync();

                var query = "DELETE FROM SyncData WHERE EntityType = @EntityType AND EntityId = @EntityId";
                using var command = new SqlCommand(query, _connection);
                command.Parameters.AddWithValue("@EntityType", entityType);
                command.Parameters.AddWithValue("@EntityId", entityId);

                await command.ExecuteNonQueryAsync();
                await _connection.CloseAsync();

                return true;
            }
            catch (Exception ex)
            {
                OnSyncError(ex, "فشل في حذف البيانات من السحابة", entityType, entityId);
                return false;
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية كاملة
        /// </summary>
        public async Task<BackupResult> CreateFullBackupAsync()
        {
            var result = new BackupResult();
            var startTime = DateTime.UtcNow;

            try
            {
                if (_connection == null || !_isConnected)
                {
                    result.Message = "غير متصل بالخدمة السحابية";
                    return result;
                }

                await _connection.OpenAsync();

                // جلب جميع البيانات
                var query = "SELECT * FROM SyncData";
                using var command = new SqlCommand(query, _connection);
                using var reader = await command.ExecuteReaderAsync();

                var allData = new List<object>();
                while (await reader.ReadAsync())
                {
                    var data = new
                    {
                        EntityType = reader.GetString("EntityType"),
                        EntityId = reader.GetString("EntityId"),
                        JsonData = reader.GetString("JsonData"),
                        Operation = reader.GetString("Operation"),
                        Timestamp = reader.GetDateTime("Timestamp"),
                        UserId = reader.IsDBNull("UserId") ? null : reader.GetString("UserId"),
                        DeviceId = reader.IsDBNull("DeviceId") ? null : reader.GetString("DeviceId"),
                        Hash = reader.IsDBNull("Hash") ? null : reader.GetString("Hash"),
                        Metadata = reader.IsDBNull("Metadata") ? null : reader.GetString("Metadata")
                    };
                    allData.Add(data);
                }

                await reader.CloseAsync();

                if (allData.Any())
                {
                    var backupData = new
                    {
                        CreatedAt = DateTime.UtcNow,
                        Version = "1.0",
                        RecordCount = allData.Count,
                        Data = allData
                    };

                    var backupJson = JsonConvert.SerializeObject(backupData);

                    // تشفير النسخة الاحتياطية
                    if (_encryptionService != null)
                    {
                        backupJson = await _encryptionService.EncryptAsync(backupJson);
                    }

                    var backupId = Guid.NewGuid().ToString();

                    // حفظ النسخة الاحتياطية
                    var insertQuery = @"
                        INSERT INTO Backups (Id, Name, CreatedAt, Size, Data, Type, Version)
                        VALUES (@Id, @Name, @CreatedAt, @Size, @Data, @Type, @Version)";

                    using var insertCommand = new SqlCommand(insertQuery, _connection);
                    insertCommand.Parameters.AddWithValue("@Id", backupId);
                    insertCommand.Parameters.AddWithValue("@Name", $"نسخة احتياطية {DateTime.Now:yyyy-MM-dd HH:mm}");
                    insertCommand.Parameters.AddWithValue("@CreatedAt", DateTime.UtcNow);
                    insertCommand.Parameters.AddWithValue("@Size", System.Text.Encoding.UTF8.GetByteCount(backupJson));
                    insertCommand.Parameters.AddWithValue("@Data", backupJson);
                    insertCommand.Parameters.AddWithValue("@Type", BackupType.Full.ToString());
                    insertCommand.Parameters.AddWithValue("@Version", "1.0");

                    await insertCommand.ExecuteNonQueryAsync();

                    result.Success = true;
                    result.BackupId = backupId;
                    result.Size = System.Text.Encoding.UTF8.GetByteCount(backupJson);
                    result.RecordCount = allData.Count;
                    result.Message = "تم إنشاء النسخة الاحتياطية بنجاح";
                }
                else
                {
                    result.Success = false;
                    result.Message = "لا توجد بيانات للنسخ الاحتياطي";
                }

                await _connection.CloseAsync();
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"فشل في إنشاء النسخة الاحتياطية: {ex.Message}";
                OnSyncError(ex, "فشل في إنشاء النسخة الاحتياطية");
            }
            finally
            {
                if (_connection?.State == ConnectionState.Open)
                    await _connection.CloseAsync();
                result.Duration = DateTime.UtcNow - startTime;
            }

            return result;
        }

        /// <summary>
        /// استعادة من النسخة الاحتياطية
        /// </summary>
        public async Task<RestoreResult> RestoreFromBackupAsync(string backupId)
        {
            var result = new RestoreResult { BackupId = backupId };
            var startTime = DateTime.UtcNow;

            try
            {
                if (_connection == null || !_isConnected)
                {
                    result.Message = "غير متصل بالخدمة السحابية";
                    return result;
                }

                await _connection.OpenAsync();

                // جلب النسخة الاحتياطية
                var query = "SELECT Data FROM Backups WHERE Id = @BackupId";
                using var command = new SqlCommand(query, _connection);
                command.Parameters.AddWithValue("@BackupId", backupId);

                var backupData = await command.ExecuteScalarAsync() as string;

                if (string.IsNullOrEmpty(backupData))
                {
                    result.Message = "النسخة الاحتياطية غير موجودة";
                    return result;
                }

                // فك تشفير البيانات
                if (_encryptionService != null)
                {
                    backupData = await _encryptionService.DecryptAsync(backupData);
                }

                var backup = JsonConvert.DeserializeObject<dynamic>(backupData);

                // هنا يمكن إضافة منطق استعادة البيانات
                // يتطلب تنسيق مع قاعدة البيانات المحلية

                await _connection.CloseAsync();

                result.Success = true;
                result.Message = "تم استعادة النسخة الاحتياطية بنجاح";
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"فشل في استعادة النسخة الاحتياطية: {ex.Message}";
                OnSyncError(ex, "فشل في استعادة النسخة الاحتياطية");
            }
            finally
            {
                if (_connection?.State == ConnectionState.Open)
                    await _connection.CloseAsync();
                result.Duration = DateTime.UtcNow - startTime;
            }

            return result;
        }

        /// <summary>
        /// الحصول على قائمة النسخ الاحتياطية
        /// </summary>
        public async Task<List<BackupInfo>> GetBackupsAsync()
        {
            var backups = new List<BackupInfo>();

            try
            {
                if (_connection == null || !_isConnected) return backups;

                await _connection.OpenAsync();

                var query = "SELECT Id, Name, CreatedAt, Size, Type, Version FROM Backups ORDER BY CreatedAt DESC";
                using var command = new SqlCommand(query, _connection);
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    backups.Add(new BackupInfo
                    {
                        Id = reader.GetString("Id"),
                        Name = reader.GetString("Name"),
                        CreatedAt = reader.GetDateTime("CreatedAt"),
                        Size = reader.GetInt64("Size"),
                        Type = Enum.Parse<BackupType>(reader.GetString("Type")),
                        Version = reader.GetString("Version")
                    });
                }

                await _connection.CloseAsync();
            }
            catch (Exception ex)
            {
                OnSyncError(ex, "فشل في جلب قائمة النسخ الاحتياطية");
            }

            return backups;
        }

        /// <summary>
        /// قطع الاتصال
        /// </summary>
        public async Task DisconnectAsync()
        {
            try
            {
                if (_connection != null)
                {
                    if (_connection.State == ConnectionState.Open)
                        await _connection.CloseAsync();

                    await _connection.DisposeAsync();
                    _connection = null;
                }

                _isConnected = false;
                OnConnectionStatusChanged(ConnectionStatus.Connected, ConnectionStatus.Disconnected, "تم قطع الاتصال");
            }
            catch (Exception ex)
            {
                OnSyncError(ex, "فشل في قطع الاتصال");
            }
        }

        #region Helper Methods

        /// <summary>
        /// إنشاء الجداول إذا لم تكن موجودة
        /// </summary>
        private async Task CreateTablesIfNotExistAsync()
        {
            try
            {
                await _connection.OpenAsync();

                // إنشاء جدول SyncData
                var createSyncDataTable = @"
                    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SyncData' AND xtype='U')
                    CREATE TABLE SyncData (
                        Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
                        EntityType NVARCHAR(100) NOT NULL,
                        EntityId NVARCHAR(100) NOT NULL,
                        JsonData NVARCHAR(MAX) NOT NULL,
                        Operation NVARCHAR(50) NOT NULL,
                        Timestamp DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
                        UserId NVARCHAR(100),
                        DeviceId NVARCHAR(100),
                        Hash NVARCHAR(500),
                        Metadata NVARCHAR(MAX),
                        CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
                        UpdatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
                        INDEX IX_SyncData_EntityType_EntityId (EntityType, EntityId),
                        INDEX IX_SyncData_Timestamp (Timestamp)
                    )";

                using var command1 = new SqlCommand(createSyncDataTable, _connection);
                await command1.ExecuteNonQueryAsync();

                // إنشاء جدول Backups
                var createBackupsTable = @"
                    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Backups' AND xtype='U')
                    CREATE TABLE Backups (
                        Id NVARCHAR(100) PRIMARY KEY,
                        Name NVARCHAR(200) NOT NULL,
                        CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
                        Size BIGINT NOT NULL,
                        Data NVARCHAR(MAX) NOT NULL,
                        Type NVARCHAR(50) NOT NULL,
                        Version NVARCHAR(20) NOT NULL,
                        Description NVARCHAR(500),
                        INDEX IX_Backups_CreatedAt (CreatedAt)
                    )";

                using var command2 = new SqlCommand(createBackupsTable, _connection);
                await command2.ExecuteNonQueryAsync();

                await _connection.CloseAsync();
            }
            catch (Exception ex)
            {
                OnSyncError(ex, "فشل في إنشاء الجداول");
            }
        }

        /// <summary>
        /// إدراج بيانات المزامنة
        /// </summary>
        private async Task InsertSyncDataAsync(SyncData data, string encryptedJsonData)
        {
            var query = @"
                INSERT INTO SyncData (EntityType, EntityId, JsonData, Operation, Timestamp, UserId, DeviceId, Hash, Metadata)
                VALUES (@EntityType, @EntityId, @JsonData, @Operation, @Timestamp, @UserId, @DeviceId, @Hash, @Metadata)";

            using var command = new SqlCommand(query, _connection);
            command.Parameters.AddWithValue("@EntityType", data.EntityType);
            command.Parameters.AddWithValue("@EntityId", data.EntityId);
            command.Parameters.AddWithValue("@JsonData", encryptedJsonData);
            command.Parameters.AddWithValue("@Operation", data.Operation.ToString());
            command.Parameters.AddWithValue("@Timestamp", data.Timestamp);
            command.Parameters.AddWithValue("@UserId", (object?)data.UserId ?? DBNull.Value);
            command.Parameters.AddWithValue("@DeviceId", (object?)data.DeviceId ?? DBNull.Value);
            command.Parameters.AddWithValue("@Hash", (object?)data.Hash ?? DBNull.Value);
            command.Parameters.AddWithValue("@Metadata", (object?)JsonConvert.SerializeObject(data.Metadata) ?? DBNull.Value);

            await command.ExecuteNonQueryAsync();
        }

        /// <summary>
        /// تحديث بيانات المزامنة
        /// </summary>
        private async Task UpdateSyncDataAsync(SyncData data, string encryptedJsonData)
        {
            var query = @"
                UPDATE SyncData
                SET JsonData = @JsonData, Operation = @Operation, Timestamp = @Timestamp,
                    UserId = @UserId, DeviceId = @DeviceId, Hash = @Hash, Metadata = @Metadata, UpdatedAt = GETUTCDATE()
                WHERE EntityType = @EntityType AND EntityId = @EntityId";

            using var command = new SqlCommand(query, _connection);
            command.Parameters.AddWithValue("@EntityType", data.EntityType);
            command.Parameters.AddWithValue("@EntityId", data.EntityId);
            command.Parameters.AddWithValue("@JsonData", encryptedJsonData);
            command.Parameters.AddWithValue("@Operation", data.Operation.ToString());
            command.Parameters.AddWithValue("@Timestamp", data.Timestamp);
            command.Parameters.AddWithValue("@UserId", (object?)data.UserId ?? DBNull.Value);
            command.Parameters.AddWithValue("@DeviceId", (object?)data.DeviceId ?? DBNull.Value);
            command.Parameters.AddWithValue("@Hash", (object?)data.Hash ?? DBNull.Value);
            command.Parameters.AddWithValue("@Metadata", (object?)JsonConvert.SerializeObject(data.Metadata) ?? DBNull.Value);

            await command.ExecuteNonQueryAsync();
        }

        /// <summary>
        /// حذف بيانات المزامنة
        /// </summary>
        private async Task DeleteSyncDataAsync(SyncData data)
        {
            var query = "DELETE FROM SyncData WHERE EntityType = @EntityType AND EntityId = @EntityId";

            using var command = new SqlCommand(query, _connection);
            command.Parameters.AddWithValue("@EntityType", data.EntityType);
            command.Parameters.AddWithValue("@EntityId", data.EntityId);

            await command.ExecuteNonQueryAsync();
        }

        #endregion

        #region Event Handlers

        private void OnConnectionStatusChanged(ConnectionStatus oldStatus, ConnectionStatus newStatus, string message)
        {
            ConnectionStatusChanged?.Invoke(this, new ConnectionStatusChangedEventArgs
            {
                OldStatus = oldStatus,
                NewStatus = newStatus,
                Message = message
            });
        }

        private void OnSyncProgress(int total, int processed, string operation, string entityType)
        {
            SyncProgress?.Invoke(this, new SyncProgressEventArgs
            {
                TotalRecords = total,
                ProcessedRecords = processed,
                CurrentOperation = operation,
                EntityType = entityType
            });
        }

        private void OnSyncCompleted(SyncResult result)
        {
            SyncCompleted?.Invoke(this, new SyncCompletedEventArgs { Result = result });
        }

        private void OnSyncError(Exception exception, string message, string entityType = "", string entityId = "")
        {
            SyncError?.Invoke(this, new SyncErrorEventArgs
            {
                Exception = exception,
                ErrorMessage = message,
                EntityType = entityType,
                EntityId = entityId
            });
        }

        #endregion
    }
}
