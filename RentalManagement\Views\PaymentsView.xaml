<UserControl x:Class="RentalManagement.Views.PaymentsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:models="clr-namespace:RentalManagement.Models"
             xmlns:helpers="clr-namespace:RentalManagement.Helpers"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <helpers:PaymentStatusConverter x:Key="PaymentStatusConverter"/>
    </UserControl.Resources>

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Page Title -->
        <TextBlock Grid.Row="0"
                   Text="إدارة الأداءات"
                   Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                   Margin="0,0,0,24"/>

        <!-- Search and Filters -->
        <Grid Grid.Row="1" Margin="0,0,0,16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Search and Actions -->
            <Grid Grid.Row="0" Margin="0,0,0,8">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Search Box -->
                <TextBox Grid.Column="0"
                         Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                         materialDesign:HintAssist.Hint="البحث في الأداءات..."
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         Margin="0,0,16,0">
                    <TextBox.InputBindings>
                        <KeyBinding Key="Enter" Command="{Binding SearchCommand}"/>
                    </TextBox.InputBindings>
                </TextBox>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="بحث"
                            Command="{Binding SearchCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Margin="0,0,8,0"/>
                    <Button Content="إضافة أداء"
                            Command="{Binding AddPaymentCommand}"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Margin="0,0,8,0"/>
                    <Button Content="تحديث"
                            Command="{Binding RefreshCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"/>
                </StackPanel>
            </Grid>

            <!-- Filters -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Status Filter -->
                <ComboBox Grid.Column="0"
                          SelectedValue="{Binding StatusFilter}"
                          materialDesign:HintAssist.Hint="حالة الأداء"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          Margin="0,0,8,0">
                    <ComboBoxItem Content="الكل"/>
                    <ComboBoxItem Content="غير مدفوع">
                        <ComboBoxItem.Tag>
                            <models:PaymentStatus>Unpaid</models:PaymentStatus>
                        </ComboBoxItem.Tag>
                    </ComboBoxItem>
                    <ComboBoxItem Content="مدفوع">
                        <ComboBoxItem.Tag>
                            <models:PaymentStatus>Paid</models:PaymentStatus>
                        </ComboBoxItem.Tag>
                    </ComboBoxItem>
                    <ComboBoxItem Content="متأخر">
                        <ComboBoxItem.Tag>
                            <models:PaymentStatus>Overdue</models:PaymentStatus>
                        </ComboBoxItem.Tag>
                    </ComboBoxItem>
                </ComboBox>

                <!-- From Date -->
                <DatePicker Grid.Column="1"
                            SelectedDate="{Binding FromDate}"
                            materialDesign:HintAssist.Hint="من تاريخ"
                            Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                            Margin="8,0"/>

                <!-- To Date -->
                <DatePicker Grid.Column="2"
                            SelectedDate="{Binding ToDate}"
                            materialDesign:HintAssist.Hint="إلى تاريخ"
                            Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                            Margin="8,0"/>

                <!-- Clear Filters -->
                <Button Grid.Column="3"
                        Content="مسح الفلاتر"
                        Command="{Binding ClearFiltersCommand}"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Margin="8,0,0,0"/>
            </Grid>
        </Grid>

        <!-- Payments List -->
        <materialDesign:Card Grid.Row="2"
                           materialDesign:ElevationAssist.Elevation="Dp2">
            <DataGrid ItemsSource="{Binding Payments}"
                      SelectedItem="{Binding SelectedPayment}"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      IsReadOnly="True"
                      materialDesign:DataGridAssist.CellPadding="13 8 8 8"
                      materialDesign:DataGridAssist.ColumnHeaderPadding="8">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="الزبون"
                                      Binding="{Binding Customer.FullName}"
                                      Width="150"/>

                    <DataGridTextColumn Header="العقار"
                                      Binding="{Binding Property.Location}"
                                      Width="*"/>

                    <DataGridTextColumn Header="المبلغ"
                                      Binding="{Binding Amount, StringFormat='{}{0:C}'}"
                                      Width="100"/>

                    <DataGridTextColumn Header="تاريخ الاستحقاق"
                                      Binding="{Binding DueDate, StringFormat='{}{0:dd/MM/yyyy}'}"
                                      Width="120"/>

                    <DataGridTextColumn Header="تاريخ الدفع"
                                      Binding="{Binding PaymentDate, StringFormat='{}{0:dd/MM/yyyy}'}"
                                      Width="120"/>

                    <DataGridTextColumn Header="الحالة" Width="80">
                        <DataGridTextColumn.Binding>
                            <Binding Path="Status" Converter="{StaticResource PaymentStatusConverter}"/>
                        </DataGridTextColumn.Binding>
                        <DataGridTextColumn.CellStyle>
                            <Style TargetType="DataGridCell">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding Status}" Value="{x:Static models:PaymentStatus.Paid}">
                                        <Setter Property="Foreground" Value="{StaticResource SuccessBrush}"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding Status}" Value="{x:Static models:PaymentStatus.Overdue}">
                                        <Setter Property="Foreground" Value="{StaticResource ErrorBrush}"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding Status}" Value="{x:Static models:PaymentStatus.Unpaid}">
                                        <Setter Property="Foreground" Value="{StaticResource WarningBrush}"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </DataGridTextColumn.CellStyle>
                    </DataGridTextColumn>

                    <DataGridTemplateColumn Header="الإجراءات" Width="250">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Button ToolTip="تعديل"
                                            Command="{Binding DataContext.EditPaymentCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                            CommandParameter="{Binding}"
                                            Style="{StaticResource MaterialDesignIconButton}"
                                            Margin="0,0,4,0">
                                        <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"/>
                                    </Button>
                                    <Button ToolTip="تحديد كمدفوع"
                                            Command="{Binding DataContext.MarkAsPaidCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                            CommandParameter="{Binding}"
                                            Foreground="{StaticResource SuccessBrush}"
                                            Margin="0,0,4,0">
                                        <materialDesign:PackIcon Kind="Check" Width="16" Height="16"/>
                                        <Button.Style>
                                            <Style TargetType="Button" BasedOn="{StaticResource MaterialDesignIconButton}">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Status}" Value="{x:Static models:PaymentStatus.Paid}">
                                                        <Setter Property="Visibility" Value="Collapsed"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Button.Style>
                                    </Button>
                                    <Button ToolTip="طباعة إيصال"
                                            Command="{Binding DataContext.GenerateReceiptCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                            CommandParameter="{Binding}"
                                            Margin="0,0,4,0">
                                        <materialDesign:PackIcon Kind="Printer" Width="16" Height="16"/>
                                        <Button.Style>
                                            <Style TargetType="Button" BasedOn="{StaticResource MaterialDesignIconButton}">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Status}" Value="{x:Static models:PaymentStatus.Unpaid}">
                                                        <Setter Property="Visibility" Value="Collapsed"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="{x:Static models:PaymentStatus.Overdue}">
                                                        <Setter Property="Visibility" Value="Collapsed"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Button.Style>
                                    </Button>
                                    <Button ToolTip="حذف"
                                            Command="{Binding DataContext.DeletePaymentCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                            CommandParameter="{Binding}"
                                            Style="{StaticResource MaterialDesignIconButton}"
                                            Foreground="{StaticResource ErrorBrush}">
                                        <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                    </Button>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>
    </Grid>
</UserControl>
