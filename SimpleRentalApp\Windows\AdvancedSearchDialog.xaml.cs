using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using SimpleRentalApp.Models;

namespace SimpleRentalApp.Windows
{
    public partial class AdvancedSearchDialog : Window
    {
        private List<PaymentDisplayInfo> _allPayments;
        public List<PaymentDisplayInfo> FilteredPayments { get; private set; } = new();
        public bool HasSearchCriteria { get; private set; } = false;

        public AdvancedSearchDialog(List<PaymentDisplayInfo> allPayments)
        {
            InitializeComponent();
            _allPayments = allPayments;
            InitializeControls();
        }

        private void InitializeControls()
        {
            // Initialize year combo box with dynamic range
            InitializeYearComboBox();

            // Set default date range (current year)
            var currentYear = DateTime.Now.Year;
            DateFromPicker.SelectedDate = new DateTime(currentYear, 1, 1);
            DateToPicker.SelectedDate = new DateTime(currentYear, 12, 31);
        }

        /// <summary>
        /// Initialize year combo box with both existing payment years and extended range
        /// </summary>
        private void InitializeYearComboBox()
        {
            try
            {
                // Get years from existing payments
                var paymentYears = _allPayments
                    .Where(p => p.Payment.PaymentDate.HasValue)
                    .Select(p => p.Payment.PaymentDate.Value.Year)
                    .Distinct()
                    .ToHashSet();

                // Generate extended year range (2015 to current + 5 years)
                var currentYear = DateTime.Now.Year;
                var startYear = 2015;
                var endYear = currentYear + 5;

                var allYears = new HashSet<int>();

                // Add payment years
                foreach (var year in paymentYears)
                {
                    allYears.Add(year);
                }

                // Add extended range
                for (int year = startYear; year <= endYear; year++)
                {
                    allYears.Add(year);
                }

                // Convert to sorted list (descending order)
                var sortedYears = allYears.OrderByDescending(y => y).ToList();

                // Populate ComboBox
                YearComboBox.Items.Add(new ComboBoxItem { Content = "جميع السنوات", IsSelected = true });
                foreach (var year in sortedYears)
                {
                    YearComboBox.Items.Add(new ComboBoxItem { Content = year.ToString(), Tag = year });
                }

                System.Diagnostics.Debug.WriteLine($"AdvancedSearch - Generated years: {string.Join(", ", sortedYears)} (Total: {sortedYears.Count})");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing year combo box: {ex.Message}");

                // Fallback to simple range
                YearComboBox.Items.Add(new ComboBoxItem { Content = "جميع السنوات", IsSelected = true });
                var currentYear = DateTime.Now.Year;
                for (int year = currentYear + 2; year >= currentYear - 5; year--)
                {
                    YearComboBox.Items.Add(new ComboBoxItem { Content = year.ToString(), Tag = year });
                }
            }
        }

        private void PreviewButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var filteredResults = ApplySearchCriteria();
                var count = filteredResults.Count;
                var totalAmount = filteredResults.Sum(p => p.Payment.CleaningTaxAmount);

                ResultsPreviewText.Text = $"عدد النتائج المتوقعة: {count} دفعة\n" +
                                         $"إجمالي المبلغ: {totalAmount:N0} درهم";

                if (count == 0)
                {
                    ResultsPreviewText.Text += "\n⚠️ لا توجد نتائج تطابق المعايير المحددة";
                }
                else if (count > 100)
                {
                    ResultsPreviewText.Text += "\n💡 نتائج كثيرة، فكر في تضييق نطاق البحث";
                }
            }
            catch (Exception ex)
            {
                ResultsPreviewText.Text = $"خطأ في المعاينة: {ex.Message}";
            }
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                FilteredPayments = ApplySearchCriteria();
                HasSearchCriteria = HasAnyCriteria();
                
                if (FilteredPayments.Count == 0)
                {
                    var result = MessageBox.Show(
                        "لا توجد نتائج تطابق معايير البحث المحددة.\nهل تريد تعديل المعايير؟",
                        "لا توجد نتائج",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Information);

                    if (result == MessageBoxResult.No)
                    {
                        DialogResult = true;
                        Close();
                    }
                }
                else
                {
                    DialogResult = true;
                    Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق البحث: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private List<PaymentDisplayInfo> ApplySearchCriteria()
        {
            var results = new List<PaymentDisplayInfo>(_allPayments);

            // Filter by receipt number
            if (!string.IsNullOrWhiteSpace(ReceiptNumberTextBox.Text))
            {
                var receiptNumber = ReceiptNumberTextBox.Text.Trim();
                if (ExactMatchCheckBox.IsChecked == true)
                {
                    results = results.Where(p => p.ReceiptNumber.Equals(receiptNumber, StringComparison.OrdinalIgnoreCase)).ToList();
                }
                else
                {
                    results = results.Where(p => p.ReceiptNumber.Contains(receiptNumber, StringComparison.OrdinalIgnoreCase)).ToList();
                }
            }

            // Filter by amount range
            if (decimal.TryParse(AmountFromTextBox.Text, out var amountFrom))
            {
                results = results.Where(p => p.Payment.CleaningTaxAmount >= amountFrom).ToList();
            }

            if (decimal.TryParse(AmountToTextBox.Text, out var amountTo))
            {
                results = results.Where(p => p.Payment.CleaningTaxAmount <= amountTo).ToList();
            }

            // Filter by date range
            if (DateFromPicker.SelectedDate.HasValue)
            {
                results = results.Where(p => p.Payment.PaymentDate >= DateFromPicker.SelectedDate.Value).ToList();
            }

            if (DateToPicker.SelectedDate.HasValue)
            {
                results = results.Where(p => p.Payment.PaymentDate <= DateToPicker.SelectedDate.Value).ToList();
            }

            // Filter by payment method
            var paymentMethodItem = PaymentMethodComboBox.SelectedItem as ComboBoxItem;
            if (paymentMethodItem?.Content.ToString() != "جميع الطرق")
            {
                var paymentMethod = paymentMethodItem?.Content.ToString();
                results = results.Where(p => p.PaymentMethod.Equals(paymentMethod, StringComparison.OrdinalIgnoreCase)).ToList();
            }

            // Filter by payment status
            var statusItem = PaymentStatusComboBox.SelectedItem as ComboBoxItem;
            if (statusItem?.Tag != null)
            {
                var statusTag = statusItem.Tag.ToString();
                results = results.Where(p => statusTag switch
                {
                    "Complete" => p.RemainingAfterPayment == 0,
                    "Partial" => p.RemainingAfterPayment > 0,
                    "Paid" => p.Payment.Status == PaymentStatus.Paid,
                    "Unpaid" => p.Payment.Status == PaymentStatus.Unpaid,
                    _ => true
                }).ToList();
            }

            // Filter by year
            var yearItem = YearComboBox.SelectedItem as ComboBoxItem;
            if (yearItem?.Tag != null && int.TryParse(yearItem.Tag.ToString(), out var year))
            {
                results = results.Where(p => p.Payment.PaymentDate?.Year == year).ToList();
            }

            // Filter by notes
            if (!string.IsNullOrWhiteSpace(NotesTextBox.Text))
            {
                var notesText = NotesTextBox.Text.Trim();
                if (ExactMatchCheckBox.IsChecked == true)
                {
                    results = results.Where(p => p.Notes.Equals(notesText, StringComparison.OrdinalIgnoreCase)).ToList();
                }
                else
                {
                    results = results.Where(p => p.Notes.Contains(notesText, StringComparison.OrdinalIgnoreCase)).ToList();
                }
            }

            // Apply quick filters
            if (LastMonthCheckBox.IsChecked == true)
            {
                var lastMonth = DateTime.Now.AddMonths(-1);
                results = results.Where(p => p.Payment.PaymentDate >= lastMonth).ToList();
            }

            if (Last3MonthsCheckBox.IsChecked == true)
            {
                var last3Months = DateTime.Now.AddMonths(-3);
                results = results.Where(p => p.Payment.PaymentDate >= last3Months).ToList();
            }

            if (CurrentYearCheckBox.IsChecked == true)
            {
                var currentYear = DateTime.Now.Year;
                results = results.Where(p => p.Payment.PaymentDate?.Year == currentYear).ToList();
            }

            if (OverdueCheckBox.IsChecked == true)
            {
                var currentDate = DateTime.Now;
                results = results.Where(p => p.Payment.PaymentDate.HasValue && 
                                           p.RemainingAfterPayment > 0 &&
                                           new DateTime(p.Payment.PaymentDate.Value.Year, 5, 31) < currentDate).ToList();
            }

            if (LargeAmountsCheckBox.IsChecked == true)
            {
                results = results.Where(p => p.Payment.CleaningTaxAmount > 1000).ToList();
            }

            if (SmallAmountsCheckBox.IsChecked == true)
            {
                results = results.Where(p => p.Payment.CleaningTaxAmount < 500).ToList();
            }

            return results;
        }

        private bool HasAnyCriteria()
        {
            return !string.IsNullOrWhiteSpace(ReceiptNumberTextBox.Text) ||
                   !string.IsNullOrWhiteSpace(AmountFromTextBox.Text) ||
                   !string.IsNullOrWhiteSpace(AmountToTextBox.Text) ||
                   DateFromPicker.SelectedDate.HasValue ||
                   DateToPicker.SelectedDate.HasValue ||
                   (PaymentMethodComboBox.SelectedItem as ComboBoxItem)?.Content.ToString() != "جميع الطرق" ||
                   (PaymentStatusComboBox.SelectedItem as ComboBoxItem)?.Tag != null ||
                   (YearComboBox.SelectedItem as ComboBoxItem)?.Tag != null ||
                   !string.IsNullOrWhiteSpace(NotesTextBox.Text) ||
                   LastMonthCheckBox.IsChecked == true ||
                   Last3MonthsCheckBox.IsChecked == true ||
                   CurrentYearCheckBox.IsChecked == true ||
                   OverdueCheckBox.IsChecked == true ||
                   LargeAmountsCheckBox.IsChecked == true ||
                   SmallAmountsCheckBox.IsChecked == true;
        }

        private void ResetButton_Click(object sender, RoutedEventArgs e)
        {
            // Reset all controls
            ReceiptNumberTextBox.Text = "";
            AmountFromTextBox.Text = "";
            AmountToTextBox.Text = "";
            DateFromPicker.SelectedDate = null;
            DateToPicker.SelectedDate = null;
            PaymentMethodComboBox.SelectedIndex = 0;
            PaymentStatusComboBox.SelectedIndex = 0;
            YearComboBox.SelectedIndex = 0;
            NotesTextBox.Text = "";
            ExactMatchCheckBox.IsChecked = false;

            // Reset quick filters
            LastMonthCheckBox.IsChecked = false;
            Last3MonthsCheckBox.IsChecked = false;
            CurrentYearCheckBox.IsChecked = false;
            OverdueCheckBox.IsChecked = false;
            LargeAmountsCheckBox.IsChecked = false;
            SmallAmountsCheckBox.IsChecked = false;

            // Reset preview
            ResultsPreviewText.Text = "اضغط 'معاينة' لرؤية عدد النتائج المتوقعة";
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
