using Microsoft.EntityFrameworkCore;
using SimpleRentalApp.Models;
using System.IO;

namespace SimpleRentalApp.Data;

public class RentalDbContext : DbContext
{
    public DbSet<Customer> Customers { get; set; }
    public DbSet<Property> Properties { get; set; }
    public DbSet<Payment> Payments { get; set; }
    public DbSet<Contract> Contracts { get; set; }
    public DbSet<ContractAttachment> ContractAttachments { get; set; }
    public DbSet<RentIncrease> RentIncreases { get; set; }
    public DbSet<ContractAugment> ContractAugments { get; set; }
    public DbSet<RentReceipt> RentReceipts { get; set; }
    public DbSet<CleaningTaxReceipt> CleaningTaxReceipts { get; set; }
    public DbSet<User> Users { get; set; }
    public DbSet<RentChangeLog> RentChangeLogs { get; set; }
    public DbSet<WhatsAppNotification> WhatsAppNotifications { get; set; }
    public DbSet<MessageTemplate> MessageTemplates { get; set; }
    public DbSet<NotificationSettings> NotificationSettings { get; set; }
    
    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        // استخدام مسار آمن لقاعدة البيانات
        var appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "TadbirAlKira");
        var dbPath = Path.Combine(appDataPath, "rental_management.db");

        // إنشاء المجلد إذا لم يكن موجوداً
        Directory.CreateDirectory(appDataPath);

        optionsBuilder.UseSqlite($"Data Source={dbPath}");
    }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Customer configuration
        modelBuilder.Entity<Customer>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.FirstName).IsRequired().HasMaxLength(100);
            entity.Property(e => e.LastName).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Phone).IsRequired().HasMaxLength(15);
            entity.Property(e => e.Email).HasMaxLength(100);
            entity.Property(e => e.NationalId).HasMaxLength(20);
            entity.Property(e => e.Address).HasMaxLength(200);
        });
        
        // Property configuration
        modelBuilder.Entity<Property>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Address).IsRequired().HasMaxLength(200);
            entity.Property(e => e.MonthlyRent).HasColumnType("decimal(18,2)");
            entity.Property(e => e.CleaningTaxPaymentOption).HasColumnType("decimal(18,2)");
            entity.Property(e => e.Area).HasColumnType("decimal(18,2)");
            entity.Property(e => e.Description).HasMaxLength(500);
            
            entity.HasOne(e => e.Customer)
                  .WithMany(e => e.Properties)
                  .HasForeignKey(e => e.CustomerId)
                  .OnDelete(DeleteBehavior.SetNull);
        });
        
        // Payment configuration
        modelBuilder.Entity<Payment>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.RentAmount).HasColumnType("decimal(18,2)");
            entity.Property(e => e.CleaningTaxAmount).HasColumnType("decimal(18,2)");
            entity.Property(e => e.Notes).HasMaxLength(500);
            entity.Property(e => e.ReceiptNumber).HasMaxLength(50);
            entity.Property(e => e.PaymentMethod).HasMaxLength(100).HasDefaultValue("نقداً");

            entity.HasOne(e => e.Property)
                  .WithMany(e => e.Payments)
                  .HasForeignKey(e => e.PropertyId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.Customer)
                  .WithMany(e => e.Payments)
                  .HasForeignKey(e => e.CustomerId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // Contract configuration
        modelBuilder.Entity<Contract>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.InitialRentAmount).HasColumnType("decimal(18,2)");
            entity.Property(e => e.RentIncreasePercentage).HasColumnType("decimal(5,2)");
            entity.Property(e => e.Notes).HasMaxLength(500);

            entity.HasOne(e => e.Customer)
                  .WithMany()
                  .HasForeignKey(e => e.CustomerId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.Property)
                  .WithMany()
                  .HasForeignKey(e => e.PropertyId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // ContractAttachment configuration
        modelBuilder.Entity<ContractAttachment>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.FileName).HasMaxLength(255);
            entity.Property(e => e.FilePath).HasMaxLength(500);
            entity.Property(e => e.FileType).HasMaxLength(100);
            entity.Property(e => e.Description).HasMaxLength(200);

            entity.HasOne(e => e.Contract)
                  .WithMany(e => e.Attachments)
                  .HasForeignKey(e => e.ContractId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // RentIncrease configuration
        modelBuilder.Entity<RentIncrease>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.PreviousAmount).HasColumnType("decimal(18,2)");
            entity.Property(e => e.NewAmount).HasColumnType("decimal(18,2)");
            entity.Property(e => e.IncreasePercentage).HasColumnType("decimal(5,2)");
            entity.Property(e => e.Reason).HasMaxLength(200);

            entity.HasOne(e => e.Contract)
                  .WithMany(e => e.RentIncreases)
                  .HasForeignKey(e => e.ContractId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // ContractAugment configuration
        modelBuilder.Entity<ContractAugment>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.AugmentType).HasMaxLength(100);
            entity.Property(e => e.Percentage).HasColumnType("decimal(5,2)");
            entity.Property(e => e.BaseRent).HasColumnType("decimal(18,2)");
            entity.Property(e => e.CalculatedAmount).HasColumnType("decimal(18,2)");
            entity.Property(e => e.Notes).HasMaxLength(500);

            entity.HasOne(e => e.Contract)
                  .WithMany(e => e.Augments)
                  .HasForeignKey(e => e.ContractId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // RentReceipt configuration
        modelBuilder.Entity<RentReceipt>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.ReceiptNumber).HasMaxLength(50);
            entity.Property(e => e.Amount).HasColumnType("decimal(18,2)");
            entity.Property(e => e.PaymentPeriod).HasMaxLength(100);
            entity.Property(e => e.PaymentMethod).HasMaxLength(50);
            entity.Property(e => e.Notes).HasMaxLength(500);
            entity.Property(e => e.CreatedBy).HasMaxLength(100);

            entity.HasOne(e => e.Contract)
                  .WithMany()
                  .HasForeignKey(e => e.ContractId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.Payment)
                  .WithMany(p => p.Receipts)
                  .HasForeignKey(e => e.PaymentId)
                  .OnDelete(DeleteBehavior.SetNull);

            // Make ReceiptNumber unique per Contract (Property)
            entity.HasIndex(e => new { e.ReceiptNumber, e.ContractId }).IsUnique();
        });

        // CleaningTaxReceipt configuration
        modelBuilder.Entity<CleaningTaxReceipt>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.ReceiptNumber).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Amount).HasColumnType("decimal(18,2)");
            entity.Property(e => e.PaymentMethod).HasMaxLength(50);
            entity.Property(e => e.Notes).HasMaxLength(500);
            entity.Property(e => e.CreatedBy).HasMaxLength(100);

            entity.HasOne(e => e.Property)
                  .WithMany()
                  .HasForeignKey(e => e.PropertyId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.Payment)
                  .WithMany()
                  .HasForeignKey(e => e.PaymentId)
                  .OnDelete(DeleteBehavior.SetNull);

            // Make ReceiptNumber unique per Property and TaxYear
            entity.HasIndex(e => new { e.ReceiptNumber, e.PropertyId, e.TaxYear }).IsUnique();
        });



        // User configuration
        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Username).IsRequired().HasMaxLength(50);
            entity.Property(e => e.FullName).IsRequired().HasMaxLength(100);
            entity.Property(e => e.PasswordHash).IsRequired().HasMaxLength(255);
            entity.Property(e => e.Email).HasMaxLength(100);
            entity.HasIndex(e => e.Username).IsUnique();
        });

        // Seed data
        SeedData(modelBuilder);
    }
    
    private void SeedData(ModelBuilder modelBuilder)
    {
        // Seed Customers
        modelBuilder.Entity<Customer>().HasData(
            new Customer { Id = 1, FirstName = "أحمد", LastName = "محمد علي", Phone = "0501234567", Email = "<EMAIL>", NationalId = "1234567890", Address = "حي الملز، الرياض", CreatedDate = DateTime.Now.AddDays(-30) },
            new Customer { Id = 2, FirstName = "فاطمة", LastName = "عبدالله", Phone = "0509876543", Email = "<EMAIL>", NationalId = "0987654321", Address = "حي النخيل، الرياض", CreatedDate = DateTime.Now.AddDays(-25) },
            new Customer { Id = 3, FirstName = "محمد", LastName = "أحمد", Phone = "0551122334", Email = "<EMAIL>", NationalId = "1122334455", Address = "حي العليا، الرياض", CreatedDate = DateTime.Now.AddDays(-20) },
            new Customer { Id = 4, FirstName = "سارة", LastName = "خالد", Phone = "0555566778", Email = "<EMAIL>", NationalId = "5566778899", Address = "حي الورود، الرياض", CreatedDate = DateTime.Now.AddDays(-15) },
            new Customer { Id = 5, FirstName = "عبدالله", LastName = "سعد", Phone = "0559988776", Email = "<EMAIL>", NationalId = "9988776655", Address = "حي الصحافة، الرياض", CreatedDate = DateTime.Now.AddDays(-10) }
        );
        
        // Seed Properties
        modelBuilder.Entity<Property>().HasData(
            new Property { Id = 1, Name = "شقة الملز الفاخرة", Address = "حي الملز، شارع الأمير محمد بن عبدالعزيز", Type = PropertyType.Apartment, Status = PropertyStatus.Rented, MonthlyRent = 3500, CleaningTaxPaymentOption = 1, Rooms = 3, Bathrooms = 2, Area = 120, Description = "شقة فاخرة مع إطلالة رائعة", CustomerId = 1, CreatedDate = DateTime.Now.AddDays(-30) },
            new Property { Id = 2, Name = "محل التحلية التجاري", Address = "شارع التحلية، وسط الدار البيضاء", Type = PropertyType.Shop, Status = PropertyStatus.Rented, MonthlyRent = 7000, CleaningTaxPaymentOption = 4, Area = 80, Description = "محل تجاري في موقع ممتاز", CustomerId = 2, CreatedDate = DateTime.Now.AddDays(-25) },
            new Property { Id = 3, Name = "شقة النخيل العائلية", Address = "حي النخيل، شارع الملك فهد", Type = PropertyType.Apartment, Status = PropertyStatus.Rented, MonthlyRent = 4200, CleaningTaxPaymentOption = 2, Rooms = 4, Bathrooms = 3, Area = 150, Description = "شقة عائلية واسعة", CustomerId = 3, CreatedDate = DateTime.Now.AddDays(-20) },
            new Property { Id = 4, Name = "مكتب برج الفيصلية", Address = "برج الفيصلية، حي العليا", Type = PropertyType.Office, Status = PropertyStatus.Available, MonthlyRent = 11000, CleaningTaxPaymentOption = 12, Area = 200, Description = "مكتب فاخر في برج الفيصلية", CreatedDate = DateTime.Now.AddDays(-15) },
            new Property { Id = 5, Name = "شقة العليا الحديثة", Address = "حي العليا، شارع العروبة", Type = PropertyType.Apartment, Status = PropertyStatus.Rented, MonthlyRent = 5500, CleaningTaxPaymentOption = 3, Rooms = 3, Bathrooms = 2, Area = 130, Description = "شقة حديثة مع جميع المرافق", CustomerId = 4, CreatedDate = DateTime.Now.AddDays(-10) },
            new Property { Id = 6, Name = "فيلا الورود الفاخرة", Address = "حي الورود، شارع الأمير سلطان", Type = PropertyType.Villa, Status = PropertyStatus.Available, MonthlyRent = 16000, CleaningTaxPaymentOption = 2, Rooms = 6, Bathrooms = 4, Area = 400, Description = "فيلا فاخرة مع حديقة", CreatedDate = DateTime.Now.AddDays(-5) }
        );

        // RentChangeLog configuration
        modelBuilder.Entity<RentChangeLog>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.OldRentAmount).HasColumnType("decimal(18,2)");
            entity.Property(e => e.NewRentAmount).HasColumnType("decimal(18,2)");
            entity.Property(e => e.ChangeReason).HasMaxLength(200);
            entity.Property(e => e.Notes).HasMaxLength(500);
            entity.Property(e => e.ChangedBy).HasMaxLength(100);

            entity.HasOne(e => e.Property)
                  .WithMany()
                  .HasForeignKey(e => e.PropertyId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.Contract)
                  .WithMany()
                  .HasForeignKey(e => e.ContractId)
                  .OnDelete(DeleteBehavior.SetNull);
        });

        // Seed Users (using simple passwords for now)
        modelBuilder.Entity<User>().HasData(
            new User { Id = 1, Username = "hafid", FullName = "حفيظ عبدو", PasswordHash = "hafidos159357", Email = "<EMAIL>", Role = UserRole.Admin, CreatedDate = new DateTime(2024, 1, 1, 8, 0, 0) },
            new User { Id = 2, Username = "manager", FullName = "أحمد المدير", PasswordHash = "manager123", Email = "<EMAIL>", Role = UserRole.Manager, CreatedDate = new DateTime(2024, 1, 2, 9, 0, 0) },
            new User { Id = 3, Username = "employee", FullName = "فاطمة الموظفة", PasswordHash = "employee123", Email = "<EMAIL>", Role = UserRole.Employee, CreatedDate = new DateTime(2024, 1, 3, 10, 0, 0) }
        );
    }
}
