using SimpleRentalApp.Data;
using SimpleRentalApp.Models;
using System;
using System.Windows;
using System.Windows.Controls;

namespace SimpleRentalApp.Windows
{
    public partial class EditNotificationWindow : Window
    {
        private readonly RentalDbContext _context;
        private readonly NotificationDisplayInfo _notification;

        public EditNotificationWindow(NotificationDisplayInfo notification)
        {
            InitializeComponent();
            _context = new RentalDbContext();
            _notification = notification;
            
            InitializeComboBoxes();
            LoadNotificationData();
        }

        private void InitializeComboBoxes()
        {
            // Type ComboBox
            TypeComboBox.Items.Add(new ComboBoxItem { Content = "إيجار شهري", Tag = NotificationType.MonthlyRent });
            TypeComboBox.Items.Add(new ComboBoxItem { Content = "ضريبة النظافة", Tag = NotificationType.CleaningTax });
            TypeComboBox.Items.Add(new ComboBoxItem { Content = "تأخير إيجار", Tag = NotificationType.RentOverdue });
            TypeComboBox.Items.Add(new ComboBoxItem { Content = "تأخير ضريبة النظافة", Tag = NotificationType.CleaningTaxOverdue });
            TypeComboBox.Items.Add(new ComboBoxItem { Content = "انتهاء العقد", Tag = NotificationType.ContractExpiry });
            TypeComboBox.Items.Add(new ComboBoxItem { Content = "تذكير دفع", Tag = NotificationType.PaymentReminder });

            // Status ComboBox
            StatusComboBox.Items.Add(new ComboBoxItem { Content = "بانتظار الإرسال", Tag = NotificationStatus.Pending });
            StatusComboBox.Items.Add(new ComboBoxItem { Content = "تم الإرسال", Tag = NotificationStatus.Sent });
            StatusComboBox.Items.Add(new ComboBoxItem { Content = "فشل الإرسال", Tag = NotificationStatus.Failed });
            StatusComboBox.Items.Add(new ComboBoxItem { Content = "ملغي", Tag = NotificationStatus.Cancelled });
            StatusComboBox.Items.Add(new ComboBoxItem { Content = "مجدول", Tag = NotificationStatus.Scheduled });

            // Priority ComboBox
            PriorityComboBox.Items.Add(new ComboBoxItem { Content = "منخفضة", Tag = NotificationPriority.Low });
            PriorityComboBox.Items.Add(new ComboBoxItem { Content = "عادية", Tag = NotificationPriority.Normal });
            PriorityComboBox.Items.Add(new ComboBoxItem { Content = "عالية", Tag = NotificationPriority.High });
            PriorityComboBox.Items.Add(new ComboBoxItem { Content = "عاجلة", Tag = NotificationPriority.Urgent });
        }

        private void LoadNotificationData()
        {
            try
            {
                // Basic Info
                CustomerNameTextBox.Text = _notification.CustomerName;
                PropertyNameTextBox.Text = _notification.PropertyName;
                AmountTextBox.Text = _notification.Amount.ToString("F2");

                // Set ComboBox selections
                SetComboBoxSelection(TypeComboBox, _notification.Type);
                SetComboBoxSelection(StatusComboBox, _notification.Status);
                SetComboBoxSelection(PriorityComboBox, _notification.Priority);

                // Dates
                DueDatePicker.SelectedDate = _notification.DueDate;
                SentDatePicker.SelectedDate = _notification.SentDate;

                // Notes
                NotesTextBox.Text = _notification.Notes ?? "أضف ملاحظات إضافية حول الإشعار...";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الإشعار: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SetComboBoxSelection<T>(ComboBox comboBox, T value) where T : Enum
        {
            foreach (ComboBoxItem item in comboBox.Items)
            {
                if (item.Tag != null && item.Tag.Equals(value))
                {
                    comboBox.SelectedItem = item;
                    break;
                }
            }
        }

        private void SaveBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                // هنا يمكن إضافة منطق الحفظ الفعلي
                // مثال: تحديث الإشعار في قاعدة البيانات

                var result = MessageBox.Show(
                    "هل تريد حفظ التغييرات؟",
                    "تأكيد الحفظ",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // محاكاة عملية الحفظ
                    UpdateNotificationData();
                    
                    MessageBox.Show("تم حفظ التغييرات بنجاح!", "تم الحفظ", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    DialogResult = true;
                    Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التغييرات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(AmountTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال المبلغ", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                AmountTextBox.Focus();
                return false;
            }

            if (!decimal.TryParse(AmountTextBox.Text, out decimal amount) || amount <= 0)
            {
                MessageBox.Show("يرجى إدخال مبلغ صحيح", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                AmountTextBox.Focus();
                return false;
            }

            if (TypeComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار نوع الإشعار", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TypeComboBox.Focus();
                return false;
            }

            if (StatusComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار حالة الإشعار", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                StatusComboBox.Focus();
                return false;
            }

            if (PriorityComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار أولوية الإشعار", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                PriorityComboBox.Focus();
                return false;
            }

            if (DueDatePicker.SelectedDate == null)
            {
                MessageBox.Show("يرجى اختيار تاريخ الاستحقاق", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                DueDatePicker.Focus();
                return false;
            }

            return true;
        }

        private void UpdateNotificationData()
        {
            // تحديث بيانات الإشعار (محاكاة)
            _notification.Amount = decimal.Parse(AmountTextBox.Text);
            _notification.Type = (NotificationType)((ComboBoxItem)TypeComboBox.SelectedItem).Tag;
            _notification.Status = (NotificationStatus)((ComboBoxItem)StatusComboBox.SelectedItem).Tag;
            _notification.Priority = (NotificationPriority)((ComboBoxItem)PriorityComboBox.SelectedItem).Tag;
            _notification.DueDate = DueDatePicker.SelectedDate ?? DateTime.Now;
            _notification.SentDate = SentDatePicker.SelectedDate;
            _notification.Notes = NotesTextBox.Text == "أضف ملاحظات إضافية حول الإشعار..." ? null : NotesTextBox.Text;
        }

        private void CancelBtn_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "هل تريد إلغاء التعديل؟ ستفقد جميع التغييرات غير المحفوظة.",
                "تأكيد الإلغاء",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                DialogResult = false;
                Close();
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            base.OnClosed(e);
        }
    }
}
