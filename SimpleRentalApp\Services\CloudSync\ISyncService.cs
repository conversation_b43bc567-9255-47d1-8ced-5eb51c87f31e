using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SimpleRentalApp.Services.CloudSync
{
    /// <summary>
    /// واجهة خدمة المزامنة السحابية
    /// </summary>
    public interface ISyncService
    {
        /// <summary>
        /// اسم الخدمة السحابية
        /// </summary>
        string ServiceName { get; }

        /// <summary>
        /// حالة الاتصال
        /// </summary>
        bool IsConnected { get; }

        /// <summary>
        /// آخر وقت مزامنة
        /// </summary>
        DateTime? LastSyncTime { get; }

        /// <summary>
        /// تهيئة الاتصال بالخدمة السحابية
        /// </summary>
        /// <param name="config">إعدادات الاتصال</param>
        /// <returns>نجح الاتصال أم لا</returns>
        Task<bool> InitializeAsync(SyncConfiguration config);

        /// <summary>
        /// اختبار الاتصال
        /// </summary>
        /// <returns>حالة الاتصال</returns>
        Task<bool> TestConnectionAsync();

        /// <summary>
        /// مزامنة البيانات إلى السحابة
        /// </summary>
        /// <param name="data">البيانات المراد مزامنتها</param>
        /// <returns>نتيجة المزامنة</returns>
        Task<SyncResult> SyncToCloudAsync(SyncData data);

        /// <summary>
        /// مزامنة البيانات من السحابة
        /// </summary>
        /// <param name="lastSyncTime">آخر وقت مزامنة</param>
        /// <returns>البيانات المحدثة</returns>
        Task<SyncResult> SyncFromCloudAsync(DateTime? lastSyncTime = null);

        /// <summary>
        /// حل التعارضات
        /// </summary>
        /// <param name="conflicts">قائمة التعارضات</param>
        /// <returns>نتيجة حل التعارضات</returns>
        Task<ConflictResolutionResult> ResolveConflictsAsync(List<SyncConflict> conflicts);

        /// <summary>
        /// حذف البيانات من السحابة
        /// </summary>
        /// <param name="entityType">نوع الكيان</param>
        /// <param name="entityId">معرف الكيان</param>
        /// <returns>نجح الحذف أم لا</returns>
        Task<bool> DeleteFromCloudAsync(string entityType, string entityId);

        /// <summary>
        /// إنشاء نسخة احتياطية كاملة
        /// </summary>
        /// <returns>نتيجة النسخ الاحتياطي</returns>
        Task<BackupResult> CreateFullBackupAsync();

        /// <summary>
        /// استعادة من النسخة الاحتياطية
        /// </summary>
        /// <param name="backupId">معرف النسخة الاحتياطية</param>
        /// <returns>نتيجة الاستعادة</returns>
        Task<RestoreResult> RestoreFromBackupAsync(string backupId);

        /// <summary>
        /// الحصول على قائمة النسخ الاحتياطية
        /// </summary>
        /// <returns>قائمة النسخ الاحتياطية</returns>
        Task<List<BackupInfo>> GetBackupsAsync();

        /// <summary>
        /// قطع الاتصال
        /// </summary>
        Task DisconnectAsync();

        /// <summary>
        /// حدث تغيير حالة الاتصال
        /// </summary>
        event EventHandler<ConnectionStatusChangedEventArgs> ConnectionStatusChanged;

        /// <summary>
        /// حدث تقدم المزامنة
        /// </summary>
        event EventHandler<SyncProgressEventArgs> SyncProgress;

        /// <summary>
        /// حدث اكتمال المزامنة
        /// </summary>
        event EventHandler<SyncCompletedEventArgs> SyncCompleted;

        /// <summary>
        /// حدث حدوث خطأ
        /// </summary>
        event EventHandler<SyncErrorEventArgs> SyncError;
    }
}
