﻿#pragma checksum "..\..\..\..\..\Windows\CleaningTaxReceiptSelectionDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C7874642FFCF7F9E6D657B5FF3AAA773AB9BA962"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleRentalApp.Windows {
    
    
    /// <summary>
    /// CleaningTaxReceiptSelectionDialog
    /// </summary>
    public partial class CleaningTaxReceiptSelectionDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 81 "..\..\..\..\..\Windows\CleaningTaxReceiptSelectionDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PropertyNameText;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\..\..\Windows\CleaningTaxReceiptSelectionDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PropertyAddressText;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\..\Windows\CleaningTaxReceiptSelectionDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid PaymentsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\..\Windows\CleaningTaxReceiptSelectionDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PreviewButton;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\..\Windows\CleaningTaxReceiptSelectionDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintButton;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\..\..\Windows\CleaningTaxReceiptSelectionDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri(("/SimpleRentalApp;V1.0.0.0;component/windows/cleaningtaxreceiptselectiondialog.xam" +
                    "l"), System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\CleaningTaxReceiptSelectionDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.PropertyNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.PropertyAddressText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.PaymentsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 112 "..\..\..\..\..\Windows\CleaningTaxReceiptSelectionDialog.xaml"
            this.PaymentsDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PaymentsDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.PreviewButton = ((System.Windows.Controls.Button)(target));
            
            #line 141 "..\..\..\..\..\Windows\CleaningTaxReceiptSelectionDialog.xaml"
            this.PreviewButton.Click += new System.Windows.RoutedEventHandler(this.PreviewButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.PrintButton = ((System.Windows.Controls.Button)(target));
            
            #line 149 "..\..\..\..\..\Windows\CleaningTaxReceiptSelectionDialog.xaml"
            this.PrintButton.Click += new System.Windows.RoutedEventHandler(this.PrintButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 156 "..\..\..\..\..\Windows\CleaningTaxReceiptSelectionDialog.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

