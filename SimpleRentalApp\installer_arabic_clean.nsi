; تطبيق تدبير الكراء - سكريبت المثبت
; المطور: حفيظ عبدو
; ترميز UTF-8 لدعم اللغة العربية

; تفعيل دعم Unicode
Unicode true

!define APP_NAME "تدبير الكراء"
!define APP_VERSION "1.0.0"
!define APP_PUBLISHER "حفيظ عبدو"
!define APP_URL ""
!define APP_DESCRIPTION "نظام إدارة الكراء والعقارات"
!define APP_EXE "RentalManagement.exe"

; تضمين المكتبات المطلوبة
!include "MUI2.nsh"
!include "FileFunc.nsh"
!include "LogicLib.nsh"

; إعدادات عامة
Name "${APP_NAME}"
OutFile "تدبير_الكراء_مثبت_عربي_v${APP_VERSION}.exe"
InstallDir "$PROGRAMFILES64\${APP_NAME}"
InstallDirRegKey HKLM "Software\${APP_NAME}" "InstallDir"
RequestExecutionLevel admin

; إعدادات واجهة المستخدم الحديثة
!define MUI_ABORTWARNING
!define MUI_ICON "app_icon.ico"
!define MUI_UNICON "app_icon.ico"

; صفحات المثبت
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE.txt"
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

; صفحات إلغاء التثبيت
!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

; اللغات - تحميل ملف اللغة العربية
!insertmacro MUI_LANGUAGE "Arabic"
!insertmacro MUI_LANGUAGE "English"

; معلومات الإصدار
VIProductVersion "${APP_VERSION}.0"
VIAddVersionKey /LANG=${LANG_ARABIC} "ProductName" "${APP_NAME}"
VIAddVersionKey /LANG=${LANG_ARABIC} "Comments" "${APP_DESCRIPTION}"
VIAddVersionKey /LANG=${LANG_ARABIC} "CompanyName" "${APP_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_ARABIC} "LegalCopyright" "© 2025 ${APP_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_ARABIC} "FileDescription" "${APP_DESCRIPTION}"
VIAddVersionKey /LANG=${LANG_ARABIC} "FileVersion" "${APP_VERSION}"
VIAddVersionKey /LANG=${LANG_ARABIC} "ProductVersion" "${APP_VERSION}"

VIAddVersionKey /LANG=${LANG_ENGLISH} "ProductName" "Rental Management System"
VIAddVersionKey /LANG=${LANG_ENGLISH} "Comments" "Property Rental Management Application"
VIAddVersionKey /LANG=${LANG_ENGLISH} "CompanyName" "${APP_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "LegalCopyright" "© 2025 ${APP_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "FileDescription" "Property Rental Management Application"
VIAddVersionKey /LANG=${LANG_ENGLISH} "FileVersion" "${APP_VERSION}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "ProductVersion" "${APP_VERSION}"

; قسم التثبيت الرئيسي
Section "التطبيق الرئيسي" SecMain
  SectionIn RO
  
  ; تعيين مجلد الإخراج
  SetOutPath "$INSTDIR"
  
  ; عرض رسالة التقدم
  DetailPrint "جاري نسخ ملفات التطبيق..."
  
  ; نسخ الملفات
  File "dist\${APP_EXE}"
  File "dist\README.txt"
  File "dist\Installation_Guide.txt"
  
  ; عرض رسالة التقدم
  DetailPrint "جاري إنشاء الاختصارات..."
  
  ; إنشاء اختصار في قائمة البداية
  CreateDirectory "$SMPROGRAMS\${APP_NAME}"
  CreateShortCut "$SMPROGRAMS\${APP_NAME}\${APP_NAME}.lnk" "$INSTDIR\${APP_EXE}" "" "$INSTDIR\${APP_EXE}" 0 SW_SHOWNORMAL "" "تطبيق إدارة الكراء والعقارات"
  CreateShortCut "$SMPROGRAMS\${APP_NAME}\إلغاء التثبيت.lnk" "$INSTDIR\Uninstall.exe" "" "$INSTDIR\Uninstall.exe" 0 SW_SHOWNORMAL "" "إلغاء تثبيت ${APP_NAME}"
  
  ; إنشاء اختصار على سطح المكتب
  CreateShortCut "$DESKTOP\${APP_NAME}.lnk" "$INSTDIR\${APP_EXE}" "" "$INSTDIR\${APP_EXE}" 0 SW_SHOWNORMAL "" "تطبيق إدارة الكراء والعقارات"
  
  ; عرض رسالة التقدم
  DetailPrint "جاري تسجيل التطبيق في النظام..."
  
  ; كتابة معلومات إلغاء التثبيت في السجل
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "DisplayName" "${APP_NAME}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "UninstallString" "$INSTDIR\Uninstall.exe"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "InstallLocation" "$INSTDIR"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "DisplayIcon" "$INSTDIR\${APP_EXE}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "Publisher" "${APP_PUBLISHER}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "DisplayVersion" "${APP_VERSION}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "Comments" "${APP_DESCRIPTION}"
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "NoModify" 1
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "NoRepair" 1
  
  ; حساب حجم التثبيت
  ${GetSize} "$INSTDIR" "/S=0K" $0 $1 $2
  IntFmt $0 "0x%08X" $0
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "EstimatedSize" "$0"
  
  ; إنشاء ملف إلغاء التثبيت
  DetailPrint "جاري إنشاء أداة إلغاء التثبيت..."
  WriteUninstaller "$INSTDIR\Uninstall.exe"
  
  ; حفظ مجلد التثبيت
  WriteRegStr HKLM "Software\${APP_NAME}" "InstallDir" "$INSTDIR"
  
  ; رسالة اكتمال التثبيت
  DetailPrint "تم تثبيت ${APP_NAME} بنجاح!"
SectionEnd

; وصف الأقسام
LangString DESC_SecMain ${LANG_ARABIC} "الملفات الأساسية للتطبيق"
LangString DESC_SecMain ${LANG_ENGLISH} "Essential application files"
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
  !insertmacro MUI_DESCRIPTION_TEXT ${SecMain} $(DESC_SecMain)
!insertmacro MUI_FUNCTION_DESCRIPTION_END

; قسم إلغاء التثبيت
Section "Uninstall"
  ; رسائل التقدم باللغة العربية
  DetailPrint "جاري حذف ملفات التطبيق..."
  
  ; حذف الملفات
  Delete "$INSTDIR\${APP_EXE}"
  Delete "$INSTDIR\README.txt"
  Delete "$INSTDIR\Installation_Guide.txt"
  Delete "$INSTDIR\Uninstall.exe"
  
  DetailPrint "جاري حذف الاختصارات..."
  
  ; حذف الاختصارات
  Delete "$SMPROGRAMS\${APP_NAME}\${APP_NAME}.lnk"
  Delete "$SMPROGRAMS\${APP_NAME}\إلغاء التثبيت.lnk"
  Delete "$DESKTOP\${APP_NAME}.lnk"
  
  DetailPrint "جاري حذف المجلدات..."
  
  ; حذف المجلدات
  RMDir "$SMPROGRAMS\${APP_NAME}"
  RMDir "$INSTDIR"
  
  DetailPrint "جاري إزالة التسجيل من النظام..."
  
  ; حذف مفاتيح السجل
  DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}"
  DeleteRegKey HKLM "Software\${APP_NAME}"
  
  DetailPrint "تم إلغاء تثبيت ${APP_NAME} بنجاح!"
SectionEnd

; دالة تشغيل عند بداية التثبيت
Function .onInit
  ; عرض رسالة ترحيب
  MessageBox MB_YESNO "مرحباً بك في مثبت ${APP_NAME}$\r$\n$\r$\nهل تريد المتابعة؟" IDYES +2
  Abort
FunctionEnd

; دالة تشغيل عند انتهاء التثبيت
Function .onInstSuccess
  ; عرض رسالة نجاح التثبيت
  MessageBox MB_YESNO "تم تثبيت ${APP_NAME} بنجاح!$\r$\n$\r$\nهل تريد تشغيل التطبيق الآن؟" IDNO +2
  ExecShell "open" "$INSTDIR\${APP_EXE}"
FunctionEnd
