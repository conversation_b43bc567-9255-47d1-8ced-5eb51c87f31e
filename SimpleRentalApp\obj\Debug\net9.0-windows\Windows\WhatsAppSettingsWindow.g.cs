﻿#pragma checksum "..\..\..\..\Windows\WhatsAppSettingsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "79482C9E4984E1BB09DF386C0130EEEDA86090A3"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleRentalApp.Windows {
    
    
    /// <summary>
    /// WhatsAppSettingsWindow
    /// </summary>
    public partial class WhatsAppSettingsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 89 "..\..\..\..\Windows\WhatsAppSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton WebRadioButton;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\Windows\WhatsAppSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton AppRadioButton;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\..\Windows\WhatsAppSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CountryCodeComboBox;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\Windows\WhatsAppSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowConfirmationCheckBox;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\Windows\WhatsAppSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowPreviewCheckBox;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\Windows\WhatsAppSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoCloseCheckBox;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\..\Windows\WhatsAppSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider DelaySlider;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\..\Windows\WhatsAppSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TestPhoneTextBox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleRentalApp;V1.0.0.0;component/windows/whatsappsettingswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\WhatsAppSettingsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.WebRadioButton = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 2:
            this.AppRadioButton = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 3:
            this.CountryCodeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 4:
            this.ShowConfirmationCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 5:
            this.ShowPreviewCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 6:
            this.AutoCloseCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 7:
            this.DelaySlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 8:
            this.TestPhoneTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            
            #line 182 "..\..\..\..\Windows\WhatsAppSettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TestSendBtn_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 211 "..\..\..\..\Windows\WhatsAppSettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveBtn_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 215 "..\..\..\..\Windows\WhatsAppSettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CancelBtn_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

