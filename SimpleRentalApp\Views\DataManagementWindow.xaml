<?xml version="1.0" encoding="utf-8"?>
<Window x:Class="SimpleRentalApp.Views.DataManagementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة البيانات - تصدير واستيراد"
        Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <Style TargetType="Button" x:Key="ActionButtonStyle">
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="8"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="0.8"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Opacity" Value="0.6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style TargetType="TextBlock" x:Key="HeaderStyle">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Setter Property="Foreground" Value="#2196F3"/>
        </Style>

        <Style TargetType="TextBlock" x:Key="DescriptionStyle">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="Foreground" Value="#666"/>
        </Style>
    </Window.Resources>

    <Grid Margin="30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,30">
            <TextBlock Text="📁 إدارة البيانات" 
                       FontSize="24" FontWeight="Bold" 
                       HorizontalAlignment="Center" 
                       Foreground="#1976D2"/>
            <TextBlock Text="تصدير واستيراد بيانات التطبيق بسهولة وأمان" 
                       FontSize="14" 
                       HorizontalAlignment="Center" 
                       Foreground="#666" 
                       Margin="0,5,0,0"/>
        </StackPanel>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- Export Section -->
                <Border Background="#F8F9FA" Padding="25" Margin="0,0,0,20" CornerRadius="12">
                    <StackPanel>
                        <TextBlock Text="📤 تصدير البيانات" Style="{StaticResource HeaderStyle}"/>
                        
                        <TextBlock Text="احفظ نسخة احتياطية من بياناتك في ملفات محلية" 
                                   Style="{StaticResource DescriptionStyle}"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Export JSON -->
                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                <Button Name="ExportJsonBtn" 
                                        Content="📄 تصدير كـ JSON"
                                        Background="#4CAF50" 
                                        Foreground="White"
                                        Style="{StaticResource ActionButtonStyle}"
                                        Click="ExportJsonBtn_Click"/>
                                <TextBlock Text="ملف JSON يحتوي على جميع البيانات بتنسيق قابل للقراءة"
                                           Style="{StaticResource DescriptionStyle}"
                                           TextAlignment="Center"/>
                            </StackPanel>

                            <!-- Export Database -->
                            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                <Button Name="ExportDbBtn" 
                                        Content="🗄️ تصدير قاعدة البيانات"
                                        Background="#2196F3" 
                                        Foreground="White"
                                        Style="{StaticResource ActionButtonStyle}"
                                        Click="ExportDbBtn_Click"/>
                                <TextBlock Text="نسخة كاملة من قاعدة البيانات SQLite"
                                           Style="{StaticResource DescriptionStyle}"
                                           TextAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Import Section -->
                <Border Background="#FFF3E0" Padding="25" Margin="0,0,0,20" CornerRadius="12">
                    <StackPanel>
                        <TextBlock Text="📥 استيراد البيانات" Style="{StaticResource HeaderStyle}"/>
                        
                        <TextBlock Text="استعد بياناتك من ملفات النسخ الاحتياطية" 
                                   Style="{StaticResource DescriptionStyle}"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Import JSON -->
                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                <Button Name="ImportJsonBtn" 
                                        Content="📄 استيراد من JSON"
                                        Background="#FF9800" 
                                        Foreground="White"
                                        Style="{StaticResource ActionButtonStyle}"
                                        Click="ImportJsonBtn_Click"/>
                                <TextBlock Text="استيراد البيانات من ملف JSON محفوظ مسبقاً"
                                           Style="{StaticResource DescriptionStyle}"
                                           TextAlignment="Center"/>
                            </StackPanel>

                            <!-- Import Database -->
                            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                <Button Name="ImportDbBtn" 
                                        Content="🗄️ استيراد قاعدة البيانات"
                                        Background="#9C27B0" 
                                        Foreground="White"
                                        Style="{StaticResource ActionButtonStyle}"
                                        Click="ImportDbBtn_Click"/>
                                <TextBlock Text="استبدال قاعدة البيانات الحالية بالكامل"
                                           Style="{StaticResource DescriptionStyle}"
                                           TextAlignment="Center"/>
                            </StackPanel>
                        </Grid>

                        <!-- Import Options -->
                        <StackPanel Margin="0,20,0,0">
                            <CheckBox Name="ReplaceExistingCheckBox" 
                                      Content="استبدال البيانات الموجودة (بدلاً من الدمج)"
                                      FontSize="12"
                                      Foreground="#E65100"/>
                            <TextBlock Text="⚠️ تحذير: هذا الخيار سيحذف جميع البيانات الحالية ويستبدلها بالبيانات المستوردة"
                                       FontSize="11"
                                       Foreground="#D32F2F"
                                       Margin="20,5,0,0"
                                       TextWrapping="Wrap"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Info Section -->
                <Border Background="#E3F2FD" Padding="25" CornerRadius="12">
                    <StackPanel>
                        <TextBlock Text="ℹ️ معلومات مهمة" Style="{StaticResource HeaderStyle}"/>
                        
                        <StackPanel>
                            <TextBlock Text="📁 مجلد التصدير:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBlock Name="ExportPathText" 
                                       FontFamily="Consolas" 
                                       FontSize="11" 
                                       Background="White" 
                                       Padding="10" 
                                       Margin="0,0,0,15"/>

                            <TextBlock Text="📋 نصائح:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBlock Text="• قم بعمل نسخة احتياطية دورية من بياناتك&#x0a;• احفظ ملفات النسخ الاحتياطية في مكان آمن&#x0a;• تأكد من صحة الملفات قبل الاستيراد&#x0a;• يتم إنشاء نسخة احتياطية تلقائية قبل الاستيراد"
                                       FontSize="12"
                                       TextWrapping="Wrap"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>

        <!-- Footer Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,20,0,0">
            <Button Name="OpenExportFolderBtn" 
                    Content="📂 فتح مجلد التصدير"
                    Background="#607D8B" 
                    Foreground="White"
                    Style="{StaticResource ActionButtonStyle}"
                    Click="OpenExportFolderBtn_Click"/>
            
            <Button Name="CloseBtn" 
                    Content="إغلاق"
                    Background="#757575" 
                    Foreground="White"
                    Style="{StaticResource ActionButtonStyle}"
                    Click="CloseBtn_Click"/>
        </StackPanel>

    </Grid>
</Window>
