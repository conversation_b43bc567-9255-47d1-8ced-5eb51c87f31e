﻿#pragma checksum "..\..\..\..\..\Windows\WhatsAppSettingsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "0CC4E907837EC8D8F494E4C28C5D359F47AB8B9F"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleRentalApp.Windows {
    
    
    /// <summary>
    /// WhatsAppSettingsWindow
    /// </summary>
    public partial class WhatsAppSettingsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 133 "..\..\..\..\..\Windows\WhatsAppSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoNotificationsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\..\Windows\WhatsAppSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider MessageDelaySlider;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\..\Windows\WhatsAppSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox TestModeCheckBox;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\..\Windows\WhatsAppSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox UseCustomTemplatesCheckBox;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\..\..\Windows\WhatsAppSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoCreateTemplatesCheckBox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleRentalApp;V1.0.0.0;component/windows/whatsappsettingswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\WhatsAppSettingsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AutoNotificationsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 2:
            this.MessageDelaySlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 3:
            this.TestModeCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 4:
            this.UseCustomTemplatesCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 5:
            this.AutoCreateTemplatesCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 6:
            
            #line 192 "..\..\..\..\..\Windows\WhatsAppSettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CreateDefaultTemplatesBtn_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 197 "..\..\..\..\..\Windows\WhatsAppSettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CleanupOldNotificationsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 202 "..\..\..\..\..\Windows\WhatsAppSettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SystemStatsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 226 "..\..\..\..\..\Windows\WhatsAppSettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveBtn_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 230 "..\..\..\..\..\Windows\WhatsAppSettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseBtn_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

