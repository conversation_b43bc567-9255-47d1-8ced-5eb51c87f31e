<Window x:Class="SimpleRentalApp.Windows.WhatsAppNotificationSystemWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="🔔 نظام إشعارات الواتساب - تدبير الكراء"
        Height="900" Width="1600"
        WindowStartupLocation="CenterScreen"
        Background="#F5F5F5"
        FontFamily="Segoe UI"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <!-- Color Resources -->
        <SolidColorBrush x:Key="PrimaryColor" Color="#25D366"/>
        <SolidColorBrush x:Key="PrimaryDarkColor" Color="#128C7E"/>
        <SolidColorBrush x:Key="SecondaryColor" Color="#34495E"/>
        <SolidColorBrush x:Key="AccentColor" Color="#3498DB"/>
        <SolidColorBrush x:Key="SuccessColor" Color="#27AE60"/>
        <SolidColorBrush x:Key="WarningColor" Color="#F39C12"/>
        <SolidColorBrush x:Key="DangerColor" Color="#E74C3C"/>
        <SolidColorBrush x:Key="InfoColor" Color="#3498DB"/>
        <SolidColorBrush x:Key="BackgroundColor" Color="#F8F9FA"/>
        <SolidColorBrush x:Key="SurfaceColor" Color="White"/>
        <SolidColorBrush x:Key="BorderColor" Color="#E9ECEF"/>
        <SolidColorBrush x:Key="TextColor" Color="#2C3E50"/>

        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryColor}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource PrimaryDarkColor}"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#BDC3C7"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Success Button Style -->
        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="{StaticResource SuccessColor}"/>
        </Style>

        <!-- Warning Button Style -->
        <Style x:Key="WarningButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="{StaticResource WarningColor}"/>
        </Style>

        <!-- Danger Button Style -->
        <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="{StaticResource DangerColor}"/>
        </Style>

        <!-- Info Button Style -->
        <Style x:Key="InfoButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="{StaticResource InfoColor}"/>
        </Style>

        <!-- Modern Card Style -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Header Style -->
        <Style x:Key="HeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="{StaticResource TextColor}"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>

        <!-- SubHeader Style -->
        <Style x:Key="SubHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="{StaticResource TextColor}"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="10,10,10,5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Vertical">
                    <TextBlock Text="🔔 نظام إشعارات الواتساب" Style="{StaticResource HeaderStyle}"/>
                    <TextBlock Text="إدارة وإرسال الإشعارات الحقيقية عبر الواتساب" 
                               FontSize="14" Foreground="#7F8C8D"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="🔄 تحديث" Style="{StaticResource InfoButtonStyle}"
                            Margin="5" Click="RefreshBtn_Click"
                            ToolTip="تحديث قائمة الإشعارات"/>
                    
                    <Button Content="⚙️ الإعدادات" Style="{StaticResource ModernButtonStyle}"
                            Margin="5" Click="SettingsBtn_Click"
                            ToolTip="إعدادات نظام الإشعارات"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Control Panel -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}" Margin="10,5,10,5">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Generation Buttons -->
                <TextBlock Grid.Row="0" Text="📋 توليد الإشعارات" Style="{StaticResource SubHeaderStyle}"/>
                
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <Button Grid.Column="0" Content="🏠 إيجار شهري" Style="{StaticResource SuccessButtonStyle}"
                            Margin="5" Click="GenerateMonthlyRentBtn_Click"
                            ToolTip="توليد إشعارات الإيجار الشهري"/>

                    <Button Grid.Column="1" Content="🧹 ضريبة النظافة" Style="{StaticResource WarningButtonStyle}"
                            Margin="5" Click="GenerateCleaningTaxBtn_Click"
                            ToolTip="توليد إشعارات ضريبة النظافة"/>

                    <Button Grid.Column="2" Content="⚠️ إشعارات التأخير" Style="{StaticResource DangerButtonStyle}"
                            Margin="5" Click="GenerateOverdueBtn_Click"
                            ToolTip="توليد إشعارات التأخير"/>

                    <Button Grid.Column="3" Content="📝 قوالب الرسائل" Style="{StaticResource InfoButtonStyle}"
                            Margin="5" Click="MessageTemplatesBtn_Click"
                            ToolTip="إدارة قوالب الرسائل"/>

                    <Button Grid.Column="4" Content="📤 إرسال جماعي" Style="{StaticResource ModernButtonStyle}"
                            Margin="5" Click="BulkSendBtn_Click"
                            ToolTip="إرسال جميع الإشعارات المحددة"/>
                </Grid>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}" Margin="10,5,10,5">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Filters -->
                <TextBlock Grid.Row="0" Text="🔍 الفلاتر والبحث" Style="{StaticResource SubHeaderStyle}"/>
                
                <Grid Grid.Row="1" Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="النوع:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <ComboBox Grid.Column="1" Name="TypeFilterComboBox" Margin="0,0,15,0"
                              SelectionChanged="TypeFilterComboBox_SelectionChanged"/>

                    <TextBlock Grid.Column="2" Text="الحالة:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <ComboBox Grid.Column="3" Name="StatusFilterComboBox" Margin="0,0,15,0"
                              SelectionChanged="StatusFilterComboBox_SelectionChanged"/>

                    <TextBlock Grid.Column="4" Text="الأولوية:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <ComboBox Grid.Column="5" Name="PriorityFilterComboBox" Margin="0,0,15,0"
                              SelectionChanged="PriorityFilterComboBox_SelectionChanged"/>

                    <TextBox Grid.Column="6" Name="SearchTextBox"
                             Text=""
                             TextChanged="SearchTextBox_TextChanged">
                        <TextBox.Style>
                            <Style TargetType="TextBox">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="TextBox">
                                            <Grid>
                                                <Border Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="4"/>
                                                <ScrollViewer x:Name="PART_ContentHost" Margin="8,4"/>
                                                <TextBlock Text="البحث في الإشعارات..."
                                                          Foreground="#999999"
                                                          Margin="8,4"
                                                          IsHitTestVisible="False">
                                                    <TextBlock.Style>
                                                        <Style TargetType="TextBlock">
                                                            <Setter Property="Visibility" Value="Collapsed"/>
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding Text, RelativeSource={RelativeSource TemplatedParent}}" Value="">
                                                                    <Setter Property="Visibility" Value="Visible"/>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </TextBlock.Style>
                                                </TextBlock>
                                            </Grid>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </TextBox.Style>
                    </TextBox>
                </Grid>

                <!-- Notifications DataGrid -->
                <DataGrid Grid.Row="2" Name="NotificationsDataGrid"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          SelectionMode="Extended"
                          Background="White"
                          BorderBrush="{StaticResource BorderColor}"
                          BorderThickness="1"
                          RowHeight="50">
                    
                    <DataGrid.Columns>
                        <DataGridCheckBoxColumn Header="تحديد" Binding="{Binding IsSelected}" Width="60"/>
                        
                        <DataGridTextColumn Header="النوع" Binding="{Binding TypeDisplay}" Width="120"/>
                        
                        <DataGridTextColumn Header="المكتري" Binding="{Binding CustomerName}" Width="150"/>
                        
                        <DataGridTextColumn Header="المحل" Binding="{Binding PropertyName}" Width="150"/>
                        
                        <DataGridTextColumn Header="المبلغ" Binding="{Binding AmountDisplay}" Width="100"/>
                        
                        <DataGridTextColumn Header="تاريخ الاستحقاق" Binding="{Binding DueDateDisplay}" Width="120"/>
                        
                        <DataGridTextColumn Header="الحالة" Binding="{Binding StatusDisplay}" Width="100"/>
                        
                        <DataGridTextColumn Header="الأولوية" Binding="{Binding PriorityDisplay}" Width="80"/>
                        
                        <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedDateDisplay}" Width="140"/>
                        
                        <DataGridTemplateColumn Header="الإجراءات" Width="200">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <Button Content="👁️" ToolTip="معاينة" 
                                                Click="PreviewBtn_Click" Tag="{Binding}"
                                                Style="{StaticResource InfoButtonStyle}"
                                                Margin="2" Padding="8,4"/>
                                        
                                        <Button Content="📤" ToolTip="إرسال" 
                                                Click="SendBtn_Click" Tag="{Binding}"
                                                Style="{StaticResource SuccessButtonStyle}"
                                                Margin="2" Padding="8,4"/>
                                        
                                        <Button Content="✏️" ToolTip="تعديل" 
                                                Click="EditBtn_Click" Tag="{Binding}"
                                                Style="{StaticResource WarningButtonStyle}"
                                                Margin="2" Padding="8,4"/>
                                        
                                        <Button Content="🗑️" ToolTip="حذف" 
                                                Click="DeleteBtn_Click" Tag="{Binding}"
                                                Style="{StaticResource DangerButtonStyle}"
                                                Margin="2" Padding="8,4"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>

        <!-- Status Bar -->
        <Border Grid.Row="3" Background="{StaticResource SecondaryColor}" Padding="15,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Name="StatusTextBlock" 
                           Text="جاهز" Foreground="White" FontSize="14"/>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Name="StatisticsTextBlock" 
                               Text="الإجمالي: 0 | معلق: 0 | تم الإرسال: 0 | فشل: 0"
                               Foreground="White" FontSize="12" Margin="0,0,20,0"/>
                    
                    <TextBlock Name="LastUpdateTextBlock" 
                               Text="آخر تحديث: --"
                               Foreground="#BDC3C7" FontSize="12"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
