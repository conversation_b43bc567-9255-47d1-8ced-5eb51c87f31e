<?xml version="1.0" encoding="utf-8"?>
<Window x:Class="SimpleRentalApp.Windows.WhatsAppNotificationsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="📢 نظام إشعارات الواتساب - إدارة الكراء"
        Height="800" Width="1400"
        MinHeight="600" MinWidth="900"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        FlowDirection="RightToLeft"
        WindowState="Maximized">

    <Window.Resources>
        <!-- Color Resources - Soft & Eye-Friendly Palette -->
        <SolidColorBrush x:Key="PrimaryColor" Color="#5DADE2"/>
        <SolidColorBrush x:Key="SecondaryColor" Color="#85C1E9"/>
        <SolidColorBrush x:Key="AccentColor" Color="#3A506B"/>
        <SolidColorBrush x:Key="BackgroundColor" Color="#F2F4F7"/>
        <SolidColorBrush x:Key="SurfaceColor" Color="#FFFFFF"/>
        <SolidColorBrush x:Key="AlternateRowColor" Color="#F7F9FA"/>
        <SolidColorBrush x:Key="TextPrimaryColor" Color="#2C3E50"/>
        <SolidColorBrush x:Key="TextSecondaryColor" Color="#6C757D"/>
        <SolidColorBrush x:Key="BorderColor" Color="#DEE2E6"/>
        <SolidColorBrush x:Key="FocusBorderColor" Color="#A9CCE3"/>
        <SolidColorBrush x:Key="SuccessColor" Color="#52C41A"/>
        <SolidColorBrush x:Key="SuccessLightColor" Color="#D4EDDA"/>
        <SolidColorBrush x:Key="WarningColor" Color="#FAAD14"/>
        <SolidColorBrush x:Key="WarningLightColor" Color="#FFF3CD"/>
        <SolidColorBrush x:Key="ErrorColor" Color="#FF4D4F"/>
        <SolidColorBrush x:Key="ErrorLightColor" Color="#F8D7DA"/>
        <SolidColorBrush x:Key="InfoColor" Color="#2E8BC0"/>
        <SolidColorBrush x:Key="InfoLightColor" Color="#D6EAF8"/>
        <SolidColorBrush x:Key="HoverColor" Color="#E8F4FD"/>
        <SolidColorBrush x:Key="SelectedColor" Color="#D6EAF8"/>

        <!-- Gradient Brushes -->
        <LinearGradientBrush x:Key="HeaderGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#5DADE2" Offset="0"/>
            <GradientStop Color="#3A506B" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="SoftGradient" StartPoint="0,0" EndPoint="0,1">
            <GradientStop Color="#FFFFFF" Offset="0"/>
            <GradientStop Color="#F2F4F7" Offset="1"/>
        </LinearGradientBrush>

        <!-- Styles -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="20"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Opacity="0.3" ShadowDepth="1" BlurRadius="2"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SubHeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
        </Style>

        <Style x:Key="SoftTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Normal"/>
            <Setter Property="Foreground" Value="{StaticResource TextSecondaryColor}"/>
        </Style>

        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="Padding" Value="16,10"/>
            <Setter Property="Margin" Value="8,4"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                BorderThickness="0">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Opacity" Value="0.95"/>
                                <Setter TargetName="border" Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="#3A506B" Opacity="0.2" ShadowDepth="1" BlurRadius="6"/>
                                    </Setter.Value>
                                </Setter>
                                <Setter Property="Cursor" Value="Hand"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Opacity" Value="0.85"/>
                                <Setter TargetName="border" Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SmallButtonStyle" TargetType="Button">
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="Padding" Value="10,6"/>
            <Setter Property="Margin" Value="3,2"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="6"
                                BorderThickness="0">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Opacity" Value="0.95"/>
                                <Setter TargetName="border" Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="#3A506B" Opacity="0.15" ShadowDepth="1" BlurRadius="3"/>
                                    </Setter.Value>
                                </Setter>
                                <Setter Property="Cursor" Value="Hand"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Opacity" Value="0.85"/>
                                <Setter TargetName="border" Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.96" ScaleY="0.96"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="border" Property="Opacity" Value="0.4"/>
                                <Setter TargetName="border" Property="Background" Value="{StaticResource TextSecondaryColor}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
            <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6">
                            <ScrollViewer x:Name="PART_ContentHost"
                                        Focusable="false"
                                        HorizontalScrollBarVisibility="Hidden"
                                        VerticalScrollBarVisibility="Hidden"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource FocusBorderColor}"/>
                                <Setter TargetName="border" Property="Background" Value="{StaticResource HoverColor}"/>
                            </Trigger>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryColor}"/>
                                <Setter TargetName="border" Property="BorderThickness" Value="2"/>
                                <Setter TargetName="border" Property="Background" Value="White"/>
                                <Setter TargetName="border" Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="#5DADE2"
                                                        Opacity="0.1" ShadowDepth="0" BlurRadius="4"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox">
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
            <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
        </Style>
    </Window.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto"
                  HorizontalScrollBarVisibility="Auto"
                  Background="{StaticResource SoftGradient}">
        <Grid Margin="25">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0"
                Background="{StaticResource HeaderGradient}"
                CornerRadius="12"
                Margin="0,0,0,20"
                Padding="20,15">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="10"/>
            </Border.Effect>
            <StackPanel Orientation="Horizontal">
                <Border Background="White"
                        CornerRadius="25"
                        Width="50"
                        Height="50"
                        Margin="0,0,15,0">
                    <TextBlock Text="📱"
                               FontSize="28"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"/>
                </Border>
                <StackPanel VerticalAlignment="Center">
                    <TextBlock Text="نظام إشعارات الواتساب"
                               Style="{StaticResource HeaderTextStyle}"/>
                    <TextBlock Text="إدارة وإرسال الإشعارات للمكترين"
                               FontFamily="Segoe UI"
                               FontSize="12"
                               Foreground="White"
                               Opacity="0.9"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Action Buttons -->
        <Border Grid.Row="1"
                Background="{StaticResource SurfaceColor}"
                CornerRadius="10"
                Margin="0,0,0,20"
                Padding="20,15">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.05" ShadowDepth="1" BlurRadius="5"/>
            </Border.Effect>
            <WrapPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Name="GenerateMonthlyRentButton"
                        Content="🏠 توليد إشعارات الكراء الشهري"
                        Style="{StaticResource ActionButtonStyle}"
                        Background="{StaticResource InfoColor}"
                        Foreground="White"
                        Click="GenerateMonthlyRentButton_Click"/>

                <Button Name="GenerateCleaningTaxButton"
                        Content="🧹 توليد إشعارات ضريبة النظافة المتأخرة"
                        Style="{StaticResource ActionButtonStyle}"
                        Background="{StaticResource WarningColor}"
                        Foreground="White"
                        Click="GenerateCleaningTaxButton_Click"/>

                <Button Name="GenerateLateRemindersButton"
                        Content="⏰ توليد إشعارات متأخرة"
                        Style="{StaticResource ActionButtonStyle}"
                        Background="{StaticResource ErrorColor}"
                        Foreground="White"
                        Click="GenerateLateRemindersButton_Click"/>

                <Border Background="{StaticResource BackgroundColor}"
                        CornerRadius="8"
                        Padding="12,8"
                        Margin="8,4"
                        VerticalAlignment="Center">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="أيام التأخير:"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"
                                   FontFamily="Segoe UI"
                                   FontWeight="Medium"
                                   FontSize="12"
                                   Foreground="{StaticResource TextPrimaryColor}"/>
                        <TextBox Name="DaysLateTextBox"
                                 Width="60"
                                 Text="3"
                                 VerticalAlignment="Center"
                                 HorizontalContentAlignment="Center"
                                 Style="{StaticResource ModernTextBoxStyle}"/>
                        <TextBlock Text="أيام"
                                   VerticalAlignment="Center"
                                   Margin="8,0,0,0"
                                   FontFamily="Segoe UI"
                                   FontSize="12"
                                   Foreground="{StaticResource TextSecondaryColor}"/>
                    </StackPanel>
                </Border>

                <Button Name="SendAllButton"
                        Content="📤 إرسال الكل"
                        Style="{StaticResource ActionButtonStyle}"
                        Background="{StaticResource PrimaryColor}"
                        Foreground="White"
                        Click="SendAllButton_Click"/>

                <Button Name="TemplatesButton"
                        Content="📝 إدارة القوالب"
                        Style="{StaticResource ActionButtonStyle}"
                        Background="#9B59B6"
                        Foreground="White"
                        Click="TemplatesButton_Click"/>
            </WrapPanel>
        </Border>

        <!-- Filters -->
        <Border Grid.Row="2"
                Background="{StaticResource SurfaceColor}"
                CornerRadius="10"
                Margin="0,0,0,20"
                Padding="20,15">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.05" ShadowDepth="1" BlurRadius="5"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border Background="{StaticResource AccentColor}"
                            CornerRadius="20"
                            Padding="12,6"
                            Margin="0,0,15,0">
                        <TextBlock Text="🔍 تصفية البيانات"
                                   Foreground="White"
                                   FontFamily="Segoe UI"
                                   FontWeight="SemiBold"
                                   FontSize="12"/>
                    </Border>

                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="النوع:"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"
                                   FontFamily="Segoe UI"
                                   FontWeight="Medium"
                                   FontSize="12"
                                   Foreground="{StaticResource TextPrimaryColor}"/>
                        <ComboBox Name="TypeFilterComboBox"
                                  Width="160"
                                  Margin="0,0,15,0"
                                  Style="{StaticResource ModernComboBoxStyle}"
                                  SelectionChanged="TypeFilterComboBox_SelectionChanged">
                            <ComboBoxItem Content="جميع الأنواع" IsSelected="True"/>
                            <ComboBoxItem Content="الكراء الشهري" Tag="MonthlyRent"/>
                            <ComboBoxItem Content="ضريبة النظافة" Tag="CleaningTax"/>
                            <ComboBoxItem Content="دفعة متأخرة" Tag="OverduePayment"/>
                            <ComboBoxItem Content="إشعار متأخر" Tag="LateReminder"/>
                        </ComboBox>

                        <TextBlock Text="الحالة:"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"
                                   FontFamily="Segoe UI"
                                   FontWeight="Medium"
                                   FontSize="12"
                                   Foreground="{StaticResource TextPrimaryColor}"/>
                        <ComboBox Name="StatusFilterComboBox"
                                  Width="130"
                                  Margin="0,0,15,0"
                                  Style="{StaticResource ModernComboBoxStyle}"
                                  SelectionChanged="StatusFilterComboBox_SelectionChanged">
                            <ComboBoxItem Content="جميع الحالات" IsSelected="True"/>
                            <ComboBoxItem Content="⏳ معلق" Tag="Pending"/>
                            <ComboBoxItem Content="✅ تم الإرسال" Tag="Sent"/>
                            <ComboBoxItem Content="❌ فشل" Tag="Failed"/>
                            <ComboBoxItem Content="🚫 ملغي" Tag="Cancelled"/>
                        </ComboBox>

                        <!-- Date Range Filter -->
                        <TextBlock Text="الفترة:"
                                   VerticalAlignment="Center"
                                   Margin="15,0,8,0"
                                   FontFamily="Segoe UI"
                                   FontWeight="Medium"
                                   FontSize="12"
                                   Foreground="{StaticResource TextPrimaryColor}"/>
                        <ComboBox Name="DateRangeFilterComboBox"
                                  Width="140"
                                  Margin="0,0,15,0"
                                  Style="{StaticResource ModernComboBoxStyle}"
                                  SelectionChanged="DateRangeFilterComboBox_SelectionChanged">
                            <ComboBoxItem Content="جميع التواريخ" IsSelected="True"/>
                            <ComboBoxItem Content="اليوم" Tag="Today"/>
                            <ComboBoxItem Content="هذا الأسبوع" Tag="ThisWeek"/>
                            <ComboBoxItem Content="هذا الشهر" Tag="ThisMonth"/>
                            <ComboBoxItem Content="متأخرة" Tag="Overdue"/>
                            <ComboBoxItem Content="قريبة الاستحقاق" Tag="DueSoon"/>
                        </ComboBox>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Center">
                    <!-- Sort Controls -->
                    <TextBlock Text="ترتيب:"
                               VerticalAlignment="Center"
                               Margin="0,0,8,0"
                               FontFamily="Segoe UI"
                               FontWeight="Medium"
                               FontSize="12"
                               Foreground="{StaticResource TextPrimaryColor}"/>
                    <ComboBox Name="SortComboBox"
                              Width="120"
                              Margin="0,0,10,0"
                              Style="{StaticResource ModernComboBoxStyle}"
                              SelectionChanged="SortComboBox_SelectionChanged">
                        <ComboBoxItem Content="تاريخ الاستحقاق" Tag="DueDate" IsSelected="True"/>
                        <ComboBoxItem Content="تاريخ الإنشاء" Tag="CreatedDate"/>
                        <ComboBoxItem Content="اسم المكتري" Tag="CustomerName"/>
                        <ComboBoxItem Content="نوع الإشعار" Tag="Type"/>
                        <ComboBoxItem Content="الحالة" Tag="Status"/>
                    </ComboBox>

                    <Button Name="SortDirectionButton"
                            Content="⬇️"
                            Style="{StaticResource SmallButtonStyle}"
                            Background="{StaticResource AccentColor}"
                            Foreground="White"
                            Margin="0,0,15,0"
                            Padding="6,4"
                            FontSize="12"
                            Click="SortDirectionButton_Click"
                            ToolTip="تغيير اتجاه الترتيب"/>

                    <!-- Search -->
                    <TextBlock Text="🔍"
                               VerticalAlignment="Center"
                               FontSize="16"
                               Margin="0,0,8,0"/>
                    <TextBox Name="SearchTextBox"
                             Width="200"
                             Style="{StaticResource ModernTextBoxStyle}"
                             Text="البحث بالاسم أو المحل..."
                             Foreground="{StaticResource TextSecondaryColor}"
                             GotFocus="SearchTextBox_GotFocus"
                             LostFocus="SearchTextBox_LostFocus"
                             TextChanged="SearchTextBox_TextChanged"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main DataGrid -->
        <Border Grid.Row="3"
                Background="{StaticResource SurfaceColor}"
                CornerRadius="12"
                Margin="0,0,0,20"
                Padding="0">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.08" ShadowDepth="2" BlurRadius="10"/>
            </Border.Effect>
            <DataGrid Name="NotificationsDataGrid"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      IsReadOnly="True"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column"
                      AlternatingRowBackground="{StaticResource AlternateRowColor}"
                      Background="{StaticResource SurfaceColor}"
                      RowBackground="{StaticResource SurfaceColor}"
                      SelectionMode="Single"
                      FontFamily="Segoe UI"
                      FontSize="12"
                      RowHeight="45"
                      ColumnHeaderHeight="50"
                      BorderThickness="0">

                <DataGrid.Resources>
                    <Style TargetType="DataGridColumnHeader">
                        <Setter Property="Background" Value="{StaticResource AccentColor}"/>
                        <Setter Property="Foreground" Value="White"/>
                        <Setter Property="FontFamily" Value="Segoe UI"/>
                        <Setter Property="FontWeight" Value="SemiBold"/>
                        <Setter Property="FontSize" Value="13"/>
                        <Setter Property="Padding" Value="12,8"/>
                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                        <Setter Property="VerticalContentAlignment" Value="Center"/>
                        <Setter Property="BorderThickness" Value="0,0,1,0"/>
                        <Setter Property="BorderBrush" Value="White"/>
                    </Style>

                    <Style TargetType="DataGridRow">
                        <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
                        <Setter Property="BorderThickness" Value="0"/>
                        <Setter Property="MinHeight" Value="45"/>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource HoverColor}"/>
                                <Setter Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="#5DADE2"
                                                        Opacity="0.05" ShadowDepth="0" BlurRadius="2"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Background" Value="{StaticResource SelectedColor}"/>
                                <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
                                <Setter Property="FontWeight" Value="Medium"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>

                    <Style TargetType="DataGridCell">
                        <Setter Property="BorderThickness" Value="0"/>
                        <Setter Property="Padding" Value="12,8"/>
                        <Setter Property="VerticalAlignment" Value="Center"/>
                        <Setter Property="HorizontalAlignment" Value="Center"/>
                        <Setter Property="FontFamily" Value="Segoe UI"/>
                        <Setter Property="FontSize" Value="12"/>
                        <Style.Triggers>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Background" Value="Transparent"/>
                                <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
                                <Setter Property="FontWeight" Value="Medium"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </DataGrid.Resources>
                
                <DataGrid.Columns>
                    <!-- معلومات المكتري والمحل -->
                    <DataGridTextColumn Header="المكتري" Binding="{Binding CustomerName}" Width="150">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="FontWeight" Value="SemiBold"/>
                                <Setter Property="Foreground" Value="#2C3E50"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <DataGridTextColumn Header="المحل" Binding="{Binding PropertyName}" Width="150">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="FontWeight" Value="Medium"/>
                                <Setter Property="Foreground" Value="#34495E"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- نوع الإشعار مع أيقونة -->
                    <DataGridTextColumn Header="نوع الإشعار" Binding="{Binding TypeDisplay}" Width="140">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="FontWeight" Value="Medium"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- تاريخ الاستحقاق -->
                    <DataGridTextColumn Header="تاريخ الاستحقاق" Binding="{Binding DueDateDisplay}" Width="120">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="FontFamily" Value="Consolas"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- الأيام المتبقية/المتأخرة -->
                    <DataGridTemplateColumn Header="المدة المتبقية" Width="120">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border Background="{Binding DaysRemainingColor}"
                                        CornerRadius="12" Padding="8,4" Margin="2">
                                    <TextBlock Text="{Binding DaysRemainingDisplay}"
                                               Foreground="White" FontWeight="Bold"
                                               HorizontalAlignment="Center" FontSize="11"/>
                                </Border>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- المبلغ -->
                    <DataGridTextColumn Header="المبلغ" Binding="{Binding AmountDisplay}" Width="100">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="FontWeight" Value="Bold"/>
                                <Setter Property="Foreground" Value="#27AE60"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- حالة الإشعار -->
                    <DataGridTemplateColumn Header="الحالة" Width="120">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border Background="{Binding StatusColor}"
                                        CornerRadius="15" Padding="10,5" Margin="2">
                                    <TextBlock Text="{Binding StatusDisplay}"
                                               Foreground="White" FontWeight="Bold"
                                               HorizontalAlignment="Center" FontSize="11"/>
                                </Border>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- تاريخ الإنشاء -->
                    <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedDateDisplay}" Width="140">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="FontFamily" Value="Consolas"/>
                                <Setter Property="FontSize" Value="10"/>
                                <Setter Property="Foreground" Value="#6C757D"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <!-- الإجراءات -->
                    <DataGridTemplateColumn Header="الإجراءات" Width="350">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <WrapPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <!-- تعديل التاريخ -->
                                    <Button Content="📅 تعديل"
                                            Style="{StaticResource SmallButtonStyle}"
                                            Background="#6C5CE7"
                                            Foreground="White"
                                            Margin="1"
                                            Padding="5,3"
                                            FontSize="9"
                                            Click="EditDateButton_Click"
                                            Tag="{Binding Id}"
                                            ToolTip="تعديل تاريخ الاستحقاق"/>

                                    <!-- إرسال الإشعار -->
                                    <Button Content="📤 إرسال"
                                            Style="{StaticResource SmallButtonStyle}"
                                            Background="{StaticResource SuccessColor}"
                                            Foreground="White"
                                            Margin="1"
                                            Padding="5,3"
                                            FontSize="9"
                                            Click="SendNotificationButton_Click"
                                            Tag="{Binding Id}"
                                            ToolTip="إرسال الإشعار"/>

                                    <!-- معاينة الرسالة -->
                                    <Button Content="👁️ معاينة"
                                            Style="{StaticResource SmallButtonStyle}"
                                            Background="{StaticResource AccentColor}"
                                            Foreground="White"
                                            Margin="1"
                                            Padding="5,3"
                                            FontSize="9"
                                            Click="PreviewNotificationButton_Click"
                                            Tag="{Binding Id}"
                                            ToolTip="معاينة الرسالة"/>

                                    <!-- تذكير متأخر -->
                                    <Button Content="⏰ متأخر"
                                            Style="{StaticResource SmallButtonStyle}"
                                            Background="{StaticResource ErrorColor}"
                                            Foreground="White"
                                            Margin="1"
                                            Padding="5,3"
                                            FontSize="9"
                                            Click="SendLateReminderButton_Click"
                                            Tag="{Binding Id}"
                                            ToolTip="إرسال تذكير متأخر"/>

                                    <!-- حذف -->
                                    <Button Content="🗑️"
                                            Style="{StaticResource SmallButtonStyle}"
                                            Background="#DC3545"
                                            Foreground="White"
                                            Margin="1"
                                            Padding="5,3"
                                            FontSize="9"
                                            Click="DeleteNotificationButton_Click"
                                            Tag="{Binding Id}"
                                            ToolTip="حذف الإشعار"/>
                                </WrapPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- Status Bar -->
        <Border Grid.Row="4"
                Background="{StaticResource AccentColor}"
                CornerRadius="10"
                Padding="20,12">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="1" BlurRadius="5"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border Background="White"
                            CornerRadius="15"
                            Padding="8,4"
                            Margin="0,0,10,0">
                        <TextBlock Name="StatusTextBlock"
                                   Text="جاهز"
                                   FontFamily="Segoe UI"
                                   FontWeight="Medium"
                                   FontSize="12"
                                   Foreground="{StaticResource AccentColor}"/>
                    </Border>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border Background="White"
                            CornerRadius="15"
                            Padding="10,6"
                            Margin="5,0"
                            Opacity="0.95">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📊" FontSize="14" Margin="0,0,5,0"/>
                            <TextBlock Name="CountTextBlock"
                                       Text="0 إشعار"
                                       FontFamily="Segoe UI"
                                       FontWeight="SemiBold"
                                       FontSize="12"
                                       Foreground="{StaticResource AccentColor}"/>
                        </StackPanel>
                    </Border>

                    <Border Background="{StaticResource WarningLightColor}"
                            CornerRadius="15"
                            Padding="10,6"
                            Margin="5,0"
                            BorderBrush="{StaticResource WarningColor}"
                            BorderThickness="1">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="⏳" FontSize="14" Margin="0,0,5,0"/>
                            <TextBlock Name="PendingCountTextBlock"
                                       Text="0 معلق"
                                       FontFamily="Segoe UI"
                                       FontWeight="SemiBold"
                                       FontSize="12"
                                       Foreground="{StaticResource WarningColor}"/>
                        </StackPanel>
                    </Border>

                    <Border Background="{StaticResource SuccessLightColor}"
                            CornerRadius="15"
                            Padding="10,6"
                            Margin="5,0"
                            BorderBrush="{StaticResource SuccessColor}"
                            BorderThickness="1">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="✅" FontSize="14" Margin="0,0,5,0"/>
                            <TextBlock Name="SentCountTextBlock"
                                       Text="0 مرسل"
                                       FontFamily="Segoe UI"
                                       FontWeight="SemiBold"
                                       FontSize="12"
                                       Foreground="{StaticResource SuccessColor}"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>
        </Grid>
    </ScrollViewer>
</Window>
