using System.Security.Cryptography;
using System.Text;
using SimpleRentalApp.Models;

namespace SimpleRentalApp.Services;

public class AuthService
{
    private static AuthService? _instance;
    public static AuthService Instance => _instance ??= new AuthService();
    
    public User? CurrentUser { get; private set; }
    
    private AuthService() { }
    
    public string HashPassword(string password)
    {
        using var sha256 = SHA256.Create();
        var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + "SALT_KEY_2024"));
        return Convert.ToBase64String(hashedBytes);
    }
    
    public async Task<bool> LoginAsync(string username, string password)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine($"Attempting login for: {username}");

            var user = await DatabaseService.Instance.GetUserByUsernameAsync(username);
            System.Diagnostics.Debug.WriteLine($"User found: {user != null}");

            if (user == null || !user.IsActive)
            {
                System.Diagnostics.Debug.WriteLine("User not found or inactive");
                return false;
            }

            System.Diagnostics.Debug.WriteLine($"Stored password: {user.PasswordHash}, Entered: {password}");

            // For now, use simple password comparison (will be improved later)
            if (user.PasswordHash != password)
            {
                System.Diagnostics.Debug.WriteLine("Password mismatch");
                return false;
            }

            CurrentUser = user;
            user.LastLoginDate = DateTime.Now;
            await DatabaseService.Instance.SaveUserAsync(user);

            System.Diagnostics.Debug.WriteLine("Login successful");
            return true;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Login error: {ex.Message}");
            throw; // Re-throw to see the error in UI
        }
    }
    
    public void Logout()
    {
        CurrentUser = null;
    }
    
    public bool IsLoggedIn => CurrentUser != null;
    
    public bool HasPermission(UserRole requiredRole)
    {
        if (CurrentUser == null) return false;
        return CurrentUser.Role <= requiredRole; // Admin=1, Manager=2, Employee=3
    }
    
    public async Task<User> CreateUserAsync(string username, string fullName, string password, string email, UserRole role)
    {
        var user = new User
        {
            Username = username,
            FullName = fullName,
            PasswordHash = HashPassword(password),
            Email = email,
            Role = role,
            CreatedDate = DateTime.Now
        };
        
        return await DatabaseService.Instance.SaveUserAsync(user);
    }
}
