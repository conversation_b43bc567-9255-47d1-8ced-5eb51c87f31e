<Window x:Class="SimpleRentalApp.Windows.CleaningTaxReceiptPreviewWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="معاينة وصل ضريبة النظافة"
        Height="800"
        Width="900"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        Background="#F8F9FA"
        ResizeMode="CanResize"
        WindowStyle="None"
        AllowsTransparency="True">

    <Window.Resources>
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="PrintButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#4CAF50"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#388E3C"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="CloseButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#757575"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#616161"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Border Background="White" CornerRadius="12" Margin="20">
        <Border.Effect>
            <DropShadowEffect Color="#424242"
                            Direction="270"
                            ShadowDepth="12"
                            BlurRadius="24"
                            Opacity="0.3"/>
        </Border.Effect>

        <Grid Margin="30">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Modern Header -->
            <Border Grid.Row="0"
                    Background="#1976D2"
                    CornerRadius="8"
                    Padding="20,15"
                    Margin="0,0,0,25">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <TextBlock Text="👁️" FontSize="24" Margin="0,0,10,0"/>
                        <TextBlock Text="معاينة وصل ضريبة النظافة"
                                  FontSize="18"
                                  FontWeight="Bold"
                                  Foreground="White"/>
                    </StackPanel>

                    <!-- Property Info -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <TextBlock Name="PropertyInfoTextBlock"
                                  Text=""
                                  FontSize="12"
                                  Foreground="White"
                                  VerticalAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Enhanced Preview Area -->
            <Border Grid.Row="1"
                    Background="#F8F9FA"
                    BorderBrush="#E0E0E0"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="20"
                    Margin="0,0,0,20">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Preview Controls -->
                    <Border Grid.Row="0"
                            Background="White"
                            CornerRadius="6"
                            Padding="15,10"
                            Margin="0,0,0,15"
                            BorderBrush="#E0E0E0"
                            BorderThickness="1">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Orientation="Horizontal">
                                <TextBlock Text="📄" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                <TextBlock Name="ReceiptTitleTextBlock"
                                          Text="وصل ضريبة النظافة"
                                          FontSize="14"
                                          FontWeight="Bold"
                                          VerticalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <Button Name="ExportPdfButton"
                                       Content="📄 تصدير PDF"
                                       Style="{StaticResource ModernButton}"
                                       Click="ExportPdfButton_Click"
                                       Margin="5,0"/>
                                <Button Name="ZoomInButton"
                                       Content="🔍+"
                                       Style="{StaticResource ModernButton}"
                                       Click="ZoomInButton_Click"
                                       Width="40"
                                       Margin="2,0"/>
                                <Button Name="ZoomOutButton"
                                       Content="🔍-"
                                       Style="{StaticResource ModernButton}"
                                       Click="ZoomOutButton_Click"
                                       Width="40"
                                       Margin="2,0"/>
                                <Button Name="ResetZoomButton"
                                       Content="↻"
                                       Style="{StaticResource ModernButton}"
                                       Click="ResetZoomButton_Click"
                                       Width="40"
                                       Margin="2,0"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- Receipt Preview -->
                    <Border Grid.Row="1"
                            Background="White"
                            BorderBrush="#E0E0E0"
                            BorderThickness="1"
                            CornerRadius="6">
                        <ScrollViewer Name="PreviewScrollViewer"
                                     HorizontalScrollBarVisibility="Auto"
                                     VerticalScrollBarVisibility="Auto"
                                     CanContentScroll="True"
                                     Background="Transparent">
                            <Border Name="ReceiptPreviewBorder"
                                   Background="White"
                                   Margin="20"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Top">
                                <!-- Receipt content will be loaded here -->
                                <ContentPresenter Name="ReceiptContentPresenter"/>
                            </Border>
                        </ScrollViewer>
                    </Border>
                </Grid>
            </Border>

            <!-- Action Buttons -->
            <StackPanel Grid.Row="2"
                       Orientation="Horizontal"
                       HorizontalAlignment="Center"
                       Margin="0,20,0,0">
                <Button Name="PrintFromPreviewButton"
                       Content="🖨️ طباعة الوصل"
                       Style="{StaticResource PrintButton}"
                       Click="PrintFromPreviewButton_Click"
                       Padding="20,10"
                       Margin="10,0"
                       FontWeight="Bold"/>
                <Button Name="ClosePreviewButton"
                       Content="❌ إغلاق"
                       Style="{StaticResource CloseButton}"
                       Click="ClosePreviewButton_Click"
                       Padding="20,10"
                       Margin="10,0"
                       FontWeight="Bold"/>
            </StackPanel>
        </Grid>
    </Border>
</Window>
