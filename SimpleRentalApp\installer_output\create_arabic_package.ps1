# Arabic Package Creator for Rental Management System
# Creator: <PERSON><PERSON><PERSON>(
    [string]$OutputName = "RentalManagement_Arabic_v1.0.zip"
)

Write-Host "========================================" -ForegroundColor Green
Write-Host "    Creating Arabic Installation Package" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Check if portable folder exists
if (-not (Test-Path "portable")) {
    Write-Host "Error: portable folder not found" -ForegroundColor Red
    exit 1
}

# Create temporary directory for Arabic package
$tempDir = "arabic_package_temp"
if (Test-Path $tempDir) {
    Remove-Item $tempDir -Recurse -Force
}
New-Item -ItemType Directory -Path $tempDir | Out-Null

Write-Host "[1/5] Copying application files..." -ForegroundColor Cyan

# Copy portable files
Copy-Item "portable\*" -Destination $tempDir -Recurse

Write-Host "[2/5] Adding Arabic files..." -ForegroundColor Cyan

# Copy Arabic documentation
if (Test-Path "README_Arabic.txt") {
    Copy-Item "README_Arabic.txt" -Destination "$tempDir\README_Arabic.txt"
}
if (Test-Path "LICENSE_Arabic.txt") {
    Copy-Item "LICENSE_Arabic.txt" -Destination "$tempDir\LICENSE_Arabic.txt"
}

Write-Host "[3/5] Creating Arabic launcher files..." -ForegroundColor Cyan

# Create Arabic batch file
$arabicBatchContent = @"
@echo off
chcp 65001 > nul
title Rental Management System
echo ========================================
echo       Rental Management System
echo          نظام تدبير الكراء
echo ========================================
echo.
echo Developer: Hafid Abdo
echo Version: 1.0.0
echo.
echo Starting system...
echo.
start SimpleRentalApp.exe
"@

$arabicBatchContent | Out-File -FilePath "$tempDir\run_arabic.bat" -Encoding UTF8

# Create installation instructions in English (to avoid encoding issues)
$installInstructions = @"
Rental Management System - Arabic Version
==========================================

Welcome to the Rental Management System!

Installation Steps:
==================
1. Extract this file to any folder you want
2. Run "run_arabic.bat" or "SimpleRentalApp.exe"
3. Login with the following credentials:
   - Username: hafid
   - Password: hafidos159357

Important Files:
===============
• run_arabic.bat - To run the program
• SimpleRentalApp.exe - Main application file
• README_Arabic.txt - Complete user guide in Arabic
• LICENSE_Arabic.txt - License agreement in Arabic

System Requirements:
===================
• Windows 10 or later
• 4 GB RAM
• 200 MB free space

Features:
=========
✓ Property and shop management
✓ Rental contract management
✓ Payment tracking
✓ Receipt generation
✓ Cleaning tax management
✓ WhatsApp notifications
✓ Reports and statistics
✓ Backup system

Technical Support:
=================
Developer: Hafid Abdo
Please contact the developer for support

Thank you for using the Rental Management System!

Note: The application interface is in Arabic and fully supports
Arabic text input and display.
"@

$installInstructions | Out-File -FilePath "$tempDir\INSTALLATION_INSTRUCTIONS.txt" -Encoding UTF8

Write-Host "[4/5] Creating compressed file..." -ForegroundColor Cyan

# Create ZIP file
if (Test-Path $OutputName) {
    Remove-Item $OutputName -Force
}

Add-Type -AssemblyName System.IO.Compression.FileSystem
[System.IO.Compression.ZipFile]::CreateFromDirectory($tempDir, $OutputName)

Write-Host "[5/5] Cleaning temporary files..." -ForegroundColor Cyan

# Cleanup
Remove-Item $tempDir -Recurse -Force

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Package created successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "File name: $OutputName" -ForegroundColor Yellow

if (Test-Path $OutputName) {
    $fileSize = (Get-Item $OutputName).Length
    $fileSizeMB = [math]::Round($fileSize / 1MB, 2)
    Write-Host "File size: $fileSizeMB MB" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Package contents:" -ForegroundColor Cyan
Write-Host "• All application files" -ForegroundColor White
Write-Host "• Arabic user guide" -ForegroundColor White
Write-Host "• Arabic launcher file" -ForegroundColor White
Write-Host "• Installation instructions" -ForegroundColor White
Write-Host "• License agreement" -ForegroundColor White
Write-Host ""
Write-Host "Package ready for distribution!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
