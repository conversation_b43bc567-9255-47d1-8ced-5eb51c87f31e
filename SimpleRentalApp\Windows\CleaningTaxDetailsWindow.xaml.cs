using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using SimpleRentalApp.Models;
using SimpleRentalApp.Services;
using SimpleRentalApp.Utils;

namespace SimpleRentalApp.Windows
{
    public partial class CleaningTaxDetailsWindow : Window
    {
        private readonly Property _property;
        private readonly int _selectedYear;
        private List<PaymentDisplayInfo> _payments = new();
        private decimal _annualTax;
        private Customer? _customer;
        private readonly CleaningTaxCalculationService _calculationService;

        public CleaningTaxDetailsWindow(Property property, int selectedYear)
        {
            InitializeComponent();
            _property = property;
            _selectedYear = selectedYear;
            _calculationService = new CleaningTaxCalculationService();
            InitializeWindow();
        }

        private async void InitializeWindow()
        {
            try
            {
                await LoadPropertyData();
                await LoadPayments();
                UpdateUI();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadPropertyData()
        {
            // Load customer data
            if (_property.CustomerId.HasValue)
            {
                _customer = await DatabaseService.Instance.GetCustomerByIdAsync(_property.CustomerId.Value);
            }

            // Calculate annual tax
            _annualTax = _property.MonthlyRent * 12 * 0.105m;
        }

        private async Task LoadPayments()
        {
            try
            {
                var allPayments = await DatabaseService.Instance.GetPaymentsAsync();
                var propertyPayments = allPayments.Where(p => 
                    p.PropertyId == _property.Id && 
                    p.PaymentDate?.Year == _selectedYear && 
                    p.CleaningTaxAmount > 0).ToList();

                _payments = new List<PaymentDisplayInfo>();

                foreach (var payment in propertyPayments)
                {
                    // حساب المبلغ المتبقي قبل هذه الدفعة
                    var remainingBefore = await _calculationService.CalculateRemainingAmountBeforePaymentAsync(
                        _property.Id, _selectedYear, payment.PaymentDate ?? DateTime.Now, payment.Id);

                    // حساب المبلغ المتبقي بعد هذه الدفعة
                    var remainingAfter = await _calculationService.CalculateRemainingAmountAfterPaymentAsync(
                        _property.Id, _selectedYear, payment.PaymentDate ?? DateTime.Now,
                        payment.CleaningTaxAmount, payment.Id);

                    var displayInfo = new PaymentDisplayInfo
                    {
                        Payment = payment,
                        PaymentDateDisplay = payment.PaymentDate?.ToString("dd/MM/yyyy") ?? "غير محدد",
                        CleaningTaxAmountDisplay = $"{payment.CleaningTaxAmount:N0} درهم",
                        AmountInWords = NumberToArabicWords.ConvertCurrencyToWords(payment.CleaningTaxAmount),
                        PaymentMethod = payment.PaymentMethod ?? "نقداً",
                        StatusDisplay = GetStatusDisplay(payment.Status),
                        Notes = payment.Notes ?? "",
                        ReceiptNumber = payment.ReceiptNumber ?? "", // رقم التوصيل الجديد
                        RemainingBeforePayment = remainingBefore,
                        RemainingAfterPayment = remainingAfter
                    };

                    _payments.Add(displayInfo);
                }

                // ترتيب الدفعات حسب التاريخ (الأحدث أولاً للعرض)
                _payments = _payments.OrderByDescending(p => p.Payment.PaymentDate).ToList();

                PaymentsDataGrid.ItemsSource = _payments;
                StatusTextBlock.Text = $"تم تحميل {_payments.Count} دفعة";
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "خطأ في تحميل الدفعات";
                MessageBox.Show($"خطأ في تحميل الدفعات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateUI()
        {
            // Update property information
            PropertyNameText.Text = $"المحل: {_property.Name}";
            CustomerNameText.Text = _customer != null ?
                $"المكتري: {_customer.FullName}" : "لا يوجد مكتري";

            // Update year and tax information
            YearText.Text = $"السنة الضريبية: {_selectedYear}";
            AnnualTaxText.Text = $"الضريبة السنوية: {_annualTax:N0} درهم";
            PaymentOptionText.Text = $"طريقة الدفع: {_property.CleaningTaxPaymentOptionDisplay ?? "غير محدد"}";

            // Calculate and display payment statistics
            var paidAmount = _payments.Where(p => p.Payment.Status == PaymentStatus.Paid)
                                    .Sum(p => p.Payment.CleaningTaxAmount);
            var remainingAmount = Math.Max(0, _annualTax - paidAmount);

            PaidAmountText.Text = $"المدفوع: {paidAmount:N0} درهم";
            RemainingAmountText.Text = $"المتبقي: {remainingAmount:N0} درهم";
        }

        private string GetStatusDisplay(PaymentStatus status)
        {
            return status switch
            {
                PaymentStatus.Paid => "مدفوع",
                PaymentStatus.Unpaid => "غير مدفوع",
                PaymentStatus.Partial => "دفع جزئي",
                PaymentStatus.Overdue => "متأخر",
                _ => "غير محدد"
            };
        }

        private void PaymentsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var isSelected = PaymentsDataGrid.SelectedItem != null;
            EditPaymentButton.IsEnabled = isSelected;
            DeletePaymentButton.IsEnabled = isSelected;
        }

        private async void AddPaymentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var dialog = new SimpleCleaningTaxPaymentDialog(_property, _selectedYear);
                dialog.Owner = this;

                if (dialog.ShowDialog() == true)
                {
                    await LoadPayments();
                    UpdateUI();
                    StatusTextBlock.Text = "تم إضافة دفعة جديدة بنجاح";
                }
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "خطأ في إضافة الدفعة";
                MessageBox.Show($"خطأ في إضافة دفعة جديدة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void EditPaymentButton_Click(object sender, RoutedEventArgs e)
        {
            if (PaymentsDataGrid.SelectedItem is not PaymentDisplayInfo selectedPayment)
                return;

            try
            {
                var dialog = new EditPaymentDialog(selectedPayment.Payment, _property);
                dialog.Owner = this;
                
                if (dialog.ShowDialog() == true)
                {
                    await LoadPayments();
                    UpdateUI();
                    StatusTextBlock.Text = "تم تعديل الدفعة بنجاح";
                }
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "خطأ في تعديل الدفعة";
                MessageBox.Show($"خطأ في تعديل الدفعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void DeletePaymentButton_Click(object sender, RoutedEventArgs e)
        {
            if (PaymentsDataGrid.SelectedItem is not PaymentDisplayInfo selectedPayment)
                return;

            try
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف هذه الدفعة؟\n\nالتاريخ: {selectedPayment.PaymentDateDisplay}\nالمبلغ: {selectedPayment.CleaningTaxAmountDisplay}",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    await DatabaseService.Instance.DeletePaymentAsync(selectedPayment.Payment.Id);
                    await LoadPayments();
                    UpdateUI();
                    StatusTextBlock.Text = "تم حذف الدفعة بنجاح";
                }
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "خطأ في حذف الدفعة";
                MessageBox.Show($"خطأ في حذف الدفعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                await LoadPayments();
                UpdateUI();
                StatusTextBlock.Text = "تم تحديث البيانات";
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "خطأ في تحديث البيانات";
                MessageBox.Show($"خطأ في تحديث البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }

    public class PaymentDisplayInfo
    {
        public Payment Payment { get; set; } = new();
        public string PaymentDateDisplay { get; set; } = string.Empty;
        public string CleaningTaxAmountDisplay { get; set; } = string.Empty;
        public string AmountInWords { get; set; } = string.Empty;
        public string PaymentMethod { get; set; } = string.Empty;
        public string StatusDisplay { get; set; } = string.Empty;
        public string Notes { get; set; } = string.Empty;
        public string ReceiptNumber { get; set; } = string.Empty; // رقم التوصيل الجديد
        public decimal RemainingBeforePayment { get; set; }
        public decimal RemainingAfterPayment { get; set; }

        // Additional properties for PaymentsManagementWindow
        public int Id => Payment?.Id ?? 0;
        public string PropertyName { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string TypeDisplay => Payment?.TypeDisplay ?? string.Empty;
        public string RentAmountDisplay => $"{Payment?.RentAmount ?? 0:N0} درهم";
        public string TotalAmountDisplay => $"{Payment?.TotalAmount ?? 0:N0} درهم";
        public string DueDateDisplay => Payment?.DueDate.ToString("yyyy/MM/dd") ?? string.Empty;

        // Display properties for remaining amounts
        public string RemainingBeforeDisplay => $"{RemainingBeforePayment:N0} درهم";
        public string RemainingAfterDisplay => $"{RemainingAfterPayment:N0} درهم";
        public string PaymentStatusText => RemainingAfterPayment == 0 ? "مكتملة" : "جزئية";
    }
}
