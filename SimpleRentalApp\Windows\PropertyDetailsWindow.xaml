<Window x:Class="SimpleRentalApp.Windows.PropertyDetailsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تفاصيل المحل" 
        Height="900"
        Width="1600"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        Background="#F8F9FA"
        ResizeMode="CanResize"
        MinHeight="700"
        MinWidth="1000"
        MaxHeight="1000"
        MaxWidth="1800">

    <Window.Resources>
        <!-- Modern Card Style -->
        <Style x:Key="ModernCard" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="4" BlurRadius="12" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern Button Style -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,10"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="8"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#1565C0"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Success Button Style -->
        <Style x:Key="SuccessButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#4CAF50"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#388E3C"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Warning Button Style -->
        <Style x:Key="WarningButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#FF9800"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#F57C00"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Danger Button Style -->
        <Style x:Key="DangerButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#F44336"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#D32F2F"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Modern TextBlock Style -->
        <Style x:Key="CardTitle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#1976D2"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style x:Key="InfoLabel" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="#424242"/>
            <Setter Property="Margin" Value="0,5,0,2"/>
        </Style>

        <Style x:Key="InfoValue" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#616161"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>

        <!-- Modern DataGrid Style -->
        <Style x:Key="ModernDataGrid" TargetType="DataGrid">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HorizontalGridLinesBrush" Value="#F0F0F0"/>
            <Setter Property="RowBackground" Value="White"/>
            <Setter Property="AlternatingRowBackground" Value="#FAFAFA"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
            <Setter Property="AutoGenerateColumns" Value="False"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="CanUserDeleteRows" Value="False"/>
            <Setter Property="IsReadOnly" Value="True"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="RowHeight" Value="35"/>
        </Style>

        <!-- Modern DataGrid Header Style -->
        <Style x:Key="ModernDataGridHeader" TargetType="DataGridColumnHeader">
            <Setter Property="Background" Value="#F8F9FA"/>
            <Setter Property="Foreground" Value="#424242"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Padding" Value="12,10"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="0,0,1,1"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <!-- Info Button Style -->
        <Style x:Key="InfoButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#2196F3"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#1976D2"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Toggle Button Style for Dark/Light Mode -->
        <Style x:Key="ThemeToggle" TargetType="ToggleButton">
            <Setter Property="Background" Value="#E0E0E0"/>
            <Setter Property="Foreground" Value="#424242"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ToggleButton">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="20"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsChecked" Value="True">
                                <Setter Property="Background" Value="#424242"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <!-- Main ScrollViewer for entire content -->
    <ScrollViewer VerticalScrollBarVisibility="Auto"
                  HorizontalScrollBarVisibility="Auto"
                  PanningMode="Both"
                  CanContentScroll="True"
                  Margin="5">
        <Grid Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0"
                CornerRadius="12,12,0,0"
                Padding="25,20"
                Margin="0,0,0,20">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#2196F3" Offset="0"/>
                    <GradientStop Color="#1976D2" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="🏢" FontSize="28" Margin="0,0,15,0" VerticalAlignment="Center"/>
                    <StackPanel>
                        <TextBlock Name="PropertyNameTitle" 
                                  Text="تفاصيل المحل" 
                                  FontSize="24" 
                                  FontWeight="Bold" 
                                  Foreground="White"/>
                        <TextBlock Name="PropertyAddressSubtitle" 
                                  Text="معلومات شاملة عن المحل والدفعات" 
                                  FontSize="14" 
                                  Foreground="#E3F2FD"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <ToggleButton Name="ThemeToggleButton" 
                                 Style="{StaticResource ThemeToggle}"
                                 Content="🌙 الوضع الليلي"
                                 Click="ThemeToggleButton_Click"
                                 Margin="10,0"/>

                </StackPanel>
            </Grid>
        </Border>

        <!-- Property Info Cards -->
        <Grid Grid.Row="1" Margin="0,0,0,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="1.5*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Property Details Card -->
            <Border Grid.Column="0" Style="{StaticResource ModernCard}" Margin="0,0,8,0">
                <StackPanel>
                    <TextBlock Text="🏢 بيانات المحل الأساسية" Style="{StaticResource CardTitle}" Margin="0,0,0,15"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Margin="0,0,12,0">
                            <TextBlock Text="اسم المحل:" Style="{StaticResource InfoLabel}"/>
                            <Border Background="#F8F9FA" CornerRadius="4" Padding="8,6" Margin="0,0,0,12">
                                <TextBlock Name="PropertyNameText" Style="{StaticResource InfoValue}"
                                          FontWeight="SemiBold" Foreground="#1976D2"/>
                            </Border>

                            <TextBlock Text="العنوان:" Style="{StaticResource InfoLabel}"/>
                            <Border Background="#F8F9FA" CornerRadius="4" Padding="8,6" Margin="0,0,0,12">
                                <TextBlock Name="PropertyAddressText" Style="{StaticResource InfoValue}"
                                          TextWrapping="Wrap"/>
                            </Border>

                            <TextBlock Text="نوع النشاط:" Style="{StaticResource InfoLabel}"/>
                            <Border Background="#F8F9FA" CornerRadius="4" Padding="8,6" Margin="0,0,0,12">
                                <TextBlock Name="PropertyTypeText" Style="{StaticResource InfoValue}"/>
                            </Border>
                        </StackPanel>

                        <StackPanel Grid.Column="1">
                            <TextBlock Text="الإيجار الشهري:" Style="{StaticResource InfoLabel}"/>
                            <Border Background="#E3F2FD" CornerRadius="6" Padding="12,10" Margin="0,0,0,12">
                                <TextBlock Name="MonthlyRentText" Style="{StaticResource InfoValue}"
                                          FontWeight="Bold" Foreground="#1976D2" FontSize="16"
                                          HorizontalAlignment="Center"/>
                            </Border>

                            <TextBlock Text="طريقة دفع الضريبة:" Style="{StaticResource InfoLabel}"/>
                            <Border Background="#F8F9FA" CornerRadius="4" Padding="8,6" Margin="0,0,0,12">
                                <TextBlock Name="TaxPaymentMethodText" Style="{StaticResource InfoValue}"/>
                            </Border>

                            <TextBlock Text="اسم المكتري:" Style="{StaticResource InfoLabel}"/>
                            <Border Background="#F8F9FA" CornerRadius="4" Padding="8,6" Margin="0,0,0,12">
                                <TextBlock Name="CustomerNameText" Style="{StaticResource InfoValue}"
                                          FontWeight="SemiBold"/>
                            </Border>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- Tax Summary Card -->
            <Border Grid.Column="1" Style="{StaticResource ModernCard}" Margin="4,0">
                <StackPanel>
                    <TextBlock Text="💰 ملخص الضريبة" Style="{StaticResource CardTitle}" Margin="0,0,0,15"/>

                    <TextBlock Text="السنة المختارة:" Style="{StaticResource InfoLabel}"/>
                    <ComboBox Name="YearComboBox"
                             SelectionChanged="YearComboBox_SelectionChanged"
                             Margin="0,0,0,15"
                             Padding="12,10"
                             FontSize="14"
                             FontWeight="SemiBold"
                             Background="White"
                             BorderBrush="#E0E0E0"
                             BorderThickness="1"
                             HorizontalContentAlignment="Center"
                             VerticalContentAlignment="Center"
                             MaxDropDownHeight="200"
                             IsEditable="False"
                             IsReadOnly="True"
                             ToolTip="اختر السنة الضريبية لعرض البيانات المتعلقة بها">
                        <ComboBox.ItemTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding}"
                                          FontSize="14"
                                          FontWeight="SemiBold"
                                          HorizontalAlignment="Center"
                                          Padding="5,3"/>
                            </DataTemplate>
                        </ComboBox.ItemTemplate>
                    </ComboBox>

                    <TextBlock Text="المبلغ الإجمالي:" Style="{StaticResource InfoLabel}"/>
                    <Border Background="#E3F2FD" CornerRadius="6" Padding="10,8" Margin="0,0,0,10">
                        <TextBlock Name="TotalTaxText" Style="{StaticResource InfoValue}"
                                  FontWeight="Bold" Foreground="#1976D2" FontSize="15"
                                  HorizontalAlignment="Center"/>
                    </Border>

                    <TextBlock Text="مجموع المدفوع:" Style="{StaticResource InfoLabel}"/>
                    <Border Background="#E8F5E8" CornerRadius="6" Padding="10,8" Margin="0,0,0,10">
                        <TextBlock Name="PaidAmountText" Style="{StaticResource InfoValue}"
                                  FontWeight="Bold" Foreground="#388E3C" FontSize="15"
                                  HorizontalAlignment="Center"/>
                    </Border>

                    <TextBlock Text="المبلغ المتبقي:" Style="{StaticResource InfoLabel}"/>
                    <Border Background="#FFEBEE" CornerRadius="6" Padding="10,8" Margin="0,0,0,15">
                        <TextBlock Name="RemainingAmountText" Style="{StaticResource InfoValue}"
                                  FontWeight="Bold" Foreground="#D32F2F" FontSize="15"
                                  HorizontalAlignment="Center"/>
                    </Border>

                    <!-- Progress Bar -->
                    <TextBlock Text="نسبة الإنجاز:" Style="{StaticResource InfoLabel}" Margin="0,0,0,8"/>
                    <Border Background="#F5F5F5" CornerRadius="10" Padding="2">
                        <ProgressBar Name="PaymentProgressBar"
                                    Height="16"
                                    Background="Transparent"
                                    Foreground="#4CAF50"
                                    BorderThickness="0"/>
                    </Border>
                    <TextBlock Name="ProgressPercentageText"
                              FontSize="13"
                              FontWeight="SemiBold"
                              HorizontalAlignment="Center"
                              Foreground="#666"
                              Margin="0,5,0,0"/>
                </StackPanel>
            </Border>

            <!-- Advanced Statistics Card -->
            <Border Grid.Column="2" Style="{StaticResource ModernCard}" Name="StatisticsCard">
                <StackPanel>
                    <TextBlock Text="📊 إحصائيات متقدمة" Style="{StaticResource CardTitle}"/>

                    <TextBlock Text="عدد الدفعات:" Style="{StaticResource InfoLabel}"/>
                    <TextBlock Name="PaymentCountText" Style="{StaticResource InfoValue}"/>

                    <TextBlock Text="متوسط الدفعة:" Style="{StaticResource InfoLabel}"/>
                    <TextBlock Name="AveragePaymentText" Style="{StaticResource InfoValue}"/>

                    <TextBlock Text="أكبر دفعة:" Style="{StaticResource InfoLabel}"/>
                    <TextBlock Name="LargestPaymentText" Style="{StaticResource InfoValue}"/>

                    <TextBlock Text="أصغر دفعة:" Style="{StaticResource InfoLabel}"/>
                    <TextBlock Name="SmallestPaymentText" Style="{StaticResource InfoValue}"/>

                    <TextBlock Text="آخر دفعة:" Style="{StaticResource InfoLabel}"/>
                    <TextBlock Name="LastPaymentDateText" Style="{StaticResource InfoValue}"/>

                    <TextBlock Text="الدفعة القادمة:" Style="{StaticResource InfoLabel}"/>
                    <TextBlock Name="NextPaymentDueText" Style="{StaticResource InfoValue}"/>

                    <!-- Status Indicator -->
                    <Border Name="StatusIndicatorBorder"
                           CornerRadius="15"
                           Padding="10,5"
                           Margin="0,10,0,0"
                           HorizontalAlignment="Center">
                        <TextBlock Name="StatusIndicatorText"
                                  FontWeight="Bold"
                                  FontSize="12"
                                  Foreground="White"/>
                    </Border>
                </StackPanel>
            </Border>
        </Grid>

        <!-- Payments DataGrid -->
        <Border Grid.Row="2" Style="{StaticResource ModernCard}" Margin="0,5,0,0" MinHeight="500">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Payments Header -->
                <Grid Grid.Row="0" Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" Text="📊 دفعات ضريبة النظافة" Style="{StaticResource CardTitle}"/>
                    
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <!-- Primary Actions -->
                        <Border Background="#E8F5E8" CornerRadius="8" Padding="8,4" Margin="0,0,10,0">
                            <StackPanel Orientation="Horizontal">
                                <Button Name="AddPaymentButton"
                                       Style="{StaticResource SuccessButton}"
                                       Content="➕ إضافة دفعة جديدة"
                                       Click="AddPaymentButton_Click"
                                       Padding="15,10"
                                       FontWeight="SemiBold"
                                       Margin="0,0,10,0"/>

                                <Button Name="RefreshDataButton"
                                       Style="{StaticResource InfoButton}"
                                       Content="🔄 تحديث المعلومات"
                                       Click="RefreshDataButton_Click"
                                       Padding="15,10"
                                       FontWeight="SemiBold"/>
                            </StackPanel>
                        </Border>

                        <!-- Secondary Actions -->
                        <Border Background="#FFF3E0" CornerRadius="8" Padding="8,4" Margin="0,0,10,0">
                            <StackPanel Orientation="Horizontal">
                                <Button Name="EditPaymentButton"
                                       Style="{StaticResource WarningButton}"
                                       Content="✏️ تعديل"
                                       IsEnabled="False"
                                       Click="EditPaymentButton_Click"
                                       Padding="12,8"
                                       ToolTip="تعديل الدفعة المحددة"/>
                                <Button Name="DeletePaymentButton"
                                       Style="{StaticResource DangerButton}"
                                       Content="🗑️ حذف"
                                       IsEnabled="False"
                                       Click="DeletePaymentButton_Click"
                                       Padding="12,8"
                                       Margin="5,0,0,0"
                                       ToolTip="حذف الدفعة المحددة"/>
                            </StackPanel>
                        </Border>

                        <!-- Print Actions -->
                        <Border Background="#E3F2FD" CornerRadius="8" Padding="8,4">
                            <StackPanel Orientation="Horizontal">
                                <Button Name="PrintReceiptButton"
                                       Style="{StaticResource InfoButton}"
                                       Content="🖨️ طباعة الوصل"
                                       IsEnabled="False"
                                       Click="PrintReceiptButton_Click"
                                       Padding="12,8"
                                       ToolTip="طباعة وصل الدفعة المحددة"/>
                                <Button Name="PreviewReceiptButton"
                                       Style="{StaticResource InfoButton}"
                                       Content="👁️ معاينة"
                                       IsEnabled="False"
                                       Click="PreviewReceiptButton_Click"
                                       Padding="12,8"
                                       Margin="5,0,0,0"
                                       Background="#9C27B0"
                                       ToolTip="معاينة وصل الدفعة المحددة"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Grid>

                <!-- Enhanced Filter Controls -->
                <Border Grid.Row="1" Background="#FAFAFA" CornerRadius="6" Padding="15" Margin="0,0,0,15">
                    <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- First Row: Basic Filters -->
                    <Grid Grid.Row="0" Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="180"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="180"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="ترتيب حسب:" VerticalAlignment="Center" Margin="0,0,10,0" FontWeight="Medium"/>
                        <ComboBox Grid.Column="1" Name="SortComboBox" SelectionChanged="SortComboBox_SelectionChanged" Padding="8">
                            <ComboBoxItem Content="التاريخ (الأحدث أولاً)" IsSelected="True"/>
                            <ComboBoxItem Content="التاريخ (الأقدم أولاً)"/>
                            <ComboBoxItem Content="المبلغ (الأكبر أولاً)"/>
                            <ComboBoxItem Content="المبلغ (الأصغر أولاً)"/>
                            <ComboBoxItem Content="رقم التوصيل"/>
                            <ComboBoxItem Content="حالة الدفع"/>
                        </ComboBox>

                        <TextBlock Grid.Column="2" Text="حالة الدفع:" VerticalAlignment="Center" Margin="20,0,10,0" FontWeight="Medium"/>
                        <ComboBox Grid.Column="3" Name="StatusFilterComboBox" SelectionChanged="StatusFilterComboBox_SelectionChanged" Padding="8">
                            <ComboBoxItem Content="جميع الحالات" IsSelected="True"/>
                            <ComboBoxItem Content="مكتملة" Tag="Complete"/>
                            <ComboBoxItem Content="جزئية" Tag="Partial"/>
                            <ComboBoxItem Content="مدفوعة" Tag="Paid"/>
                            <ComboBoxItem Content="غير مدفوعة" Tag="Unpaid"/>
                        </ComboBox>

                        <TextBlock Grid.Column="4" Text="الفترة:" VerticalAlignment="Center" Margin="20,0,10,0" FontWeight="Medium"/>
                        <ComboBox Grid.Column="5" Name="PeriodFilterComboBox" SelectionChanged="PeriodFilterComboBox_SelectionChanged" Padding="8">
                            <ComboBoxItem Content="كل الفترات" IsSelected="True"/>
                            <ComboBoxItem Content="آخر 30 يوم"/>
                            <ComboBoxItem Content="آخر 3 أشهر"/>
                            <ComboBoxItem Content="آخر 6 أشهر"/>
                            <ComboBoxItem Content="السنة الحالية"/>
                        </ComboBox>

                        <Button Grid.Column="7"
                               Style="{StaticResource ModernButton}"
                               Content="🔄 إعادة تعيين"
                               Click="ResetFiltersButton_Click"/>
                    </Grid>

                    <!-- Second Row: Search and Advanced Options -->
                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="300"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="بحث سريع:" VerticalAlignment="Center" Margin="0,0,10,0" FontWeight="Medium"/>
                        <TextBox Grid.Column="1" Name="SearchTextBox"
                                TextChanged="SearchTextBox_TextChanged"
                                Padding="10,8"
                                FontSize="14"
                                ToolTip="ابحث في رقم التوصيل، المبلغ، طريقة الدفع، أو الملاحظات"/>

                        <CheckBox Grid.Column="2" Name="ShowOnlyCurrentYearCheckBox"
                                 Content="السنة الحالية فقط"
                                 Margin="20,0,0,0"
                                 Checked="ShowOnlyCurrentYearCheckBox_Changed"
                                 Unchecked="ShowOnlyCurrentYearCheckBox_Changed"/>

                        <CheckBox Grid.Column="3" Name="ShowStatisticsCheckBox"
                                 Content="عرض الإحصائيات"
                                 Margin="20,0,0,0"
                                 IsChecked="True"
                                 Checked="ShowStatisticsCheckBox_Changed"
                                 Unchecked="ShowStatisticsCheckBox_Changed"/>

                        <Button Grid.Column="5"
                               Style="{StaticResource ModernButton}"
                               Content="🔍 بحث متقدم"
                               Click="AdvancedSearchButton_Click"
                               Margin="10,0"/>

                        <Button Grid.Column="6"
                               Style="{StaticResource ModernButton}"
                               Content="📊 تقرير مفصل"
                               Background="#FF9800"
                               Click="GenerateDetailedReportButton_Click"/>
                    </Grid>
                    </Grid>
                </Border>

                <!-- DataGrid -->
                <Border Grid.Row="2" Background="White" CornerRadius="8" BorderBrush="#E0E0E0" BorderThickness="1" Margin="0,10,0,0">
                    <DataGrid Name="PaymentsDataGrid"
                             Style="{StaticResource ModernDataGrid}"
                             SelectionChanged="PaymentsDataGrid_SelectionChanged"
                             Height="350"
                             VerticalScrollBarVisibility="Auto"
                             HorizontalScrollBarVisibility="Auto">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="📅 التاريخ"
                                               Binding="{Binding PaymentDateDisplay}"
                                               Width="130"
                                               HeaderStyle="{StaticResource ModernDataGridHeader}"/>
                            <DataGridTextColumn Header="💰 المبلغ"
                                               Binding="{Binding CleaningTaxAmountDisplay}"
                                               Width="130"
                                               HeaderStyle="{StaticResource ModernDataGridHeader}"/>
                            <DataGridTextColumn Header="🧾 رقم التوصيل"
                                               Binding="{Binding ReceiptNumber}"
                                               Width="140"
                                               HeaderStyle="{StaticResource ModernDataGridHeader}"/>
                            <DataGridTextColumn Header="⏪ المتبقي قبل"
                                               Binding="{Binding RemainingBeforeDisplay}"
                                               Width="130"
                                               HeaderStyle="{StaticResource ModernDataGridHeader}"/>
                            <DataGridTextColumn Header="⏩ المتبقي بعد"
                                               Binding="{Binding RemainingAfterDisplay}"
                                               Width="130"
                                               HeaderStyle="{StaticResource ModernDataGridHeader}"/>
                            <DataGridTextColumn Header="📊 حالة الدفع"
                                               Binding="{Binding PaymentStatusText}"
                                               Width="110"
                                               HeaderStyle="{StaticResource ModernDataGridHeader}"/>
                            <DataGridTextColumn Header="💳 طريقة الدفع"
                                               Binding="{Binding PaymentMethod}"
                                               Width="130"
                                               HeaderStyle="{StaticResource ModernDataGridHeader}"/>
                            <DataGridTextColumn Header="📝 الملاحظات"
                                               Binding="{Binding Notes}"
                                               Width="*"
                                               HeaderStyle="{StaticResource ModernDataGridHeader}"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Border>

                <!-- Summary Footer -->
                <Border Grid.Row="3" Background="#F8F9FA" CornerRadius="8" Padding="15" Margin="0,10,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                            <TextBlock Text="📊 إجمالي الدفعات" FontWeight="SemiBold" FontSize="12" Foreground="#666" HorizontalAlignment="Center"/>
                            <TextBlock Name="TotalPaymentsCountText" Text="0" FontWeight="Bold" FontSize="16" Foreground="#1976D2" HorizontalAlignment="Center"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                            <TextBlock Text="💰 إجمالي المبلغ" FontWeight="SemiBold" FontSize="12" Foreground="#666" HorizontalAlignment="Center"/>
                            <TextBlock Name="TotalAmountText" Text="0 درهم" FontWeight="Bold" FontSize="16" Foreground="#388E3C" HorizontalAlignment="Center"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                            <TextBlock Text="📈 متوسط الدفعة" FontWeight="SemiBold" FontSize="12" Foreground="#666" HorizontalAlignment="Center"/>
                            <TextBlock Name="AverageAmountText" Text="0 درهم" FontWeight="Bold" FontSize="16" Foreground="#FF9800" HorizontalAlignment="Center"/>
                        </StackPanel>

                        <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                            <TextBlock Text="📅 آخر دفعة" FontWeight="SemiBold" FontSize="12" Foreground="#666" HorizontalAlignment="Center"/>
                            <TextBlock Name="LastPaymentText" Text="لا توجد" FontWeight="Bold" FontSize="16" Foreground="#9C27B0" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Grid>
                </Border>
            </Grid>
        </Border>

        <!-- Action Buttons and Status Bar -->
        <Grid Grid.Row="3" Margin="0,15,0,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Action Buttons -->
            <StackPanel Grid.Row="0"
                       Orientation="Horizontal"
                       HorizontalAlignment="Center"
                       Margin="0,0,0,15">
                <Button Name="ExportButton"
                       Style="{StaticResource ModernButton}"
                       Content="📤 تصدير البيانات"
                       Click="ExportButton_Click"
                       Padding="15,10"/>
                <Button Name="CloseButton"
                       Style="{StaticResource ModernButton}"
                       Content="❌ إغلاق"
                       Background="#757575"
                       Click="CloseButton_Click"
                       Padding="15,10"/>
            </StackPanel>

            <!-- Status Bar -->
            <Border Grid.Row="1"
                   Background="#F8F9FA"
                   BorderBrush="#E0E0E0"
                   BorderThickness="0,1,0,0"
                   Padding="15,8">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0"
                              Name="StatusTextBlock"
                              Text="جاهز"
                              FontSize="12"
                              Foreground="#666"
                              VerticalAlignment="Center"/>

                    <TextBlock Grid.Column="1"
                              Name="RecordCountText"
                              Text="0 دفعة"
                              FontSize="12"
                              Foreground="#666"
                              VerticalAlignment="Center"
                              Margin="0,0,20,0"/>

                    <TextBlock Grid.Column="2"
                              Text="تدبير الكراء - نافذة تفاصيل المحل"
                              FontSize="12"
                              Foreground="#999"
                              VerticalAlignment="Center"/>
                </Grid>
            </Border>
        </Grid>
        </Grid>
    </ScrollViewer>
</Window>
