using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using SimpleRentalApp.Services.CloudSync;
using SimpleRentalApp.Services.CloudSync.Security;
using SimpleRentalApp.Models;

namespace SimpleRentalApp.Tests.CloudSync
{
    /// <summary>
    /// اختبارات الأمان والأداء
    /// </summary>
    public class SecurityAndPerformanceTests
    {
        private readonly EncryptionService _encryptionService;
        private readonly string _testKey;

        public SecurityAndPerformanceTests()
        {
            _testKey = EncryptionService.GenerateEncryptionKey();
            _encryptionService = new EncryptionService(_testKey);
        }

        #region Security Tests

        [Fact]
        public async Task EncryptionService_ShouldProduceUnpredictableResults()
        {
            // Arrange
            var plaintext = "Sensitive rental data";
            var encryptedResults = new List<string>();

            // Act - تشفير نفس النص عدة مرات
            for (int i = 0; i < 10; i++)
            {
                var encrypted = await _encryptionService.EncryptAsync(plaintext);
                encryptedResults.Add(encrypted);
            }

            // Assert - يجب أن تكون جميع النتائج مختلفة
            var uniqueResults = encryptedResults.Distinct().Count();
            Assert.Equal(10, uniqueResults);
        }

        [Fact]
        public async Task EncryptionService_ShouldResistTamperingAttempts()
        {
            // Arrange
            var originalData = "Important rental contract data";
            var encrypted = await _encryptionService.EncryptAsync(originalData);

            // Act - محاولة تعديل البيانات المشفرة
            var tamperedData = encrypted.Substring(0, encrypted.Length - 5) + "XXXXX";

            // Assert - يجب أن يفشل فك التشفير
            await Assert.ThrowsAsync<Exception>(() => _encryptionService.DecryptAsync(tamperedData));
        }

        [Fact]
        public void HashVerification_ShouldDetectDataModification()
        {
            // Arrange
            var originalData = "Customer payment record";
            var hash = _encryptionService.CreateHash(originalData);

            // Act - تعديل البيانات
            var modifiedData = originalData + " MODIFIED";

            // Assert - يجب أن يكتشف التعديل
            Assert.False(_encryptionService.VerifyHash(modifiedData, hash));
        }

        [Fact]
        public void EncryptionKey_ShouldHaveSufficientEntropy()
        {
            // Arrange & Act
            var keys = new List<string>();
            for (int i = 0; i < 100; i++)
            {
                keys.Add(EncryptionService.GenerateEncryptionKey());
            }

            // Assert - جميع المفاتيح يجب أن تكون مختلفة
            var uniqueKeys = keys.Distinct().Count();
            Assert.Equal(100, uniqueKeys);

            // التحقق من طول المفاتيح
            Assert.All(keys, key => Assert.True(key.Length >= 32));
        }

        [Fact]
        public void DeviceId_ShouldBeUnique()
        {
            // Arrange & Act
            var deviceIds = new List<string>();
            for (int i = 0; i < 50; i++)
            {
                deviceIds.Add(EncryptionService.GenerateDeviceId());
            }

            // Assert - جميع معرفات الأجهزة يجب أن تكون مختلفة
            var uniqueIds = deviceIds.Distinct().Count();
            Assert.Equal(50, uniqueIds);
        }

        [Fact]
        public async Task EncryptionService_ShouldHandleSensitiveDataSecurely()
        {
            // Arrange
            var sensitiveData = new[]
            {
                "كلمة مرور المستخدم: password123",
                "رقم بطاقة ائتمان: 1234-5678-9012-3456",
                "رقم هوية: 1234567890",
                "عنوان منزل: شارع الملك فهد، الرياض"
            };

            // Act & Assert
            foreach (var data in sensitiveData)
            {
                var encrypted = await _encryptionService.EncryptAsync(data);
                var decrypted = await _encryptionService.DecryptAsync(encrypted);

                // التأكد من عدم وجود البيانات الحساسة في النص المشفر
                Assert.DoesNotContain("password", encrypted, StringComparison.OrdinalIgnoreCase);
                Assert.DoesNotContain("1234", encrypted);
                Assert.DoesNotContain("الملك فهد", encrypted);

                // التأكد من صحة فك التشفير
                Assert.Equal(data, decrypted);
            }
        }

        #endregion

        #region Performance Tests

        [Fact]
        public async Task EncryptionService_BulkOperations_ShouldMeetPerformanceRequirements()
        {
            // Arrange
            var testData = Enumerable.Range(1, 100)
                .Select(i => $"Customer {i} rental data with some details")
                .ToList();

            var stopwatch = Stopwatch.StartNew();

            // Act
            var encryptedData = new List<string>();
            foreach (var data in testData)
            {
                var encrypted = await _encryptionService.EncryptAsync(data);
                encryptedData.Add(encrypted);
            }

            var encryptionTime = stopwatch.Elapsed;
            stopwatch.Restart();

            var decryptedData = new List<string>();
            foreach (var encrypted in encryptedData)
            {
                var decrypted = await _encryptionService.DecryptAsync(encrypted);
                decryptedData.Add(decrypted);
            }

            var decryptionTime = stopwatch.Elapsed;
            stopwatch.Stop();

            // Assert
            Assert.True(encryptionTime.TotalSeconds < 5, $"تشفير 100 عنصر استغرق {encryptionTime.TotalSeconds} ثانية");
            Assert.True(decryptionTime.TotalSeconds < 5, $"فك تشفير 100 عنصر استغرق {decryptionTime.TotalSeconds} ثانية");
            Assert.Equal(testData, decryptedData);
        }

        [Fact]
        public async Task EncryptionService_LargeData_ShouldHandleEfficiently()
        {
            // Arrange
            var largeData = new string('A', 100000); // 100KB
            var stopwatch = Stopwatch.StartNew();

            // Act
            var encrypted = await _encryptionService.EncryptAsync(largeData);
            var encryptionTime = stopwatch.Elapsed;

            stopwatch.Restart();
            var decrypted = await _encryptionService.DecryptAsync(encrypted);
            var decryptionTime = stopwatch.Elapsed;

            stopwatch.Stop();

            // Assert
            Assert.True(encryptionTime.TotalSeconds < 2, $"تشفير 100KB استغرق {encryptionTime.TotalSeconds} ثانية");
            Assert.True(decryptionTime.TotalSeconds < 2, $"فك تشفير 100KB استغرق {decryptionTime.TotalSeconds} ثانية");
            Assert.Equal(largeData, decrypted);
        }

        [Fact]
        public void HashCreation_Performance_ShouldBeAcceptable()
        {
            // Arrange
            var testData = Enumerable.Range(1, 1000)
                .Select(i => $"Hash test data {i}")
                .ToList();

            var stopwatch = Stopwatch.StartNew();

            // Act
            var hashes = new List<string>();
            foreach (var data in testData)
            {
                var hash = _encryptionService.CreateHash(data);
                hashes.Add(hash);
            }

            stopwatch.Stop();

            // Assert
            Assert.True(stopwatch.Elapsed.TotalSeconds < 1, $"إنشاء 1000 hash استغرق {stopwatch.Elapsed.TotalSeconds} ثانية");
            Assert.Equal(1000, hashes.Count);
            Assert.Equal(1000, hashes.Distinct().Count()); // جميع الـ hashes مختلفة
        }

        [Fact]
        public async Task SyncData_Creation_Performance_ShouldBeOptimal()
        {
            // Arrange
            var customers = Enumerable.Range(1, 1000)
                .Select(i => new Customer
                {
                    Id = i,
                    Name = $"Customer {i}",
                    Email = $"customer{i}@example.com",
                    Phone = $"123456789{i}",
                    Address = $"Address {i}",
                    CreatedDate = DateTime.UtcNow,
                    UpdatedDate = DateTime.UtcNow
                })
                .ToList();

            var stopwatch = Stopwatch.StartNew();

            // Act
            var syncDataList = new List<SyncData>();
            foreach (var customer in customers)
            {
                var syncData = new SyncData
                {
                    EntityType = "Customer",
                    EntityId = customer.Id.ToString(),
                    JsonData = System.Text.Json.JsonSerializer.Serialize(customer),
                    Operation = SyncOperation.Create,
                    Timestamp = DateTime.UtcNow,
                    UserId = "test-user",
                    DeviceId = "test-device",
                    Hash = _encryptionService.CreateHash(customer.Name),
                    Metadata = new Dictionary<string, object> { { "version", "1.0" } }
                };
                syncDataList.Add(syncData);
            }

            stopwatch.Stop();

            // Assert
            Assert.True(stopwatch.Elapsed.TotalSeconds < 2, $"إنشاء 1000 SyncData استغرق {stopwatch.Elapsed.TotalSeconds} ثانية");
            Assert.Equal(1000, syncDataList.Count);
        }

        [Fact]
        public async Task ConcurrentEncryption_ShouldHandleMultipleThreads()
        {
            // Arrange
            var testData = Enumerable.Range(1, 50)
                .Select(i => $"Concurrent test data {i}")
                .ToList();

            var stopwatch = Stopwatch.StartNew();

            // Act
            var tasks = testData.Select(async data =>
            {
                var encrypted = await _encryptionService.EncryptAsync(data);
                var decrypted = await _encryptionService.DecryptAsync(encrypted);
                return new { Original = data, Decrypted = decrypted };
            });

            var results = await Task.WhenAll(tasks);
            stopwatch.Stop();

            // Assert
            Assert.True(stopwatch.Elapsed.TotalSeconds < 5, $"التشفير المتزامن لـ 50 عنصر استغرق {stopwatch.Elapsed.TotalSeconds} ثانية");
            Assert.All(results, result => Assert.Equal(result.Original, result.Decrypted));
        }

        #endregion

        #region Memory and Resource Tests

        [Fact]
        public async Task EncryptionService_MemoryUsage_ShouldBeReasonable()
        {
            // Arrange
            var initialMemory = GC.GetTotalMemory(true);
            var testData = new string('X', 10000); // 10KB

            // Act
            for (int i = 0; i < 100; i++)
            {
                var encrypted = await _encryptionService.EncryptAsync(testData);
                var decrypted = await _encryptionService.DecryptAsync(encrypted);
                Assert.Equal(testData, decrypted);
            }

            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            var finalMemory = GC.GetTotalMemory(true);
            var memoryIncrease = finalMemory - initialMemory;

            // Assert - الزيادة في الذاكرة يجب أن تكون معقولة (أقل من 10MB)
            Assert.True(memoryIncrease < 10 * 1024 * 1024, $"زيادة استخدام الذاكرة: {memoryIncrease / 1024 / 1024} MB");
        }

        [Fact]
        public void EncryptionService_Disposal_ShouldCleanupResources()
        {
            // Arrange
            var encryptionKey = EncryptionService.GenerateEncryptionKey();
            var service = new EncryptionService(encryptionKey);

            // Act
            service.Dispose();

            // Assert - يجب ألا يرمي استثناء عند التخلص من الموارد
            service.Dispose(); // استدعاء مرة أخرى للتأكد
        }

        #endregion

        #region Edge Cases and Error Handling

        [Theory]
        [InlineData("")]
        [InlineData(" ")]
        [InlineData("\n")]
        [InlineData("\t")]
        public async Task EncryptionService_EdgeCases_ShouldHandleCorrectly(string edgeCase)
        {
            // Act
            var encrypted = await _encryptionService.EncryptAsync(edgeCase);
            var decrypted = await _encryptionService.DecryptAsync(encrypted);

            // Assert
            Assert.Equal(edgeCase, decrypted);
        }

        [Fact]
        public async Task EncryptionService_VeryLongString_ShouldHandleCorrectly()
        {
            // Arrange
            var veryLongString = new string('A', 1000000); // 1MB

            // Act
            var encrypted = await _encryptionService.EncryptAsync(veryLongString);
            var decrypted = await _encryptionService.DecryptAsync(encrypted);

            // Assert
            Assert.Equal(veryLongString, decrypted);
        }

        [Fact]
        public async Task EncryptionService_SpecialCharacters_ShouldHandleCorrectly()
        {
            // Arrange
            var specialChars = "!@#$%^&*()_+-=[]{}|;':\",./<>?`~مرحبا🏠🏢";

            // Act
            var encrypted = await _encryptionService.EncryptAsync(specialChars);
            var decrypted = await _encryptionService.DecryptAsync(encrypted);

            // Assert
            Assert.Equal(specialChars, decrypted);
        }

        #endregion
    }
}
