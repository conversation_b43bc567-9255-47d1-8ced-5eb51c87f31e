# Simple Installer Builder Script
# Developer: <PERSON><PERSON><PERSON>

Write-Host "Building Rental Management System Installer..." -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

# Check if NSIS is installed
$nsisPath = "C:\Program Files (x86)\NSIS\makensis.exe"
if (-not (Test-Path $nsisPath)) {
    Write-Host "ERROR: NSIS not found at expected path" -ForegroundColor Red
    Write-Host "Please install NSIS from: https://nsis.sourceforge.io/" -ForegroundColor Yellow
    exit 1
}

# Check if source directory exists
$sourceDir = "bin\Release\net9.0-windows\win-x64"
if (-not (Test-Path $sourceDir)) {
    Write-Host "ERROR: Source directory not found: $sourceDir" -ForegroundColor Red
    Write-Host "Please run: dotnet publish -c Release -r win-x64" -ForegroundColor Yellow
    exit 1
}

# Check if executable exists
$exePath = "$sourceDir\SimpleRentalApp.exe"
if (-not (Test-Path $exePath)) {
    Write-Host "ERROR: Executable not found: $exePath" -ForegroundColor Red
    exit 1
}

# Create Installer directory on Desktop
$installerDir = "C:\Users\<USER>\Desktop\Installer"
if (-not (Test-Path $installerDir)) {
    New-Item -ItemType Directory -Path $installerDir -Force | Out-Null
    Write-Host "Created directory: $installerDir" -ForegroundColor Green
}

# Check if NSIS script exists
$nsisScript = "installer_arabic_complete.nsi"
if (-not (Test-Path $nsisScript)) {
    Write-Host "ERROR: NSIS script not found: $nsisScript" -ForegroundColor Red
    exit 1
}

Write-Host "Build Information:" -ForegroundColor Cyan
Write-Host "- Source Directory: $sourceDir" -ForegroundColor White
Write-Host "- NSIS Script: $nsisScript" -ForegroundColor White
Write-Host "- Output Directory: $installerDir" -ForegroundColor White

# Build the installer
Write-Host "Building installer..." -ForegroundColor Yellow

try {
    $process = Start-Process -FilePath $nsisPath -ArgumentList $nsisScript -Wait -PassThru -NoNewWindow
    
    if ($process.ExitCode -eq 0) {
        Write-Host "Installer built successfully!" -ForegroundColor Green
        
        # Find the installer file
        $installerFile = Get-ChildItem -Path $installerDir -Filter "*.exe" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
        
        if ($installerFile) {
            Write-Host "Installer File: $($installerFile.Name)" -ForegroundColor Green
            Write-Host "File Size: $([math]::Round($installerFile.Length / 1MB, 2)) MB" -ForegroundColor Green
            Write-Host "Location: $($installerFile.FullName)" -ForegroundColor Green
            
            # Open Installer folder
            Start-Process -FilePath "explorer.exe" -ArgumentList $installerDir
            
            Write-Host "Installer is ready for distribution!" -ForegroundColor Green
        } else {
            Write-Host "WARNING: Installer file not found in output directory" -ForegroundColor Yellow
        }
    } else {
        Write-Host "Failed to build installer. Exit code: $($process.ExitCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "Error running NSIS: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Build script completed" -ForegroundColor Green
