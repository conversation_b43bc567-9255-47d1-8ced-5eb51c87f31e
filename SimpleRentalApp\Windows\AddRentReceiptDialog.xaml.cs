using SimpleRentalApp.Models;
using SimpleRentalApp.Services;
using System;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace SimpleRentalApp.Windows
{
    public partial class AddRentReceiptDialog : Window
    {
        private readonly Property _property;
        private readonly Customer? _customer;
        private readonly RentReceipt? _templateReceipt;

        public AddRentReceiptDialog(Property property, Customer? customer, RentReceipt? templateReceipt = null)
        {
            InitializeComponent();
            _property = property;
            _customer = customer;
            _templateReceipt = templateReceipt;

            // Update window title to show property info
            Title = $"إضافة توصيل كراء جديد - {property.Name}";

            InitializeForm();
        }

        private async void InitializeForm()
        {
            try
            {
                // Set property info
                PropertyInfoText.Text = $"المحل: {_property.Name} - المكتري: {_customer?.FullName ?? "غير محدد"}";

                // Generate receipt number
                var nextReceiptNumber = await DatabaseService.Instance.GetNextReceiptNumberForPropertyAsync(_property.Id);
                ReceiptNumberTextBox.Text = nextReceiptNumber;

                // Update hint text
                UpdateReceiptNumberHint();

                // Set default values
                PaymentDatePicker.SelectedDate = DateTime.Now;
                AmountTextBox.Text = _property.MonthlyRent.ToString("0");

                // Set default period (current month)
                var today = DateTime.Now;
                var startOfMonth = new DateTime(today.Year, today.Month, 1);
                var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);

                PeriodStartDatePicker.SelectedDate = startOfMonth;
                PeriodEndDatePicker.SelectedDate = endOfMonth;

                // If template receipt provided, copy values
                if (_templateReceipt != null)
                {
                    AmountTextBox.Text = _templateReceipt.Amount.ToString("0");
                    NotesTextBox.Text = _templateReceipt.Notes;
                    // Don't copy date or receipt number - those should be new
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النموذج: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string GetPaymentPeriodText()
        {
            var startDate = PeriodStartDatePicker.SelectedDate ?? DateTime.Now;
            var endDate = PeriodEndDatePicker.SelectedDate ?? DateTime.Now;

            return $"من {startDate:dd/MM/yyyy} إلى {endDate:dd/MM/yyyy}";
        }

        private async void SaveBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validate inputs
                if (!ValidateInputs())
                    return;

                // Check if receipt number already exists for this property
                var receiptNumber = ReceiptNumberTextBox.Text.Trim();
                if (await IsReceiptNumberDuplicate(receiptNumber))
                {
                    var result = MessageBox.Show(
                        $"⚠️ رقم التوصيل '{receiptNumber}' موجود مسبقاً لمحل '{_property.Name}'.\n\n" +
                        "هل تريد إنشاء رقم جديد تلقائياً؟\n\n" +
                        "• اختر 'نعم' لإنشاء رقم جديد تلقائياً\n" +
                        "• اختر 'لا' للعودة وتعديل الرقم يدوياً",
                        "رقم مكرر لنفس المحل",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        try
                        {
                            var newReceiptNumber = await GenerateUniqueReceiptNumber();
                            ReceiptNumberTextBox.Text = newReceiptNumber;
                            ReceiptNumberHintText.Text = $"✅ تم إنشاء رقم جديد: {newReceiptNumber}";
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"خطأ في إنشاء رقم جديد: {ex.Message}", "خطأ",
                                MessageBoxButton.OK, MessageBoxImage.Error);
                            ReceiptNumberTextBox.Focus();
                            ReceiptNumberTextBox.SelectAll();
                            return;
                        }
                    }
                    else
                    {
                        ReceiptNumberTextBox.Focus();
                        ReceiptNumberTextBox.SelectAll();
                        return;
                    }
                }

                // Get active contract for property
                var activeContract = await DatabaseService.Instance.GetActiveContractForPropertyAsync(_property.Id);
                if (activeContract == null)
                {
                    MessageBox.Show("لا يوجد عقد نشط لهذا المحل. يرجى إنشاء عقد أولاً.", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Create new receipt with validation
                var receipt = new RentReceipt();

                try
                {
                    receipt.ReceiptNumber = ReceiptNumberTextBox.Text.Trim();
                    receipt.ContractId = activeContract.Id;
                    receipt.Amount = decimal.Parse(AmountTextBox.Text.Trim());
                    receipt.PaymentDate = PaymentDatePicker.SelectedDate ?? DateTime.Now;
                    receipt.PaymentPeriod = GetPaymentPeriodText();
                    receipt.Notes = NotesTextBox.Text.Trim();
                    receipt.IsPrinted = false;
                    receipt.CreatedDate = DateTime.Now;
                    receipt.PeriodStartDate = PeriodStartDatePicker.SelectedDate ?? DateTime.Now;
                    receipt.PeriodEndDate = PeriodEndDatePicker.SelectedDate ?? DateTime.Now;
                    receipt.PaymentMethod = "نقداً"; // Default payment method
                    receipt.CreatedBy = "المستخدم"; // Default created by
                    receipt.ShowLegalNotices = true; // Default show legal notices
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في إعداد بيانات التوصيل: {ex.Message}", "خطأ في البيانات",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // Save to database
                await DatabaseService.Instance.SaveRentReceiptAsync(receipt);

                MessageBox.Show($"تم إنشاء التوصيل بنجاح! ✅\n\nرقم التوصيل: {receipt.ReceiptNumber}\nالمحل: {_property.Name}",
                    "نجح الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                var errorMessage = "خطأ في حفظ التوصيل:\n\n";
                errorMessage += $"الخطأ الرئيسي: {ex.Message}\n\n";

                // Check for inner exception details
                var currentEx = ex;
                var level = 1;
                while (currentEx.InnerException != null && level <= 3)
                {
                    currentEx = currentEx.InnerException;
                    errorMessage += $"التفاصيل ({level}): {currentEx.Message}\n\n";
                    level++;
                }

                // Check for specific database errors
                var fullErrorText = ex.ToString().ToLower();
                if (fullErrorText.Contains("unique constraint") ||
                    fullErrorText.Contains("duplicate") ||
                    fullErrorText.Contains("receiptnumber"))
                {
                    errorMessage += "🔍 السبب المحتمل: رقم التوصيل مكرر\n";
                    errorMessage += "💡 الحل: استخدم زر 🔄 لإنشاء رقم جديد أو غيّر الرقم يدوياً";
                }
                else if (fullErrorText.Contains("foreign key") || fullErrorText.Contains("contractid"))
                {
                    errorMessage += "🔍 السبب المحتمل: مشكلة في ربط العقد\n";
                    errorMessage += "💡 الحل: تأكد من وجود عقد نشط للمحل";
                }
                else if (fullErrorText.Contains("not null") || fullErrorText.Contains("required"))
                {
                    errorMessage += "🔍 السبب المحتمل: حقل مطلوب فارغ\n";
                    errorMessage += "💡 الحل: تأكد من ملء جميع الحقول المطلوبة";
                }

                MessageBox.Show(errorMessage, "خطأ في حفظ الإيصال",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInputs()
        {
            // Validate receipt number
            if (string.IsNullOrWhiteSpace(ReceiptNumberTextBox.Text))
            {
                MessageBox.Show("رقم التوصيل مطلوب", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            // Validate payment date
            if (!PaymentDatePicker.SelectedDate.HasValue)
            {
                MessageBox.Show("تاريخ الدفع مطلوب", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            // Validate amount
            if (!decimal.TryParse(AmountTextBox.Text.Trim(), out decimal amount) || amount <= 0)
            {
                MessageBox.Show("يرجى إدخال مبلغ صحيح أكبر من صفر", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                AmountTextBox.Focus();
                return false;
            }

            // Validate payment period
            if (!PeriodStartDatePicker.SelectedDate.HasValue || !PeriodEndDatePicker.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى تحديد فترة الأداء (من وإلى)", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (PeriodStartDatePicker.SelectedDate > PeriodEndDatePicker.SelectedDate)
            {
                MessageBox.Show("تاريخ البداية يجب أن يكون قبل تاريخ النهاية", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }



        private void CancelBtn_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private async Task<bool> IsReceiptNumberDuplicate(string receiptNumber)
        {
            try
            {
                // Get active contract for this property
                var activeContract = await DatabaseService.Instance.GetActiveContractForPropertyAsync(_property.Id);
                if (activeContract == null)
                    return false; // No contract, so no duplicates possible

                // Check for duplicates only within the same contract (property)
                var existingReceipts = await DatabaseService.Instance.GetRentReceiptsForPropertyAsync(_property.Id);
                return existingReceipts.Any(r =>
                    r.ReceiptNumber.Equals(receiptNumber, StringComparison.OrdinalIgnoreCase) &&
                    r.ContractId == activeContract.Id);
            }
            catch
            {
                return false; // If we can't check, assume it's not duplicate
            }
        }

        private void ReceiptNumberTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // Enable/disable save button based on receipt number
            var receiptNumber = ReceiptNumberTextBox.Text.Trim();
            SaveBtn.IsEnabled = !string.IsNullOrWhiteSpace(receiptNumber);

            // Update hint text
            UpdateReceiptNumberHint();
        }

        private async void UpdateReceiptNumberHint()
        {
            try
            {
                var nextNumber = await DatabaseService.Instance.GetNextReceiptNumberForPropertyAsync(_property.Id);
                var currentText = ReceiptNumberTextBox.Text.Trim();

                if (string.IsNullOrEmpty(currentText) || currentText == nextNumber)
                {
                    ReceiptNumberHintText.Text = $"الرقم التالي المتاح لمحل '{_property.Name}': {nextNumber}";
                }
                else
                {
                    ReceiptNumberHintText.Text = $"رقم مخصص. الرقم التالي المتاح: {nextNumber}";
                }
            }
            catch
            {
                ReceiptNumberHintText.Text = "الرقم التالي المتاح لهذا المحل سيتم إنشاؤه تلقائياً";
            }
        }

        private async void GenerateReceiptNumber_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var newReceiptNumber = await GenerateUniqueReceiptNumber();
                ReceiptNumberTextBox.Text = newReceiptNumber;
                ReceiptNumberTextBox.Focus();
                ReceiptNumberTextBox.SelectAll();

                // Show confirmation
                ReceiptNumberHintText.Text = $"تم إنشاء رقم جديد لمحل '{_property.Name}': {newReceiptNumber}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء رقم التوصيل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void GenerateNewNumberBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    $"هل تريد إنشاء رقم توصيل جديد تلقائياً لمحل '{_property.Name}'؟\n\nسيتم استبدال الرقم الحالي بالرقم التالي المتاح.",
                    "إنشاء رقم جديد",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    var newReceiptNumber = await GenerateUniqueReceiptNumber();
                    ReceiptNumberTextBox.Text = newReceiptNumber;
                    ReceiptNumberTextBox.Focus();
                    ReceiptNumberTextBox.SelectAll();

                    // Show success message
                    ReceiptNumberHintText.Text = $"✅ تم إنشاء رقم جديد: {newReceiptNumber}";
                    ReceiptNumberHintText.Foreground = new SolidColorBrush(Colors.Green);

                    // Reset color after 3 seconds
                    var timer = new System.Windows.Threading.DispatcherTimer();
                    timer.Interval = TimeSpan.FromSeconds(3);
                    timer.Tick += (s, args) =>
                    {
                        ReceiptNumberHintText.Foreground = new SolidColorBrush(Colors.Gray);
                        timer.Stop();
                    };
                    timer.Start();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء رقم التوصيل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task<string> GenerateUniqueReceiptNumber()
        {
            try
            {
                // Use the existing database service method that already handles per-property numbering
                return await DatabaseService.Instance.GetNextReceiptNumberForPropertyAsync(_property.Id);
            }
            catch
            {
                // Fallback: try to generate manually
                try
                {
                    var existingReceipts = await DatabaseService.Instance.GetRentReceiptsForPropertyAsync(_property.Id);

                    // Find the highest number for this property
                    var maxNumber = 0;
                    foreach (var receipt in existingReceipts)
                    {
                        if (receipt.ReceiptNumber.StartsWith("R-") &&
                            int.TryParse(receipt.ReceiptNumber.Substring(2), out int number))
                        {
                            maxNumber = Math.Max(maxNumber, number);
                        }
                    }

                    // Generate next number
                    var nextNumber = maxNumber + 1;
                    return $"R-{nextNumber:D3}";
                }
                catch
                {
                    // Final fallback to timestamp-based number
                    return $"R-{DateTime.Now:HHmmss}";
                }
            }
        }
    }
}
