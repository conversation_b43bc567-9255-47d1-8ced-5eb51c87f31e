# Simple redistribution script for Rental Management App

Write-Host "========================================" -ForegroundColor Green
Write-Host "    Redistributing Rental Management App" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Clean and build
Write-Host "[1/5] Cleaning and building..." -ForegroundColor Cyan
& dotnet clean --configuration Release | Out-Null
& dotnet build --configuration Release

if ($LASTEXITCODE -ne 0) {
    Write-Host "Build failed!" -ForegroundColor Red
    exit 1
}

# Create portable release
Write-Host "[2/5] Creating portable release..." -ForegroundColor Cyan
if (Test-Path "dist_new") {
    Remove-Item "dist_new" -Recurse -Force
}

& dotnet publish --configuration Release --runtime win-x64 --self-contained true --output dist_new

if ($LASTEXITCODE -ne 0) {
    Write-Host "Publish failed!" -ForegroundColor Red
    exit 1
}

# Copy additional files
Write-Host "[3/5] Copying additional files..." -ForegroundColor Cyan
if (Test-Path "app_icon.ico") {
    Copy-Item "app_icon.ico" -Destination "dist_new\"
}
if (Test-Path "rental_icon.png") {
    Copy-Item "rental_icon.png" -Destination "dist_new\"
}

# Create launcher
Write-Host "[4/5] Creating launcher..." -ForegroundColor Cyan
$launcher = @"
@echo off
chcp 65001 > nul
title Rental Management System
echo ========================================
echo       Rental Management System
echo ========================================
echo.
echo Developer: Hafid Abdo
echo Version: 1.0.0 with New Icon
echo.
echo Starting system...
start SimpleRentalApp.exe
"@

$launcher | Out-File -FilePath "dist_new\run_app.bat" -Encoding UTF8

# Create user guide
$guide = @"
Rental Management System - User Guide
====================================

Version: 1.0.0 with New Icon
Developer: Hafid Abdo

How to Run:
1. Run run_app.bat
2. Or run SimpleRentalApp.exe directly

Login Credentials:
Username: hafid
Password: hafidos159357

System Requirements:
- Windows 10 or later
- 4 GB RAM
- 200 MB free space

Features:
- Property management
- Contract management
- Payment tracking
- Receipt generation
- Cleaning tax management
- WhatsApp notifications
- Reports and analytics

For support, contact the developer.
"@

$guide | Out-File -FilePath "dist_new\USER_GUIDE.txt" -Encoding UTF8

# Create ZIP
Write-Host "[5/5] Creating ZIP archive..." -ForegroundColor Cyan
$zipName = "RentalManagement_NewIcon_v1.0.zip"
if (Test-Path $zipName) {
    Remove-Item $zipName -Force
}

Add-Type -AssemblyName System.IO.Compression.FileSystem
[System.IO.Compression.ZipFile]::CreateFromDirectory("dist_new", $zipName)

# Results
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Redistribution completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "Files created:" -ForegroundColor Cyan
Write-Host "- dist_new\ (Portable folder)" -ForegroundColor White
Write-Host "- $zipName (ZIP archive)" -ForegroundColor White
Write-Host ""

if (Test-Path $zipName) {
    $fileSize = (Get-Item $zipName).Length
    $fileSizeMB = [math]::Round($fileSize / 1MB, 2)
    Write-Host "ZIP file size: $fileSizeMB MB" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Application ready for distribution!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
