using System;
using System.Text;

namespace SimpleRentalApp.Utils
{
    public static class NumberToArabicWords
    {
        private static readonly string[] Ones = {
            "", "واحد", "اثنان", "ثلاثة", "أربعة", "خمسة", "ستة", "سبعة", "ثمانية", "تسعة",
            "عشرة", "أحد عشر", "اثنا عشر", "ثلاثة عشر", "أربعة عشر", "خمسة عشر",
            "ستة عشر", "سبعة عشر", "ثمانية عشر", "تسعة عشر"
        };

        private static readonly string[] Tens = {
            "", "", "عشرون", "ثلاثون", "أربعون", "خمسون", "ستون", "سبعون", "ثمانون", "تسعون"
        };

        private static readonly string[] Hundreds = {
            "", "مئة", "مئتان", "ثلاثمئة", "أربعمئة", "خمسمئة", "ستمئة", "سبعمئة", "ثمانمئة", "تسعمئة"
        };

        public static string ConvertToWords(decimal number)
        {
            if (number == 0)
                return "صفر درهم";

            if (number < 0)
                return "ناقص " + ConvertToWords(-number);

            // Split into integer and decimal parts
            var integerPart = (long)Math.Floor(number);
            var decimalPart = (int)Math.Round((number - integerPart) * 100);

            var result = new StringBuilder();

            // Convert integer part
            if (integerPart > 0)
            {
                result.Append(ConvertIntegerToWords(integerPart));
                
                // Add currency
                if (integerPart == 1)
                    result.Append(" درهم");
                else if (integerPart == 2)
                    result.Append(" درهمان");
                else if (integerPart >= 3 && integerPart <= 10)
                    result.Append(" دراهم");
                else
                    result.Append(" درهماً");
            }

            // Convert decimal part (cents)
            if (decimalPart > 0)
            {
                if (result.Length > 0)
                    result.Append(" و");
                
                result.Append(ConvertIntegerToWords(decimalPart));
                
                if (decimalPart == 1)
                    result.Append(" سنت");
                else if (decimalPart == 2)
                    result.Append(" سنتان");
                else if (decimalPart >= 3 && decimalPart <= 10)
                    result.Append(" سنتات");
                else
                    result.Append(" سنتاً");
            }

            return result.ToString().Trim();
        }

        private static string ConvertIntegerToWords(long number)
        {
            if (number == 0)
                return "";

            if (number < 20)
                return Ones[number];

            if (number < 100)
            {
                var tens = number / 10;
                var ones = number % 10;
                
                if (ones == 0)
                    return Tens[tens];
                else
                    return Ones[ones] + " و" + Tens[tens];
            }

            if (number < 1000)
            {
                var hundreds = number / 100;
                var remainder = number % 100;
                
                var result = Hundreds[hundreds];
                
                if (remainder > 0)
                {
                    if (remainder < 20)
                        result += " و" + Ones[remainder];
                    else
                    {
                        var tens = remainder / 10;
                        var ones = remainder % 10;
                        
                        if (ones == 0)
                            result += " و" + Tens[tens];
                        else
                            result += " و" + Ones[ones] + " و" + Tens[tens];
                    }
                }
                
                return result;
            }

            if (number < 1000000)
            {
                var thousands = number / 1000;
                var remainder = number % 1000;
                
                var result = new StringBuilder();
                
                if (thousands == 1)
                    result.Append("ألف");
                else if (thousands == 2)
                    result.Append("ألفان");
                else if (thousands >= 3 && thousands <= 10)
                    result.Append(ConvertIntegerToWords(thousands) + " آلاف");
                else
                    result.Append(ConvertIntegerToWords(thousands) + " ألفاً");
                
                if (remainder > 0)
                {
                    result.Append(" و");
                    result.Append(ConvertIntegerToWords(remainder));
                }
                
                return result.ToString();
            }

            if (number < 1000000000)
            {
                var millions = number / 1000000;
                var remainder = number % 1000000;
                
                var result = new StringBuilder();
                
                if (millions == 1)
                    result.Append("مليون");
                else if (millions == 2)
                    result.Append("مليونان");
                else if (millions >= 3 && millions <= 10)
                    result.Append(ConvertIntegerToWords(millions) + " ملايين");
                else
                    result.Append(ConvertIntegerToWords(millions) + " مليوناً");
                
                if (remainder > 0)
                {
                    result.Append(" و");
                    result.Append(ConvertIntegerToWords(remainder));
                }
                
                return result.ToString();
            }

            // For very large numbers, just return the number as text
            return number.ToString();
        }

        // Helper method for currency formatting
        public static string ConvertCurrencyToWords(decimal amount, string currency = "درهم")
        {
            if (amount == 0)
                return $"صفر {currency}";

            var words = ConvertToWords(amount);
            
            // Replace "درهم" with the specified currency if different
            if (currency != "درهم")
            {
                words = words.Replace("درهم", currency)
                            .Replace("دراهم", currency)
                            .Replace("درهماً", currency);
            }

            return words;
        }
    }
}
