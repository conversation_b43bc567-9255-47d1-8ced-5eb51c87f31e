using System.Windows;
using SimpleRentalApp.Models;
using SimpleRentalApp.Services;

namespace SimpleRentalApp.Windows;

public partial class CreateReceiptWindow : Window
{
    private readonly Payment _payment;
    private RentReceipt? _createdReceipt;

    public RentReceipt? CreatedReceipt => _createdReceipt;

    public CreateReceiptWindow(Payment payment)
    {
        InitializeComponent();
        _payment = payment;
        LoadData();
        SetDefaultValues();
    }

    private void LoadData()
    {
        // Set property info
        PropertyInfoTextBlock.Text = $"عقار: {_payment.Property.Name} - {_payment.Customer.FullName}";

        // Fill tenant name and property address
        TenantNameTextBox.Text = _payment.Customer.FullName;
        PropertyAddressTextBox.Text = _payment.Property.Address;
    }

    private async void SetDefaultValues()
    {
        PaymentDatePicker.SelectedDate = _payment.PaymentDate ?? DateTime.Today;
        AmountTextBox.Text = _payment.TotalAmount.ToString("F0");
        PaymentMethodComboBox.SelectedIndex = 0;

        // Set payment period dates
        var dueDate = _payment.DueDate;
        PeriodStartDatePicker.SelectedDate = new DateTime(dueDate.Year, dueDate.Month, 1);
        PeriodEndDatePicker.SelectedDate = new DateTime(dueDate.Year, dueDate.Month, DateTime.DaysInMonth(dueDate.Year, dueDate.Month));

        // Set legal notices checkbox
        ShowLegalNoticesCheckBox.IsChecked = true;

        // Generate receipt number
        await GenerateReceiptNumber();

        // Update amount in words
        UpdateAmountInWords();
    }

    private async Task GenerateReceiptNumber()
    {
        try
        {
            var propertyId = _payment.Property.Id;
            var lastSequence = await DatabaseService.Instance.GetLastPropertyReceiptSequenceAsync(propertyId);
            var receiptNumber = ReceiptNumberGenerator.GeneratePropertyReceiptNumber(propertyId, lastSequence);
            ReceiptNumberTextBox.Text = receiptNumber;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في توليد رقم الإيصال: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Warning);
            ReceiptNumberTextBox.Text = ReceiptNumberGenerator.GenerateReceiptNumber();
        }
    }

    private async void GenerateNumberButton_Click(object sender, RoutedEventArgs e)
    {
        await GenerateReceiptNumber();
    }

    private void AmountTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
    {
        UpdateAmountInWords();
    }

    private void UpdateAmountInWords()
    {
        if (decimal.TryParse(AmountTextBox.Text, out decimal amount) && amount > 0)
        {
            var tempReceipt = new RentReceipt { Amount = amount };
            AmountInWordsTextBlock.Text = $"المبلغ بالحروف: {tempReceipt.AmountInWords}";
        }
        else
        {
            AmountInWordsTextBlock.Text = "المبلغ بالحروف: --";
        }
    }

    private async void PreviewAndPrintButton_Click(object sender, RoutedEventArgs e)
    {
        var receipt = await CreateReceipt();
        if (receipt != null)
        {
            _createdReceipt = receipt;
            
            // Open print preview window
            var printWindow = new PrintReceiptWindow(receipt);
            
            // Set owner safely
            try
            {
                if (this.IsLoaded)
                {
                    printWindow.Owner = this;
                }
                else
                {
                    printWindow.WindowStartupLocation = WindowStartupLocation.CenterScreen;
                }
            }
            catch
            {
                printWindow.WindowStartupLocation = WindowStartupLocation.CenterScreen;
            }
            
            printWindow.ShowDialog();
            
            DialogResult = true;
            Close();
        }
    }

    private async void SaveAsPdfButton_Click(object sender, RoutedEventArgs e)
    {
        var receipt = await CreateReceipt();
        if (receipt != null)
        {
            _createdReceipt = receipt;

            // Export directly to PDF
            var success = await Services.PdfService.Instance.ExportReceiptToPdfAsync(receipt);

            if (success)
            {
                MessageBox.Show($"تم إنشاء وحفظ التوصيل رقم {receipt.ReceiptNumber} كـ PDF بنجاح! ✅",
                    "نجح الإنشاء والحفظ", MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
        }
    }

    private async void CreateOnlyButton_Click(object sender, RoutedEventArgs e)
    {
        var receipt = await CreateReceipt();
        if (receipt != null)
        {
            _createdReceipt = receipt;
            MessageBox.Show("تم إنشاء الإيصال بنجاح! ✅", "نجح الإنشاء", 
                MessageBoxButton.OK, MessageBoxImage.Information);
            
            DialogResult = true;
            Close();
        }
    }

    private async Task<RentReceipt?> CreateReceipt()
    {
        try
        {
            // Validation
            if (string.IsNullOrWhiteSpace(ReceiptNumberTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الإيصال", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return null;
            }

            if (!decimal.TryParse(AmountTextBox.Text, out decimal amount) || amount <= 0)
            {
                MessageBox.Show("يرجى إدخال مبلغ صحيح", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return null;
            }

            // Validate period dates if provided
            if (PeriodStartDatePicker.SelectedDate.HasValue && PeriodEndDatePicker.SelectedDate.HasValue)
            {
                if (PeriodStartDatePicker.SelectedDate.Value > PeriodEndDatePicker.SelectedDate.Value)
                {
                    MessageBox.Show("تاريخ البداية يجب أن يكون قبل تاريخ النهاية", "خطأ في البيانات",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return null;
                }
            }

            if (!PaymentDatePicker.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى اختيار تاريخ الأداء", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return null;
            }

            // Create receipt
            var receipt = new RentReceipt
            {
                ReceiptNumber = ReceiptNumberTextBox.Text.Trim(),
                PaymentId = _payment.Id,
                ContractId = 0, // Will be set from payment's contract if available
                Amount = amount,
                PaymentDate = PaymentDatePicker.SelectedDate.Value,
                PeriodStartDate = PeriodStartDatePicker.SelectedDate,
                PeriodEndDate = PeriodEndDatePicker.SelectedDate,
                PaymentPeriod = PeriodStartDatePicker.SelectedDate.HasValue && PeriodEndDatePicker.SelectedDate.HasValue
                    ? $"من {PeriodStartDatePicker.SelectedDate.Value:dd/MM/yyyy} إلى {PeriodEndDatePicker.SelectedDate.Value:dd/MM/yyyy}"
                    : "غير محدد",
                PaymentMethod = ((System.Windows.Controls.ComboBoxItem)PaymentMethodComboBox.SelectedItem).Content.ToString() ?? "نقداً",
                Notes = NotesTextBox.Text.Trim(),
                ShowLegalNotices = ShowLegalNoticesCheckBox.IsChecked ?? true,
                CreatedDate = DateTime.Now,
                CreatedBy = "النظام"
            };

            // Save to database
            var savedReceipt = await DatabaseService.Instance.SaveRentReceiptAsync(receipt);
            return savedReceipt;
        }
        catch (Exception ex)
        {
            var innerMessage = ex.InnerException?.Message ?? "لا توجد تفاصيل إضافية";
            MessageBox.Show($"خطأ في إنشاء الإيصال:\n{ex.Message}\n\nالتفاصيل:\n{innerMessage}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
            return null;
        }
    }

    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        DialogResult = false;
        Close();
    }
}
