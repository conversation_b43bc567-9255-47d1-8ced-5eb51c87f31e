using SimpleRentalApp.Models;
using SimpleRentalApp.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace SimpleRentalApp.Windows
{
    public partial class MessagePreviewDialog : Window
    {
        private readonly NotificationDisplayInfo _notification;
        private readonly NotificationTemplateService _templateService;
        private string _originalMessage;
        private List<TemplateVariable> _availableVariables;

        public string FinalMessage { get; private set; } = string.Empty;
        public bool SendMessage { get; private set; } = false;

        public MessagePreviewDialog(NotificationDisplayInfo notification, string message, NotificationTemplateService templateService)
        {
            InitializeComponent();
            _notification = notification;
            _templateService = templateService;
            _originalMessage = message;
            
            InitializeDialog();
        }

        private void InitializeDialog()
        {
            try
            {
                // تحديث معلومات المكتري
                CustomerNameTextBlock.Text = _notification.CustomerName;
                PhoneNumberTextBlock.Text = _notification.PhoneNumber;
                
                // تحديث العنوان الفرعي
                SubtitleTextBlock.Text = $"إشعار {_notification.TypeDisplay} - {_notification.PropertyName}";

                // تحديث نص الرسالة
                MessageTextBox.Text = _originalMessage;
                FinalMessage = _originalMessage;

                // تحميل المتغيرات المتاحة
                LoadAvailableVariables();

                // تحديث عداد الأحرف
                UpdateCharacterCount();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النافذة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadAvailableVariables()
        {
            try
            {
                _availableVariables = _templateService.GetAvailableVariables(_notification.Type);
                
                VariablesPanel.Children.Clear();

                foreach (var variable in _availableVariables)
                {
                    var panel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 2, 0, 2) };
                    
                    var variableText = new TextBlock
                    {
                        Text = variable.Variable,
                        FontFamily = new FontFamily("Consolas"),
                        FontWeight = FontWeights.Bold,
                        Foreground = new SolidColorBrush(Color.FromRgb(0x21, 0x96, 0xF3)),
                        Width = 150,
                        Cursor = System.Windows.Input.Cursors.Hand
                    };
                    
                    variableText.MouseLeftButtonDown += (s, e) =>
                    {
                        // إدراج المتغير في موضع المؤشر
                        var textBox = MessageTextBox;
                        var caretIndex = textBox.CaretIndex;
                        var text = textBox.Text;
                        textBox.Text = text.Insert(caretIndex, variable.Variable);
                        textBox.CaretIndex = caretIndex + variable.Variable.Length;
                        textBox.Focus();
                    };

                    var descriptionText = new TextBlock
                    {
                        Text = $" - {variable.Description}",
                        Foreground = new SolidColorBrush(Color.FromRgb(0x66, 0x66, 0x66))
                    };

                    panel.Children.Add(variableText);
                    panel.Children.Add(descriptionText);
                    VariablesPanel.Children.Add(panel);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل المتغيرات: {ex.Message}");
            }
        }

        private void MessageTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateCharacterCount();
            FinalMessage = MessageTextBox.Text;
        }

        private void UpdateCharacterCount()
        {
            var count = MessageTextBox.Text?.Length ?? 0;
            CharCountTextBlock.Text = count.ToString();
            
            // تغيير لون العداد حسب طول الرسالة
            if (count > 1000)
            {
                CharCountTextBlock.Foreground = new SolidColorBrush(Colors.Red);
            }
            else if (count > 500)
            {
                CharCountTextBlock.Foreground = new SolidColorBrush(Colors.Orange);
            }
            else
            {
                CharCountTextBlock.Foreground = new SolidColorBrush(Colors.Green);
            }
        }

        private async void RestoreTemplateBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "هل تريد استعادة النص الأصلي للقالب؟\n\nسيتم فقدان جميع التعديلات التي أجريتها.",
                    "استعادة القالب الأصلي",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // إعادة تطبيق القالب الأصلي
                    var originalMessage = await _templateService.ApplyTemplateToNotificationAsync(_notification.Notification);
                    
                    if (!string.IsNullOrWhiteSpace(originalMessage))
                    {
                        MessageTextBox.Text = originalMessage;
                        _originalMessage = originalMessage;
                    }
                    else
                    {
                        MessageTextBox.Text = _originalMessage;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استعادة القالب: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CopyTextBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(MessageTextBox.Text))
                {
                    Clipboard.SetText(MessageTextBox.Text);
                    MessageBox.Show("تم نسخ النص إلى الحافظة بنجاح", "تم النسخ",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في نسخ النص: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SendBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(MessageTextBox.Text))
                {
                    MessageBox.Show("لا يمكن إرسال رسالة فارغة", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var result = MessageBox.Show(
                    $"هل تريد إرسال الرسالة التالية للمكتري {_notification.CustomerName}؟\n\n" +
                    $"الرقم: {_notification.PhoneNumber}\n\n" +
                    $"الرسالة:\n{MessageTextBox.Text.Substring(0, Math.Min(MessageTextBox.Text.Length, 200))}" +
                    (MessageTextBox.Text.Length > 200 ? "..." : ""),
                    "تأكيد الإرسال",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    FinalMessage = MessageTextBox.Text;
                    SendMessage = true;
                    DialogResult = true;
                    Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تأكيد الإرسال: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PreviewBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                FinalMessage = MessageTextBox.Text;
                SendMessage = false;
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في المعاينة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                SendMessage = false;
                DialogResult = false;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الإلغاء: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
