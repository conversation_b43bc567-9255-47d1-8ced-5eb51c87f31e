using SimpleRentalApp.Services;
using System;
using System.Windows;

namespace SimpleRentalApp.Windows
{
    /// <summary>
    /// نافذة معاينة الرسالة البسيطة
    /// </summary>
    public partial class MessagePreviewDialog : Window
    {
        private readonly WhatsAppNotificationDisplayInfo _notification;
        private readonly string _messageContent;
        private readonly NotificationTemplateService _templateService;

        public bool SendMessage { get; private set; } = false;
        public string FinalMessage { get; private set; } = string.Empty;

        public MessagePreviewDialog(WhatsAppNotificationDisplayInfo notification, string messageContent, NotificationTemplateService templateService)
        {
            InitializeComponent();
            
            _notification = notification;
            _messageContent = messageContent;
            _templateService = templateService;
            
            InitializeDialog();
        }

        private void InitializeDialog()
        {
            try
            {
                // تحديث معلومات المكتري
                CustomerNameTextBlock.Text = _notification.CustomerName;
                PhoneNumberTextBlock.Text = _notification.PhoneNumber;
                
                // تحديث العنوان الفرعي
                SubtitleTextBlock.Text = $"إشعار {_notification.TypeDisplay} - {_notification.PropertyName}";

                // تحديث نص الرسالة
                MessageContentTextBlock.Text = _messageContent;
                FinalMessage = _messageContent;

                // تحديث عداد الأحرف
                UpdateCharacterCount();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النافذة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديث عداد الأحرف
        /// </summary>
        private void UpdateCharacterCount()
        {
            try
            {
                var characterCount = _messageContent.Length;
                CharacterCountTextBlock.Text = characterCount.ToString();

                // تحديد طول الرسالة
                string lengthDescription;
                if (characterCount <= 160)
                {
                    lengthDescription = "قصيرة (رسالة واحدة)";
                }
                else if (characterCount <= 320)
                {
                    lengthDescription = "متوسطة (رسالتان)";
                }
                else if (characterCount <= 480)
                {
                    lengthDescription = "طويلة (3 رسائل)";
                }
                else
                {
                    lengthDescription = "طويلة جداً (4+ رسائل)";
                }

                MessageLengthTextBlock.Text = lengthDescription;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث عداد الأحرف: {ex.Message}");
            }
        }

        private void CloseBtn_Click(object sender, RoutedEventArgs e)
        {
            SendMessage = false;
            DialogResult = false;
            Close();
        }
    }
}
