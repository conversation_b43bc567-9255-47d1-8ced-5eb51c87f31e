<Window x:Class="SimpleRentalApp.Windows.RentIncreaseDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="زيادة الإيجار" 
        Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        Background="#F5F7FA"
        ResizeMode="NoResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" 
                Background="#1976D2" 
                CornerRadius="8" 
                Padding="20,15"
                Margin="0,0,0,20">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📈" FontSize="24" Foreground="White" Margin="0,0,15,0"/>
                <StackPanel>
                    <TextBlock Text="زيادة الإيجار" 
                               FontSize="20" 
                               FontWeight="Bold"
                               Foreground="White"/>
                    <TextBlock Name="ContractInfoText" 
                               Text="معلومات العقد"
                               FontSize="12" 
                               Foreground="#E3F2FD"
                               Margin="0,5,0,0"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Form Content -->
        <Border Grid.Row="1" 
                Background="White" 
                CornerRadius="8" 
                Padding="25"
                BorderBrush="#E0E0E0"
                BorderThickness="1">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Current Rent Info -->
                <TextBlock Grid.Row="0" Grid.Column="0" 
                           Text="الإيجار الحالي:" 
                           FontWeight="SemiBold" 
                           VerticalAlignment="Center"
                           Margin="0,0,0,15"/>
                <TextBlock Name="CurrentRentText" 
                           Grid.Row="0" Grid.Column="1"
                           Text="0 درهم"
                           FontSize="16"
                           FontWeight="Bold"
                           Foreground="#4CAF50"
                           VerticalAlignment="Center"
                           Margin="0,0,0,15"/>

                <!-- Increase Type -->
                <TextBlock Grid.Row="1" Grid.Column="0" 
                           Text="نوع الزيادة:" 
                           FontWeight="SemiBold" 
                           VerticalAlignment="Center"
                           Margin="0,0,0,15"/>
                <ComboBox Name="IncreaseTypeComboBox"
                          Grid.Row="1" Grid.Column="1"
                          Padding="10"
                          FontSize="14"
                          Margin="0,0,0,15"
                          SelectionChanged="IncreaseTypeComboBox_SelectionChanged">
                    <ComboBoxItem Content="نسبة مئوية %" IsSelected="True"/>
                    <ComboBoxItem Content="مبلغ ثابت"/>
                </ComboBox>

                <!-- Increase Value -->
                <TextBlock Grid.Row="2" Grid.Column="0" 
                           Text="قيمة الزيادة:" 
                           FontWeight="SemiBold" 
                           VerticalAlignment="Center"
                           Margin="0,0,0,15"/>
                <StackPanel Grid.Row="2" Grid.Column="1" Orientation="Horizontal" Margin="0,0,0,15">
                    <TextBox Name="IncreaseValueTextBox"
                             Width="150"
                             Padding="10"
                             FontSize="14"
                             TextChanged="IncreaseValueTextBox_TextChanged"/>
                    <TextBlock Name="IncreaseUnitText" 
                               Text="%"
                               FontSize="14"
                               VerticalAlignment="Center"
                               Margin="10,0,0,0"/>
                </StackPanel>

                <!-- Effective Date -->
                <TextBlock Grid.Row="3" Grid.Column="0" 
                           Text="تاريخ السريان:" 
                           FontWeight="SemiBold" 
                           VerticalAlignment="Center"
                           Margin="0,0,0,15"/>
                <DatePicker Name="EffectiveDatePicker"
                            Grid.Row="3" Grid.Column="1"
                            Padding="10"
                            FontSize="14"
                            Margin="0,0,0,15"/>

                <!-- New Rent Preview -->
                <TextBlock Grid.Row="4" Grid.Column="0"
                           Text="الإيجار الجديد:"
                           FontWeight="SemiBold"
                           VerticalAlignment="Center"
                           Margin="0,0,0,15"/>
                <StackPanel Grid.Row="4" Grid.Column="1" Margin="0,0,0,15">
                    <TextBlock Name="NewRentText"
                               Text="0 درهم"
                               FontSize="16"
                               FontWeight="Bold"
                               Foreground="#FF9800"/>
                    <TextBlock Name="NewCleaningTaxText"
                               Text="ضريبة النظافة: 0 درهم سنوياً"
                               FontSize="12"
                               Foreground="#666"
                               Margin="0,5,0,0"/>
                </StackPanel>

                <!-- Notes -->
                <TextBlock Grid.Row="5" Grid.Column="0" 
                           Text="ملاحظات:" 
                           FontWeight="SemiBold" 
                           VerticalAlignment="Top"
                           Margin="0,5,0,0"/>
                <TextBox Name="NotesTextBox"
                         Grid.Row="5" Grid.Column="1"
                         Height="80"
                         Padding="10"
                         FontSize="14"
                         TextWrapping="Wrap"
                         AcceptsReturn="True"
                         VerticalScrollBarVisibility="Auto"/>
            </Grid>
        </Border>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center"
                    Margin="0,20,0,0">
            <Button Name="SaveButton"
                    Content="💾 حفظ الزيادة"
                    Background="#4CAF50"
                    Foreground="White"
                    Padding="20,12"
                    Margin="10,0"
                    BorderThickness="0"
                    FontSize="14"
                    FontWeight="SemiBold"
                    Click="SaveButton_Click"/>
            
            <Button Name="CancelButton"
                    Content="❌ إلغاء"
                    Background="#F44336"
                    Foreground="White"
                    Padding="20,12"
                    Margin="10,0"
                    BorderThickness="0"
                    FontSize="14"
                    FontWeight="SemiBold"
                    Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
