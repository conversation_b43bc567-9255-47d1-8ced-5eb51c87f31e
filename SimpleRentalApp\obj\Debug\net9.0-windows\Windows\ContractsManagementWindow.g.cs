﻿#pragma checksum "..\..\..\..\Windows\ContractsManagementWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "4925B1D2381F7B3827A7697BF6B65F057A7E916F"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleRentalApp.Windows {
    
    
    /// <summary>
    /// ContractsManagementWindow
    /// </summary>
    public partial class ContractsManagementWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 42 "..\..\..\..\Windows\ContractsManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SubtitleText;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\Windows\ContractsManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddContractButton;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\Windows\ContractsManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\Windows\ContractsManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\Windows\ContractsManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\Windows\ContractsManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SortComboBox;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\Windows\ContractsManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button FixContractsButton;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\Windows\ContractsManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\..\Windows\ContractsManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalContractsText;
        
        #line default
        #line hidden
        
        
        #line 220 "..\..\..\..\Windows\ContractsManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ActiveContractsText;
        
        #line default
        #line hidden
        
        
        #line 229 "..\..\..\..\Windows\ContractsManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ExpiringSoonText;
        
        #line default
        #line hidden
        
        
        #line 238 "..\..\..\..\Windows\ContractsManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ExpiredContractsText;
        
        #line default
        #line hidden
        
        
        #line 247 "..\..\..\..\Windows\ContractsManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AverageDurationText;
        
        #line default
        #line hidden
        
        
        #line 257 "..\..\..\..\Windows\ContractsManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid ContractsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 317 "..\..\..\..\Windows\ContractsManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ResultsCountText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleRentalApp;V1.0.0.0;component/windows/contractsmanagementwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\ContractsManagementWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.SubtitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.AddContractButton = ((System.Windows.Controls.Button)(target));
            
            #line 60 "..\..\..\..\Windows\ContractsManagementWindow.xaml"
            this.AddContractButton.Click += new System.Windows.RoutedEventHandler(this.AddContractButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 70 "..\..\..\..\Windows\ContractsManagementWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 99 "..\..\..\..\Windows\ContractsManagementWindow.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.StatusFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 126 "..\..\..\..\Windows\ContractsManagementWindow.xaml"
            this.StatusFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.StatusFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.SortComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 140 "..\..\..\..\Windows\ContractsManagementWindow.xaml"
            this.SortComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SortComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.FixContractsButton = ((System.Windows.Controls.Button)(target));
            
            #line 159 "..\..\..\..\Windows\ContractsManagementWindow.xaml"
            this.FixContractsButton.Click += new System.Windows.RoutedEventHandler(this.FixContractsButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 170 "..\..\..\..\Windows\ContractsManagementWindow.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.TotalContractsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.ActiveContractsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.ExpiringSoonText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.ExpiredContractsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.AverageDurationText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.ContractsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 19:
            this.ResultsCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 15:
            
            #line 284 "..\..\..\..\Windows\ContractsManagementWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditContractButton_Click);
            
            #line default
            #line hidden
            break;
            case 16:
            
            #line 287 "..\..\..\..\Windows\ContractsManagementWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddRentIncreaseButton_Click);
            
            #line default
            #line hidden
            break;
            case 17:
            
            #line 290 "..\..\..\..\Windows\ContractsManagementWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ManageAttachmentsButton_Click);
            
            #line default
            #line hidden
            break;
            case 18:
            
            #line 293 "..\..\..\..\Windows\ContractsManagementWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteContractButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

