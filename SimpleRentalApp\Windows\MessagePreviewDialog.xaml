<Window x:Class="SimpleRentalApp.Windows.MessagePreviewDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="👁️ معاينة الرسالة"
        Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        Background="#F5F5F5"
        FontFamily="Segoe UI"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <!-- Color Resources -->
        <SolidColorBrush x:Key="PrimaryColor" Color="#25D366"/>
        <SolidColorBrush x:Key="SecondaryColor" Color="#34495E"/>
        <SolidColorBrush x:Key="SuccessColor" Color="#27AE60"/>
        <SolidColorBrush x:Key="DangerColor" Color="#E74C3C"/>
        <SolidColorBrush x:Key="BackgroundColor" Color="#F8F9FA"/>
        <SolidColorBrush x:Key="SurfaceColor" Color="White"/>
        <SolidColorBrush x:Key="BorderColor" Color="#E9ECEF"/>
        <SolidColorBrush x:Key="TextColor" Color="#2C3E50"/>

        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryColor}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#128C7E"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Success Button Style -->
        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="{StaticResource SuccessColor}"/>
        </Style>

        <!-- Danger Button Style -->
        <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="{StaticResource DangerColor}"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource SurfaceColor}" 
                BorderBrush="{StaticResource BorderColor}" BorderThickness="0,0,0,1"
                Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="👁️ معاينة الرسالة" FontSize="20" FontWeight="Bold" 
                               Foreground="{StaticResource TextColor}"/>
                    <TextBlock Name="SubtitleTextBlock" Text="معاينة محتوى الإشعار قبل الإرسال" 
                               FontSize="14" Foreground="#7F8C8D" Margin="0,5,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="📱" FontSize="24" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <StackPanel>
                        <TextBlock Name="CustomerNameTextBlock" Text="اسم المكتري" 
                                   FontWeight="Bold" FontSize="14"/>
                        <TextBlock Name="PhoneNumberTextBlock" Text="رقم الهاتف" 
                                   FontSize="12" Foreground="#7F8C8D"/>
                    </StackPanel>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Message Content -->
        <Border Grid.Row="1" Background="{StaticResource SurfaceColor}" 
                Margin="20" CornerRadius="12" Padding="20"
                BorderBrush="{StaticResource BorderColor}" BorderThickness="1">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Text="📝 محتوى الرسالة:" 
                           FontSize="16" FontWeight="SemiBold" 
                           Foreground="{StaticResource TextColor}" Margin="0,0,0,15"/>

                <Border Grid.Row="1" Background="#F8F9FA" CornerRadius="8" 
                        BorderBrush="{StaticResource BorderColor}" BorderThickness="1">
                    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="15">
                        <TextBlock Name="MessageContentTextBlock" 
                                   Text="محتوى الرسالة سيظهر هنا..."
                                   FontSize="14" LineHeight="22"
                                   TextWrapping="Wrap"
                                   Foreground="{StaticResource TextColor}"/>
                    </ScrollViewer>
                </Border>

                <StackPanel Grid.Row="2" Orientation="Horizontal" 
                            HorizontalAlignment="Left" Margin="0,15,0,0">
                    <TextBlock Text="📊 عدد الأحرف: " FontSize="12" Foreground="#7F8C8D"/>
                    <TextBlock Name="CharacterCountTextBlock" Text="0" FontSize="12" Foreground="#7F8C8D"/>
                    <TextBlock Text=" | طول الرسالة: " FontSize="12" Foreground="#7F8C8D" Margin="10,0,0,0"/>
                    <TextBlock Name="MessageLengthTextBlock" Text="قصيرة" FontSize="12" Foreground="#7F8C8D"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Footer Buttons -->
        <Border Grid.Row="2" Background="{StaticResource SurfaceColor}" 
                BorderBrush="{StaticResource BorderColor}" BorderThickness="0,1,0,0"
                Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="ℹ️ هذه معاينة فقط. لن يتم إرسال أي رسالة." 
                               FontSize="12" Foreground="#7F8C8D" VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="❌ إغلاق" Style="{StaticResource DangerButtonStyle}"
                            Click="CloseBtn_Click"
                            ToolTip="إغلاق النافذة"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
