<Window x:Class="SimpleRentalApp.Windows.MessagePreviewDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="معاينة وتعديل الرسالة" Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        ResizeMode="CanResize"
        MinHeight="500" MinWidth="600">

    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Success Button Style -->
        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#4CAF50"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#388E3C"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#2E7D32"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Cancel Button Style -->
        <Style x:Key="CancelButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#F44336"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#D32F2F"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#C62828"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Warning Button Style -->
        <Style x:Key="WarningButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#FF9800"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#F57C00"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#EF6C00"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="📱 معاينة وتعديل الرسالة"
                       FontSize="24" FontWeight="Bold"
                       Foreground="#1976D2" Margin="0,0,0,10"/>
            <TextBlock Name="SubtitleTextBlock" Text="يمكنك مراجعة وتعديل الرسالة قبل الإرسال"
                       FontSize="14" Foreground="#666"/>
        </StackPanel>

        <!-- Recipient Info -->
        <Border Grid.Row="1" Background="#F8F9FA" CornerRadius="8" Padding="15" Margin="0,0,0,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="المكتري:" FontWeight="Bold" Margin="0,0,10,0"/>
                <TextBlock Grid.Column="1" Name="CustomerNameTextBlock" Text="اسم المكتري"/>
                
                <TextBlock Grid.Column="2" Text="رقم الهاتف:" FontWeight="Bold" Margin="20,0,10,0"/>
                <TextBlock Grid.Column="3" Name="PhoneNumberTextBlock" Text="رقم الهاتف"/>
            </Grid>
        </Border>

        <!-- Message Content -->
        <GroupBox Grid.Row="2" Header="📝 نص الرسالة" FontSize="16" FontWeight="Bold" Margin="0,0,0,15">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Toolbar -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                    <Button Content="🔄 استعادة القالب الأصلي" Style="{StaticResource WarningButtonStyle}"
                            Name="RestoreTemplateBtn" Click="RestoreTemplateBtn_Click"
                            Margin="0,0,10,0" Padding="15,8"/>
                    <Button Content="📋 نسخ النص" Style="{StaticResource ModernButtonStyle}"
                            Name="CopyTextBtn" Click="CopyTextBtn_Click"
                            Margin="0,0,10,0" Padding="15,8"/>
                    <TextBlock Text="عدد الأحرف:" VerticalAlignment="Center" Margin="20,0,5,0"/>
                    <TextBlock Name="CharCountTextBlock" Text="0" VerticalAlignment="Center" FontWeight="Bold"/>
                </StackPanel>

                <!-- Message TextBox -->
                <TextBox Grid.Row="1" Name="MessageTextBox"
                         TextWrapping="Wrap" AcceptsReturn="True"
                         VerticalScrollBarVisibility="Auto"
                         FontSize="14" FontFamily="Segoe UI"
                         Padding="10" BorderThickness="2"
                         BorderBrush="#E0E0E0"
                         TextChanged="MessageTextBox_TextChanged"/>
            </Grid>
        </GroupBox>

        <!-- Variables Help -->
        <Expander Grid.Row="3" Header="💡 المتغيرات المتاحة" Margin="0,0,0,15">
            <ScrollViewer MaxHeight="120" VerticalScrollBarVisibility="Auto">
                <StackPanel Name="VariablesPanel" Margin="10"/>
            </ScrollViewer>
        </Expander>

        <!-- Action Buttons -->
        <Grid Grid.Row="4">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <Button Content="📱 إرسال الرسالة" Style="{StaticResource SuccessButtonStyle}"
                        Name="SendBtn" Click="SendBtn_Click"
                        Margin="0,0,15,0" Padding="20,12"/>
                
                <Button Content="👁️ معاينة فقط" Style="{StaticResource ModernButtonStyle}"
                        Name="PreviewBtn" Click="PreviewBtn_Click"
                        Margin="0,0,15,0" Padding="20,12"/>
                
                <Button Content="❌ إلغاء" Style="{StaticResource CancelButtonStyle}"
                        Name="CancelBtn" Click="CancelBtn_Click"
                        Padding="20,12"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
