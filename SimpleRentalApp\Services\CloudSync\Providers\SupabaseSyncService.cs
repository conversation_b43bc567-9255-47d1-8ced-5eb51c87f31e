using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Supabase;
using Supabase.Postgrest;
using Supabase.Postgrest.Models;
using Supabase.Realtime;
using SimpleRentalApp.Services.CloudSync.Security;
using Newtonsoft.Json;
using System.Text.Json;

namespace SimpleRentalApp.Services.CloudSync.Providers
{
    /// <summary>
    /// خدمة مزامنة Supabase
    /// </summary>
    public class SupabaseSyncService : ISyncService
    {
        private Supabase.Client? _client;
        private EncryptionService? _encryptionService;
        private SyncConfiguration? _config;
        private bool _isConnected;
        private DateTime? _lastSyncTime;

        public string ServiceName => "Supabase";
        public bool IsConnected => _isConnected;
        public DateTime? LastSyncTime => _lastSyncTime;

        public event EventHandler<ConnectionStatusChangedEventArgs>? ConnectionStatusChanged;
        public event EventHandler<SyncProgressEventArgs>? SyncProgress;
        public event EventHandler<SyncCompletedEventArgs>? SyncCompleted;
        public event EventHandler<SyncErrorEventArgs>? SyncError;

        /// <summary>
        /// تهيئة الاتصال بـ Supabase
        /// </summary>
        public async Task<bool> InitializeAsync(SyncConfiguration config)
        {
            try
            {
                _config = config ?? throw new ArgumentNullException(nameof(config));

                if (_config.EnableEncryption && !string.IsNullOrEmpty(_config.EncryptionKey))
                {
                    _encryptionService = new EncryptionService(_config.EncryptionKey);
                }

                var options = new SupabaseOptions
                {
                    AutoConnectRealtime = _config.EnableRealTimeSync,
                    AutoRefreshToken = true
                };

                _client = new Supabase.Client(_config.DatabaseUrl, _config.ApiKey, options);
                await _client.InitializeAsync();

                // اختبار الاتصال
                var testResult = await TestConnectionAsync();
                if (testResult)
                {
                    _isConnected = true;
                    OnConnectionStatusChanged(ConnectionStatus.Disconnected, ConnectionStatus.Connected, "تم الاتصال بنجاح");

                    // التحقق من وجود الجداول وإنشاؤها إذا لزم الأمر
                    await EnsureTablesExistAsync();

                    // إعداد Real-time إذا كان مفعل
                    if (_config.EnableRealTimeSync)
                    {
                        await SetupRealtimeSubscriptions();
                    }
                }

                return testResult;
            }
            catch (Exception ex)
            {
                OnSyncError(ex, "فشل في تهيئة الاتصال بـ Supabase");
                return false;
            }
        }

        /// <summary>
        /// اختبار الاتصال
        /// </summary>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                if (_client == null) return false;

                // اختبار اتصال بسيط باستخدام RPC function
                var result = await _client.Rpc("version", null);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"فشل اختبار الاتصال: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود الجداول وإنشاؤها إذا لزم الأمر
        /// </summary>
        private async Task EnsureTablesExistAsync()
        {
            try
            {
                if (_client == null) return;

                // محاولة قراءة بسيطة من جدول sync_data للتحقق من وجوده
                try
                {
                    await _client.From<SyncDataModel>().Select("id").Limit(1).Get();
                    System.Diagnostics.Debug.WriteLine("جدول sync_data موجود ✅");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"جدول sync_data غير موجود أو غير قابل للوصول: {ex.Message}");

                    // إنشاء الجداول باستخدام SQL
                    await CreateTablesAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التحقق من الجداول: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء الجداول المطلوبة
        /// </summary>
        private async Task CreateTablesAsync()
        {
            try
            {
                if (_client == null) return;

                // SQL لإنشاء جدول sync_data
                var createSyncDataTable = @"
                    CREATE TABLE IF NOT EXISTS sync_data (
                        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                        entity_type VARCHAR(100) NOT NULL,
                        entity_id VARCHAR(100) NOT NULL,
                        json_data TEXT NOT NULL,
                        operation VARCHAR(50) NOT NULL,
                        timestamp TIMESTAMPTZ DEFAULT NOW() NOT NULL,
                        user_id VARCHAR(100),
                        device_id VARCHAR(100),
                        hash VARCHAR(500),
                        metadata JSONB,
                        created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
                        updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
                    );";

                // SQL لإنشاء جدول backups
                var createBackupsTable = @"
                    CREATE TABLE IF NOT EXISTS backups (
                        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                        name VARCHAR(200) NOT NULL,
                        created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
                        size BIGINT DEFAULT 0,
                        data TEXT,
                        type VARCHAR(50) DEFAULT 'Full',
                        version VARCHAR(20) DEFAULT '1.0',
                        description TEXT
                    );";

                // تنفيذ SQL لإنشاء الجداول
                await _client.Rpc("exec_sql", new { sql = createSyncDataTable });
                await _client.Rpc("exec_sql", new { sql = createBackupsTable });

                System.Diagnostics.Debug.WriteLine("تم إنشاء الجداول بنجاح ✅");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"فشل في إنشاء الجداول: {ex.Message}");
                // لا نرمي استثناء هنا لأن الجداول قد تكون موجودة بالفعل
            }
        }

        /// <summary>
        /// مزامنة البيانات إلى السحابة
        /// </summary>
        public async Task<SyncResult> SyncToCloudAsync(SyncData data)
        {
            var result = new SyncResult();
            var startTime = DateTime.UtcNow;

            try
            {
                if (_client == null || !_isConnected)
                {
                    result.Message = "غير متصل بالخدمة السحابية";
                    return result;
                }

                OnSyncProgress(1, 0, "بدء المزامنة", data.EntityType);

                // تشفير البيانات إذا كان مفعل
                var jsonData = data.JsonData;
                if (_encryptionService != null)
                {
                    jsonData = await _encryptionService.EncryptAsync(data.JsonData);
                }

                // إنشاء نموذج البيانات
                var syncModel = new SyncDataModel
                {
                    EntityType = data.EntityType,
                    EntityId = data.EntityId,
                    JsonData = jsonData,
                    Operation = data.Operation.ToString(),
                    Timestamp = data.Timestamp,
                    UserId = data.UserId,
                    DeviceId = data.DeviceId,
                    Hash = data.Hash,
                    Metadata = System.Text.Json.JsonSerializer.Serialize(data.Metadata)
                };

                // تنفيذ العملية حسب النوع مع معالجة أفضل للأخطاء
                switch (data.Operation)
                {
                    case SyncOperation.Create:
                        var insertResult = await _client.From<SyncDataModel>().Insert(syncModel);
                        if (insertResult?.Models?.Any() != true)
                        {
                            throw new Exception("فشل في إدراج البيانات - لم يتم إرجاع نتيجة");
                        }
                        System.Diagnostics.Debug.WriteLine($"تم إدراج {data.EntityType} بنجاح: {data.EntityId}");
                        break;

                    case SyncOperation.Update:
                        var updateResult = await _client.From<SyncDataModel>()
                            .Where(x => x.EntityType == data.EntityType && x.EntityId == data.EntityId)
                            .Update(syncModel);
                        System.Diagnostics.Debug.WriteLine($"تم تحديث {data.EntityType} بنجاح: {data.EntityId}");
                        break;

                    case SyncOperation.Delete:
                        await _client.From<SyncDataModel>()
                            .Where(x => x.EntityType == data.EntityType && x.EntityId == data.EntityId)
                            .Delete();
                        System.Diagnostics.Debug.WriteLine($"تم حذف {data.EntityType} بنجاح: {data.EntityId}");
                        break;
                }

                OnSyncProgress(1, 1, "اكتملت المزامنة", data.EntityType);

                result.Success = true;
                result.SyncedData.Add(data);
                result.SuccessfulRecords = 1;
                result.TotalRecords = 1;
                result.Message = "تمت المزامنة بنجاح";
                _lastSyncTime = DateTime.UtcNow;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.FailedRecords = 1;
                result.TotalRecords = 1;

                // تحليل نوع الخطأ وإعطاء رسالة مفصلة
                var errorMessage = AnalyzeError(ex);
                result.Message = errorMessage;

                System.Diagnostics.Debug.WriteLine($"خطأ في المزامنة: {errorMessage}");
                System.Diagnostics.Debug.WriteLine($"تفاصيل الخطأ: {ex}");

                OnSyncError(ex, errorMessage, data.EntityType, data.EntityId);
            }
            finally
            {
                result.Duration = DateTime.UtcNow - startTime;
                OnSyncCompleted(result);
            }

            return result;
        }

        /// <summary>
        /// مزامنة البيانات من السحابة
        /// </summary>
        public async Task<SyncResult> SyncFromCloudAsync(DateTime? lastSyncTime = null)
        {
            var result = new SyncResult();
            var startTime = DateTime.UtcNow;

            try
            {
                if (_client == null || !_isConnected)
                {
                    result.Message = "غير متصل بالخدمة السحابية";
                    return result;
                }

                var syncTime = lastSyncTime ?? _lastSyncTime ?? DateTime.UtcNow.AddDays(-30);
                
                OnSyncProgress(0, 0, "جاري جلب البيانات من السحابة", "");

                // جلب البيانات المحدثة
                var query = _client.From<SyncDataModel>()
                    .Where(x => x.Timestamp > syncTime)
                    .Order(x => x.Timestamp, Supabase.Postgrest.Constants.Ordering.Ascending);

                var cloudData = await query.Get();
                
                if (cloudData?.Models?.Any() == true)
                {
                    result.TotalRecords = cloudData.Models.Count;
                    OnSyncProgress(result.TotalRecords, 0, "معالجة البيانات", "");

                    foreach (var item in cloudData.Models)
                    {
                        try
                        {
                            // فك تشفير البيانات إذا كان مفعل
                            var jsonData = item.JsonData;
                            if (_encryptionService != null)
                            {
                                jsonData = await _encryptionService.DecryptAsync(item.JsonData);
                            }

                            var syncData = new SyncData
                            {
                                EntityType = item.EntityType,
                                EntityId = item.EntityId,
                                JsonData = jsonData,
                                Operation = Enum.Parse<SyncOperation>(item.Operation),
                                Timestamp = item.Timestamp,
                                UserId = item.UserId,
                                DeviceId = item.DeviceId,
                                Hash = item.Hash,
                                Metadata = string.IsNullOrEmpty(item.Metadata) 
                                    ? new Dictionary<string, object>() 
                                    : System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(item.Metadata) ?? new()
                            };

                            result.SyncedData.Add(syncData);
                            result.SuccessfulRecords++;
                            
                            OnSyncProgress(result.TotalRecords, result.SuccessfulRecords, "معالجة البيانات", item.EntityType);
                        }
                        catch (Exception ex)
                        {
                            result.FailedRecords++;
                            OnSyncError(ex, "فشل في معالجة عنصر البيانات", item.EntityType, item.EntityId);
                        }
                    }
                }

                result.Success = true;
                result.Message = $"تم جلب {result.SuccessfulRecords} عنصر من السحابة";
                _lastSyncTime = DateTime.UtcNow;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"فشل في جلب البيانات من السحابة: {ex.Message}";
                OnSyncError(ex, "فشل في مزامنة البيانات من السحابة");
            }
            finally
            {
                result.Duration = DateTime.UtcNow - startTime;
                OnSyncCompleted(result);
            }

            return result;
        }

        /// <summary>
        /// حل التعارضات
        /// </summary>
        public async Task<ConflictResolutionResult> ResolveConflictsAsync(List<SyncConflict> conflicts)
        {
            var result = new ConflictResolutionResult();

            try
            {
                foreach (var conflict in conflicts)
                {
                    switch (_config?.ConflictResolution)
                    {
                        case ConflictResolutionStrategy.ServerWins:
                            // استخدام البيانات من السيرفر
                            await SyncToCloudAsync(conflict.RemoteData);
                            conflict.IsResolved = true;
                            conflict.Resolution = "تم اختيار بيانات السيرفر";
                            break;

                        case ConflictResolutionStrategy.ClientWins:
                            // استخدام البيانات المحلية
                            await SyncToCloudAsync(conflict.LocalData);
                            conflict.IsResolved = true;
                            conflict.Resolution = "تم اختيار البيانات المحلية";
                            break;

                        case ConflictResolutionStrategy.Manual:
                            // يتطلب تدخل يدوي
                            result.UnresolvedConflicts.Add(conflict);
                            continue;

                        default:
                            result.UnresolvedConflicts.Add(conflict);
                            continue;
                    }

                    conflict.ResolvedAt = DateTime.UtcNow;
                    result.ResolvedConflicts.Add(conflict);
                }

                result.Success = result.ResolvedConflicts.Any();
                result.Message = $"تم حل {result.ResolvedConflicts.Count} تعارض من أصل {conflicts.Count}";
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"فشل في حل التعارضات: {ex.Message}";
                OnSyncError(ex, "فشل في حل التعارضات");
            }

            return result;
        }

        /// <summary>
        /// حذف البيانات من السحابة
        /// </summary>
        public async Task<bool> DeleteFromCloudAsync(string entityType, string entityId)
        {
            try
            {
                if (_client == null || !_isConnected) return false;

                await _client.From<SyncDataModel>()
                    .Where(x => x.EntityType == entityType && x.EntityId == entityId)
                    .Delete();

                return true;
            }
            catch (Exception ex)
            {
                OnSyncError(ex, "فشل في حذف البيانات من السحابة", entityType, entityId);
                return false;
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية كاملة
        /// </summary>
        public async Task<BackupResult> CreateFullBackupAsync()
        {
            var result = new BackupResult();
            var startTime = DateTime.UtcNow;

            try
            {
                if (_client == null || !_isConnected)
                {
                    result.Message = "غير متصل بالخدمة السحابية";
                    return result;
                }

                // جلب جميع البيانات
                var allData = await _client.From<SyncDataModel>().Get();

                if (allData?.Models?.Any() == true)
                {
                    var backupData = new
                    {
                        CreatedAt = DateTime.UtcNow,
                        Version = "1.0",
                        RecordCount = allData.Models.Count,
                        Data = allData.Models
                    };

                    var backupJson = System.Text.Json.JsonSerializer.Serialize(backupData);

                    // تشفير النسخة الاحتياطية
                    if (_encryptionService != null)
                    {
                        backupJson = await _encryptionService.EncryptAsync(backupJson);
                    }

                    // حفظ النسخة الاحتياطية
                    var backupModel = new BackupModel
                    {
                        Id = Guid.NewGuid().ToString(),
                        Name = $"نسخة احتياطية {DateTime.Now:yyyy-MM-dd HH:mm}",
                        CreatedAt = DateTime.UtcNow,
                        Size = System.Text.Encoding.UTF8.GetByteCount(backupJson),
                        Data = backupJson,
                        Type = BackupType.Full.ToString(),
                        Version = "1.0"
                    };

                    await _client.From<BackupModel>().Insert(backupModel);

                    result.Success = true;
                    result.BackupId = backupModel.Id;
                    result.Size = backupModel.Size;
                    result.RecordCount = allData.Models.Count;
                    result.Message = "تم إنشاء النسخة الاحتياطية بنجاح";
                }
                else
                {
                    result.Success = false;
                    result.Message = "لا توجد بيانات للنسخ الاحتياطي";
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"فشل في إنشاء النسخة الاحتياطية: {ex.Message}";
                OnSyncError(ex, "فشل في إنشاء النسخة الاحتياطية");
            }
            finally
            {
                result.Duration = DateTime.UtcNow - startTime;
            }

            return result;
        }

        /// <summary>
        /// استعادة من النسخة الاحتياطية
        /// </summary>
        public async Task<RestoreResult> RestoreFromBackupAsync(string backupId)
        {
            var result = new RestoreResult { BackupId = backupId };
            var startTime = DateTime.UtcNow;

            try
            {
                if (_client == null || !_isConnected)
                {
                    result.Message = "غير متصل بالخدمة السحابية";
                    return result;
                }

                // جلب النسخة الاحتياطية
                var backup = await _client.From<BackupModel>()
                    .Where(x => x.Id == backupId)
                    .Single();

                if (backup == null)
                {
                    result.Message = "النسخة الاحتياطية غير موجودة";
                    return result;
                }

                // فك تشفير البيانات
                var backupJson = backup.Data;
                if (_encryptionService != null)
                {
                    backupJson = await _encryptionService.DecryptAsync(backup.Data);
                }

                var backupData = System.Text.Json.JsonSerializer.Deserialize<dynamic>(backupJson);

                // هنا يمكن إضافة منطق استعادة البيانات
                // يتطلب تنسيق مع قاعدة البيانات المحلية

                result.Success = true;
                result.Message = "تم استعادة النسخة الاحتياطية بنجاح";
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"فشل في استعادة النسخة الاحتياطية: {ex.Message}";
                OnSyncError(ex, "فشل في استعادة النسخة الاحتياطية");
            }
            finally
            {
                result.Duration = DateTime.UtcNow - startTime;
            }

            return result;
        }

        /// <summary>
        /// الحصول على قائمة النسخ الاحتياطية
        /// </summary>
        public async Task<List<BackupInfo>> GetBackupsAsync()
        {
            var backups = new List<BackupInfo>();

            try
            {
                if (_client == null || !_isConnected) return backups;

                var result = await _client.From<BackupModel>()
                    .Order(x => x.CreatedAt, Supabase.Postgrest.Constants.Ordering.Descending)
                    .Get();

                if (result?.Models?.Any() == true)
                {
                    backups = result.Models.Select(b => new BackupInfo
                    {
                        Id = b.Id,
                        Name = b.Name,
                        CreatedAt = b.CreatedAt,
                        Size = b.Size,
                        Type = Enum.Parse<BackupType>(b.Type),
                        Version = b.Version
                    }).ToList();
                }
            }
            catch (Exception ex)
            {
                OnSyncError(ex, "فشل في جلب قائمة النسخ الاحتياطية");
            }

            return backups;
        }

        /// <summary>
        /// قطع الاتصال
        /// </summary>
        public async Task DisconnectAsync()
        {
            try
            {
                if (_client != null)
                {
                    _client = null;
                }

                _isConnected = false;
                OnConnectionStatusChanged(ConnectionStatus.Connected, ConnectionStatus.Disconnected, "تم قطع الاتصال");
            }
            catch (Exception ex)
            {
                OnSyncError(ex, "فشل في قطع الاتصال");
            }
        }

        /// <summary>
        /// إعداد اشتراكات Real-time
        /// </summary>
        private async Task SetupRealtimeSubscriptions()
        {
            try
            {
                if (_client?.Realtime == null) return;

                var channel = _client.Realtime.Channel("sync_data");

                // TODO: إعداد Real-time listeners
                // channel.On(...);

                // معالجة البيانات الجديدة والمحدثة
                OnSyncProgress(1, 1, "تم تفعيل المزامنة الفورية", "");

                await channel.Subscribe();
            }
            catch (Exception ex)
            {
                OnSyncError(ex, "فشل في إعداد Real-time");
            }
        }

        /// <summary>
        /// تحليل الخطأ وإعطاء رسالة مفهومة
        /// </summary>
        private string AnalyzeError(Exception ex)
        {
            var message = ex.Message.ToLower();

            if (message.Contains("401") || message.Contains("unauthorized"))
            {
                return "خطأ في التفويض: API Key غير صحيح أو منتهي الصلاحية";
            }
            else if (message.Contains("403") || message.Contains("forbidden"))
            {
                return "ممنوع: تحقق من سياسات RLS في Supabase";
            }
            else if (message.Contains("404") || message.Contains("not found"))
            {
                return "الجدول غير موجود: تحقق من إعداد قاعدة البيانات";
            }
            else if (message.Contains("422") || message.Contains("unprocessable"))
            {
                return "خطأ في تنسيق البيانات: تحقق من مطابقة أسماء الأعمدة";
            }
            else if (message.Contains("network") || message.Contains("timeout"))
            {
                return "مشكلة في الشبكة: تحقق من الاتصال بالإنترنت";
            }
            else if (message.Contains("connection"))
            {
                return "مشكلة في الاتصال: تحقق من Database URL";
            }
            else
            {
                return $"خطأ غير متوقع: {ex.Message}";
            }
        }

        #region Event Handlers

        private void OnConnectionStatusChanged(ConnectionStatus oldStatus, ConnectionStatus newStatus, string message)
        {
            ConnectionStatusChanged?.Invoke(this, new ConnectionStatusChangedEventArgs
            {
                OldStatus = oldStatus,
                NewStatus = newStatus,
                Message = message
            });
        }

        private void OnSyncProgress(int total, int processed, string operation, string entityType)
        {
            SyncProgress?.Invoke(this, new SyncProgressEventArgs
            {
                TotalRecords = total,
                ProcessedRecords = processed,
                CurrentOperation = operation,
                EntityType = entityType
            });
        }

        private void OnSyncCompleted(SyncResult result)
        {
            SyncCompleted?.Invoke(this, new SyncCompletedEventArgs { Result = result });
        }

        private void OnSyncError(Exception exception, string message, string entityType = "", string entityId = "")
        {
            SyncError?.Invoke(this, new SyncErrorEventArgs
            {
                Exception = exception,
                ErrorMessage = message,
                EntityType = entityType,
                EntityId = entityId
            });
        }

        #endregion
    }

    /// <summary>
    /// نموذج بيانات المزامنة في Supabase
    /// </summary>
    [Supabase.Postgrest.Attributes.Table("sync_data")]
    public class SyncDataModel : BaseModel
    {
        [Supabase.Postgrest.Attributes.PrimaryKey("id")]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Supabase.Postgrest.Attributes.Column("entity_type")]
        public string EntityType { get; set; } = "";

        [Supabase.Postgrest.Attributes.Column("entity_id")]
        public string EntityId { get; set; } = "";

        [Supabase.Postgrest.Attributes.Column("json_data")]
        public string JsonData { get; set; } = "";

        [Supabase.Postgrest.Attributes.Column("operation")]
        public string Operation { get; set; } = "";

        [Supabase.Postgrest.Attributes.Column("timestamp")]
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        [Supabase.Postgrest.Attributes.Column("user_id")]
        public string UserId { get; set; } = "";

        [Supabase.Postgrest.Attributes.Column("device_id")]
        public string DeviceId { get; set; } = "";

        [Supabase.Postgrest.Attributes.Column("hash")]
        public string Hash { get; set; } = "";

        [Supabase.Postgrest.Attributes.Column("metadata")]
        public string Metadata { get; set; } = "";

        [Supabase.Postgrest.Attributes.Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Supabase.Postgrest.Attributes.Column("updated_at")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// نموذج النسخ الاحتياطية في Supabase
    /// </summary>
    [Supabase.Postgrest.Attributes.Table("backups")]
    public class BackupModel : BaseModel
    {
        [Supabase.Postgrest.Attributes.PrimaryKey("id")]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Supabase.Postgrest.Attributes.Column("name")]
        public string Name { get; set; } = "";

        [Supabase.Postgrest.Attributes.Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Supabase.Postgrest.Attributes.Column("size")]
        public long Size { get; set; }

        [Supabase.Postgrest.Attributes.Column("data")]
        public string Data { get; set; } = "";

        [Supabase.Postgrest.Attributes.Column("type")]
        public string Type { get; set; } = "";

        [Supabase.Postgrest.Attributes.Column("version")]
        public string Version { get; set; } = "";

        [Supabase.Postgrest.Attributes.Column("description")]
        public string Description { get; set; } = "";
    }
}
