# سكريبت بناء مثبت نظام تدبير الكراء
# المطور: حفيظ عبدو

Write-Host "🏠 بناء مثبت نظام تدبير الكراء" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

# التحقق من وجود NSIS
$nsisPath = "C:\Program Files (x86)\NSIS\makensis.exe"
if (-not (Test-Path $nsisPath)) {
    Write-Host "❌ خطأ: NSIS غير مثبت في المسار المتوقع" -ForegroundColor Red
    Write-Host "يرجى تثبيت NSIS من: https://nsis.sourceforge.io/" -ForegroundColor Yellow
    exit 1
}

# التحقق من وجود مجلد المصدر
$sourceDir = "bin\Release\net9.0-windows\win-x64"
if (-not (Test-Path $sourceDir)) {
    Write-Host "❌ خطأ: مجلد المصدر غير موجود: $sourceDir" -ForegroundColor Red
    Write-Host "يرجى تشغيل: dotnet publish -c Release -r win-x64" -ForegroundColor Yellow
    exit 1
}

# التحقق من وجود الملف التنفيذي
$exePath = "$sourceDir\SimpleRentalApp.exe"
if (-not (Test-Path $exePath)) {
    Write-Host "❌ خطأ: الملف التنفيذي غير موجود: $exePath" -ForegroundColor Red
    exit 1
}

# إنشاء مجلد Installer على سطح المكتب
$installerDir = "C:\Users\<USER>\Desktop\Installer"
if (-not (Test-Path $installerDir)) {
    New-Item -ItemType Directory -Path $installerDir -Force | Out-Null
    Write-Host "✅ تم إنشاء مجلد: $installerDir" -ForegroundColor Green
}

# التحقق من وجود سكريبت NSIS
$nsisScript = "installer_arabic_complete.nsi"
if (-not (Test-Path $nsisScript)) {
    Write-Host "❌ خطأ: سكريبت NSIS غير موجود: $nsisScript" -ForegroundColor Red
    exit 1
}

Write-Host "📋 معلومات البناء:" -ForegroundColor Cyan
Write-Host "- مجلد المصدر: $sourceDir" -ForegroundColor White
Write-Host "- سكريبت NSIS: $nsisScript" -ForegroundColor White
Write-Host "- مجلد الإخراج: $installerDir" -ForegroundColor White

# بناء المثبت
Write-Host "🔨 جاري بناء المثبت..." -ForegroundColor Yellow

try {
    $process = Start-Process -FilePath $nsisPath -ArgumentList $nsisScript -Wait -PassThru -NoNewWindow
    
    if ($process.ExitCode -eq 0) {
        Write-Host "✅ تم بناء المثبت بنجاح!" -ForegroundColor Green
        
        # البحث عن ملف المثبت
        $installerFile = Get-ChildItem -Path $installerDir -Filter "*.exe" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
        
        if ($installerFile) {
            Write-Host "📦 ملف المثبت: $($installerFile.Name)" -ForegroundColor Green
            Write-Host "📏 حجم الملف: $([math]::Round($installerFile.Length / 1MB, 2)) MB" -ForegroundColor Green
            Write-Host "📍 المسار: $($installerFile.FullName)" -ForegroundColor Green
            
            # فتح مجلد Installer
            Start-Process -FilePath "explorer.exe" -ArgumentList $installerDir
            
            Write-Host "🎉 المثبت جاهز للتوزيع!" -ForegroundColor Green
        } else {
            Write-Host "⚠️ تحذير: لم يتم العثور على ملف المثبت في مجلد الإخراج" -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ فشل في بناء المثبت. رمز الخطأ: $($process.ExitCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ خطأ في تشغيل NSIS: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "🏁 انتهى سكريبت البناء" -ForegroundColor Green
