<Window x:Class="SimpleRentalApp.Windows.MessagePreviewWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="👁️ معاينة رسالة الواتساب - تدبير الكراء"
        Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        Background="#F5F5F5"
        FontFamily="Segoe UI"
        ResizeMode="CanResize">

    <Window.Resources>
        <!-- Color Resources -->
        <SolidColorBrush x:Key="PrimaryColor" Color="#25D366"/>
        <SolidColorBrush x:Key="SecondaryColor" Color="#128C7E"/>
        <SolidColorBrush x:Key="AccentColor" Color="#34B7F1"/>
        <SolidColorBrush x:Key="BackgroundColor" Color="#ECE5DD"/>
        <SolidColorBrush x:Key="MessageBubbleColor" Color="#DCF8C6"/>
        <SolidColorBrush x:Key="SurfaceColor" Color="White"/>
        <SolidColorBrush x:Key="TextColor" Color="#303030"/>
        <SolidColorBrush x:Key="BorderColor" Color="#E0E0E0"/>

        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryColor}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource SecondaryColor}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#6C757D"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource SurfaceColor}" CornerRadius="15" Padding="25" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="3" BlurRadius="15"/>
            </Border.Effect>
            <StackPanel>
                <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                    <TextBlock Text="👁️" FontSize="32" VerticalAlignment="Center" Margin="0,0,15,0"/>
                    <StackPanel>
                        <TextBlock Text="معاينة رسالة الواتساب" 
                                   FontSize="24" 
                                   FontWeight="Bold" 
                                   Foreground="{StaticResource TextColor}"/>
                        <TextBlock Name="SubtitleText" 
                                   Text="معاينة الرسالة قبل الإرسال مع إمكانية التعديل" 
                                   FontSize="14" 
                                   Foreground="#7F8C8D" 
                                   Margin="0,5,0,0"/>
                    </StackPanel>
                </StackPanel>
                
                <!-- Notification Info -->
                <Grid Margin="0,15,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" Grid.Column="0" Text="المكتري:" FontWeight="Bold" Margin="0,0,10,5"/>
                    <TextBlock Grid.Row="0" Grid.Column="1" Name="CustomerNameText" Margin="0,0,20,5"/>
                    
                    <TextBlock Grid.Row="0" Grid.Column="2" Text="المحل:" FontWeight="Bold" Margin="0,0,10,5"/>
                    <TextBlock Grid.Row="0" Grid.Column="3" Name="PropertyNameText" Margin="0,0,0,5"/>
                    
                    <TextBlock Grid.Row="1" Grid.Column="0" Text="النوع:" FontWeight="Bold" Margin="0,0,10,0"/>
                    <TextBlock Grid.Row="1" Grid.Column="1" Name="NotificationTypeText" Margin="0,0,20,0"/>
                    
                    <TextBlock Grid.Row="1" Grid.Column="2" Text="المبلغ:" FontWeight="Bold" Margin="0,0,10,0"/>
                    <TextBlock Grid.Row="1" Grid.Column="3" Name="AmountText"/>
                </Grid>
            </StackPanel>
        </Border>

        <!-- Message Preview -->
        <Border Grid.Row="1" Background="{StaticResource BackgroundColor}" CornerRadius="15" Padding="20">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="10"/>
            </Border.Effect>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- WhatsApp Header -->
                <Border Grid.Row="0" Background="{StaticResource PrimaryColor}" CornerRadius="10,10,0,0" Padding="15,10">
                    <StackPanel Orientation="Horizontal">
                        <Ellipse Width="40" Height="40" Fill="White" Margin="0,0,10,0">
                            <Ellipse.OpacityMask>
                                <ImageBrush ImageSource="/Resources/whatsapp-icon.png" Stretch="UniformToFill"/>
                            </Ellipse.OpacityMask>
                        </Ellipse>
                        <StackPanel VerticalAlignment="Center">
                            <TextBlock Name="ContactNameText" Text="اسم المكتري" Foreground="White" FontWeight="Bold" FontSize="16"/>
                            <TextBlock Name="PhoneNumberText" Text="+212 XXX XXX XXX" Foreground="White" FontSize="12" Opacity="0.8"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Message Content -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
                    <Border Background="{StaticResource MessageBubbleColor}" 
                            CornerRadius="15,15,5,15" 
                            Padding="15" 
                            HorizontalAlignment="Right" 
                            MaxWidth="400"
                            Margin="0,20,0,0">
                        <StackPanel>
                            <TextBlock Name="MessageContentText" 
                                       TextWrapping="Wrap" 
                                       FontSize="14" 
                                       Foreground="{StaticResource TextColor}"
                                       LineHeight="20"/>
                            <TextBlock Text="✓✓" 
                                       HorizontalAlignment="Right" 
                                       Foreground="#4FC3F7" 
                                       FontSize="12" 
                                       Margin="0,5,0,0"/>
                        </StackPanel>
                    </Border>
                </ScrollViewer>

                <!-- Template Selection -->
                <Border Grid.Row="2" Background="{StaticResource SurfaceColor}" CornerRadius="0,0,10,10" Padding="15">
                    <StackPanel>
                        <TextBlock Text="🎨 اختيار القالب:" FontWeight="Bold" Margin="0,0,0,10"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <ComboBox Grid.Column="0" Name="TemplateComboBox" 
                                      Margin="0,0,10,0" 
                                      SelectionChanged="TemplateComboBox_SelectionChanged"/>
                            
                            <Button Grid.Column="1" Content="📝 تعديل القالب" 
                                    Style="{StaticResource SecondaryButtonStyle}"
                                    Click="EditTemplateBtn_Click"/>
                        </Grid>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>

        <!-- Action Buttons -->
        <Border Grid.Row="2" Background="{StaticResource SurfaceColor}" CornerRadius="12" Padding="20" Margin="0,20,0,0">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="10"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="📱" FontSize="16" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="جاهز للإرسال عبر الواتساب" 
                               FontSize="14" 
                               VerticalAlignment="Center" 
                               Foreground="{StaticResource TextColor}"/>
                </StackPanel>

                <Button Grid.Column="1" Name="SendNowBtn" Content="📤 إرسال الآن"
                        Style="{StaticResource ModernButtonStyle}"
                        Margin="0,0,10,0"
                        Click="SendNowBtn_Click"/>

                <Button Grid.Column="2" Content="📋 نسخ النص" 
                        Style="{StaticResource SecondaryButtonStyle}"
                        Margin="0,0,10,0"
                        Click="CopyTextBtn_Click"/>

                <Button Grid.Column="3" Content="❌ إغلاق" 
                        Style="{StaticResource SecondaryButtonStyle}"
                        Click="CloseBtn_Click"/>
            </Grid>
        </Border>
    </Grid>
</Window>
