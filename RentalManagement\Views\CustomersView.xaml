<UserControl x:Class="RentalManagement.Views.CustomersView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800"
             FlowDirection="RightToLeft">
    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Page Title -->
        <TextBlock Grid.Row="0"
                   Text="إدارة الزبائن" 
                   Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                   Margin="0,0,0,24"/>

        <!-- Search and Actions -->
        <Grid Grid.Row="1" Margin="0,0,0,16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- Search Box -->
            <TextBox Grid.Column="0"
                     Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                     materialDesign:HintAssist.Hint="البحث عن زبون..."
                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                     Margin="0,0,16,0">
                <TextBox.InputBindings>
                    <KeyBinding Key="Enter" Command="{Binding SearchCommand}"/>
                </TextBox.InputBindings>
            </TextBox>

            <!-- Action Buttons -->
            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <Button Content="بحث"
                        Command="{Binding SearchCommand}"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Margin="0,0,8,0"/>
                <Button Content="إضافة زبون"
                        Command="{Binding AddCustomerCommand}"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        Margin="0,0,8,0"/>
                <Button Content="تحديث"
                        Command="{Binding RefreshCommand}"
                        Style="{StaticResource MaterialDesignOutlinedButton}"/>
            </StackPanel>
        </Grid>

        <!-- Customers List -->
        <materialDesign:Card Grid.Row="2" 
                           materialDesign:ElevationAssist.Elevation="Dp2">
            <DataGrid ItemsSource="{Binding Customers}"
                      SelectedItem="{Binding SelectedCustomer}"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      IsReadOnly="True"
                      materialDesign:DataGridAssist.CellPadding="13 8 8 8"
                      materialDesign:DataGridAssist.ColumnHeaderPadding="8">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="الاسم الكامل" 
                                      Binding="{Binding FullName}"
                                      Width="*"/>
                    <DataGridTextColumn Header="رقم البطاقة الوطنية" 
                                      Binding="{Binding NationalId}"
                                      Width="150"/>
                    <DataGridTextColumn Header="رقم الهاتف" 
                                      Binding="{Binding PhoneNumber}"
                                      Width="120"/>
                    <DataGridTextColumn Header="العنوان" 
                                      Binding="{Binding Address}"
                                      Width="*"/>
                    <DataGridTextColumn Header="تاريخ الإضافة" 
                                      Binding="{Binding CreatedDate, StringFormat='{}{0:dd/MM/yyyy}'}"
                                      Width="120"/>
                    <DataGridTemplateColumn Header="الإجراءات" Width="200">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Button ToolTip="تعديل"
                                            Command="{Binding DataContext.EditCustomerCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                            CommandParameter="{Binding}"
                                            Style="{StaticResource MaterialDesignIconButton}"
                                            Margin="0,0,4,0">
                                        <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"/>
                                    </Button>
                                    <Button ToolTip="حذف"
                                            Command="{Binding DataContext.DeleteCustomerCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                            CommandParameter="{Binding}"
                                            Style="{StaticResource MaterialDesignIconButton}"
                                            Foreground="{StaticResource ErrorBrush}"
                                            Margin="0,0,4,0">
                                        <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                    </Button>
                                    <Button ToolTip="عرض التفاصيل"
                                            Command="{Binding DataContext.ViewCustomerDetailsCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                            CommandParameter="{Binding}"
                                            Style="{StaticResource MaterialDesignIconButton}">
                                        <materialDesign:PackIcon Kind="Eye" Width="16" Height="16"/>
                                    </Button>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>
    </Grid>
</UserControl>
