using Microsoft.EntityFrameworkCore;
using RentalManagement.Data;
using RentalManagement.Helpers;
using RentalManagement.Models;
using RentalManagement.Services;
using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;
using System.Windows.Input;

namespace RentalManagement.ViewModels
{
    public class PaymentDialogViewModel : BaseViewModel
    {
        private Payment _payment;
        private DateTime _dueDate = DateTime.Now.AddDays(30);
        private DateTime? _paymentDate;
        private decimal _amount;
        private PaymentStatus _status = PaymentStatus.Unpaid;
        private string _notes = string.Empty;
        private Customer? _selectedCustomer;
        private Property? _selectedProperty;

        public PaymentDialogViewModel() : this(new Payment())
        {
            Title = "إضافة أداء جديد";
        }

        public PaymentDialogViewModel(Payment payment)
        {
            _payment = payment ?? new Payment();
            Title = payment.Id == 0 ? "إضافة أداء جديد" : "تعديل بيانات الأداء";
            
            Customers = new ObservableCollection<Customer>();
            Properties = new ObservableCollection<Property>();
            
            // Initialize properties
            DueDate = _payment.DueDate != default ? _payment.DueDate : DateTime.Now.AddDays(30);
            PaymentDate = _payment.PaymentDate;
            Amount = _payment.Amount;
            Status = _payment.Status;
            Notes = _payment.Notes;

            // Initialize commands
            SaveCommand = new RelayCommand(Save, CanSave);
            CancelCommand = new RelayCommand(Cancel);

            LoadCustomers();
            LoadProperties();
        }

        public Payment Payment => _payment;

        public ObservableCollection<Customer> Customers { get; }
        public ObservableCollection<Property> Properties { get; }

        [Required(ErrorMessage = "تاريخ الاستحقاق مطلوب")]
        public DateTime DueDate
        {
            get => _dueDate;
            set
            {
                if (SetProperty(ref _dueDate, value))
                {
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        public DateTime? PaymentDate
        {
            get => _paymentDate;
            set => SetProperty(ref _paymentDate, value);
        }

        [Required(ErrorMessage = "المبلغ مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "المبلغ يجب أن يكون أكبر من صفر")]
        public decimal Amount
        {
            get => _amount;
            set
            {
                if (SetProperty(ref _amount, value))
                {
                    ValidateProperty(value);
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        public PaymentStatus Status
        {
            get => _status;
            set
            {
                if (SetProperty(ref _status, value))
                {
                    // If status is paid, set payment date to now
                    if (value == PaymentStatus.Paid && !PaymentDate.HasValue)
                    {
                        PaymentDate = DateTime.Now;
                    }
                    // If status is not paid, clear payment date
                    else if (value != PaymentStatus.Paid)
                    {
                        PaymentDate = null;
                    }
                }
            }
        }

        [StringLength(500, ErrorMessage = "الملاحظات لا يجب أن تتجاوز 500 حرف")]
        public string Notes
        {
            get => _notes;
            set
            {
                if (SetProperty(ref _notes, value))
                {
                    ValidateProperty(value);
                }
            }
        }

        [Required(ErrorMessage = "الزبون مطلوب")]
        public Customer? SelectedCustomer
        {
            get => _selectedCustomer;
            set
            {
                if (SetProperty(ref _selectedCustomer, value))
                {
                    LoadPropertiesForCustomer();
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        [Required(ErrorMessage = "العقار مطلوب")]
        public Property? SelectedProperty
        {
            get => _selectedProperty;
            set
            {
                if (SetProperty(ref _selectedProperty, value))
                {
                    // Auto-fill amount from property rent amount
                    if (value != null && Amount == 0)
                    {
                        Amount = value.RentAmount;
                    }
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        // Commands
        public ICommand SaveCommand { get; }
        public ICommand CancelCommand { get; }

        // Events
        public event EventHandler<bool>? DialogClosed;

        private bool CanSave()
        {
            return SelectedCustomer != null &&
                   SelectedProperty != null &&
                   Amount > 0 &&
                   IsValid();
        }

        private void Save()
        {
            if (!CanSave()) return;

            try
            {
                ClearError();

                // Update payment properties
                _payment.DueDate = DueDate;
                _payment.PaymentDate = PaymentDate;
                _payment.Amount = Amount;
                _payment.Status = Status;
                _payment.Notes = Notes.Trim();
                _payment.CustomerId = SelectedCustomer!.Id;
                _payment.PropertyId = SelectedProperty!.Id;

                if (_payment.Id == 0)
                {
                    _payment.CreatedDate = DateTime.Now;
                }

                DialogClosed?.Invoke(this, true);
            }
            catch (Exception ex)
            {
                SetError($"خطأ في حفظ البيانات: {ex.Message}");
            }
        }

        private void Cancel()
        {
            DialogClosed?.Invoke(this, false);
        }

        private async void LoadCustomers()
        {
            try
            {
                using var context = DatabaseService.Instance.CreateContext();
                var customers = await context.Customers
                    .OrderBy(c => c.FullName)
                    .ToListAsync();

                Customers.Clear();
                foreach (var customer in customers)
                {
                    Customers.Add(customer);
                }

                // Set selected customer if payment has one
                if (_payment.CustomerId > 0)
                {
                    SelectedCustomer = Customers.FirstOrDefault(c => c.Id == _payment.CustomerId);
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في تحميل الزبائن: {ex.Message}");
            }
        }

        private async void LoadProperties()
        {
            try
            {
                using var context = DatabaseService.Instance.CreateContext();
                var properties = await context.Properties
                    .OrderBy(p => p.Location)
                    .ToListAsync();

                Properties.Clear();
                foreach (var property in properties)
                {
                    Properties.Add(property);
                }

                // Set selected property if payment has one
                if (_payment.PropertyId > 0)
                {
                    SelectedProperty = Properties.FirstOrDefault(p => p.Id == _payment.PropertyId);
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في تحميل العقارات: {ex.Message}");
            }
        }

        private async void LoadPropertiesForCustomer()
        {
            if (SelectedCustomer == null) return;

            try
            {
                using var context = DatabaseService.Instance.CreateContext();
                var properties = await context.Properties
                    .Where(p => p.CustomerId == SelectedCustomer.Id)
                    .OrderBy(p => p.Location)
                    .ToListAsync();

                Properties.Clear();
                foreach (var property in properties)
                {
                    Properties.Add(property);
                }

                // Clear selected property if it doesn't belong to the selected customer
                if (SelectedProperty != null && SelectedProperty.CustomerId != SelectedCustomer.Id)
                {
                    SelectedProperty = null;
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في تحميل عقارات الزبون: {ex.Message}");
            }
        }

        private void ValidateProperty(object? value, [System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            if (propertyName == null) return;

            var context = new ValidationContext(this) { MemberName = propertyName };
            var results = new List<ValidationResult>();
            
            Validator.TryValidateProperty(value, context, results);
            
            if (results.Any())
            {
                SetError(results.First().ErrorMessage ?? "خطأ في التحقق من صحة البيانات");
            }
            else
            {
                ClearError();
            }
        }

        private bool IsValid()
        {
            var context = new ValidationContext(this);
            var results = new List<ValidationResult>();
            return Validator.TryValidateObject(this, context, results, true);
        }
    }
}
