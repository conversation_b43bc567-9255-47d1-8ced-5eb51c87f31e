using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Media;
using System.Windows.Media.Effects;
using System.Windows.Input;
using SimpleRentalApp.Services;
using SimpleRentalApp.Models;

namespace SimpleRentalApp;

public partial class MainWindow
{
    // Dashboard
    private async Task ShowDashboard()
    {
        try
        {
            var stats = await DatabaseService.Instance.GetDashboardStatsAsync();
            ContentArea.Content = CreateDashboardContent(stats);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل لوحة التحكم: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private FrameworkElement CreateDashboardContent(DashboardStats stats)
    {
        var mainGrid = new Grid { Margin = new Thickness(0) };
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });

        // Modern Header with Welcome Message
        var headerPanel = CreateEnhancedDashboardHeader();
        Grid.SetRow(headerPanel, 0);
        mainGrid.Children.Add(headerPanel);

        // Quick Stats Cards
        var statsPanel = CreateQuickStatsPanel(stats);
        Grid.SetRow(statsPanel, 1);
        mainGrid.Children.Add(statsPanel);

        // Charts and Recent Activity Section
        var chartsPanel = CreateChartsAndActivityPanel(stats);
        Grid.SetRow(chartsPanel, 2);
        mainGrid.Children.Add(chartsPanel);

        return mainGrid;
    }

    private StackPanel CreateEnhancedDashboardHeader()
    {
        var headerPanel = new StackPanel { Margin = new Thickness(0, 0, 0, 30) };

        // Welcome Section
        var welcomeGrid = new Grid();
        welcomeGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        welcomeGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

        // Welcome Text
        var welcomeStack = new StackPanel();
        var welcomeText = new TextBlock
        {
            Text = "مرحباً بك في لوحة التحكم",
            FontSize = 28,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#1976D2")),
            Margin = new Thickness(0, 0, 0, 5)
        };

        var dateText = new TextBlock
        {
            Text = $"اليوم: {DateTime.Now:dddd، dd MMMM yyyy}",
            FontSize = 14,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#666")),
            Margin = new Thickness(0, 0, 0, 10)
        };

        welcomeStack.Children.Add(welcomeText);
        welcomeStack.Children.Add(dateText);
        Grid.SetColumn(welcomeStack, 0);
        welcomeGrid.Children.Add(welcomeStack);

        // Quick Actions
        var actionsStack = new StackPanel { Orientation = Orientation.Horizontal };

        var addPropertyBtn = CreateQuickActionButton("➕ إضافة محل", "#4CAF50", async () => await AddNewProperty());
        var addCustomerBtn = CreateQuickActionButton("👤 إضافة مكتري", "#2196F3", async () => await AddNewCustomer());

        actionsStack.Children.Add(addPropertyBtn);
        actionsStack.Children.Add(addCustomerBtn);

        Grid.SetColumn(actionsStack, 1);
        welcomeGrid.Children.Add(actionsStack);

        headerPanel.Children.Add(welcomeGrid);
        return headerPanel;
    }

    private Button CreateQuickActionButton(string content, string color, Func<Task> action)
    {
        var button = new Button
        {
            Content = content,
            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color)),
            Foreground = Brushes.White,
            Padding = new Thickness(15, 8, 15, 8),
            Margin = new Thickness(5, 0, 5, 0),
            BorderThickness = new Thickness(0),
            FontSize = 12,
            FontWeight = FontWeights.Medium,
            Cursor = Cursors.Hand
        };

        // Apply rounded corners using border radius
        button.Template = CreateRoundedButtonTemplate();

        button.Click += async (s, e) => await action();

        return button;
    }

    private ControlTemplate CreateRoundedButtonTemplate()
    {
        var template = new ControlTemplate(typeof(Button));

        var border = new FrameworkElementFactory(typeof(Border));
        border.SetValue(Border.BackgroundProperty, new TemplateBindingExtension(Button.BackgroundProperty));
        border.SetValue(Border.CornerRadiusProperty, new CornerRadius(6));
        border.SetValue(Border.PaddingProperty, new TemplateBindingExtension(Button.PaddingProperty));

        var contentPresenter = new FrameworkElementFactory(typeof(ContentPresenter));
        contentPresenter.SetValue(ContentPresenter.HorizontalAlignmentProperty, HorizontalAlignment.Center);
        contentPresenter.SetValue(ContentPresenter.VerticalAlignmentProperty, VerticalAlignment.Center);

        border.AppendChild(contentPresenter);
        template.VisualTree = border;

        return template;
    }

    private StackPanel CreateQuickStatsPanel(DashboardStats stats)
    {
        var panel = new StackPanel { Margin = new Thickness(0, 0, 0, 30) };

        // Section Title
        var titleText = new TextBlock
        {
            Text = "📊 الإحصائيات السريعة",
            FontSize = 20,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#333")),
            Margin = new Thickness(0, 0, 0, 20)
        };
        panel.Children.Add(titleText);

        // Stats Grid
        var statsGrid = new Grid();
        for (int i = 0; i < 4; i++)
        {
            statsGrid.ColumnDefinitions.Add(new ColumnDefinition());
        }
        statsGrid.RowDefinitions.Add(new RowDefinition());
        statsGrid.RowDefinitions.Add(new RowDefinition());

        // Create enhanced stat cards
        var cards = new[]
        {
            CreateEnhancedStatCard("🏢", "إجمالي المحلات", stats.TotalProperties.ToString(), "#1976D2", 0, 0),
            CreateEnhancedStatCard("✅", "المحلات المكتراة", stats.RentedProperties.ToString(), "#4CAF50", 0, 1),
            CreateEnhancedStatCard("📋", "المحلات المتاحة", stats.AvailableProperties.ToString(), "#FF9800", 0, 2),
            CreateEnhancedStatCard("👥", "إجمالي المكترين", stats.TotalCustomers.ToString(), "#9C27B0", 0, 3),
            CreateEnhancedStatCard("⚠️", "أداءات متأخرة", stats.OverduePayments.ToString(), "#F44336", 1, 0),
            CreateEnhancedStatCard("💰", "الإيرادات الشهرية", $"{stats.MonthlyRevenue:N0} درهم", "#00BCD4", 1, 1, 2),
            CreateEnhancedStatCard("🧹", "ضريبة النظافة", $"{stats.TotalCleaningTaxCollected:N0} درهم", "#795548", 1, 3)
        };

        foreach (var card in cards)
        {
            statsGrid.Children.Add(card);
        }

        panel.Children.Add(statsGrid);
        return panel;
    }

    private Border CreateEnhancedStatCard(string icon, string title, string value, string color, int row, int col, int colSpan = 1)
    {
        var border = new Border
        {
            Background = Brushes.White,
            Margin = new Thickness(10),
            Padding = new Thickness(20),
            CornerRadius = new CornerRadius(12),
            BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E0E0E0")),
            BorderThickness = new Thickness(1)
        };

        // Add shadow effect
        border.Effect = new DropShadowEffect
        {
            Color = (Color)ColorConverter.ConvertFromString("#E0E0E0"),
            Direction = 270,
            ShadowDepth = 4,
            BlurRadius = 12,
            Opacity = 0.3
        };

        Grid.SetRow(border, row);
        Grid.SetColumn(border, col);
        Grid.SetColumnSpan(border, colSpan);

        var mainGrid = new Grid();
        mainGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
        mainGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

        // Icon
        var iconText = new TextBlock
        {
            Text = icon,
            FontSize = 32,
            VerticalAlignment = VerticalAlignment.Center,
            Margin = new Thickness(0, 0, 15, 0)
        };
        Grid.SetColumn(iconText, 0);
        mainGrid.Children.Add(iconText);

        // Content
        var contentStack = new StackPanel
        {
            VerticalAlignment = VerticalAlignment.Center
        };

        var titleText = new TextBlock
        {
            Text = title,
            FontSize = 12,
            FontWeight = FontWeights.Medium,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#666")),
            Margin = new Thickness(0, 0, 0, 5)
        };

        var valueText = new TextBlock
        {
            Text = value,
            FontSize = 24,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color))
        };

        contentStack.Children.Add(titleText);
        contentStack.Children.Add(valueText);
        Grid.SetColumn(contentStack, 1);
        mainGrid.Children.Add(contentStack);

        border.Child = mainGrid;
        return border;
    }

    private StackPanel CreateChartsAndActivityPanel(DashboardStats stats)
    {
        var panel = new StackPanel { Margin = new Thickness(0, 20, 0, 0) };

        // Section Title
        var titleText = new TextBlock
        {
            Text = "📈 التحليلات والنشاط الأخير",
            FontSize = 20,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#333")),
            Margin = new Thickness(0, 0, 0, 20)
        };
        panel.Children.Add(titleText);

        var contentGrid = new Grid();
        contentGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        contentGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

        // Analytics Row
        var analyticsRow = new Grid();
        analyticsRow.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        analyticsRow.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        analyticsRow.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

        // Revenue Analytics
        var revenuePanel = CreateRevenueAnalyticsPanel(stats);
        Grid.SetColumn(revenuePanel, 0);
        analyticsRow.Children.Add(revenuePanel);

        // Property Status Chart
        var chartPanel = CreateImprovedPropertyStatusChart(stats);
        Grid.SetColumn(chartPanel, 1);
        analyticsRow.Children.Add(chartPanel);

        // Monthly Trends
        var trendsPanel = CreateMonthlyTrendsPanel(stats);
        Grid.SetColumn(trendsPanel, 2);
        analyticsRow.Children.Add(trendsPanel);

        Grid.SetRow(analyticsRow, 0);
        contentGrid.Children.Add(analyticsRow);

        // Recent Activity Row
        var activityRow = new Grid();
        activityRow.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(2, GridUnitType.Star) });
        activityRow.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        activityRow.Margin = new Thickness(0, 20, 0, 0);

        // Recent Activity Panel
        var recentActivityPanel = CreateRecentActivityPanel();
        Grid.SetColumn(recentActivityPanel, 0);
        activityRow.Children.Add(recentActivityPanel);

        // Quick Actions Panel
        var actionsPanel = CreateQuickActionsPanel();
        Grid.SetColumn(actionsPanel, 1);
        activityRow.Children.Add(actionsPanel);

        Grid.SetRow(activityRow, 1);
        contentGrid.Children.Add(activityRow);

        panel.Children.Add(contentGrid);
        return panel;
    }

    private Border CreateRevenueAnalyticsPanel(DashboardStats stats)
    {
        var border = new Border
        {
            Background = new LinearGradientBrush(
                (Color)ColorConverter.ConvertFromString("#4CAF50"),
                (Color)ColorConverter.ConvertFromString("#45A049"),
                90),
            Margin = new Thickness(0, 0, 10, 0),
            Padding = new Thickness(20),
            CornerRadius = new CornerRadius(12)
        };

        border.Effect = new DropShadowEffect
        {
            Color = (Color)ColorConverter.ConvertFromString("#E0E0E0"),
            Direction = 270,
            ShadowDepth = 4,
            BlurRadius = 12,
            Opacity = 0.3
        };

        var panel = new StackPanel();

        var titleText = new TextBlock
        {
            Text = "💰 الإيرادات الشهرية",
            FontSize = 14,
            FontWeight = FontWeights.Bold,
            Foreground = Brushes.White,
            Margin = new Thickness(0, 0, 0, 15)
        };
        panel.Children.Add(titleText);

        var revenueText = new TextBlock
        {
            Text = $"{stats.MonthlyRevenue:N0} درهم",
            FontSize = 24,
            FontWeight = FontWeights.Bold,
            Foreground = Brushes.White,
            Margin = new Thickness(0, 0, 0, 5)
        };
        panel.Children.Add(revenueText);

        var changeText = new TextBlock
        {
            Text = "📈 +12% من الشهر الماضي",
            FontSize = 12,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E8F5E8")),
            Margin = new Thickness(0, 0, 0, 10)
        };
        panel.Children.Add(changeText);

        var detailsGrid = new Grid();
        detailsGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        detailsGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

        var paymentsText = new TextBlock
        {
            Text = $"💳 {stats.OverduePayments} متأخرة",
            FontSize = 11,
            Foreground = Brushes.White,
            Opacity = 0.9
        };
        Grid.SetColumn(paymentsText, 0);
        detailsGrid.Children.Add(paymentsText);

        var avgText = new TextBlock
        {
            Text = $"📊 {stats.MonthlyRevenue:N0} متوسط",
            FontSize = 11,
            Foreground = Brushes.White,
            Opacity = 0.9
        };
        Grid.SetColumn(avgText, 1);
        detailsGrid.Children.Add(avgText);

        panel.Children.Add(detailsGrid);
        border.Child = panel;
        return border;
    }

    private Border CreateImprovedPropertyStatusChart(DashboardStats stats)
    {
        var border = new Border
        {
            Background = Brushes.White,
            Margin = new Thickness(0, 0, 15, 0),
            Padding = new Thickness(25),
            CornerRadius = new CornerRadius(12),
            BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E0E0E0")),
            BorderThickness = new Thickness(1)
        };

        border.Effect = new DropShadowEffect
        {
            Color = (Color)ColorConverter.ConvertFromString("#E0E0E0"),
            Direction = 270,
            ShadowDepth = 4,
            BlurRadius = 12,
            Opacity = 0.3
        };

        var panel = new StackPanel();

        var titleText = new TextBlock
        {
            Text = "🏢 حالة المحلات",
            FontSize = 14,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#333")),
            Margin = new Thickness(0, 0, 0, 15),
            HorizontalAlignment = HorizontalAlignment.Center
        };
        panel.Children.Add(titleText);

        // Circular progress indicators
        var statusGrid = new Grid { Height = 120 };

        // Total properties in center
        var totalPanel = new StackPanel
        {
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center
        };

        var totalText = new TextBlock
        {
            Text = stats.TotalProperties.ToString(),
            FontSize = 28,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#333")),
            HorizontalAlignment = HorizontalAlignment.Center
        };
        totalPanel.Children.Add(totalText);

        var totalLabel = new TextBlock
        {
            Text = "إجمالي المحلات",
            FontSize = 10,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#666")),
            HorizontalAlignment = HorizontalAlignment.Center
        };
        totalPanel.Children.Add(totalLabel);

        statusGrid.Children.Add(totalPanel);
        panel.Children.Add(statusGrid);

        // Status breakdown
        var statusBreakdown = new StackPanel { Margin = new Thickness(0, 15, 0, 0) };

        var rentedStatus = CreateStatusIndicator("🟢", "مكتراة", stats.RentedProperties, "#4CAF50");
        var availableStatus = CreateStatusIndicator("🟡", "متاحة", stats.AvailableProperties, "#FF9800");

        statusBreakdown.Children.Add(rentedStatus);
        statusBreakdown.Children.Add(availableStatus);

        panel.Children.Add(statusBreakdown);
        border.Child = panel;
        return border;
    }

    private Grid CreateStatusIndicator(string icon, string label, int value, string color)
    {
        var grid = new Grid();
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
        grid.Margin = new Thickness(0, 5, 0, 5);

        var iconText = new TextBlock
        {
            Text = icon,
            FontSize = 12,
            VerticalAlignment = VerticalAlignment.Center,
            Margin = new Thickness(0, 0, 8, 0)
        };
        Grid.SetColumn(iconText, 0);
        grid.Children.Add(iconText);

        var labelText = new TextBlock
        {
            Text = label,
            FontSize = 12,
            VerticalAlignment = VerticalAlignment.Center,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#333"))
        };
        Grid.SetColumn(labelText, 1);
        grid.Children.Add(labelText);

        var valueText = new TextBlock
        {
            Text = value.ToString(),
            FontSize = 12,
            FontWeight = FontWeights.Bold,
            VerticalAlignment = VerticalAlignment.Center,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color))
        };
        Grid.SetColumn(valueText, 2);
        grid.Children.Add(valueText);

        return grid;
    }

    private StackPanel CreateSimpleBar(string label, int value, int total, string color, int index)
    {
        var barPanel = new StackPanel { Margin = new Thickness(0, 10, 0, 10) };

        var labelGrid = new Grid();
        labelGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        labelGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

        var labelText = new TextBlock
        {
            Text = label,
            FontSize = 14,
            FontWeight = FontWeights.Medium,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#333"))
        };
        Grid.SetColumn(labelText, 0);
        labelGrid.Children.Add(labelText);

        var valueText = new TextBlock
        {
            Text = $"{value} ({(total > 0 ? (value * 100.0 / total):0):F1}%)",
            FontSize = 12,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#666"))
        };
        Grid.SetColumn(valueText, 1);
        labelGrid.Children.Add(valueText);

        barPanel.Children.Add(labelGrid);

        // Progress bar
        var progressBorder = new Border
        {
            Height = 8,
            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F0F0F0")),
            CornerRadius = new CornerRadius(4),
            Margin = new Thickness(0, 5, 0, 0)
        };

        var progressBar = new Border
        {
            Height = 8,
            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color)),
            CornerRadius = new CornerRadius(4),
            HorizontalAlignment = HorizontalAlignment.Left,
            Width = total > 0 ? (value * 200.0 / total) : 0
        };

        var progressGrid = new Grid();
        progressGrid.Children.Add(progressBorder);
        progressGrid.Children.Add(progressBar);

        barPanel.Children.Add(progressGrid);
        return barPanel;
    }

    private Border CreateMonthlyTrendsPanel(DashboardStats stats)
    {
        var border = new Border
        {
            Background = new LinearGradientBrush(
                (Color)ColorConverter.ConvertFromString("#2196F3"),
                (Color)ColorConverter.ConvertFromString("#1976D2"),
                90),
            Margin = new Thickness(10, 0, 0, 0),
            Padding = new Thickness(20),
            CornerRadius = new CornerRadius(12)
        };

        border.Effect = new DropShadowEffect
        {
            Color = (Color)ColorConverter.ConvertFromString("#E0E0E0"),
            Direction = 270,
            ShadowDepth = 4,
            BlurRadius = 12,
            Opacity = 0.3
        };

        var panel = new StackPanel();

        var titleText = new TextBlock
        {
            Text = "📊 الاتجاهات الشهرية",
            FontSize = 14,
            FontWeight = FontWeights.Bold,
            Foreground = Brushes.White,
            Margin = new Thickness(0, 0, 0, 15)
        };
        panel.Children.Add(titleText);

        // Trend indicators
        var trendsGrid = new StackPanel();

        var contractsTrend = CreateTrendIndicator("📋", "عقود جديدة", "3", "↗️", "+2");
        var paymentsTrend = CreateTrendIndicator("💰", "مدفوعات", "12", "↗️", "+5");
        var occupancyTrend = CreateTrendIndicator("🏠", "نسبة الإشغال",
            $"{(stats.TotalProperties > 0 ? (stats.RentedProperties * 100.0 / stats.TotalProperties):0):F0}%",
            "↗️", "+8%");

        trendsGrid.Children.Add(contractsTrend);
        trendsGrid.Children.Add(paymentsTrend);
        trendsGrid.Children.Add(occupancyTrend);

        panel.Children.Add(trendsGrid);
        border.Child = panel;
        return border;
    }

    private Grid CreateTrendIndicator(string icon, string label, string value, string trendIcon, string change)
    {
        var grid = new Grid();
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
        grid.Margin = new Thickness(0, 5, 0, 5);

        var iconText = new TextBlock
        {
            Text = icon,
            FontSize = 12,
            VerticalAlignment = VerticalAlignment.Center,
            Margin = new Thickness(0, 0, 8, 0)
        };
        Grid.SetColumn(iconText, 0);
        grid.Children.Add(iconText);

        var contentPanel = new StackPanel();
        var labelText = new TextBlock
        {
            Text = label,
            FontSize = 10,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E3F2FD"))
        };
        contentPanel.Children.Add(labelText);

        var valueText = new TextBlock
        {
            Text = value,
            FontSize = 14,
            FontWeight = FontWeights.Bold,
            Foreground = Brushes.White
        };
        contentPanel.Children.Add(valueText);

        Grid.SetColumn(contentPanel, 1);
        grid.Children.Add(contentPanel);

        var changePanel = new StackPanel { Orientation = Orientation.Horizontal };
        var trendIconText = new TextBlock
        {
            Text = trendIcon,
            FontSize = 10,
            VerticalAlignment = VerticalAlignment.Center
        };
        changePanel.Children.Add(trendIconText);

        var changeText = new TextBlock
        {
            Text = change,
            FontSize = 10,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E8F5E8")),
            Margin = new Thickness(2, 0, 0, 0)
        };
        changePanel.Children.Add(changeText);

        Grid.SetColumn(changePanel, 2);
        grid.Children.Add(changePanel);

        return grid;
    }

    private Border CreateQuickActionsPanel()
    {
        var border = new Border
        {
            Background = Brushes.White,
            Margin = new Thickness(15, 0, 0, 0),
            Padding = new Thickness(25),
            CornerRadius = new CornerRadius(12),
            BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E0E0E0")),
            BorderThickness = new Thickness(1)
        };

        border.Effect = new DropShadowEffect
        {
            Color = (Color)ColorConverter.ConvertFromString("#E0E0E0"),
            Direction = 270,
            ShadowDepth = 4,
            BlurRadius = 12,
            Opacity = 0.3
        };

        var panel = new StackPanel();

        var titleText = new TextBlock
        {
            Text = "⚡ إجراءات سريعة",
            FontSize = 14,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#333")),
            Margin = new Thickness(0, 0, 0, 15)
        };
        panel.Children.Add(titleText);

        // Quick action buttons
        var actions = new[]
        {
            new { Icon = "🏢", Text = "إدارة المحلات", Color = "#1976D2", Action = new Func<Task>(async () => await ShowProperties()) },
            new { Icon = "👥", Text = "إدارة المكترين", Color = "#4CAF50", Action = new Func<Task>(async () => await ShowCustomers()) },
            new { Icon = "💰", Text = "إدارة الأداءات", Color = "#FF9800", Action = new Func<Task>(async () => await ShowPayments()) },
            new { Icon = "📊", Text = "التقارير", Color = "#9C27B0", Action = new Func<Task>(async () => await ShowReports()) },
            new { Icon = "📁", Text = "إدارة البيانات", Color = "#607D8B", Action = new Func<Task>(async () => await ShowDataManagement()) }
        };

        foreach (var action in actions)
        {
            var actionBtn = CreateActionButton(action.Icon, action.Text, action.Color, action.Action);
            panel.Children.Add(actionBtn);
        }

        border.Child = panel;
        return border;
    }

    private Border CreateRecentActivityPanel()
    {
        var border = new Border
        {
            Background = Brushes.White,
            Margin = new Thickness(0, 0, 15, 0),
            Padding = new Thickness(25),
            CornerRadius = new CornerRadius(12),
            BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E0E0E0")),
            BorderThickness = new Thickness(1)
        };

        border.Effect = new DropShadowEffect
        {
            Color = (Color)ColorConverter.ConvertFromString("#E0E0E0"),
            Direction = 270,
            ShadowDepth = 4,
            BlurRadius = 12,
            Opacity = 0.3
        };

        var panel = new StackPanel();

        var titleText = new TextBlock
        {
            Text = "🕒 النشاط الأخير",
            FontSize = 16,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#333")),
            Margin = new Thickness(0, 0, 0, 20)
        };
        panel.Children.Add(titleText);

        // Recent activities list
        var activitiesPanel = new StackPanel();

        // Sample recent activities
        var activities = new[]
        {
            new { Icon = "💰", Text = "دفعة جديدة من أحمد محمد", Time = "منذ 5 دقائق", Color = "#4CAF50" },
            new { Icon = "📋", Text = "عقد جديد للمحل رقم 12", Time = "منذ ساعة", Color = "#2196F3" },
            new { Icon = "🏠", Text = "تحديث معلومات المحل رقم 8", Time = "منذ ساعتين", Color = "#FF9800" },
            new { Icon = "👤", Text = "إضافة مكتري جديد", Time = "منذ 3 ساعات", Color = "#9C27B0" },
            new { Icon = "📊", Text = "تم إنشاء تقرير شهري", Time = "أمس", Color = "#607D8B" }
        };

        foreach (var activity in activities)
        {
            var activityItem = CreateActivityItem(activity.Icon, activity.Text, activity.Time, activity.Color);
            activitiesPanel.Children.Add(activityItem);
        }

        panel.Children.Add(activitiesPanel);

        // View all button
        var viewAllBtn = new Button
        {
            Content = "عرض جميع الأنشطة →",
            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F5F5F5")),
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#2196F3")),
            BorderThickness = new Thickness(0),
            Padding = new Thickness(15, 10, 15, 10),
            Margin = new Thickness(0, 15, 0, 0),
            FontSize = 12,
            FontWeight = FontWeights.Medium,
            Cursor = Cursors.Hand,
            HorizontalAlignment = HorizontalAlignment.Stretch
        };

        viewAllBtn.Template = CreateRoundedButtonTemplate();
        panel.Children.Add(viewAllBtn);

        border.Child = panel;
        return border;
    }

    private Border CreateActivityItem(string icon, string text, string time, string color)
    {
        var border = new Border
        {
            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FAFAFA")),
            CornerRadius = new CornerRadius(8),
            Padding = new Thickness(15, 12, 15, 12),
            Margin = new Thickness(0, 0, 0, 10)
        };

        var grid = new Grid();
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

        // Icon
        var iconBorder = new Border
        {
            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color)),
            CornerRadius = new CornerRadius(15),
            Width = 30,
            Height = 30,
            Margin = new Thickness(0, 0, 12, 0)
        };

        var iconText = new TextBlock
        {
            Text = icon,
            FontSize = 12,
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center
        };
        iconBorder.Child = iconText;
        Grid.SetColumn(iconBorder, 0);
        grid.Children.Add(iconBorder);

        // Text
        var textPanel = new StackPanel { VerticalAlignment = VerticalAlignment.Center };
        var mainText = new TextBlock
        {
            Text = text,
            FontSize = 12,
            FontWeight = FontWeights.Medium,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#333")),
            TextWrapping = TextWrapping.Wrap
        };
        textPanel.Children.Add(mainText);
        Grid.SetColumn(textPanel, 1);
        grid.Children.Add(textPanel);

        // Time
        var timeText = new TextBlock
        {
            Text = time,
            FontSize = 10,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#999")),
            VerticalAlignment = VerticalAlignment.Center
        };
        Grid.SetColumn(timeText, 2);
        grid.Children.Add(timeText);

        border.Child = grid;
        return border;
    }

    private Button CreateActionButton(string icon, string text, string color, Func<Task> action)
    {
        var button = new Button
        {
            Background = Brushes.Transparent,
            BorderThickness = new Thickness(0),
            Padding = new Thickness(15, 12, 15, 12),
            Margin = new Thickness(0, 5, 0, 5),
            Cursor = Cursors.Hand,
            HorizontalContentAlignment = HorizontalAlignment.Left
        };

        var grid = new Grid();
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

        var iconText = new TextBlock
        {
            Text = icon,
            FontSize = 18,
            VerticalAlignment = VerticalAlignment.Center,
            Margin = new Thickness(0, 0, 12, 0)
        };
        Grid.SetColumn(iconText, 0);
        grid.Children.Add(iconText);

        var textBlock = new TextBlock
        {
            Text = text,
            FontSize = 14,
            FontWeight = FontWeights.Medium,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#333")),
            VerticalAlignment = VerticalAlignment.Center
        };
        Grid.SetColumn(textBlock, 1);
        grid.Children.Add(textBlock);

        button.Content = grid;

        // Hover effect
        button.MouseEnter += (s, e) =>
        {
            button.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F5F5F5"));
        };

        button.MouseLeave += (s, e) =>
        {
            button.Background = Brushes.Transparent;
        };

        button.Click += async (s, e) => await action();

        return button;
    }

    // Helper methods for quick actions
    private async Task AddNewProperty()
    {
        try
        {
            var dialog = new Windows.PropertyDialog();
            dialog.Owner = this;
            if (dialog.ShowDialog() == true)
            {
                await ShowDashboard(); // Refresh dashboard
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إضافة محل جديد: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task AddNewCustomer()
    {
        try
        {
            var dialog = new Windows.CustomerDialog();
            dialog.Owner = this;
            if (dialog.ShowDialog() == true)
            {
                await ShowDashboard(); // Refresh dashboard
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إضافة مكتري جديد: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task ShowDataManagement()
    {
        try
        {
            var dataManagementWindow = new Views.DataManagementWindow();
            dataManagementWindow.Owner = this;
            dataManagementWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح إدارة البيانات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private Border CreateStatCard(string title, string value, string color, int row, int col, int colSpan = 1)
    {
        var border = new Border
        {
            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color)),
            Margin = new Thickness(15),
            Padding = new Thickness(25),
            CornerRadius = new CornerRadius(12)
        };

        // Add shadow effect
        border.Effect = new DropShadowEffect
        {
            Color = (Color)ColorConverter.ConvertFromString("#E0E0E0"),
            Direction = 270,
            ShadowDepth = 6,
            BlurRadius = 15,
            Opacity = 0.4
        };

        Grid.SetRow(border, row);
        Grid.SetColumn(border, col);
        Grid.SetColumnSpan(border, colSpan);

        var stackPanel = new StackPanel
        {
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center
        };

        var valueText = new TextBlock
        {
            Text = value,
            FontSize = 32,
            FontWeight = FontWeights.Bold,
            Foreground = Brushes.White,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 8)
        };

        var titleText = new TextBlock
        {
            Text = title,
            FontSize = 16,
            FontWeight = FontWeights.Medium,
            Foreground = Brushes.White,
            HorizontalAlignment = HorizontalAlignment.Center,
            Opacity = 0.9,
            TextWrapping = TextWrapping.Wrap
        };

        stackPanel.Children.Add(valueText);
        stackPanel.Children.Add(titleText);
        border.Child = stackPanel;

        return border;
    }

    // Customers
    private async Task ShowCustomers()
    {
        try
        {
            var customers = await DatabaseService.Instance.GetCustomersAsync();
            ContentArea.Content = CreateCustomersContent(customers);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل الزبائن: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private FrameworkElement CreateCustomersContent(List<Customer> customers)
    {
        var mainPanel = new StackPanel { Margin = new Thickness(20) };

        // Modern Header
        var headerBorder = new Border
        {
            Background = new LinearGradientBrush(Color.FromRgb(76, 175, 80), Color.FromRgb(56, 142, 60), 0),
            CornerRadius = new CornerRadius(12),
            Padding = new Thickness(20, 15, 20, 15),
            Margin = new Thickness(0, 0, 0, 25)
        };

        var headerStack = new StackPanel
        {
            Orientation = Orientation.Horizontal,
            HorizontalAlignment = HorizontalAlignment.Center
        };

        var icon = new TextBlock
        {
            Text = "👥",
            FontSize = 28,
            Margin = new Thickness(0, 0, 15, 0),
            VerticalAlignment = VerticalAlignment.Center
        };

        var title = new TextBlock
        {
            Text = "إدارة المكترين",
            FontSize = 24,
            FontWeight = FontWeights.Bold,
            Foreground = Brushes.White,
            VerticalAlignment = VerticalAlignment.Center
        };

        headerStack.Children.Add(icon);
        headerStack.Children.Add(title);
        headerBorder.Child = headerStack;
        mainPanel.Children.Add(headerBorder);

        // Search and Actions Panel
        var searchPanel = new Border
        {
            Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
            BorderBrush = new SolidColorBrush(Color.FromRgb(222, 226, 230)),
            BorderThickness = new Thickness(1),
            CornerRadius = new CornerRadius(8),
            Padding = new Thickness(15),
            Margin = new Thickness(0, 0, 0, 20)
        };

        var searchGrid = new Grid();
        searchGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        searchGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

        // Search TextBox
        var searchBox = new TextBox
        {
            Name = "CustomerSearchBox",
            FontSize = 14,
            Padding = new Thickness(12, 10, 12, 10),
            BorderBrush = new SolidColorBrush(Color.FromRgb(206, 212, 218)),
            BorderThickness = new Thickness(1),
            Background = Brushes.White,
            Margin = new Thickness(0, 0, 15, 0)
        };

        // Add placeholder text
        var placeholder = new TextBlock
        {
            Text = "🔍 البحث في المكترين (الاسم، الهاتف، البطاقة الوطنية...)",
            FontSize = 14,
            Foreground = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
            IsHitTestVisible = false,
            Margin = new Thickness(15, 10, 0, 0)
        };

        var searchContainer = new Grid();
        searchContainer.Children.Add(searchBox);
        searchContainer.Children.Add(placeholder);

        // Hide placeholder when text is entered
        searchBox.TextChanged += (s, e) =>
        {
            placeholder.Visibility = string.IsNullOrEmpty(searchBox.Text) ? Visibility.Visible : Visibility.Collapsed;
            FilterCustomers(customers, searchBox.Text, mainPanel);
        };

        Grid.SetColumn(searchContainer, 0);
        searchGrid.Children.Add(searchContainer);

        // Add button
        var addButton = new Button
        {
            Content = "➕ إضافة مكتري جديد",
            Padding = new Thickness(20, 12, 20, 12),
            Background = new SolidColorBrush(Color.FromRgb(76, 175, 80)),
            Foreground = Brushes.White,
            FontWeight = FontWeights.Bold,
            FontSize = 14,
            BorderThickness = new Thickness(0)
        };
        addButton.Click += async (s, e) => await AddCustomer();

        Grid.SetColumn(addButton, 1);
        searchGrid.Children.Add(addButton);
        searchPanel.Child = searchGrid;
        mainPanel.Children.Add(searchPanel);

        // Statistics Panel
        var statsPanel = new Border
        {
            Background = new SolidColorBrush(Color.FromRgb(255, 255, 255)),
            BorderBrush = new SolidColorBrush(Color.FromRgb(222, 226, 230)),
            BorderThickness = new Thickness(1),
            CornerRadius = new CornerRadius(8),
            Padding = new Thickness(15),
            Margin = new Thickness(0, 0, 0, 20)
        };

        var statsText = new TextBlock
        {
            Name = "CustomerStatsText",
            Text = $"📊 إجمالي المكترين: {customers.Count}",
            FontSize = 14,
            FontWeight = FontWeights.SemiBold,
            Foreground = new SolidColorBrush(Color.FromRgb(73, 80, 87))
        };

        statsPanel.Child = statsText;
        mainPanel.Children.Add(statsPanel);

        // Customers list with modern cards
        var scrollViewer = new ScrollViewer
        {
            Height = 450,
            VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
            HorizontalScrollBarVisibility = ScrollBarVisibility.Disabled
        };

        var customersPanel = new StackPanel { Name = "CustomersPanel" };

        foreach (var customer in customers)
        {
            var customerCard = CreateModernCustomerCard(customer);
            customersPanel.Children.Add(customerCard);
        }

        scrollViewer.Content = customersPanel;
        mainPanel.Children.Add(scrollViewer);
        return mainPanel;
    }

    private void FilterCustomers(List<Customer> allCustomers, string searchText, StackPanel mainPanel)
    {
        var customersPanel = mainPanel.Children.OfType<ScrollViewer>().FirstOrDefault()?.Content as StackPanel;
        var statsText = mainPanel.Children.OfType<Border>().Skip(1).FirstOrDefault()?.Child as TextBlock;

        if (customersPanel == null || statsText == null) return;

        customersPanel.Children.Clear();

        var filteredCustomers = allCustomers.Where(c =>
            string.IsNullOrEmpty(searchText) ||
            c.FullName.ToLower().Contains(searchText.ToLower()) ||
            c.Phone.ToLower().Contains(searchText.ToLower()) ||
            c.Email.ToLower().Contains(searchText.ToLower()) ||
            c.NationalId.ToLower().Contains(searchText.ToLower()) ||
            c.Address.ToLower().Contains(searchText.ToLower())
        ).ToList();

        foreach (var customer in filteredCustomers)
        {
            var customerCard = CreateModernCustomerCard(customer);
            customersPanel.Children.Add(customerCard);
        }

        statsText.Text = $"📊 إجمالي المكترين: {filteredCustomers.Count} من أصل {allCustomers.Count}";
    }

    private Border CreateModernCustomerCard(Customer customer)
    {
        var border = new Border
        {
            Background = Brushes.White,
            BorderBrush = new SolidColorBrush(Color.FromRgb(222, 226, 230)),
            BorderThickness = new Thickness(1),
            CornerRadius = new CornerRadius(12),
            Margin = new Thickness(0, 8, 0, 8),
            Padding = new Thickness(20, 15, 20, 15),
            Effect = new DropShadowEffect
            {
                Color = Colors.Gray,
                Direction = 270,
                ShadowDepth = 2,
                BlurRadius = 8,
                Opacity = 0.1
            }
        };

        // Hover effect
        border.MouseEnter += (s, e) => border.Background = new SolidColorBrush(Color.FromRgb(248, 249, 250));
        border.MouseLeave += (s, e) => border.Background = Brushes.White;

        var grid = new Grid();
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

        // Customer details with modern layout
        var detailsPanel = new StackPanel();

        // Name with icon
        var namePanel = new StackPanel { Orientation = Orientation.Horizontal };
        namePanel.Children.Add(new TextBlock
        {
            Text = "👤",
            FontSize = 18,
            Margin = new Thickness(0, 0, 10, 0),
            VerticalAlignment = VerticalAlignment.Center
        });
        namePanel.Children.Add(new TextBlock
        {
            Text = customer.FullName,
            FontSize = 18,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush(Color.FromRgb(52, 58, 64)),
            VerticalAlignment = VerticalAlignment.Center
        });
        detailsPanel.Children.Add(namePanel);

        // Contact info grid
        var contactGrid = new Grid { Margin = new Thickness(0, 10, 0, 0) };
        contactGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        contactGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

        // Phone
        var phonePanel = new StackPanel { Orientation = Orientation.Horizontal };
        phonePanel.Children.Add(new TextBlock
        {
            Text = "📞",
            FontSize = 14,
            Margin = new Thickness(0, 0, 8, 0),
            VerticalAlignment = VerticalAlignment.Center
        });
        phonePanel.Children.Add(new TextBlock
        {
            Text = customer.Phone,
            FontSize = 14,
            Foreground = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
            VerticalAlignment = VerticalAlignment.Center
        });
        Grid.SetColumn(phonePanel, 0);
        contactGrid.Children.Add(phonePanel);

        // National ID
        if (!string.IsNullOrEmpty(customer.NationalId))
        {
            var idPanel = new StackPanel { Orientation = Orientation.Horizontal };
            idPanel.Children.Add(new TextBlock
            {
                Text = "🆔",
                FontSize = 14,
                Margin = new Thickness(0, 0, 8, 0),
                VerticalAlignment = VerticalAlignment.Center
            });
            idPanel.Children.Add(new TextBlock
            {
                Text = customer.NationalId,
                FontSize = 14,
                Foreground = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
                VerticalAlignment = VerticalAlignment.Center
            });
            Grid.SetColumn(idPanel, 1);
            contactGrid.Children.Add(idPanel);
        }

        detailsPanel.Children.Add(contactGrid);

        // Additional info
        if (!string.IsNullOrEmpty(customer.Email))
        {
            var emailPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 8, 0, 0) };
            emailPanel.Children.Add(new TextBlock
            {
                Text = "📧",
                FontSize = 14,
                Margin = new Thickness(0, 0, 8, 0),
                VerticalAlignment = VerticalAlignment.Center
            });
            emailPanel.Children.Add(new TextBlock
            {
                Text = customer.Email,
                FontSize = 14,
                Foreground = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
                VerticalAlignment = VerticalAlignment.Center
            });
            detailsPanel.Children.Add(emailPanel);
        }

        if (!string.IsNullOrEmpty(customer.Address))
        {
            var addressPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 8, 0, 0) };
            addressPanel.Children.Add(new TextBlock
            {
                Text = "🏠",
                FontSize = 14,
                Margin = new Thickness(0, 0, 8, 0),
                VerticalAlignment = VerticalAlignment.Center
            });
            addressPanel.Children.Add(new TextBlock
            {
                Text = customer.Address,
                FontSize = 14,
                Foreground = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
                VerticalAlignment = VerticalAlignment.Center,
                TextWrapping = TextWrapping.Wrap
            });
            detailsPanel.Children.Add(addressPanel);
        }

        // Statistics
        var statsPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 12, 0, 0) };
        statsPanel.Children.Add(new Border
        {
            Background = new SolidColorBrush(Color.FromRgb(220, 248, 198)),
            CornerRadius = new CornerRadius(12),
            Padding = new Thickness(8, 4, 8, 4),
            Margin = new Thickness(0, 0, 10, 0),
            Child = new TextBlock
            {
                Text = $"🏢 {customer.Properties.Count} محل",
                FontSize = 12,
                FontWeight = FontWeights.SemiBold,
                Foreground = new SolidColorBrush(Color.FromRgb(76, 175, 80))
            }
        });

        statsPanel.Children.Add(new Border
        {
            Background = new SolidColorBrush(Color.FromRgb(227, 242, 253)),
            CornerRadius = new CornerRadius(12),
            Padding = new Thickness(8, 4, 8, 4),
            Child = new TextBlock
            {
                Text = $"📅 {customer.CreatedDate:dd/MM/yyyy}",
                FontSize = 12,
                Foreground = new SolidColorBrush(Color.FromRgb(25, 118, 210))
            }
        });

        detailsPanel.Children.Add(statsPanel);

        Grid.SetColumn(detailsPanel, 0);
        grid.Children.Add(detailsPanel);

        // Modern Action buttons
        var buttonsPanel = new StackPanel
        {
            Orientation = Orientation.Vertical,
            VerticalAlignment = VerticalAlignment.Center
        };

        var editButton = new Button
        {
            Content = "✏️ تعديل",
            Padding = new Thickness(15, 8, 15, 8),
            Margin = new Thickness(0, 0, 0, 8),
            Background = new SolidColorBrush(Color.FromRgb(255, 152, 0)),
            Foreground = Brushes.White,
            FontSize = 12,
            FontWeight = FontWeights.SemiBold,
            BorderThickness = new Thickness(0),
            MinWidth = 80
        };
        editButton.Click += async (s, e) => await EditCustomer(customer);

        // Hover effects
        editButton.MouseEnter += (s, e) => editButton.Background = new SolidColorBrush(Color.FromRgb(245, 124, 0));
        editButton.MouseLeave += (s, e) => editButton.Background = new SolidColorBrush(Color.FromRgb(255, 152, 0));

        var deleteButton = new Button
        {
            Content = "🗑️ حذف",
            Padding = new Thickness(15, 8, 15, 8),
            Background = new SolidColorBrush(Color.FromRgb(244, 67, 54)),
            Foreground = Brushes.White,
            FontSize = 12,
            FontWeight = FontWeights.SemiBold,
            BorderThickness = new Thickness(0),
            MinWidth = 80
        };
        deleteButton.Click += async (s, e) => await DeleteCustomer(customer);

        // Hover effects
        deleteButton.MouseEnter += (s, e) => deleteButton.Background = new SolidColorBrush(Color.FromRgb(211, 47, 47));
        deleteButton.MouseLeave += (s, e) => deleteButton.Background = new SolidColorBrush(Color.FromRgb(244, 67, 54));

        buttonsPanel.Children.Add(editButton);
        buttonsPanel.Children.Add(deleteButton);

        Grid.SetColumn(buttonsPanel, 1);
        grid.Children.Add(buttonsPanel);

        border.Child = grid;
        return border;
    }

    // Properties
    private async Task ShowProperties()
    {
        try
        {
            var properties = await DatabaseService.Instance.GetPropertiesAsync();
            ContentArea.Content = CreatePropertiesContent(properties);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل المحلات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private FrameworkElement CreatePropertiesContent(List<Property> properties)
    {
        var mainGrid = new Grid { Margin = new Thickness(0) };
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });

        // Enhanced Header
        var headerPanel = CreatePropertiesHeader(properties.Count);
        Grid.SetRow(headerPanel, 0);
        mainGrid.Children.Add(headerPanel);

        // Statistics Cards
        var statsPanel = CreatePropertiesStatsPanel(properties);
        Grid.SetRow(statsPanel, 1);
        mainGrid.Children.Add(statsPanel);

        // Search and Filter Panel
        var searchPanel = CreatePropertiesSearchPanel();
        Grid.SetRow(searchPanel, 2);
        mainGrid.Children.Add(searchPanel);

        // Properties Grid
        var propertiesPanel = CreatePropertiesGrid(properties);
        Grid.SetRow(propertiesPanel, 3);
        mainGrid.Children.Add(propertiesPanel);

        return mainGrid;
    }

    private StackPanel CreatePropertiesHeader(int totalCount)
    {
        var headerPanel = new StackPanel { Margin = new Thickness(0, 0, 0, 25) };

        var headerGrid = new Grid();
        headerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        headerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

        // Title Section
        var titleStack = new StackPanel();

        var titleText = new TextBlock
        {
            Text = "🏢 إدارة المحلات",
            FontSize = 28,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#1976D2")),
            Margin = new Thickness(0, 0, 0, 5)
        };

        var subtitleText = new TextBlock
        {
            Text = $"إجمالي المحلات: {totalCount} محل",
            FontSize = 14,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#666")),
            Margin = new Thickness(0, 0, 0, 10)
        };

        titleStack.Children.Add(titleText);
        titleStack.Children.Add(subtitleText);
        Grid.SetColumn(titleStack, 0);
        headerGrid.Children.Add(titleStack);

        // Action Buttons
        var actionsStack = new StackPanel { Orientation = Orientation.Horizontal };

        var addBtn = CreateHeaderActionButton("➕ إضافة محل جديد", "#4CAF50", async () => await AddNewProperty());
        var exportBtn = CreateHeaderActionButton("📊 تصدير البيانات", "#2196F3", async () => await ExportPropertiesData());

        actionsStack.Children.Add(addBtn);
        actionsStack.Children.Add(exportBtn);

        Grid.SetColumn(actionsStack, 1);
        headerGrid.Children.Add(actionsStack);

        headerPanel.Children.Add(headerGrid);
        return headerPanel;
    }

    private Button CreateHeaderActionButton(string content, string color, Func<Task> action)
    {
        var button = new Button
        {
            Content = content,
            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color)),
            Foreground = Brushes.White,
            Padding = new Thickness(20, 10, 20, 10),
            Margin = new Thickness(5, 0, 5, 0),
            BorderThickness = new Thickness(0),
            FontSize = 14,
            FontWeight = FontWeights.Medium,
            Cursor = Cursors.Hand
        };

        // Apply rounded corners using template
        button.Template = CreateRoundedButtonTemplate();

        button.Click += async (s, e) => await action();

        return button;
    }



    private StackPanel CreatePropertiesStatsPanel(List<Property> properties)
    {
        var panel = new StackPanel { Margin = new Thickness(0, 0, 0, 25) };

        var statsGrid = new Grid();
        for (int i = 0; i < 4; i++)
        {
            statsGrid.ColumnDefinitions.Add(new ColumnDefinition());
        }

        var totalProperties = properties.Count;
        var rentedProperties = properties.Count(p => p.Status == PropertyStatus.Rented);
        var availableProperties = properties.Count(p => p.Status == PropertyStatus.Available);
        var totalRevenue = properties.Where(p => p.Status == PropertyStatus.Rented).Sum(p => p.MonthlyRent);

        var cards = new[]
        {
            CreatePropertyStatCard("🏢", "إجمالي المحلات", totalProperties.ToString(), "#1976D2", 0),
            CreatePropertyStatCard("✅", "محلات مكتراة", rentedProperties.ToString(), "#4CAF50", 1),
            CreatePropertyStatCard("📋", "محلات متاحة", availableProperties.ToString(), "#FF9800", 2),
            CreatePropertyStatCard("💰", "إيرادات شهرية", $"{totalRevenue:N0} درهم", "#9C27B0", 3)
        };

        foreach (var card in cards)
        {
            statsGrid.Children.Add(card);
        }

        panel.Children.Add(statsGrid);
        return panel;
    }

    private Border CreatePropertyStatCard(string icon, string title, string value, string color, int column)
    {
        var border = new Border
        {
            Background = Brushes.White,
            Margin = new Thickness(5),
            Padding = new Thickness(20),
            CornerRadius = new CornerRadius(12),
            BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E0E0E0")),
            BorderThickness = new Thickness(1)
        };

        border.Effect = new DropShadowEffect
        {
            Color = (Color)ColorConverter.ConvertFromString("#E0E0E0"),
            Direction = 270,
            ShadowDepth = 3,
            BlurRadius = 10,
            Opacity = 0.3
        };

        Grid.SetColumn(border, column);

        var grid = new Grid();
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

        var iconText = new TextBlock
        {
            Text = icon,
            FontSize = 24,
            VerticalAlignment = VerticalAlignment.Center,
            Margin = new Thickness(0, 0, 15, 0)
        };
        Grid.SetColumn(iconText, 0);
        grid.Children.Add(iconText);

        var contentStack = new StackPanel { VerticalAlignment = VerticalAlignment.Center };

        var titleText = new TextBlock
        {
            Text = title,
            FontSize = 12,
            FontWeight = FontWeights.Medium,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#666")),
            Margin = new Thickness(0, 0, 0, 5)
        };

        var valueText = new TextBlock
        {
            Text = value,
            FontSize = 20,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color))
        };

        contentStack.Children.Add(titleText);
        contentStack.Children.Add(valueText);
        Grid.SetColumn(contentStack, 1);
        grid.Children.Add(contentStack);

        border.Child = grid;
        return border;
    }

    private Border CreatePropertiesSearchPanel()
    {
        var border = new Border
        {
            Background = Brushes.White,
            Margin = new Thickness(0, 0, 0, 20),
            Padding = new Thickness(20),
            CornerRadius = new CornerRadius(12),
            BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E0E0E0")),
            BorderThickness = new Thickness(1)
        };

        border.Effect = new DropShadowEffect
        {
            Color = (Color)ColorConverter.ConvertFromString("#E0E0E0"),
            Direction = 270,
            ShadowDepth = 3,
            BlurRadius = 10,
            Opacity = 0.3
        };

        var grid = new Grid();
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

        // Search Box
        var searchBox = new TextBox
        {
            Name = "PropertySearchBox",
            FontSize = 14,
            Padding = new Thickness(15, 12, 15, 12),
            BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#DDD")),
            BorderThickness = new Thickness(1),
            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F9F9F9")),
            Margin = new Thickness(0, 0, 15, 0)
        };

        // Placeholder
        var placeholder = new TextBlock
        {
            Text = "🔍 البحث في المحلات (الاسم، العنوان، المكتري...)",
            FontSize = 14,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#999")),
            IsHitTestVisible = false,
            Margin = new Thickness(15, 12, 15, 12),
            VerticalAlignment = VerticalAlignment.Center
        };

        // Search functionality
        searchBox.TextChanged += (s, e) =>
        {
            placeholder.Visibility = string.IsNullOrEmpty(searchBox.Text) ? Visibility.Visible : Visibility.Collapsed;
            // TODO: Implement search filtering
        };

        var searchGrid = new Grid();
        searchGrid.Children.Add(searchBox);
        searchGrid.Children.Add(placeholder);
        Grid.SetColumn(searchGrid, 0);
        grid.Children.Add(searchGrid);

        // Filter ComboBox
        var filterCombo = new ComboBox
        {
            FontSize = 14,
            Padding = new Thickness(12),
            Margin = new Thickness(0, 0, 15, 0),
            MinWidth = 150,
            Background = Brushes.White,
            BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#DDD"))
        };

        filterCombo.Items.Add(new ComboBoxItem { Content = "جميع المحلات", IsSelected = true });
        filterCombo.Items.Add(new ComboBoxItem { Content = "محلات مكتراة" });
        filterCombo.Items.Add(new ComboBoxItem { Content = "محلات متاحة" });
        filterCombo.Items.Add(new ComboBoxItem { Content = "محلات تحت الصيانة" });

        Grid.SetColumn(filterCombo, 1);
        grid.Children.Add(filterCombo);

        border.Child = grid;
        return border;
    }

    private ScrollViewer CreatePropertiesGrid(List<Property> properties)
    {
        var scrollViewer = new ScrollViewer
        {
            VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
            HorizontalScrollBarVisibility = ScrollBarVisibility.Disabled
        };

        if (properties.Count == 0)
        {
            var emptyPanel = CreateEmptyPropertiesPanel();
            scrollViewer.Content = emptyPanel;
            return scrollViewer;
        }

        var itemsPanel = new WrapPanel
        {
            Orientation = Orientation.Horizontal,
            ItemWidth = 350,
            ItemHeight = 280
        };

        foreach (var property in properties)
        {
            var propertyCard = CreatePropertyCard(property);
            itemsPanel.Children.Add(propertyCard);
        }

        scrollViewer.Content = itemsPanel;
        return scrollViewer;
    }

    private StackPanel CreateEmptyPropertiesPanel()
    {
        var panel = new StackPanel
        {
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center,
            Margin = new Thickness(50)
        };

        var iconText = new TextBlock
        {
            Text = "🏢",
            FontSize = 64,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 20),
            Opacity = 0.5
        };

        var messageText = new TextBlock
        {
            Text = "لا توجد محلات مسجلة حتى الآن",
            FontSize = 18,
            FontWeight = FontWeights.Medium,
            HorizontalAlignment = HorizontalAlignment.Center,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#666")),
            Margin = new Thickness(0, 0, 0, 10)
        };

        var subtitleText = new TextBlock
        {
            Text = "ابدأ بإضافة محل جديد لإدارة العقارات",
            FontSize = 14,
            HorizontalAlignment = HorizontalAlignment.Center,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#999")),
            Margin = new Thickness(0, 0, 0, 30)
        };

        var addBtn = new Button
        {
            Content = "➕ إضافة محل جديد",
            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4CAF50")),
            Foreground = Brushes.White,
            Padding = new Thickness(25, 15, 25, 15),
            BorderThickness = new Thickness(0),
            FontSize = 16,
            FontWeight = FontWeights.Medium,
            Cursor = Cursors.Hand
        };

        // Apply rounded corners using template
        addBtn.Template = CreateRoundedButtonTemplate();
        addBtn.Click += async (s, e) => await AddNewProperty();

        panel.Children.Add(iconText);
        panel.Children.Add(messageText);
        panel.Children.Add(subtitleText);
        panel.Children.Add(addBtn);

        return panel;
    }

    private Border CreatePropertyCard(Property property)
    {
        var card = new Border
        {
            Background = Brushes.White,
            Margin = new Thickness(10),
            CornerRadius = new CornerRadius(12),
            BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E0E0E0")),
            BorderThickness = new Thickness(1),
            Cursor = Cursors.Hand
        };

        card.Effect = new DropShadowEffect
        {
            Color = (Color)ColorConverter.ConvertFromString("#E0E0E0"),
            Direction = 270,
            ShadowDepth = 4,
            BlurRadius = 12,
            Opacity = 0.3
        };

        var mainGrid = new Grid();
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

        // Status Header
        var statusHeader = new Border
        {
            Background = GetStatusColor(property.Status),
            CornerRadius = new CornerRadius(12, 12, 0, 0),
            Padding = new Thickness(15, 10, 15, 10)
        };

        var statusGrid = new Grid();
        statusGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        statusGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

        var statusText = new TextBlock
        {
            Text = GetStatusIcon(property.Status) + " " + GetStatusText(property.Status),
            FontSize = 12,
            FontWeight = FontWeights.Bold,
            Foreground = Brushes.White
        };
        Grid.SetColumn(statusText, 0);
        statusGrid.Children.Add(statusText);

        var rentText = new TextBlock
        {
            Text = $"{property.MonthlyRent:N0} درهم/شهر",
            FontSize = 12,
            FontWeight = FontWeights.Bold,
            Foreground = Brushes.White
        };
        Grid.SetColumn(rentText, 1);
        statusGrid.Children.Add(rentText);

        statusHeader.Child = statusGrid;
        Grid.SetRow(statusHeader, 0);
        mainGrid.Children.Add(statusHeader);

        // Content
        var contentPanel = new StackPanel
        {
            Margin = new Thickness(15)
        };

        // Property Name
        var nameText = new TextBlock
        {
            Text = property.Name,
            FontSize = 16,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#333")),
            Margin = new Thickness(0, 0, 0, 8),
            TextTrimming = TextTrimming.CharacterEllipsis
        };
        contentPanel.Children.Add(nameText);

        // Address
        var addressText = new TextBlock
        {
            Text = "📍 " + property.Address,
            FontSize = 12,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#666")),
            Margin = new Thickness(0, 0, 0, 8),
            TextWrapping = TextWrapping.Wrap,
            MaxHeight = 40
        };
        contentPanel.Children.Add(addressText);

        // Property Details
        var detailsGrid = new Grid();
        detailsGrid.ColumnDefinitions.Add(new ColumnDefinition());
        detailsGrid.ColumnDefinitions.Add(new ColumnDefinition());

        var typeText = new TextBlock
        {
            Text = "🏠 " + property.Type,
            FontSize = 11,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#666"))
        };
        Grid.SetColumn(typeText, 0);
        detailsGrid.Children.Add(typeText);

        if (property.Area > 0)
        {
            var areaText = new TextBlock
            {
                Text = $"📐 {property.Area} م²",
                FontSize = 11,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#666")),
                HorizontalAlignment = HorizontalAlignment.Right
            };
            Grid.SetColumn(areaText, 1);
            detailsGrid.Children.Add(areaText);
        }

        contentPanel.Children.Add(detailsGrid);

        Grid.SetRow(contentPanel, 1);
        mainGrid.Children.Add(contentPanel);

        // Action Buttons Panel
        var actionsPanel = CreatePropertyActionButtons(property);
        Grid.SetRow(actionsPanel, 2);
        mainGrid.Children.Add(actionsPanel);

        card.Child = mainGrid;
        return card;
    }

    private Border CreatePropertyActionButtons(Property property)
    {
        var actionsPanel = new Border
        {
            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F8F9FA")),
            CornerRadius = new CornerRadius(0, 0, 12, 12),
            Padding = new Thickness(15, 10, 15, 10)
        };

        var buttonsGrid = new Grid();
        buttonsGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        buttonsGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
        buttonsGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
        buttonsGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

        // View Details Button
        var viewBtn = new Button
        {
            Content = "👁️ عرض",
            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#2196F3")),
            Foreground = Brushes.White,
            Padding = new Thickness(12, 6, 12, 6),
            BorderThickness = new Thickness(0),
            FontSize = 11,
            FontWeight = FontWeights.Medium,
            Cursor = Cursors.Hand,
            Margin = new Thickness(0, 0, 5, 0)
        };
        viewBtn.Template = CreateSmallRoundedButtonTemplate();
        viewBtn.Click += async (s, e) => await ViewPropertyDetails(property);
        Grid.SetColumn(viewBtn, 1);
        buttonsGrid.Children.Add(viewBtn);

        // Edit Button
        var editBtn = new Button
        {
            Content = "✏️ تعديل",
            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FF9800")),
            Foreground = Brushes.White,
            Padding = new Thickness(12, 6, 12, 6),
            BorderThickness = new Thickness(0),
            FontSize = 11,
            FontWeight = FontWeights.Medium,
            Cursor = Cursors.Hand,
            Margin = new Thickness(0, 0, 5, 0)
        };
        editBtn.Template = CreateSmallRoundedButtonTemplate();
        editBtn.Click += async (s, e) => await EditProperty(property);
        Grid.SetColumn(editBtn, 2);
        buttonsGrid.Children.Add(editBtn);

        // Delete Button
        var deleteBtn = new Button
        {
            Content = "🗑️ حذف",
            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F44336")),
            Foreground = Brushes.White,
            Padding = new Thickness(12, 6, 12, 6),
            BorderThickness = new Thickness(0),
            FontSize = 11,
            FontWeight = FontWeights.Medium,
            Cursor = Cursors.Hand
        };
        deleteBtn.Template = CreateSmallRoundedButtonTemplate();
        deleteBtn.Click += async (s, e) => await DeleteProperty(property);
        Grid.SetColumn(deleteBtn, 3);
        buttonsGrid.Children.Add(deleteBtn);

        actionsPanel.Child = buttonsGrid;
        return actionsPanel;
    }

    private ControlTemplate CreateSmallRoundedButtonTemplate()
    {
        var template = new ControlTemplate(typeof(Button));

        var border = new FrameworkElementFactory(typeof(Border));
        border.SetValue(Border.CornerRadiusProperty, new CornerRadius(6));
        border.SetValue(Border.BackgroundProperty, new TemplateBindingExtension(Button.BackgroundProperty));
        border.SetValue(Border.BorderBrushProperty, new TemplateBindingExtension(Button.BorderBrushProperty));
        border.SetValue(Border.BorderThicknessProperty, new TemplateBindingExtension(Button.BorderThicknessProperty));

        var contentPresenter = new FrameworkElementFactory(typeof(ContentPresenter));
        contentPresenter.SetValue(ContentPresenter.HorizontalAlignmentProperty, HorizontalAlignment.Center);
        contentPresenter.SetValue(ContentPresenter.VerticalAlignmentProperty, VerticalAlignment.Center);
        contentPresenter.SetValue(ContentPresenter.MarginProperty, new TemplateBindingExtension(Button.PaddingProperty));

        border.AppendChild(contentPresenter);
        template.VisualTree = border;

        // Hover trigger
        var trigger = new Trigger { Property = Button.IsMouseOverProperty, Value = true };
        trigger.Setters.Add(new Setter(Button.OpacityProperty, 0.8));
        template.Triggers.Add(trigger);

        return template;
    }

    // Helper methods for property cards
    private SolidColorBrush GetStatusColor(PropertyStatus status)
    {
        return status switch
        {
            PropertyStatus.Rented => new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4CAF50")),
            PropertyStatus.Available => new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FF9800")),
            _ => new SolidColorBrush((Color)ColorConverter.ConvertFromString("#9E9E9E"))
        };
    }

    private string GetStatusIcon(PropertyStatus status)
    {
        return status switch
        {
            PropertyStatus.Rented => "✅",
            PropertyStatus.Available => "📋",
            _ => "❓"
        };
    }

    private string GetStatusText(PropertyStatus status)
    {
        return status switch
        {
            PropertyStatus.Rented => "مكتراة",
            PropertyStatus.Available => "متاحة",
            _ => "غير محدد"
        };
    }

    private async Task ExportPropertiesData()
    {
        try
        {
            var dataManagementWindow = new Views.DataManagementWindow();
            dataManagementWindow.Owner = this;
            dataManagementWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح إدارة البيانات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task ViewPropertyDetails(Property property)
    {
        try
        {
            var detailsWindow = new Windows.PropertyDetailsWindow(property);
            detailsWindow.Owner = this;
            detailsWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في عرض تفاصيل المحل: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private Button CreateCardActionButton(string icon, string text, string color, Func<Task> action)
    {
        var button = new Button
        {
            Background = Brushes.Transparent,
            BorderThickness = new Thickness(0),
            Padding = new Thickness(5),
            Margin = new Thickness(2),
            Cursor = Cursors.Hand,
            ToolTip = text
        };

        var stack = new StackPanel
        {
            Orientation = Orientation.Horizontal,
            HorizontalAlignment = HorizontalAlignment.Center
        };

        var iconText = new TextBlock
        {
            Text = icon,
            FontSize = 14,
            Margin = new Thickness(0, 0, 5, 0)
        };

        var textBlock = new TextBlock
        {
            Text = text,
            FontSize = 11,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color)),
            FontWeight = FontWeights.Medium
        };

        stack.Children.Add(iconText);
        stack.Children.Add(textBlock);
        button.Content = stack;

        button.MouseEnter += (s, e) =>
        {
            button.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F0F0F0"));
        };

        button.MouseLeave += (s, e) =>
        {
            button.Background = Brushes.Transparent;
        };

        button.Click += async (s, e) => await action();
        return button;
    }

    // Cleaning Tax Management
    private async Task ShowCleaningTaxReport()
    {
        try
        {
            ContentArea.Content = await CreateCleaningTaxContentAsync();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل ضريبة النظافة: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task<FrameworkElement> CreateCleaningTaxContentAsync()
    {
        var properties = await DatabaseService.Instance.GetPropertiesAsync();
        var payments = await DatabaseService.Instance.GetPaymentsAsync();
        var cleaningTaxPayments = payments.Where(p => p.CleaningTaxAmount > 0).ToList();

        var mainGrid = new Grid { Margin = new Thickness(0) };
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });

        // Enhanced Header
        var headerPanel = CreateCleaningTaxHeader(cleaningTaxPayments.Count);
        Grid.SetRow(headerPanel, 0);
        mainGrid.Children.Add(headerPanel);

        // Year Selection and Deadline Info
        var yearPanel = CreateCleaningTaxYearPanel();
        Grid.SetRow(yearPanel, 1);
        mainGrid.Children.Add(yearPanel);

        // Statistics Cards
        var statsPanel = CreateCleaningTaxStatsPanel(properties, cleaningTaxPayments);
        Grid.SetRow(statsPanel, 2);
        mainGrid.Children.Add(statsPanel);

        // Search and Filter Panel
        var searchPanel = CreateCleaningTaxSearchPanel();
        Grid.SetRow(searchPanel, 3);
        mainGrid.Children.Add(searchPanel);

        // Properties Grid with Tax Info
        var propertiesPanel = CreateCleaningTaxPropertiesGrid(properties, cleaningTaxPayments);
        Grid.SetRow(propertiesPanel, 4);
        mainGrid.Children.Add(propertiesPanel);

        return mainGrid;
    }

    private StackPanel CreateCleaningTaxHeader(int totalPayments)
    {
        var headerPanel = new StackPanel { Margin = new Thickness(0, 0, 0, 25) };

        var headerGrid = new Grid();
        headerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        headerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

        // Title Section
        var titleStack = new StackPanel();

        var titleText = new TextBlock
        {
            Text = "🧹 إدارة ضريبة النظافة",
            FontSize = 28,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#2E7D32")),
            Margin = new Thickness(0, 0, 0, 5)
        };

        var subtitleText = new TextBlock
        {
            Text = $"إجمالي المدفوعات: {totalPayments} دفعة",
            FontSize = 14,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#666")),
            Margin = new Thickness(0, 0, 0, 10)
        };

        titleStack.Children.Add(titleText);
        titleStack.Children.Add(subtitleText);
        Grid.SetColumn(titleStack, 0);
        headerGrid.Children.Add(titleStack);

        // Action Buttons
        var actionsStack = new StackPanel { Orientation = Orientation.Horizontal };

        var addBtn = CreateHeaderActionButton("➕ دفعة جديدة", "#4CAF50", async () => await AddNewCleaningTaxPayment());
        var reportBtn = CreateHeaderActionButton("📊 تقرير سنوي", "#FF9800", async () => await ShowAnnualCleaningTaxReport());
        var exportBtn = CreateHeaderActionButton("📄 تصدير البيانات", "#2196F3", async () => await ExportCleaningTaxData());

        actionsStack.Children.Add(addBtn);
        actionsStack.Children.Add(reportBtn);
        actionsStack.Children.Add(exportBtn);

        Grid.SetColumn(actionsStack, 1);
        headerGrid.Children.Add(actionsStack);

        headerPanel.Children.Add(headerGrid);
        return headerPanel;
    }

    private Border CreateCleaningTaxYearPanel()
    {
        var border = new Border
        {
            Background = Brushes.White,
            Margin = new Thickness(0, 0, 0, 20),
            Padding = new Thickness(20),
            CornerRadius = new CornerRadius(12),
            BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E0E0E0")),
            BorderThickness = new Thickness(1)
        };

        border.Effect = new DropShadowEffect
        {
            Color = (Color)ColorConverter.ConvertFromString("#E0E0E0"),
            Direction = 270,
            ShadowDepth = 3,
            BlurRadius = 10,
            Opacity = 0.3
        };

        var grid = new Grid();
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

        // Year Selection
        var yearLabel = new TextBlock
        {
            Text = "📅 السنة الضريبية:",
            FontSize = 14,
            FontWeight = FontWeights.Bold,
            VerticalAlignment = VerticalAlignment.Center,
            Margin = new Thickness(0, 0, 15, 0)
        };
        Grid.SetColumn(yearLabel, 0);
        grid.Children.Add(yearLabel);

        var yearCombo = new ComboBox
        {
            Name = "CleaningTaxYearComboBox",
            FontSize = 14,
            Padding = new Thickness(12, 8, 12, 8),
            MinWidth = 120,
            Background = Brushes.White,
            BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#DDD")),
            Margin = new Thickness(0, 0, 30, 0)
        };

        // Add years using dynamic generation (consistent with other windows)
        var currentYear = DateTime.Now.Year;
        var years = GenerateYearsList(2020, 5, true); // Start from 2020, 5 future years, descending order

        foreach (int year in years)
        {
            var item = new ComboBoxItem
            {
                Content = year.ToString(),
                Tag = year,
                IsSelected = year == currentYear
            };
            yearCombo.Items.Add(item);
        }

        yearCombo.SelectionChanged += async (s, e) =>
        {
            if (yearCombo.SelectedItem is ComboBoxItem selectedItem && selectedItem.Tag is int selectedYear)
            {
                await RefreshCleaningTaxForYear(selectedYear);
            }
        };

        Grid.SetColumn(yearCombo, 1);
        grid.Children.Add(yearCombo);

        // Deadline Warning
        var currentYearDeadline = new DateTime(DateTime.Now.Year, 5, 31);
        var daysUntilDeadline = (currentYearDeadline - DateTime.Now).Days;

        var deadlinePanel = new StackPanel
        {
            Orientation = Orientation.Horizontal,
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center
        };

        var warningIcon = new TextBlock
        {
            Text = daysUntilDeadline <= 30 ? "⚠️" : "📅",
            FontSize = 16,
            Margin = new Thickness(0, 0, 8, 0),
            VerticalAlignment = VerticalAlignment.Center
        };

        var deadlineText = new TextBlock
        {
            FontSize = 12,
            FontWeight = FontWeights.Medium,
            VerticalAlignment = VerticalAlignment.Center
        };

        if (DateTime.Now > currentYearDeadline)
        {
            deadlineText.Text = $"انتهى الموعد النهائي لسنة {DateTime.Now.Year} (31/05/{DateTime.Now.Year})";
            deadlineText.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F44336"));
        }
        else if (daysUntilDeadline <= 30)
        {
            deadlineText.Text = $"الموعد النهائي خلال {daysUntilDeadline} يوم (31/05/{DateTime.Now.Year})";
            deadlineText.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FF9800"));
        }
        else
        {
            deadlineText.Text = $"الموعد النهائي: 31/05/{DateTime.Now.Year} (متبقي {daysUntilDeadline} يوم)";
            deadlineText.Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4CAF50"));
        }

        deadlinePanel.Children.Add(warningIcon);
        deadlinePanel.Children.Add(deadlineText);
        Grid.SetColumn(deadlinePanel, 2);
        grid.Children.Add(deadlinePanel);

        // Quick Actions
        var actionsStack = new StackPanel { Orientation = Orientation.Horizontal };

        var reportBtn = new Button
        {
            Content = "📊 تقرير سنوي",
            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#1976D2")),
            Foreground = Brushes.White,
            Padding = new Thickness(15, 8, 15, 8),
            BorderThickness = new Thickness(0),
            FontSize = 12,
            Cursor = Cursors.Hand,
            Margin = new Thickness(0, 0, 10, 0)
        };
        reportBtn.Template = CreateRoundedButtonTemplate();
        reportBtn.Click += async (s, e) => await GenerateAnnualCleaningTaxReport();

        var printBtn = new Button
        {
            Content = "🖨️ طباعة الوصولات",
            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4CAF50")),
            Foreground = Brushes.White,
            Padding = new Thickness(15, 8, 15, 8),
            BorderThickness = new Thickness(0),
            FontSize = 12,
            Cursor = Cursors.Hand
        };
        printBtn.Template = CreateRoundedButtonTemplate();
        printBtn.Click += async (s, e) => await PrintCleaningTaxReceipts();

        actionsStack.Children.Add(reportBtn);
        actionsStack.Children.Add(printBtn);

        Grid.SetColumn(actionsStack, 3);
        grid.Children.Add(actionsStack);

        border.Child = grid;
        return border;
    }

    private async Task RefreshCleaningTaxForYear(int selectedYear)
    {
        try
        {
            // Refresh the cleaning tax report
            await ShowCleaningTaxReport();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحديث بيانات السنة: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private StackPanel CreateCleaningTaxStatsPanel(List<Property> properties, List<Payment> cleaningTaxPayments)
    {
        var panel = new StackPanel { Margin = new Thickness(0, 0, 0, 25) };

        var statsGrid = new Grid();
        for (int i = 0; i < 4; i++)
        {
            statsGrid.ColumnDefinitions.Add(new ColumnDefinition());
        }

        var totalProperties = properties.Count;
        var totalCollected = cleaningTaxPayments.Where(p => p.Status == PaymentStatus.Paid).Sum(p => p.CleaningTaxAmount);
        var totalPending = properties.Sum(p => p.AnnualCleaningTax) - totalCollected;
        var thisYearCollected = cleaningTaxPayments
            .Where(p => p.PaymentDate?.Year == DateTime.Now.Year && p.Status == PaymentStatus.Paid)
            .Sum(p => p.CleaningTaxAmount);

        var cards = new[]
        {
            CreateCleaningTaxStatCard("🏢", "إجمالي المحلات", totalProperties.ToString(), "#2E7D32", 0),
            CreateCleaningTaxStatCard("💰", "محصل هذا العام", $"{thisYearCollected:N0} درهم", "#4CAF50", 1),
            CreateCleaningTaxStatCard("⏳", "متبقي للتحصيل", $"{totalPending:N0} درهم", "#FF9800", 2),
            CreateCleaningTaxStatCard("📊", "إجمالي محصل", $"{totalCollected:N0} درهم", "#1976D2", 3)
        };

        foreach (var card in cards)
        {
            statsGrid.Children.Add(card);
        }

        panel.Children.Add(statsGrid);
        return panel;
    }

    private Border CreateCleaningTaxStatCard(string icon, string title, string value, string color, int column)
    {
        var border = new Border
        {
            Background = Brushes.White,
            Margin = new Thickness(5),
            Padding = new Thickness(20),
            CornerRadius = new CornerRadius(12),
            BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E0E0E0")),
            BorderThickness = new Thickness(1)
        };

        border.Effect = new DropShadowEffect
        {
            Color = (Color)ColorConverter.ConvertFromString("#E0E0E0"),
            Direction = 270,
            ShadowDepth = 3,
            BlurRadius = 10,
            Opacity = 0.3
        };

        Grid.SetColumn(border, column);

        var grid = new Grid();
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

        var iconText = new TextBlock
        {
            Text = icon,
            FontSize = 24,
            VerticalAlignment = VerticalAlignment.Center,
            Margin = new Thickness(0, 0, 15, 0)
        };
        Grid.SetColumn(iconText, 0);
        grid.Children.Add(iconText);

        var contentStack = new StackPanel { VerticalAlignment = VerticalAlignment.Center };

        var titleText = new TextBlock
        {
            Text = title,
            FontSize = 12,
            FontWeight = FontWeights.Medium,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#666")),
            Margin = new Thickness(0, 0, 0, 5)
        };

        var valueText = new TextBlock
        {
            Text = value,
            FontSize = 20,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color))
        };

        contentStack.Children.Add(titleText);
        contentStack.Children.Add(valueText);
        Grid.SetColumn(contentStack, 1);
        grid.Children.Add(contentStack);

        border.Child = grid;
        return border;
    }

    // Reports Management
    private async Task ShowReports()
    {
        try
        {
            ContentArea.Content = await CreateReportsContentAsync();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل التقارير: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task<FrameworkElement> CreateReportsContentAsync()
    {
        var mainGrid = new Grid { Margin = new Thickness(0) };
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });

        // Enhanced Header
        var headerPanel = CreateReportsHeader();
        Grid.SetRow(headerPanel, 0);
        mainGrid.Children.Add(headerPanel);

        // Quick Stats Panel
        var quickStatsPanel = await CreateReportsQuickStatsPanel();
        Grid.SetRow(quickStatsPanel, 1);
        mainGrid.Children.Add(quickStatsPanel);

        // Reports Categories Grid
        var reportsPanel = CreateReportsCategoriesGrid();
        Grid.SetRow(reportsPanel, 2);
        mainGrid.Children.Add(reportsPanel);

        return mainGrid;
    }

    private StackPanel CreateReportsHeader()
    {
        var headerPanel = new StackPanel { Margin = new Thickness(0, 0, 0, 25) };

        var headerGrid = new Grid();
        headerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        headerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

        // Title Section
        var titleStack = new StackPanel();

        var titleText = new TextBlock
        {
            Text = "📊 مركز التقارير",
            FontSize = 28,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#7B1FA2")),
            Margin = new Thickness(0, 0, 0, 5)
        };

        var subtitleText = new TextBlock
        {
            Text = "تقارير شاملة لإدارة الممتلكات والإيجارات",
            FontSize = 14,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#666")),
            Margin = new Thickness(0, 0, 0, 10)
        };

        titleStack.Children.Add(titleText);
        titleStack.Children.Add(subtitleText);
        Grid.SetColumn(titleStack, 0);
        headerGrid.Children.Add(titleStack);

        // Action Buttons
        var actionsStack = new StackPanel { Orientation = Orientation.Horizontal };

        var customBtn = CreateHeaderActionButton("🔧 تقرير مخصص", "#FF5722", async () => await CreateCustomReport());
        var scheduleBtn = CreateHeaderActionButton("⏰ جدولة التقارير", "#607D8B", async () => await ScheduleReports());
        var exportBtn = CreateHeaderActionButton("📤 تصدير جميع التقارير", "#795548", async () => await ExportAllReports());

        actionsStack.Children.Add(customBtn);
        actionsStack.Children.Add(scheduleBtn);
        actionsStack.Children.Add(exportBtn);

        Grid.SetColumn(actionsStack, 1);
        headerGrid.Children.Add(actionsStack);

        headerPanel.Children.Add(headerGrid);
        return headerPanel;
    }

    private async Task<StackPanel> CreateReportsQuickStatsPanel()
    {
        var panel = new StackPanel { Margin = new Thickness(0, 0, 0, 25) };

        // Get data for quick stats
        var properties = await DatabaseService.Instance.GetPropertiesAsync();
        var payments = await DatabaseService.Instance.GetPaymentsAsync();
        var customers = await DatabaseService.Instance.GetCustomersAsync();
        var contracts = await DatabaseService.Instance.GetContractsAsync();

        var statsGrid = new Grid();
        for (int i = 0; i < 4; i++)
        {
            statsGrid.ColumnDefinitions.Add(new ColumnDefinition());
        }

        var thisMonthRevenue = payments
            .Where(p => p.PaymentDate?.Month == DateTime.Now.Month &&
                       p.PaymentDate?.Year == DateTime.Now.Year &&
                       p.Status == PaymentStatus.Paid)
            .Sum(p => p.RentAmount);

        var activeContracts = contracts.Count(c => c.Status == ContractStatus.Active);
        var occupancyRate = properties.Count > 0 ? (double)properties.Count(p => p.Status == PropertyStatus.Rented) / properties.Count * 100 : 0;
        var totalCustomers = customers.Count;

        var cards = new[]
        {
            CreateReportStatCard("💰", "إيرادات هذا الشهر", $"{thisMonthRevenue:N0} درهم", "#4CAF50", 0),
            CreateReportStatCard("📋", "عقود نشطة", activeContracts.ToString(), "#2196F3", 1),
            CreateReportStatCard("📈", "معدل الإشغال", $"{occupancyRate:F1}%", "#FF9800", 2),
            CreateReportStatCard("👥", "إجمالي المكترين", totalCustomers.ToString(), "#9C27B0", 3)
        };

        foreach (var card in cards)
        {
            statsGrid.Children.Add(card);
        }

        panel.Children.Add(statsGrid);
        return panel;
    }

    private Border CreateReportStatCard(string icon, string title, string value, string color, int column)
    {
        var border = new Border
        {
            Background = Brushes.White,
            Margin = new Thickness(5),
            Padding = new Thickness(20),
            CornerRadius = new CornerRadius(12),
            BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E0E0E0")),
            BorderThickness = new Thickness(1)
        };

        border.Effect = new DropShadowEffect
        {
            Color = (Color)ColorConverter.ConvertFromString("#E0E0E0"),
            Direction = 270,
            ShadowDepth = 3,
            BlurRadius = 10,
            Opacity = 0.3
        };

        Grid.SetColumn(border, column);

        var grid = new Grid();
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

        var iconText = new TextBlock
        {
            Text = icon,
            FontSize = 24,
            VerticalAlignment = VerticalAlignment.Center,
            Margin = new Thickness(0, 0, 15, 0)
        };
        Grid.SetColumn(iconText, 0);
        grid.Children.Add(iconText);

        var contentStack = new StackPanel { VerticalAlignment = VerticalAlignment.Center };

        var titleText = new TextBlock
        {
            Text = title,
            FontSize = 12,
            FontWeight = FontWeights.Medium,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#666")),
            Margin = new Thickness(0, 0, 0, 5)
        };

        var valueText = new TextBlock
        {
            Text = value,
            FontSize = 20,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color))
        };

        contentStack.Children.Add(titleText);
        contentStack.Children.Add(valueText);
        Grid.SetColumn(contentStack, 1);
        grid.Children.Add(contentStack);

        border.Child = grid;
        return border;
    }

    private ScrollViewer CreateReportsCategoriesGrid()
    {
        var scrollViewer = new ScrollViewer
        {
            VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
            HorizontalScrollBarVisibility = ScrollBarVisibility.Disabled
        };

        var itemsPanel = new WrapPanel
        {
            Orientation = Orientation.Horizontal,
            ItemWidth = 300,
            ItemHeight = 200
        };

        // Report Categories
        var reportCategories = new[]
        {
            new { Title = "تقارير الإيرادات", Icon = "💰", Description = "تقارير شاملة للإيرادات والمدفوعات", Color = "#4CAF50", Action = new Func<Task>(async () => await ShowRevenueReports()) },
            new { Title = "تقارير المحلات", Icon = "🏢", Description = "تقارير حالة وإشغال المحلات", Color = "#2196F3", Action = new Func<Task>(async () => await ShowPropertyReports()) },
            new { Title = "تقارير المكترين", Icon = "👥", Description = "تقارير المكترين والعقود", Color = "#FF9800", Action = new Func<Task>(async () => await ShowCustomerReports()) },
            new { Title = "تقارير ضريبة النظافة", Icon = "🧹", Description = "تقارير ضريبة النظافة والمدفوعات", Color = "#9C27B0", Action = new Func<Task>(async () => await ShowCleaningTaxReports()) },
            new { Title = "التقارير المالية", Icon = "📊", Description = "تقارير مالية شاملة وتحليلات", Color = "#F44336", Action = new Func<Task>(async () => await ShowFinancialReports()) },
            new { Title = "تقارير العقود", Icon = "📋", Description = "تقارير العقود والتجديدات", Color = "#795548", Action = new Func<Task>(async () => await ShowContractReports()) }
        };

        foreach (var category in reportCategories)
        {
            var categoryCard = CreateReportCategoryCard(category.Title, category.Icon, category.Description, category.Color, category.Action);
            itemsPanel.Children.Add(categoryCard);
        }

        scrollViewer.Content = itemsPanel;
        return scrollViewer;
    }

    private Border CreateReportCategoryCard(string title, string icon, string description, string color, Func<Task> action)
    {
        var card = new Border
        {
            Background = Brushes.White,
            Margin = new Thickness(10),
            CornerRadius = new CornerRadius(12),
            BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E0E0E0")),
            BorderThickness = new Thickness(1),
            Cursor = Cursors.Hand
        };

        card.Effect = new DropShadowEffect
        {
            Color = (Color)ColorConverter.ConvertFromString("#E0E0E0"),
            Direction = 270,
            ShadowDepth = 4,
            BlurRadius = 12,
            Opacity = 0.3
        };

        var mainGrid = new Grid();
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

        // Icon Header
        var iconHeader = new Border
        {
            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color)),
            CornerRadius = new CornerRadius(12, 12, 0, 0),
            Padding = new Thickness(20),
            HorizontalAlignment = HorizontalAlignment.Stretch
        };

        var iconText = new TextBlock
        {
            Text = icon,
            FontSize = 32,
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center
        };

        iconHeader.Child = iconText;
        Grid.SetRow(iconHeader, 0);
        mainGrid.Children.Add(iconHeader);

        // Content
        var contentPanel = new StackPanel
        {
            Margin = new Thickness(20),
            VerticalAlignment = VerticalAlignment.Center
        };

        var titleText = new TextBlock
        {
            Text = title,
            FontSize = 16,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#333")),
            Margin = new Thickness(0, 0, 0, 10),
            TextAlignment = TextAlignment.Center
        };

        var descriptionText = new TextBlock
        {
            Text = description,
            FontSize = 12,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#666")),
            TextWrapping = TextWrapping.Wrap,
            TextAlignment = TextAlignment.Center,
            LineHeight = 18
        };

        contentPanel.Children.Add(titleText);
        contentPanel.Children.Add(descriptionText);
        Grid.SetRow(contentPanel, 1);
        mainGrid.Children.Add(contentPanel);

        // Action Button
        var actionButton = new Button
        {
            Content = "عرض التقرير",
            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color)),
            Foreground = Brushes.White,
            Padding = new Thickness(20, 10, 20, 10),
            BorderThickness = new Thickness(0),
            FontSize = 12,
            FontWeight = FontWeights.Medium,
            Margin = new Thickness(20, 10, 20, 20),
            Cursor = Cursors.Hand
        };

        // Apply rounded corners using template
        actionButton.Template = CreateRoundedButtonTemplate();
        actionButton.Click += async (s, e) => await action();

        Grid.SetRow(actionButton, 2);
        mainGrid.Children.Add(actionButton);

        card.Child = mainGrid;

        // Hover effects
        card.MouseEnter += (s, e) =>
        {
            card.BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color));
            card.BorderThickness = new Thickness(2);
        };

        card.MouseLeave += (s, e) =>
        {
            card.BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E0E0E0"));
            card.BorderThickness = new Thickness(1);
        };

        return card;
    }

    // Report Action Methods
    private async Task CreateCustomReport()
    {
        MessageBox.Show("إنشاء تقرير مخصص - قيد التطوير", "معلومات",
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private async Task ScheduleReports()
    {
        MessageBox.Show("جدولة التقارير - قيد التطوير", "معلومات",
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private async Task ExportAllReports()
    {
        MessageBox.Show("تصدير جميع التقارير - قيد التطوير", "معلومات",
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private async Task ShowRevenueReports()
    {
        MessageBox.Show("تقارير الإيرادات - قيد التطوير", "معلومات",
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private async Task ShowPropertyReports()
    {
        MessageBox.Show("تقارير المحلات - قيد التطوير", "معلومات",
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private async Task ShowCustomerReports()
    {
        MessageBox.Show("تقارير المكترين - قيد التطوير", "معلومات",
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private async Task ShowCleaningTaxReports()
    {
        MessageBox.Show("تقارير ضريبة النظافة - قيد التطوير", "معلومات",
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private async Task ShowFinancialReports()
    {
        MessageBox.Show("التقارير المالية - قيد التطوير", "معلومات",
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private async Task ShowContractReports()
    {
        MessageBox.Show("تقارير العقود - قيد التطوير", "معلومات",
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    // Cleaning Tax Helper Methods
    private Border CreateCleaningTaxSearchPanel()
    {
        var border = new Border
        {
            Background = Brushes.White,
            Margin = new Thickness(0, 0, 0, 20),
            Padding = new Thickness(20),
            CornerRadius = new CornerRadius(12),
            BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E0E0E0")),
            BorderThickness = new Thickness(1)
        };

        border.Effect = new DropShadowEffect
        {
            Color = (Color)ColorConverter.ConvertFromString("#E0E0E0"),
            Direction = 270,
            ShadowDepth = 3,
            BlurRadius = 10,
            Opacity = 0.3
        };

        var grid = new Grid();
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

        // Search Box
        var searchBox = new TextBox
        {
            Name = "CleaningTaxSearchBox",
            FontSize = 14,
            Padding = new Thickness(15, 12, 15, 12),
            BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#DDD")),
            BorderThickness = new Thickness(1),
            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F9F9F9")),
            Margin = new Thickness(0, 0, 15, 0)
        };

        Grid.SetColumn(searchBox, 0);
        grid.Children.Add(searchBox);

        border.Child = grid;
        return border;
    }

    private ScrollViewer CreateCleaningTaxPropertiesGrid(List<Property> properties, List<Payment> cleaningTaxPayments)
    {
        var scrollViewer = new ScrollViewer
        {
            VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
            HorizontalScrollBarVisibility = ScrollBarVisibility.Disabled
        };

        var itemsPanel = new WrapPanel
        {
            Orientation = Orientation.Horizontal,
            ItemWidth = 350,
            ItemHeight = 250
        };

        foreach (var property in properties)
        {
            var propertyPayments = cleaningTaxPayments.Where(p => p.PropertyId == property.Id).ToList();
            var propertyCard = CreateCleaningTaxPropertyCard(property, propertyPayments);
            itemsPanel.Children.Add(propertyCard);
        }

        scrollViewer.Content = itemsPanel;
        return scrollViewer;
    }

    private Border CreateCleaningTaxPropertyCard(Property property, List<Payment> payments)
    {
        var card = new Border
        {
            Background = Brushes.White,
            Margin = new Thickness(10),
            CornerRadius = new CornerRadius(12),
            BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E0E0E0")),
            BorderThickness = new Thickness(1),
            Cursor = Cursors.Hand
        };

        card.Effect = new DropShadowEffect
        {
            Color = (Color)ColorConverter.ConvertFromString("#E0E0E0"),
            Direction = 270,
            ShadowDepth = 4,
            BlurRadius = 12,
            Opacity = 0.3
        };

        var mainGrid = new Grid();
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

        // Header with property info
        var headerPanel = new Border
        {
            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#2E7D32")),
            CornerRadius = new CornerRadius(12, 12, 0, 0),
            Padding = new Thickness(15, 10, 15, 10)
        };

        var headerGrid = new Grid();
        headerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        headerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

        var propertyName = new TextBlock
        {
            Text = property.Name,
            FontSize = 14,
            FontWeight = FontWeights.Bold,
            Foreground = Brushes.White,
            TextTrimming = TextTrimming.CharacterEllipsis
        };
        Grid.SetColumn(propertyName, 0);
        headerGrid.Children.Add(propertyName);

        var annualTax = property.MonthlyRent * 12 * 0.105m; // 10.5% of annual rent
        var taxAmount = new TextBlock
        {
            Text = $"{annualTax:N0} درهم/سنة",
            FontSize = 12,
            FontWeight = FontWeights.Bold,
            Foreground = Brushes.White
        };
        Grid.SetColumn(taxAmount, 1);
        headerGrid.Children.Add(taxAmount);

        headerPanel.Child = headerGrid;
        Grid.SetRow(headerPanel, 0);
        mainGrid.Children.Add(headerPanel);

        // Content with payment info
        var contentPanel = new StackPanel
        {
            Margin = new Thickness(15)
        };

        // Property details
        var addressText = new TextBlock
        {
            Text = "📍 " + property.Address,
            FontSize = 11,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#666")),
            Margin = new Thickness(0, 0, 0, 8),
            TextWrapping = TextWrapping.Wrap
        };
        contentPanel.Children.Add(addressText);

        // Payment status for current year
        var currentYear = DateTime.Now.Year;
        var currentYearPayments = payments.Where(p => p.PaymentDate?.Year == currentYear).ToList();
        var totalPaid = currentYearPayments.Where(p => p.Status == PaymentStatus.Paid).Sum(p => p.CleaningTaxAmount);
        var remaining = annualTax - totalPaid;

        // Check deadline status
        var deadline = new DateTime(currentYear, 5, 31);
        var isOverdue = DateTime.Now > deadline && remaining > 0;

        var paymentStatusGrid = new Grid();
        paymentStatusGrid.ColumnDefinitions.Add(new ColumnDefinition());
        paymentStatusGrid.ColumnDefinitions.Add(new ColumnDefinition());

        var paidText = new TextBlock
        {
            Text = $"مدفوع {currentYear}: {totalPaid:N0} درهم",
            FontSize = 11,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4CAF50")),
            FontWeight = FontWeights.Medium
        };
        Grid.SetColumn(paidText, 0);
        paymentStatusGrid.Children.Add(paidText);

        var remainingText = new TextBlock
        {
            Text = isOverdue ? $"متأخر: {remaining:N0} درهم" : $"متبقي: {remaining:N0} درهم",
            FontSize = 11,
            Foreground = isOverdue ?
                new SolidColorBrush((Color)ColorConverter.ConvertFromString("#D32F2F")) :
                remaining > 0 ?
                    new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F44336")) :
                    new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4CAF50")),
            FontWeight = FontWeights.Medium,
            HorizontalAlignment = HorizontalAlignment.Right
        };
        Grid.SetColumn(remainingText, 1);
        paymentStatusGrid.Children.Add(remainingText);

        contentPanel.Children.Add(paymentStatusGrid);

        // Progress bar
        var progressBorder = new Border
        {
            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E0E0E0")),
            Height = 6,
            CornerRadius = new CornerRadius(3),
            Margin = new Thickness(0, 8, 0, 8)
        };

        var progressFill = new Border
        {
            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4CAF50")),
            Height = 6,
            CornerRadius = new CornerRadius(3),
            HorizontalAlignment = HorizontalAlignment.Left
        };

        var progressPercentage = annualTax > 0 ? Math.Min(100, (double)(totalPaid / annualTax) * 100) : 0;
        progressFill.Width = (progressBorder.Width * progressPercentage / 100);

        var progressGrid = new Grid();
        progressGrid.Children.Add(progressBorder);
        progressGrid.Children.Add(progressFill);

        contentPanel.Children.Add(progressGrid);

        // Payment count
        var paymentCountText = new TextBlock
        {
            Text = $"عدد الدفعات: {currentYearPayments.Count}",
            FontSize = 10,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#999")),
            Margin = new Thickness(0, 5, 0, 0)
        };
        contentPanel.Children.Add(paymentCountText);

        Grid.SetRow(contentPanel, 1);
        mainGrid.Children.Add(contentPanel);

        // Action buttons
        var actionsPanel = new Border
        {
            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F8F9FA")),
            Padding = new Thickness(15, 10, 15, 10),
            CornerRadius = new CornerRadius(0, 0, 12, 12)
        };

        var actionsGrid = new Grid();
        actionsGrid.ColumnDefinitions.Add(new ColumnDefinition());
        actionsGrid.ColumnDefinitions.Add(new ColumnDefinition());
        actionsGrid.ColumnDefinitions.Add(new ColumnDefinition());
        actionsGrid.ColumnDefinitions.Add(new ColumnDefinition());
        actionsGrid.ColumnDefinitions.Add(new ColumnDefinition());

        var viewBtn = CreateCleaningTaxActionButton("👁️", "عرض", "#2196F3", async () => await ViewCleaningTaxDetails(property));
        var editBtn = CreateCleaningTaxActionButton("✏️", "تعديل", "#FF5722", async () => await EditCleaningTaxPayment(property));
        var payBtn = CreateCleaningTaxActionButton("💰", "دفع", "#4CAF50", async () => await AddCleaningTaxPaymentForProperty(property));
        var printBtn = CreateCleaningTaxActionButton("🖨️", "طباعة", "#9C27B0", async () => await PrintCleaningTaxReceiptForProperty(property));
        var historyBtn = CreateCleaningTaxActionButton("📋", "السجل", "#FF9800", async () => await ViewCleaningTaxHistory(property));

        Grid.SetColumn(viewBtn, 0);
        Grid.SetColumn(editBtn, 1);
        Grid.SetColumn(payBtn, 2);
        Grid.SetColumn(printBtn, 3);
        Grid.SetColumn(historyBtn, 4);

        actionsGrid.Children.Add(viewBtn);
        actionsGrid.Children.Add(editBtn);
        actionsGrid.Children.Add(payBtn);
        actionsGrid.Children.Add(printBtn);
        actionsGrid.Children.Add(historyBtn);

        actionsPanel.Child = actionsGrid;
        Grid.SetRow(actionsPanel, 2);
        mainGrid.Children.Add(actionsPanel);

        card.Child = mainGrid;

        // Hover effects
        card.MouseEnter += (s, e) =>
        {
            card.BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#2E7D32"));
            card.BorderThickness = new Thickness(2);
        };

        card.MouseLeave += (s, e) =>
        {
            card.BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E0E0E0"));
            card.BorderThickness = new Thickness(1);
        };

        return card;
    }

    private Button CreateCleaningTaxActionButton(string icon, string text, string color, Func<Task> action)
    {
        var button = new Button
        {
            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color)),
            BorderThickness = new Thickness(0),
            Padding = new Thickness(8, 4, 8, 4),
            Margin = new Thickness(1),
            Cursor = Cursors.Hand,
            ToolTip = text,

            MinWidth = 60,
            Height = 28
        };

        var stack = new StackPanel
        {
            Orientation = Orientation.Vertical,
            HorizontalAlignment = HorizontalAlignment.Center
        };

        var iconText = new TextBlock
        {
            Text = icon,
            FontSize = 10,
            Foreground = Brushes.White,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 1)
        };

        var textBlock = new TextBlock
        {
            Text = text,
            FontSize = 8,
            Foreground = Brushes.White,
            FontWeight = FontWeights.Medium,
            HorizontalAlignment = HorizontalAlignment.Center
        };

        stack.Children.Add(iconText);
        stack.Children.Add(textBlock);
        button.Content = stack;

        // Hover effects
        var originalColor = (Color)ColorConverter.ConvertFromString(color);
        var hoverColor = Color.FromRgb(
            (byte)Math.Min(255, originalColor.R + 20),
            (byte)Math.Min(255, originalColor.G + 20),
            (byte)Math.Min(255, originalColor.B + 20)
        );

        button.MouseEnter += (s, e) =>
        {
            button.Background = new SolidColorBrush(hoverColor);
        };

        button.MouseLeave += (s, e) =>
        {
            button.Background = new SolidColorBrush(originalColor);
        };

        button.Click += async (s, e) => await action();
        return button;
    }

    // Cleaning Tax Action Methods
    private async Task AddNewCleaningTaxPayment()
    {
        try
        {
            var properties = await DatabaseService.Instance.GetPropertiesAsync();
            if (properties.Count == 0)
            {
                MessageBox.Show("لا توجد محلات مسجلة. يرجى إضافة محل أولاً.", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var dialog = new Windows.SimpleCleaningTaxPaymentDialog(properties.First(), DateTime.Now.Year);
            dialog.Owner = this;
            if (dialog.ShowDialog() == true)
            {
                await ShowCleaningTaxReport(); // Refresh
                MessageBox.Show("تم إضافة دفعة ضريبة النظافة بنجاح", "نجح",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إضافة دفعة ضريبة النظافة: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task ViewCleaningTaxDetails(Property property)
    {
        try
        {
            var payments = await DatabaseService.Instance.GetPaymentsAsync();
            var propertyPayments = payments.Where(p => p.PropertyId == property.Id && p.CleaningTaxAmount > 0).ToList();

            // Show simple details for now
            var details = $"تفاصيل ضريبة النظافة للمحل: {property.Name}\n\n";
            details += $"الإيجار الشهري: {property.MonthlyRent:N0} درهم\n";
            details += $"ضريبة النظافة السنوية: {property.MonthlyRent * 12 * 0.105m:N0} درهم\n";
            details += $"عدد الدفعات: {propertyPayments.Count}\n";
            details += $"إجمالي المدفوع: {propertyPayments.Where(p => p.Status == PaymentStatus.Paid).Sum(p => p.CleaningTaxAmount):N0} درهم";

            MessageBox.Show(details, "تفاصيل ضريبة النظافة", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في عرض تفاصيل ضريبة النظافة: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task AddCleaningTaxPaymentForProperty(Property property)
    {
        try
        {
            var dialog = new Windows.SimpleCleaningTaxPaymentDialog(property, DateTime.Now.Year);
            dialog.Owner = this;
            if (dialog.ShowDialog() == true)
            {
                await ShowCleaningTaxReport(); // Refresh
                MessageBox.Show("تم إضافة دفعة ضريبة النظافة بنجاح", "نجح",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إضافة دفعة ضريبة النظافة: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task ViewCleaningTaxHistory(Property property)
    {
        try
        {
            var payments = await DatabaseService.Instance.GetPaymentsAsync();
            var propertyPayments = payments.Where(p => p.PropertyId == property.Id && p.CleaningTaxAmount > 0)
                                         .OrderByDescending(p => p.PaymentDate)
                                         .ToList();

            // Show simple history for now
            var history = $"سجل ضريبة النظافة للمحل: {property.Name}\n\n";

            if (propertyPayments.Count == 0)
            {
                history += "لا توجد دفعات مسجلة";
            }
            else
            {
                foreach (var payment in propertyPayments.Take(10))
                {
                    history += $"📅 {payment.PaymentDate?.ToString("dd/MM/yyyy") ?? "غير محدد"} - ";
                    history += $"💰 {payment.CleaningTaxAmount:N0} درهم - ";
                    history += $"📊 {payment.Status}\n";
                }

                if (propertyPayments.Count > 10)
                {
                    history += $"\n... و {propertyPayments.Count - 10} دفعة أخرى";
                }
            }

            MessageBox.Show(history, "سجل ضريبة النظافة", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في عرض سجل ضريبة النظافة: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task EditCleaningTaxPayment(Property property)
    {
        try
        {
            var payments = await DatabaseService.Instance.GetPaymentsAsync();
            var currentYear = DateTime.Now.Year;
            var propertyPayments = payments.Where(p => p.PropertyId == property.Id &&
                                                      p.PaymentDate?.Year == currentYear &&
                                                      p.CleaningTaxAmount > 0).ToList();

            if (propertyPayments.Count == 0)
            {
                MessageBox.Show($"لا توجد دفعات ضريبة نظافة للمحل {property.Name} في عام {currentYear}", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // Show simple edit message for now
            MessageBox.Show($"تم العثور على {propertyPayments.Count} دفعة لضريبة النظافة للمحل {property.Name}\nسيتم إضافة نافذة التعديل قريباً", "معلومات",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تعديل دفعة ضريبة النظافة: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task ShowAnnualCleaningTaxReport()
    {
        MessageBox.Show("التقرير السنوي لضريبة النظافة - قيد التطوير", "معلومات",
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private async Task ExportCleaningTaxData()
    {
        MessageBox.Show("تصدير بيانات ضريبة النظافة - قيد التطوير", "معلومات",
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    // New Cleaning Tax Methods
    private async Task GenerateAnnualCleaningTaxReport()
    {
        try
        {
            var currentYear = DateTime.Now.Year;
            var properties = await DatabaseService.Instance.GetPropertiesAsync();
            var payments = await DatabaseService.Instance.GetPaymentsAsync();
            var yearPayments = payments.Where(p => p.PaymentDate?.Year == currentYear && p.CleaningTaxAmount > 0).ToList();

            var report = $"تقرير ضريبة النظافة السنوي - {currentYear}\n";
            report += $"تاريخ التقرير: {DateTime.Now:dd/MM/yyyy}\n";
            report += $"الموعد النهائي: 31/05/{currentYear}\n\n";

            report += "=== ملخص عام ===\n";
            report += $"إجمالي المحلات: {properties.Count}\n";
            report += $"إجمالي المدفوع: {yearPayments.Where(p => p.Status == PaymentStatus.Paid).Sum(p => p.CleaningTaxAmount):N0} درهم\n";
            report += $"إجمالي المطلوب: {properties.Sum(p => p.MonthlyRent * 12 * 0.105m):N0} درهم\n";
            report += $"عدد الدفعات: {yearPayments.Count(p => p.Status == PaymentStatus.Paid)}\n\n";

            report += "=== تفاصيل المحلات ===\n";
            foreach (var property in properties)
            {
                var propertyPayments = yearPayments.Where(p => p.PropertyId == property.Id).ToList();
                var totalPaid = propertyPayments.Where(p => p.Status == PaymentStatus.Paid).Sum(p => p.CleaningTaxAmount);
                var annualTax = property.MonthlyRent * 12 * 0.105m;
                var remaining = annualTax - totalPaid;

                report += $"\n🏢 {property.Name}\n";
                report += $"   المطلوب: {annualTax:N0} درهم\n";
                report += $"   المدفوع: {totalPaid:N0} درهم\n";
                report += $"   المتبقي: {remaining:N0} درهم\n";
                report += $"   الحالة: {(remaining <= 0 ? "مكتمل" : "غير مكتمل")}\n";
            }

            // Show simple report for now
            MessageBox.Show(report, "تقرير ضريبة النظافة السنوي",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إنشاء التقرير السنوي: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task PrintCleaningTaxReceipts()
    {
        try
        {
            var properties = await DatabaseService.Instance.GetPropertiesAsync();
            var payments = await DatabaseService.Instance.GetPaymentsAsync();
            var currentYear = DateTime.Now.Year;

            var propertiesWithPayments = properties.Where(p =>
                payments.Any(pay => pay.PropertyId == p.Id &&
                           pay.PaymentDate?.Year == currentYear &&
                           pay.CleaningTaxAmount > 0 &&
                           pay.Status == PaymentStatus.Paid)).ToList();

            if (propertiesWithPayments.Count == 0)
            {
                MessageBox.Show("لا توجد دفعات ضريبة نظافة مدفوعة لهذا العام", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // Show simple list for now
            var receiptsList = "وصولات ضريبة النظافة المتاحة للطباعة:\n\n";
            foreach (var property in propertiesWithPayments)
            {
                var propertyPayments = payments.Where(p => p.PropertyId == property.Id &&
                                                          p.PaymentDate?.Year == currentYear &&
                                                          p.CleaningTaxAmount > 0 &&
                                                          p.Status == PaymentStatus.Paid).Count();
                receiptsList += $"🏢 {property.Name} - {propertyPayments} وصل\n";
            }

            MessageBox.Show(receiptsList, "وصولات ضريبة النظافة",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في طباعة الوصولات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task PrintCleaningTaxReceiptForProperty(Property property)
    {
        try
        {
            var payments = await DatabaseService.Instance.GetPaymentsAsync();
            var currentYear = DateTime.Now.Year;
            var propertyPayments = payments.Where(p => p.PropertyId == property.Id &&
                                                      p.PaymentDate?.Year == currentYear &&
                                                      p.CleaningTaxAmount > 0 &&
                                                      p.Status == PaymentStatus.Paid).ToList();

            if (propertyPayments.Count == 0)
            {
                MessageBox.Show($"لا توجد دفعات ضريبة نظافة مدفوعة للمحل {property.Name} في عام {currentYear}", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // Show receipt selection dialog
            var dialog = new Windows.CleaningTaxReceiptSelectionDialog(property, propertyPayments);
            dialog.Owner = this;
            if (dialog.ShowDialog() == true && dialog.SelectedPayment != null)
            {
                await PrintSingleCleaningTaxReceipt(property, dialog.SelectedPayment);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في طباعة وصل ضريبة النظافة: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task PrintSingleCleaningTaxReceipt(Property property, Payment payment)
    {
        try
        {
            var customer = await DatabaseService.Instance.GetCustomerAsync(property.CustomerId ?? 0);

            // Create CleaningTaxReceipt object
            var receipt = new Models.CleaningTaxReceipt
            {
                ReceiptNumber = $"CT{payment.PaymentDate?.Year}-{payment.Id:D4}",
                PropertyId = property.Id,
                Property = property,
                PaymentId = payment.Id,
                Payment = payment,
                Amount = payment.CleaningTaxAmount,
                PaymentDate = payment.PaymentDate ?? DateTime.Now,
                TaxYear = payment.PaymentDate?.Year ?? DateTime.Now.Year,
                PaymentMethod = payment.PaymentMethod ?? "نقداً",
                Notes = payment.Notes ?? "",
                CreatedDate = DateTime.Now,
                CreatedBy = "النظام"
            };

            // Show print window
            var printWindow = new Windows.PrintCleaningTaxReceiptWindow(receipt);
            printWindow.Owner = this;
            printWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في طباعة الوصل: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private string NumberToArabicWords(decimal number)
    {
        // Simple implementation - can be enhanced
        var intPart = (int)number;
        var decimalPart = (int)((number - intPart) * 100);

        if (decimalPart > 0)
        {
            return $"{ConvertNumberToWords(intPart)} درهم و {ConvertNumberToWords(decimalPart)} سنت";
        }
        else
        {
            return $"{ConvertNumberToWords(intPart)} درهم";
        }
    }

    private string ConvertNumberToWords(int number)
    {
        if (number == 0) return "صفر";

        var ones = new[] { "", "واحد", "اثنان", "ثلاثة", "أربعة", "خمسة", "ستة", "سبعة", "ثمانية", "تسعة" };
        var tens = new[] { "", "", "عشرون", "ثلاثون", "أربعون", "خمسون", "ستون", "سبعون", "ثمانون", "تسعون" };
        var teens = new[] { "عشرة", "أحد عشر", "اثنا عشر", "ثلاثة عشر", "أربعة عشر", "خمسة عشر", "ستة عشر", "سبعة عشر", "ثمانية عشر", "تسعة عشر" };

        if (number < 10) return ones[number];
        if (number < 20) return teens[number - 10];
        if (number < 100) return tens[number / 10] + (number % 10 > 0 ? " " + ones[number % 10] : "");
        if (number < 1000) return ones[number / 100] + " مائة" + (number % 100 > 0 ? " " + ConvertNumberToWords(number % 100) : "");

        return number.ToString(); // Fallback for larger numbers
    }

    private async Task ShowPayments()
    {
        try
        {
            var paymentsWindow = new Windows.PaymentsManagementWindow();
            paymentsWindow.Owner = this;
            paymentsWindow.Show();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح نافذة الأداءات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }



    private async Task AddCustomer()
    {
        try
        {
            var dialog = new Windows.CustomerDialog();
            dialog.Owner = this;
            dialog.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إضافة مكتري: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task EditCustomer(Customer customer)
    {
        try
        {
            var dialog = new Windows.CustomerDialog(customer);
            dialog.Owner = this;
            dialog.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تعديل المكتري: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task DeleteCustomer(Customer customer)
    {
        try
        {
            var result = MessageBox.Show($"هل أنت متأكد من حذف المكتري: {customer.FirstName} {customer.LastName}؟",
                "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                await DatabaseService.Instance.DeleteCustomerAsync(customer.Id);
                MessageBox.Show("تم حذف المكتري بنجاح", "نجح",
                    MessageBoxButton.OK, MessageBoxImage.Information);
                await ShowCustomers(); // Refresh
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في حذف المكتري: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task EditProperty(Property property)
    {
        try
        {
            var dialog = new Windows.PropertyDialog(property);
            dialog.Owner = this;
            if (dialog.ShowDialog() == true)
            {
                await ShowProperties(); // Refresh
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تعديل المحل: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task DeleteProperty(Property property)
    {
        try
        {
            var result = MessageBox.Show($"هل أنت متأكد من حذف المحل: {property.Name}؟",
                "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                await DatabaseService.Instance.DeletePropertyAsync(property.Id);
                MessageBox.Show("تم حذف المحل بنجاح", "نجح",
                    MessageBoxButton.OK, MessageBoxImage.Information);
                await ShowProperties(); // Refresh
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في حذف المحل: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    // Data Management Methods
    private void OpenDataManagement()
    {
        try
        {
            var dataManagementWindow = new Views.DataManagementWindow();
            dataManagementWindow.Owner = this;
            dataManagementWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح إدارة البيانات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    #region Year Generation Helper Methods
    /// <summary>
    /// This method creates a future-proof year list that automatically adapts to the current year
    /// </summary>
    /// <param name="startYear">Starting year (e.g., 2020 - when the system started)</param>
    /// <param name="futureYears">Number of future years to include (e.g., 5 years ahead)</param>
    /// <param name="descending">Whether to sort in descending order (true = newest first, better UX)</param>
    /// <returns>List of years ready for ComboBox binding</returns>
    private List<int> GenerateYearsList(int startYear = 2020, int futureYears = 5, bool descending = true)
    {
        try
        {
            var currentYear = DateTime.Now.Year;
            var endYear = currentYear + futureYears;
            var years = new List<int>();

            // Validate inputs
            if (startYear > currentYear + futureYears)
            {
                System.Diagnostics.Debug.WriteLine($"Warning: Start year {startYear} is beyond end year {endYear}, using current year");
                startYear = currentYear;
            }

            if (futureYears < 0)
            {
                System.Diagnostics.Debug.WriteLine($"Warning: Future years {futureYears} is negative, using 0");
                futureYears = 0;
            }

            // Generate years from start to end
            for (int year = startYear; year <= endYear; year++)
            {
                years.Add(year);
            }

            // Sort based on preference
            if (descending)
            {
                years.Sort((x, y) => y.CompareTo(x)); // Descending order (2025, 2024, 2023, ...)
            }
            else
            {
                years.Sort(); // Ascending order (2020, 2021, 2022, ...)
            }

            // Log generation info for debugging
            System.Diagnostics.Debug.WriteLine($"MainWindow - Generated years from {startYear} to {endYear}, " +
                $"Current: {currentYear}, Total: {years.Count}, Order: {(descending ? "Descending" : "Ascending")}");

            return years;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error generating years list: {ex.Message}");
            // Return fallback years in case of error
            var currentYear = DateTime.Now.Year;
            var fallbackYears = new List<int> { currentYear + 1, currentYear, currentYear - 1, currentYear - 2 };
            return fallbackYears;
        }
    }
    #endregion
}
