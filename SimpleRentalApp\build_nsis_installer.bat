@echo off
chcp 65001 > nul
echo ========================================
echo    بناء ملف التثبيت NSIS
echo    Building NSIS Installer
echo ========================================
echo.

echo [1/5] التحقق من وجود NSIS...
echo [1/5] Checking for NSIS...

set "NSIS_PATH="
if exist "%ProgramFiles%\NSIS\makensis.exe" (
    set "NSIS_PATH=%ProgramFiles%\NSIS\makensis.exe"
) else if exist "%ProgramFiles(x86)%\NSIS\makensis.exe" (
    set "NSIS_PATH=%ProgramFiles(x86)%\NSIS\makensis.exe"
) else (
    echo خطأ: لم يتم العثور على NSIS
    echo Error: NSIS not found
    echo.
    echo يرجى تحميل وتثبيت NSIS من:
    echo Please download and install NSIS from:
    echo https://nsis.sourceforge.io/Download
    echo.
    echo تأكد من تثبيت الإصدار الذي يدعم Unicode
    echo Make sure to install the Unicode version
    echo.
    pause
    exit /b 1
)

echo تم العثور على NSIS في: %NSIS_PATH%
echo Found NSIS at: %NSIS_PATH%
echo.

echo [2/5] التحقق من الملفات المطلوبة...
echo [2/5] Checking required files...

if not exist "RentalManagement_Installer.nsi" (
    echo خطأ: ملف NSIS غير موجود
    echo Error: NSIS script file not found
    echo المطلوب: RentalManagement_Installer.nsi
    echo Required: RentalManagement_Installer.nsi
    pause
    exit /b 1
)

if not exist "dist_new\SimpleRentalApp.exe" (
    echo خطأ: ملفات التطبيق غير موجودة
    echo Error: Application files not found
    echo.
    echo يرجى تشغيل أولاً:
    echo Please run first:
    echo powershell -ExecutionPolicy Bypass -File simple_redistribute.ps1
    echo.
    pause
    exit /b 1
)

if not exist "app_icon.ico" (
    echo تحذير: ملف الأيقونة غير موجود
    echo Warning: Icon file not found
    echo سيتم استخدام الأيقونة الافتراضية
    echo Default icon will be used
)

echo جميع الملفات المطلوبة موجودة
echo All required files found
echo.

echo [3/5] بناء ملف التثبيت...
echo [3/5] Building installer...

"%NSIS_PATH%" RentalManagement_Installer.nsi

if errorlevel 1 (
    echo خطأ في بناء ملف التثبيت
    echo Error building installer
    pause
    exit /b 1
)

echo [4/5] التحقق من ملف التثبيت المنشأ...
echo [4/5] Verifying created installer...

if exist "RentalManagement_Setup_v1.0.exe" (
    echo ========================================
    echo تم إنشاء ملف التثبيت بنجاح!
    echo Installer created successfully!
    echo.
    echo اسم الملف: RentalManagement_Setup_v1.0.exe
    echo File name: RentalManagement_Setup_v1.0.exe
    echo.
    echo حجم الملف:
    echo File size:
    for %%A in ("RentalManagement_Setup_v1.0.exe") do echo %%~zA bytes
    echo ========================================
    echo.
    echo الملف جاهز للتوزيع!
    echo File ready for distribution!
) else (
    echo خطأ: لم يتم إنشاء ملف التثبيت
    echo Error: Installer file was not created
    pause
    exit /b 1
)

echo [5/5] إنشاء معلومات التثبيت...
echo [5/5] Creating installation info...

echo نظام تدبير الكراء - معلومات ملف التثبيت > INSTALLER_INFO.txt
echo ============================================== >> INSTALLER_INFO.txt
echo. >> INSTALLER_INFO.txt
echo اسم الملف: RentalManagement_Setup_v1.0.exe >> INSTALLER_INFO.txt
echo الإصدار: 1.0.0 >> INSTALLER_INFO.txt
echo المطور: حفيظ عبدو >> INSTALLER_INFO.txt
echo تاريخ الإنشاء: %date% %time% >> INSTALLER_INFO.txt
echo. >> INSTALLER_INFO.txt
echo الميزات: >> INSTALLER_INFO.txt
echo - تثبيت تلقائي مع واجهة عربية >> INSTALLER_INFO.txt
echo - إنشاء اختصارات في قائمة ابدأ وسطح المكتب >> INSTALLER_INFO.txt
echo - تسجيل البرنامج في النظام >> INSTALLER_INFO.txt
echo - أداة إلغاء تثبيت >> INSTALLER_INFO.txt
echo - دعم كامل للغة العربية >> INSTALLER_INFO.txt
echo. >> INSTALLER_INFO.txt
echo متطلبات النظام: >> INSTALLER_INFO.txt
echo - Windows 10 أو أحدث (64-bit) >> INSTALLER_INFO.txt
echo - صلاحيات المدير للتثبيت >> INSTALLER_INFO.txt
echo - 200 MB مساحة فارغة >> INSTALLER_INFO.txt
echo. >> INSTALLER_INFO.txt
echo طريقة الاستخدام: >> INSTALLER_INFO.txt
echo 1. تشغيل RentalManagement_Setup_v1.0.exe >> INSTALLER_INFO.txt
echo 2. اتباع تعليمات معالج التثبيت >> INSTALLER_INFO.txt
echo 3. تشغيل البرنامج من قائمة ابدأ أو سطح المكتب >> INSTALLER_INFO.txt

echo.
echo ملاحظات مهمة:
echo Important notes:
echo.
echo 1. تأكد من اختبار ملف التثبيت قبل التوزيع
echo    Test the installer before distribution
echo.
echo 2. ملف التثبيت يدعم اللغة العربية بالكامل
echo    The installer fully supports Arabic language
echo.
echo 3. سيتم إنشاء اختصارات باللغة العربية
echo    Arabic shortcuts will be created
echo.
echo 4. يمكن إلغاء التثبيت من لوحة التحكم
echo    Can be uninstalled from Control Panel
echo.
pause
