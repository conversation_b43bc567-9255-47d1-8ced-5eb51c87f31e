using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SimpleRentalApp.Models;

namespace SimpleRentalApp.Services
{
    public class CleaningTaxCalculationService
    {
        private readonly DatabaseService _databaseService;

        public CleaningTaxCalculationService()
        {
            _databaseService = DatabaseService.Instance;
        }

        /// <summary>
        /// حساب المبلغ المتبقي لدفعة معينة بناءً على الدفعات السابقة فقط
        /// </summary>
        /// <param name="propertyId">معرف المحل</param>
        /// <param name="taxYear">السنة الضريبية</param>
        /// <param name="currentPaymentDate">تاريخ الدفعة الحالية</param>
        /// <param name="currentPaymentId">معرف الدفعة الحالية (اختياري لاستبعادها من الحساب)</param>
        /// <returns>المبلغ المتبقي قبل الدفعة الحالية</returns>
        public async Task<decimal> CalculateRemainingAmountBeforePaymentAsync(
            int propertyId, 
            int taxYear, 
            DateTime currentPaymentDate, 
            int? currentPaymentId = null)
        {
            try
            {
                // الحصول على المحل لحساب الضريبة السنوية
                var property = await _databaseService.GetPropertyByIdAsync(propertyId);
                if (property == null)
                    return 0;

                // حساب الضريبة السنوية الإجمالية
                var annualTax = property.MonthlyRent * 12 * 0.105m;

                // الحصول على جميع دفعات ضريبة النظافة للمحل في السنة المحددة
                var allPayments = await _databaseService.GetPaymentsAsync();
                var propertyPayments = allPayments
                    .Where(p => p.PropertyId == propertyId && 
                               p.PaymentDate?.Year == taxYear && 
                               p.CleaningTaxAmount > 0 &&
                               p.Status == PaymentStatus.Paid)
                    .ToList();

                // ترتيب الدفعات حسب التاريخ ثم حسب المعرف
                var sortedPayments = propertyPayments
                    .OrderBy(p => p.PaymentDate)
                    .ThenBy(p => p.Id)
                    .ToList();

                // حساب مجموع الدفعات السابقة فقط (قبل الدفعة الحالية)
                decimal previousPaymentsTotal = 0;

                foreach (var payment in sortedPayments)
                {
                    // إذا وصلنا للدفعة الحالية، نتوقف
                    if (currentPaymentId.HasValue && payment.Id == currentPaymentId.Value)
                        break;

                    // إذا كانت الدفعة قبل التاريخ الحالي، نضيفها
                    if (payment.PaymentDate < currentPaymentDate || 
                        (payment.PaymentDate == currentPaymentDate && 
                         (!currentPaymentId.HasValue || payment.Id < currentPaymentId.Value)))
                    {
                        previousPaymentsTotal += payment.CleaningTaxAmount;
                    }
                }

                // حساب المبلغ المتبقي
                var remainingAmount = annualTax - previousPaymentsTotal;
                return Math.Max(0, remainingAmount);
            }
            catch (Exception)
            {
                return 0;
            }
        }

        /// <summary>
        /// حساب المبلغ المتبقي بعد دفعة معينة
        /// </summary>
        public async Task<decimal> CalculateRemainingAmountAfterPaymentAsync(
            int propertyId, 
            int taxYear, 
            DateTime currentPaymentDate, 
            decimal currentPaymentAmount,
            int? currentPaymentId = null)
        {
            var remainingBefore = await CalculateRemainingAmountBeforePaymentAsync(
                propertyId, taxYear, currentPaymentDate, currentPaymentId);
            
            var remainingAfter = remainingBefore - currentPaymentAmount;
            return Math.Max(0, remainingAfter);
        }

        /// <summary>
        /// الحصول على تفاصيل حالة الدفع لمحل في سنة معينة
        /// </summary>
        public async Task<CleaningTaxPaymentStatus> GetPaymentStatusAsync(int propertyId, int taxYear)
        {
            try
            {
                var property = await _databaseService.GetPropertyByIdAsync(propertyId);
                if (property == null)
                    return new CleaningTaxPaymentStatus();

                var annualTax = property.MonthlyRent * 12 * 0.105m;

                var allPayments = await _databaseService.GetPaymentsAsync();
                var propertyPayments = allPayments
                    .Where(p => p.PropertyId == propertyId && 
                               p.PaymentDate?.Year == taxYear && 
                               p.CleaningTaxAmount > 0 &&
                               p.Status == PaymentStatus.Paid)
                    .OrderBy(p => p.PaymentDate)
                    .ThenBy(p => p.Id)
                    .ToList();

                var totalPaid = propertyPayments.Sum(p => p.CleaningTaxAmount);
                var remainingAmount = Math.Max(0, annualTax - totalPaid);

                return new CleaningTaxPaymentStatus
                {
                    PropertyId = propertyId,
                    TaxYear = taxYear,
                    AnnualTax = annualTax,
                    TotalPaid = totalPaid,
                    RemainingAmount = remainingAmount,
                    PaymentCount = propertyPayments.Count,
                    IsFullyPaid = remainingAmount == 0,
                    LastPaymentDate = propertyPayments.LastOrDefault()?.PaymentDate
                };
            }
            catch (Exception)
            {
                return new CleaningTaxPaymentStatus();
            }
        }

        /// <summary>
        /// إنشاء بيانات الوصل مع الحساب التدريجي الصحيح
        /// </summary>
        public async Task<CleaningTaxReceiptData> CreateReceiptDataAsync(Payment payment)
        {
            try
            {
                if (payment.PaymentDate == null)
                    throw new ArgumentException("تاريخ الدفع مطلوب");

                var property = await _databaseService.GetPropertyByIdAsync(payment.PropertyId);
                var customer = property?.CustomerId.HasValue == true ? 
                    await _databaseService.GetCustomerByIdAsync(property.CustomerId.Value) : null;

                var taxYear = payment.PaymentDate.Value.Year;
                
                // حساب المبلغ المتبقي قبل هذه الدفعة
                var remainingBeforePayment = await CalculateRemainingAmountBeforePaymentAsync(
                    payment.PropertyId, taxYear, payment.PaymentDate.Value, payment.Id);

                // حساب المبلغ المتبقي بعد هذه الدفعة
                var remainingAfterPayment = await CalculateRemainingAmountAfterPaymentAsync(
                    payment.PropertyId, taxYear, payment.PaymentDate.Value, 
                    payment.CleaningTaxAmount, payment.Id);

                // حساب الضريبة السنوية
                var annualTax = property?.MonthlyRent * 12 * 0.105m ?? 0;

                return new CleaningTaxReceiptData
                {
                    ReceiptNumber = !string.IsNullOrEmpty(payment.ReceiptNumber) ? payment.ReceiptNumber : $"TSC{taxYear}-{payment.Id:D3}",
                    PropertyId = payment.PropertyId,
                    PropertyName = property?.Name ?? "غير محدد",
                    PropertyAddress = property?.Address ?? "غير محدد",
                    CustomerName = customer != null ? $"{customer.FirstName} {customer.LastName}" : "غير محدد",
                    PaymentDate = payment.PaymentDate.Value,
                    TaxYear = taxYear,
                    PaymentAmount = payment.CleaningTaxAmount,
                    PaymentMethod = payment.PaymentMethod ?? "نقداً",
                    Notes = payment.Notes ?? "",
                    AnnualTax = annualTax,
                    RemainingAmountBeforePayment = remainingBeforePayment,
                    RemainingAmountAfterPayment = remainingAfterPayment,
                    PaymentSequence = await GetPaymentSequenceAsync(payment)
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء بيانات الوصل: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على ترتيب الدفعة ضمن دفعات السنة
        /// </summary>
        private async Task<int> GetPaymentSequenceAsync(Payment payment)
        {
            try
            {
                if (payment.PaymentDate == null)
                    return 1;

                var allPayments = await _databaseService.GetPaymentsAsync();
                var propertyPayments = allPayments
                    .Where(p => p.PropertyId == payment.PropertyId && 
                               p.PaymentDate?.Year == payment.PaymentDate.Value.Year && 
                               p.CleaningTaxAmount > 0 &&
                               p.Status == PaymentStatus.Paid)
                    .OrderBy(p => p.PaymentDate)
                    .ThenBy(p => p.Id)
                    .ToList();

                var sequence = propertyPayments.FindIndex(p => p.Id == payment.Id) + 1;
                return sequence > 0 ? sequence : 1;
            }
            catch
            {
                return 1;
            }
        }
    }

    /// <summary>
    /// حالة دفع ضريبة النظافة لمحل في سنة معينة
    /// </summary>
    public class CleaningTaxPaymentStatus
    {
        public int PropertyId { get; set; }
        public int TaxYear { get; set; }
        public decimal AnnualTax { get; set; }
        public decimal TotalPaid { get; set; }
        public decimal RemainingAmount { get; set; }
        public int PaymentCount { get; set; }
        public bool IsFullyPaid { get; set; }
        public DateTime? LastPaymentDate { get; set; }
    }

    /// <summary>
    /// بيانات وصل ضريبة النظافة مع الحساب التدريجي
    /// </summary>
    public class CleaningTaxReceiptData
    {
        public string ReceiptNumber { get; set; } = string.Empty;
        public int PropertyId { get; set; }
        public string PropertyName { get; set; } = string.Empty;
        public string PropertyAddress { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public DateTime PaymentDate { get; set; }
        public int TaxYear { get; set; }
        public decimal PaymentAmount { get; set; }
        public string PaymentMethod { get; set; } = string.Empty;
        public string Notes { get; set; } = string.Empty;
        public decimal AnnualTax { get; set; }
        public decimal RemainingAmountBeforePayment { get; set; }
        public decimal RemainingAmountAfterPayment { get; set; }
        public int PaymentSequence { get; set; }

        // Display properties
        public string PaymentDateDisplay => PaymentDate.ToString("dd/MM/yyyy");
        public string PaymentAmountDisplay => $"{PaymentAmount:N0} درهم";
        public string PaymentAmountInWords => Utils.NumberToArabicWords.ConvertCurrencyToWords(PaymentAmount);
        public string AnnualTaxDisplay => $"{AnnualTax:N0} درهم";
        public string RemainingBeforeDisplay => $"{RemainingAmountBeforePayment:N0} درهم";
        public string RemainingAfterDisplay => $"{RemainingAmountAfterPayment:N0} درهم";
        public string PaymentStatusText => GetPaymentStatusText();

        private string GetPaymentStatusText()
        {
            if (RemainingAmountAfterPayment == 0)
                return "تم دفع ضريبة النظافة بالكامل";
            else
                return $"المبلغ المتبقي: {RemainingAmountAfterPayment:N0} درهم";
        }
    }
}
