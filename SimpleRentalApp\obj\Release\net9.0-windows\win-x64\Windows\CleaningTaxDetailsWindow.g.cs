﻿#pragma checksum "..\..\..\..\..\Windows\CleaningTaxDetailsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "E35516DC9905FE60FCB30B2FE6676C650C25D4AA"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleRentalApp.Windows {
    
    
    /// <summary>
    /// CleaningTaxDetailsWindow
    /// </summary>
    public partial class CleaningTaxDetailsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 137 "..\..\..\..\..\Windows\CleaningTaxDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PropertyNameText;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\..\Windows\CleaningTaxDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CustomerNameText;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\..\Windows\CleaningTaxDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock YearText;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\..\Windows\CleaningTaxDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AnnualTaxText;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\..\Windows\CleaningTaxDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaymentOptionText;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\..\Windows\CleaningTaxDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaidAmountText;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\..\Windows\CleaningTaxDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RemainingAmountText;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\..\Windows\CleaningTaxDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddPaymentButton;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\..\..\Windows\CleaningTaxDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditPaymentButton;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\..\..\Windows\CleaningTaxDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeletePaymentButton;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\..\..\Windows\CleaningTaxDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\..\..\Windows\CleaningTaxDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid PaymentsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 222 "..\..\..\..\..\Windows\CleaningTaxDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 229 "..\..\..\..\..\Windows\CleaningTaxDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleRentalApp;V1.0.0.0;component/windows/cleaningtaxdetailswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\CleaningTaxDetailsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.PropertyNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.CustomerNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.YearText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.AnnualTaxText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.PaymentOptionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.PaidAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.RemainingAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.AddPaymentButton = ((System.Windows.Controls.Button)(target));
            
            #line 163 "..\..\..\..\..\Windows\CleaningTaxDetailsWindow.xaml"
            this.AddPaymentButton.Click += new System.Windows.RoutedEventHandler(this.AddPaymentButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.EditPaymentButton = ((System.Windows.Controls.Button)(target));
            
            #line 167 "..\..\..\..\..\Windows\CleaningTaxDetailsWindow.xaml"
            this.EditPaymentButton.Click += new System.Windows.RoutedEventHandler(this.EditPaymentButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.DeletePaymentButton = ((System.Windows.Controls.Button)(target));
            
            #line 172 "..\..\..\..\..\Windows\CleaningTaxDetailsWindow.xaml"
            this.DeletePaymentButton.Click += new System.Windows.RoutedEventHandler(this.DeletePaymentButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 177 "..\..\..\..\..\Windows\CleaningTaxDetailsWindow.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.PaymentsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 188 "..\..\..\..\..\Windows\CleaningTaxDetailsWindow.xaml"
            this.PaymentsDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PaymentsDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 13:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 233 "..\..\..\..\..\Windows\CleaningTaxDetailsWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

