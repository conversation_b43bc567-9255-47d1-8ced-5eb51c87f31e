<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Color Resources -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="#1976D2"/>
    <SolidColorBrush x:Key="AccentBrush" Color="#FF4081"/>
    <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="BackgroundBrush" Color="#F8F9FA"/>
    <SolidColorBrush x:Key="OnSurfaceBrush" Color="#212121"/>
    <SolidColorBrush x:Key="OnSurfaceVariantBrush" Color="#757575"/>
    <SolidColorBrush x:Key="OutlineBrush" Color="#E0E0E0"/>
    
    <!-- Success Colors -->
    <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
    <SolidColorBrush x:Key="SuccessLightBrush" Color="#E8F5E8"/>
    
    <!-- Warning Colors -->
    <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
    <SolidColorBrush x:Key="WarningLightBrush" Color="#FFF3E0"/>
    
    <!-- Error Colors -->
    <SolidColorBrush x:Key="ErrorBrush" Color="#F44336"/>
    <SolidColorBrush x:Key="ErrorLightBrush" Color="#FFEBEE"/>

    <!-- Modern Button Style -->
    <Style x:Key="PrimaryButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="24,12"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}" 
                            CornerRadius="8"
                            Padding="{TemplateBinding Padding}">
                        <Border.Effect>
                            <DropShadowEffect Color="#E0E0E0" 
                                            Direction="270" 
                                            ShadowDepth="2" 
                                            BlurRadius="8" 
                                            Opacity="0.3"/>
                        </Border.Effect>
                        <ContentPresenter HorizontalAlignment="Center" 
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource PrimaryDarkBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#1565C0"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" Value="#BDBDBD"/>
                            <Setter Property="Foreground" Value="#757575"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Secondary Button Style -->
    <Style x:Key="SecondaryButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="Padding" Value="24,12"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}" 
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center" 
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#E3F2FD"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#BBDEFB"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Modern TextBox Style -->
    <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource OnSurfaceBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource OutlineBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="16,12"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8"
                            Padding="{TemplateBinding Padding}">
                        <ScrollViewer x:Name="PART_ContentHost"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                            <Setter Property="BorderThickness" Value="2"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="BorderBrush" Value="{StaticResource PrimaryDarkBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Modern ComboBox Style -->
    <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource OnSurfaceBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource OutlineBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="16,12"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Height" Value="48"/>
    </Style>

    <!-- Card Style -->
    <Style x:Key="ModernCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="CornerRadius" Value="12"/>
        <Setter Property="Padding" Value="24"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="#E0E0E0" 
                                Direction="270" 
                                ShadowDepth="4" 
                                BlurRadius="12" 
                                Opacity="0.3"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Header Text Style -->
    <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="28"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{StaticResource OnSurfaceBrush}"/>
        <Setter Property="Margin" Value="0,0,0,16"/>
    </Style>

    <!-- Subheader Text Style -->
    <Style x:Key="SubheaderTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="20"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Foreground" Value="{StaticResource OnSurfaceBrush}"/>
        <Setter Property="Margin" Value="0,0,0,12"/>
    </Style>

    <!-- Body Text Style -->
    <Style x:Key="BodyTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="{StaticResource OnSurfaceBrush}"/>
        <Setter Property="LineHeight" Value="20"/>
    </Style>

    <!-- Caption Text Style -->
    <Style x:Key="CaptionTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Foreground" Value="{StaticResource OnSurfaceVariantBrush}"/>
    </Style>

</ResourceDictionary>
