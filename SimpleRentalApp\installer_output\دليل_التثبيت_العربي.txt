دليل إنشاء ملف التثبيت العربي لنظام تدبير الكراء
===============================================

هذا الدليل يوضح كيفية إنشاء ملف تثبيت يدعم اللغة العربية بشكل كامل.

المتطلبات:
==========
1. برنامج NSIS (Nullsoft Scriptable Install System)
   - تحميل من: https://nsis.sourceforge.io/Download
   - يجب تثبيت الإصدار الذي يدعم Unicode

2. الملفات المطلوبة:
   - installer_arabic.nsi (ملف NSIS العربي)
   - README_Arabic.txt (دليل المستخدم بالعربية)
   - LICENSE_Arabic.txt (اتفاقية الترخيص بالعربية)
   - مجلد app\ (يحتوي على ملفات التطبيق)

خطوات إنشاء ملف التثبيت:
========================

الطريقة الأولى: استخدام ملف Batch التلقائي
------------------------------------------
1. تشغيل ملف build_arabic_installer.bat
2. سيتم فحص جميع المتطلبات تلقائياً
3. سيتم إنشاء ملف "تثبيت_نظام_تدبير_الكراء.exe"

الطريقة الثانية: استخدام NSIS يدوياً
------------------------------------
1. فتح برنامج NSIS
2. سحب ملف installer_arabic.nsi إلى نافذة NSIS
3. الضغط على "Compile NSI"
4. انتظار اكتمال عملية البناء

الطريقة الثالثة: استخدام سطر الأوامر
-----------------------------------
1. فتح Command Prompt أو PowerShell
2. الانتقال إلى مجلد installer_output
3. تشغيل الأمر:
   "C:\Program Files\NSIS\makensis.exe" installer_arabic.nsi

ميزات ملف التثبيت العربي:
=========================
✅ واجهة تثبيت باللغة العربية بالكامل
✅ نصوص عربية صحيحة (بدون رموز غريبة)
✅ دعم Unicode الكامل
✅ اختصارات بأسماء عربية
✅ رسائل خطأ باللغة العربية
✅ دليل مستخدم عربي
✅ اتفاقية ترخيص عربية

محتويات ملف التثبيت:
====================
- الملفات الأساسية للتطبيق
- اختصار في قائمة ابدأ
- اختصار على سطح المكتب (اختياري)
- دليل المستخدم العربي
- إمكانية إلغاء التثبيت

مواقع التثبيت:
===============
- البرنامج: C:\Program Files\RentalManagement\
- البيانات: %APPDATA%\TadbirAlKira\
- الاختصارات: قائمة ابدأ وسطح المكتب

استكشاف الأخطاء:
=================

خطأ: "NSIS not found"
الحل: تثبيت NSIS من الموقع الرسمي

خطأ: "رموز غريبة في النص العربي"
الحل: التأكد من:
- استخدام ترميز UTF-8
- تفعيل Unicode True في ملف NSI
- حفظ الملفات بترميز UTF-8

خطأ: "Application file not found"
الحل: التأكد من وجود ملف SimpleRentalApp.exe في مجلد app\

خطأ: "License file not found"
الحل: التأكد من وجود ملف LICENSE_Arabic.txt

نصائح مهمة:
============
1. اختبر ملف التثبيت على أجهزة مختلفة قبل التوزيع
2. تأكد من أن النصوص العربية تظهر بشكل صحيح
3. اختبر عملية إلغاء التثبيت
4. تأكد من عمل جميع الاختصارات

الدعم الفني:
============
في حالة مواجهة مشاكل في إنشاء ملف التثبيت:
1. تحقق من إصدار NSIS (يجب أن يدعم Unicode)
2. تأكد من ترميز الملفات (UTF-8)
3. راجع رسائل الخطأ في NSIS
4. تواصل مع المطور للمساعدة

المطور: حفيظ عبدو
الإصدار: 1.0.0
تاريخ التحديث: 2025
