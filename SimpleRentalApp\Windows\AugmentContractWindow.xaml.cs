using System.Windows;
using SimpleRentalApp.Models;
using SimpleRentalApp.Services;

namespace SimpleRentalApp.Windows;

public partial class AugmentContractWindow : Window
{
    private readonly Contract _contract;
    
    public AugmentContractWindow(Contract contract)
    {
        InitializeComponent();
        _contract = contract;
        LoadData();
        SetDefaultValues();
    }

    private void LoadData()
    {
        // Set contract info
        ContractInfoTextBlock.Text = $"عقد رقم: {_contract.Id} - {_contract.Customer.FullName} - {_contract.Property.Name}";
        
        // Load augment types
        AugmentTypeComboBox.ItemsSource = AugmentTypes.GetTypesList();
        AugmentTypeComboBox.SelectedIndex = 0;
    }

    private void SetDefaultValues()
    {
        ApplicationDatePicker.SelectedDate = DateTime.Today;
        BaseRentTextBox.Text = _contract.InitialRentAmount.ToString("N0");
        PercentageTextBox.Text = "0";
        CalculatedAmountTextBox.Text = "0";

        // Handle end of contract increase
        HandleAugmentTypeSelection();
    }

    private void PercentageTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
    {
        CalculateAmount();
    }

    private void CalculateAmount()
    {
        if (decimal.TryParse(PercentageTextBox.Text, out decimal percentage) && percentage >= 0)
        {
            var baseRent = _contract.InitialRentAmount;
            var calculatedAmount = baseRent * (percentage / 100);
            CalculatedAmountTextBox.Text = calculatedAmount.ToString("N0");
        }
        else
        {
            CalculatedAmountTextBox.Text = "0";
        }
    }

    private void HandleAugmentTypeSelection()
    {
        if (AugmentTypeComboBox.SelectedItem is KeyValuePair<string, string> selectedType)
        {
            if (selectedType.Key == "EndOfContractIncrease")
            {
                // Set date to contract end date and disable editing
                ApplicationDatePicker.SelectedDate = _contract.EndDate;
                ApplicationDatePicker.IsEnabled = false;
            }
            else
            {
                // Enable date editing for other types
                ApplicationDatePicker.IsEnabled = true;
                if (ApplicationDatePicker.SelectedDate == _contract.EndDate)
                {
                    ApplicationDatePicker.SelectedDate = DateTime.Today;
                }
            }
        }
    }

    private void AugmentTypeComboBox_SelectionChanged(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
    {
        HandleAugmentTypeSelection();
    }

    private async void SaveButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // Validation
            if (AugmentTypeComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار نوع الزيادة", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (!decimal.TryParse(PercentageTextBox.Text, out decimal percentage) || percentage <= 0)
            {
                MessageBox.Show("يرجى إدخال نسبة صحيحة للزيادة", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (!decimal.TryParse(CalculatedAmountTextBox.Text, out decimal calculatedAmount) || calculatedAmount <= 0)
            {
                MessageBox.Show("خطأ في حساب المبلغ. يرجى التحقق من النسبة المدخلة", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (!ApplicationDatePicker.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى اختيار تاريخ تطبيق الزيادة", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // Create augment
            var selectedType = (KeyValuePair<string, string>)AugmentTypeComboBox.SelectedItem;
            var augment = new ContractAugment
            {
                ContractId = _contract.Id,
                AugmentType = selectedType.Key,
                Percentage = percentage,
                BaseRent = _contract.InitialRentAmount,
                CalculatedAmount = calculatedAmount,
                ApplicationDate = ApplicationDatePicker.SelectedDate.Value,
                Notes = NotesTextBox.Text.Trim(),
                CreatedDate = DateTime.Now,
                IsActive = true
            };

            // Save to database
            await DatabaseService.Instance.SaveContractAugmentAsync(augment);

            MessageBox.Show("تم إضافة الزيادة بنجاح! ✅", "نجح الحفظ", 
                MessageBoxButton.OK, MessageBoxImage.Information);

            DialogResult = true;
            Close();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في حفظ الزيادة: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        DialogResult = false;
        Close();
    }
}
