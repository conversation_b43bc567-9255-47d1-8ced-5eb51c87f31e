; Rental Management System - Final NSIS Installer
; نظام تدبير الكراء - ملف التثبيت النهائي
; Developer: <PERSON><PERSON><PERSON>
; Version: 1.0.0

Unicode True

; Basic Variables
!define APPNAME "Rental Management System"
!define APPNAME_SHORT "RentalManagement"
!define COMPANYNAME "Hafi<PERSON> <PERSON><PERSON>"
!define DESCRIPTION "Property and Rental Management System"
!define VERSIONMAJOR 1
!define VERSIONMINOR 0
!define VERSIONBUILD 0
!define HELPURL "<EMAIL>"
!define UPDATEURL "https://example.com/updates"
!define ABOUTURL "https://example.com/about"

; Installation Settings
RequestExecutionLevel admin
InstallDir "$PROGRAMFILES64\${APPNAME_SHORT}"
Name "${APPNAME}"
outFile "RentalManagement_Setup_Final.exe"

; Include Required Libraries
!include LogicLib.nsh
!include MUI2.nsh
!include FileFunc.nsh

; Modern UI Settings
!define MUI_ABORTWARNING
!define MUI_ICON "app_icon.ico"
!define MUI_UNICON "app_icon.ico"
!define MUI_HEADERIMAGE
!define MUI_HEADERIMAGE_RIGHT
!define MUI_HEADERIMAGE_BITMAP "app_icon.ico"

; Welcome Page
!define MUI_WELCOMEPAGE_TITLE "Welcome to ${APPNAME} Setup"
!define MUI_WELCOMEPAGE_TEXT "This wizard will guide you through the installation of ${APPNAME}.$\r$\n$\r$\nThis is a professional rental and property management system developed by ${COMPANYNAME}.$\r$\n$\r$\nClick Next to continue."

; Directory Page
!define MUI_DIRECTORYPAGE_TEXT_TOP "${APPNAME} will be installed in the following folder.$\r$\n$\r$\nTo install in a different folder, click Browse and select another folder."

; Finish Page
!define MUI_FINISHPAGE_TITLE "${APPNAME} Installation Complete"
!define MUI_FINISHPAGE_TEXT "${APPNAME} has been successfully installed.$\r$\n$\r$\nYou can now start using the application from the Start menu or desktop shortcut.$\r$\n$\r$\nDefault login credentials:$\r$\nUsername: hafid$\r$\nPassword: hafidos159357"
!define MUI_FINISHPAGE_RUN "$INSTDIR\SimpleRentalApp.exe"
!define MUI_FINISHPAGE_RUN_TEXT "Launch ${APPNAME} now"
!define MUI_FINISHPAGE_SHOWREADME "$INSTDIR\USER_GUIDE.txt"
!define MUI_FINISHPAGE_SHOWREADME_TEXT "View User Guide"

; Installation Pages
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

; Uninstallation Pages
!define MUI_UNCONFIRMPAGE_TEXT_TOP "${APPNAME} will be uninstalled from your computer.$\r$\n$\r$\nClick Uninstall to continue."
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES

; Language
!insertmacro MUI_LANGUAGE "English"

; Verify Administrator Rights
!macro VerifyUserIsAdmin
UserInfo::GetAccountType
pop $0
${If} $0 != "admin"
    messageBox mb_iconstop "Administrator privileges are required to install this application!"
    setErrorLevel 740
    quit
${EndIf}
!macroend

; Initialization Function
function .onInit
    setShellVarContext all
    !insertmacro VerifyUserIsAdmin
functionEnd

; Main Installation Section
Section "Core Application" SecMain
    SectionIn RO
    setOutPath $INSTDIR
    
    ; Copy all application files
    File /r "dist_new\*.*"
    
    ; Create uninstaller
    WriteUninstaller "$INSTDIR\uninstall.exe"
    
    ; Register application in Windows
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_SHORT}" "DisplayName" "${APPNAME}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_SHORT}" "UninstallString" "$\"$INSTDIR\uninstall.exe$\""
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_SHORT}" "QuietUninstallString" "$\"$INSTDIR\uninstall.exe$\" /S"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_SHORT}" "InstallLocation" "$\"$INSTDIR$\""
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_SHORT}" "DisplayIcon" "$\"$INSTDIR\SimpleRentalApp.exe$\""
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_SHORT}" "Publisher" "${COMPANYNAME}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_SHORT}" "HelpLink" "${HELPURL}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_SHORT}" "URLUpdateInfo" "${UPDATEURL}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_SHORT}" "URLInfoAbout" "${ABOUTURL}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_SHORT}" "DisplayVersion" "${VERSIONMAJOR}.${VERSIONMINOR}.${VERSIONBUILD}"
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_SHORT}" "VersionMajor" ${VERSIONMAJOR}
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_SHORT}" "VersionMinor" ${VERSIONMINOR}
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_SHORT}" "NoModify" 1
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_SHORT}" "NoRepair" 1
    
    ; Calculate installation size
    ${GetSize} "$INSTDIR" "/S=0K" $0 $1 $2
    IntFmt $0 "0x%08X" $0
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_SHORT}" "EstimatedSize" "$0"
    
    ; Create data directory
    CreateDirectory "$APPDATA\TadbirAlKira"
    
    ; Create installation info file
    FileOpen $0 "$INSTDIR\installation_info.txt" w
    FileWrite $0 "${APPNAME} Installation Information$\r$\n"
    FileWrite $0 "========================================$\r$\n"
    FileWrite $0 "Version: ${VERSIONMAJOR}.${VERSIONMINOR}.${VERSIONBUILD}$\r$\n"
    FileWrite $0 "Developer: ${COMPANYNAME}$\r$\n"
    FileWrite $0 "Installation Date: $$(Date)$\r$\n"
    FileWrite $0 "Installation Path: $INSTDIR$\r$\n"
    FileWrite $0 "Data Path: $APPDATA\TadbirAlKira$\r$\n"
    FileWrite $0 "$\r$\n"
    FileWrite $0 "Default Login:$\r$\n"
    FileWrite $0 "Username: hafid$\r$\n"
    FileWrite $0 "Password: askdev <EMAIL>$\r$\n"
    FileClose $0
    
SectionEnd

; Start Menu Shortcuts Section
Section "Start Menu Shortcuts" SecStartMenu
    CreateDirectory "$SMPROGRAMS\${APPNAME}"
    CreateShortCut "$SMPROGRAMS\${APPNAME}\${APPNAME}.lnk" "$INSTDIR\SimpleRentalApp.exe" "" "$INSTDIR\SimpleRentalApp.exe" 0
    CreateShortCut "$SMPROGRAMS\${APPNAME}\Uninstall.lnk" "$INSTDIR\uninstall.exe" "" "$INSTDIR\uninstall.exe" 0
    CreateShortCut "$SMPROGRAMS\${APPNAME}\User Guide.lnk" "$INSTDIR\USER_GUIDE.txt"
    CreateShortCut "$SMPROGRAMS\${APPNAME}\Installation Info.lnk" "$INSTDIR\installation_info.txt"
SectionEnd

; Desktop Shortcut Section
Section "Desktop Shortcut" SecDesktop
    CreateShortCut "$DESKTOP\${APPNAME}.lnk" "$INSTDIR\SimpleRentalApp.exe" "" "$INSTDIR\SimpleRentalApp.exe" 0
SectionEnd

; Uninstall Section
Section "Uninstall"
    ; Remove Start Menu shortcuts
    Delete "$SMPROGRAMS\${APPNAME}\${APPNAME}.lnk"
    Delete "$SMPROGRAMS\${APPNAME}\Uninstall.lnk"
    Delete "$SMPROGRAMS\${APPNAME}\User Guide.lnk"
    Delete "$SMPROGRAMS\${APPNAME}\Installation Info.lnk"
    RmDir "$SMPROGRAMS\${APPNAME}"
    
    ; Remove desktop shortcut
    Delete "$DESKTOP\${APPNAME}.lnk"
    
    ; Remove application files
    RmDir /r "$INSTDIR"
    
    ; Remove registry entries
    DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_SHORT}"
    
    ; Ask about data removal
    MessageBox MB_YESNO|MB_ICONQUESTION "Do you want to remove all application data?$\r$\n$\r$\nThis will delete all your rental management data including:$\r$\n- Properties$\r$\n- Contracts$\r$\n- Payments$\r$\n- Reports$\r$\n$\r$\nChoose 'No' if you plan to reinstall later." IDNO skip_data_removal
    RmDir /r "$APPDATA\TadbirAlKira"
    MessageBox MB_OK "All application data has been removed."
    Goto end_uninstall
    skip_data_removal:
    MessageBox MB_OK "Application data has been preserved in:$\r$\n$APPDATA\TadbirAlKira"
    end_uninstall:
    
SectionEnd
