﻿using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using SimpleRentalApp.Services;
using SimpleRentalApp.Models;
using SimpleRentalApp.Windows;

// تم إزالة using System.Windows.Threading

namespace SimpleRentalApp;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    // تم إزالة timer حالة المزامنة

    public MainWindow()
    {
        InitializeComponent();
        UpdateUserInfo();
        LoadDashboard();
        // تم إزالة تهيئة مراقبة المزامنة
    }

    // تم إزالة دالة تهيئة مراقبة حالة المزامنة

    // تم إزالة دالة تحديث حالة المزامنة

    // تم إزالة دالة تعيين حالة المزامنة

    private void UpdateUserInfo()
    {
        if (AuthService.Instance.CurrentUser != null)
        {
            UserInfoTextBlock.Text = $"مرحباً، {AuthService.Instance.CurrentUser.FullName} ({AuthService.Instance.CurrentUser.RoleDisplay})";
        }
    }

    private void LogoutButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد الخروج",
            MessageBoxButton.YesNo, MessageBoxImage.Question);

        if (result == MessageBoxResult.Yes)
        {
            AuthService.Instance.Logout();

            var loginWindow = new LoginWindow();
            loginWindow.Show();

            this.Close();
        }
    }

    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("هل تريد إغلاق التطبيق؟", "تأكيد الإغلاق",
            MessageBoxButton.YesNo, MessageBoxImage.Question);

        if (result == MessageBoxResult.Yes)
        {
            CleanupResources();
            Application.Current.Shutdown();
        }
    }

    /// <summary>
    /// تنظيف الموارد عند إغلاق النافذة
    /// </summary>
    private void CleanupResources()
    {
        try
        {
            // تم إزالة تنظيف timer المزامنة
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تنظيف الموارد: {ex.Message}");
        }
    }

    protected override void OnClosed(EventArgs e)
    {
        CleanupResources();
        base.OnClosed(e);
    }

    private async void LoadDashboard()
    {
        try
        {
            await ShowDashboard();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل لوحة التحكم: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private FrameworkElement CreateLoadingContent()
    {
        var stackPanel = new StackPanel
        {
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center,
            Margin = new Thickness(20)
        };

        var loadingText = new TextBlock
        {
            Text = "🔄 جاري تحميل النظام...",
            FontSize = 24,
            FontWeight = FontWeights.Bold,
            HorizontalAlignment = HorizontalAlignment.Center,
            Foreground = Brushes.DarkBlue
        };

        stackPanel.Children.Add(loadingText);
        return stackPanel;
    }

    private async void DashboardBtn_Click(object sender, RoutedEventArgs e)
    {
        await ShowDashboard();
    }

    private async void CustomersBtn_Click(object sender, RoutedEventArgs e)
    {
        await ShowCustomers();
    }

    private async void PropertiesBtn_Click(object sender, RoutedEventArgs e)
    {
        await ShowProperties();
    }

    private async void PaymentsManagementBtn_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var paymentsWindow = new Windows.PaymentsManagementWindow();
            paymentsWindow.Owner = this;
            paymentsWindow.Show();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح نافذة إدارة الأداءات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    // Keep the old method for backward compatibility if needed elsewhere
    private void PaymentsBtn_Click(object sender, RoutedEventArgs e)
    {
        PaymentsManagementBtn_Click(sender, e);
    }

    private void ContractsManagementBtn_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var contractsManagementWindow = new Windows.ContractsManagementWindow();
            contractsManagementWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح نافذة إدارة العقود: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void CleaningTaxManagementBtn_Click(object sender, RoutedEventArgs e)
    {
        var window = new Windows.CleaningTaxManagementWindow();
        window.Owner = this;
        window.Show();
    }

    private void WhatsAppNotificationsBtn_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var window = new Windows.AdvancedNotificationsWindow();
            window.Owner = this;
            window.Show();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح نافذة الإشعارات المتطورة: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void ReportsBtn_Click(object sender, RoutedEventArgs e)
    {
        await ShowReports();
    }

    private void DataManagementBtn_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var dataManagementWindow = new Views.DataManagementWindow();
            dataManagementWindow.Owner = this;
            dataManagementWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"فشل في فتح نافذة إدارة البيانات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    // These methods are implemented in MainWindow_Methods.cs as partial class

    // تم إزالة دالة مزامنة البيانات





    // تم إزالة دالة إعدادات المزامنة السحابية





    private void ExitButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("هل تريد إغلاق نظام إدارة العقارات؟", "تأكيد الخروج",
            MessageBoxButton.YesNo, MessageBoxImage.Question);

        if (result == MessageBoxResult.Yes)
        {
            Application.Current.Shutdown();
        }
    }
}