﻿using System.Configuration;
using System.Data;
using System.Windows;
using System.IO;
using SimpleRentalApp.Services;
using SimpleRentalApp.Windows;

namespace SimpleRentalApp;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : System.Windows.Application
{
    protected override async void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);

        try
        {
            // تأكد من وجود مجلد البيانات
            var appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "TadbirAlKira");
            if (!Directory.Exists(appDataPath))
            {
                Directory.CreateDirectory(appDataPath);
            }

            // تهيئة قاعدة البيانات
            await DatabaseService.Instance.InitializeDatabaseAsync();

            // عرض نافذة تسجيل الدخول
            var loginWindow = new LoginWindow();
            loginWindow.Show();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في بدء التطبيق: {ex.Message}\n\nتفاصيل الخطأ: {ex.InnerException?.Message}",
                "خطأ في بدء التطبيق", MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown();
        }
    }

    protected override void OnExit(ExitEventArgs e)
    {
        // تنظيف الموارد
        DatabaseService.Instance?.Dispose();
        base.OnExit(e);
    }
}

