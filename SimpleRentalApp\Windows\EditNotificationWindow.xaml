<Window x:Class="SimpleRentalApp.Windows.EditNotificationWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="✏️ تعديل الإشعار - تدبير الكراء"
        Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        Background="#F5F5F5"
        FontFamily="Segoe UI"
        ResizeMode="CanResize">

    <Window.Resources>
        <!-- Color Resources -->
        <SolidColorBrush x:Key="PrimaryColor" Color="#2E8BC0"/>
        <SolidColorBrush x:Key="SecondaryColor" Color="#3A506B"/>
        <SolidColorBrush x:Key="AccentColor" Color="#5DADE2"/>
        <SolidColorBrush x:Key="SurfaceColor" Color="White"/>
        <SolidColorBrush x:Key="TextColor" Color="#2C3E50"/>
        <SolidColorBrush x:Key="BorderColor" Color="#E0E0E0"/>

        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryColor}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource SecondaryColor}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#6C757D"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource SurfaceColor}" CornerRadius="15" Padding="25" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="3" BlurRadius="15"/>
            </Border.Effect>
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="✏️" FontSize="32" VerticalAlignment="Center" Margin="0,0,15,0"/>
                <StackPanel>
                    <TextBlock Text="تعديل الإشعار" 
                               FontSize="24" 
                               FontWeight="Bold" 
                               Foreground="{StaticResource TextColor}"/>
                    <TextBlock Text="تعديل تفاصيل الإشعار وإعداداته" 
                               FontSize="14" 
                               Foreground="#7F8C8D" 
                               Margin="0,5,0,0"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Form Content -->
        <Border Grid.Row="1" Background="{StaticResource SurfaceColor}" CornerRadius="12" Padding="25">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="10"/>
            </Border.Effect>
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!-- Basic Info -->
                    <GroupBox Header="📋 المعلومات الأساسية" FontWeight="Bold" Padding="15" Margin="0,0,0,20">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="المكتري:" VerticalAlignment="Center" Margin="0,0,15,10"/>
                            <TextBox Grid.Row="0" Grid.Column="1" Name="CustomerNameTextBox" Margin="0,0,0,10" IsReadOnly="True" Background="#F8F9FA"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="المحل:" VerticalAlignment="Center" Margin="0,0,15,10"/>
                            <TextBox Grid.Row="1" Grid.Column="1" Name="PropertyNameTextBox" Margin="0,0,0,10" IsReadOnly="True" Background="#F8F9FA"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="النوع:" VerticalAlignment="Center" Margin="0,0,15,10"/>
                            <ComboBox Grid.Row="2" Grid.Column="1" Name="TypeComboBox" Margin="0,0,0,10"/>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="المبلغ:" VerticalAlignment="Center" Margin="0,0,15,0"/>
                            <TextBox Grid.Row="3" Grid.Column="1" Name="AmountTextBox"/>
                        </Grid>
                    </GroupBox>

                    <!-- Status and Priority -->
                    <GroupBox Header="⚙️ الحالة والأولوية" FontWeight="Bold" Padding="15" Margin="0,0,0,20">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="الحالة:" VerticalAlignment="Center" Margin="0,0,15,0"/>
                            <ComboBox Grid.Column="1" Name="StatusComboBox" Margin="0,0,20,0"/>

                            <TextBlock Grid.Column="2" Text="الأولوية:" VerticalAlignment="Center" Margin="0,0,15,0"/>
                            <ComboBox Grid.Column="3" Name="PriorityComboBox"/>
                        </Grid>
                    </GroupBox>

                    <!-- Dates -->
                    <GroupBox Header="📅 التواريخ" FontWeight="Bold" Padding="15" Margin="0,0,0,20">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="تاريخ الاستحقاق:" VerticalAlignment="Center" Margin="0,0,15,0"/>
                            <DatePicker Grid.Column="1" Name="DueDatePicker" Margin="0,0,20,0"/>

                            <TextBlock Grid.Column="2" Text="تاريخ الإرسال:" VerticalAlignment="Center" Margin="0,0,15,0"/>
                            <DatePicker Grid.Column="3" Name="SentDatePicker"/>
                        </Grid>
                    </GroupBox>

                    <!-- Notes -->
                    <GroupBox Header="📝 ملاحظات" FontWeight="Bold" Padding="15">
                        <TextBox Name="NotesTextBox" 
                                 Height="80" 
                                 TextWrapping="Wrap" 
                                 AcceptsReturn="True" 
                                 VerticalScrollBarVisibility="Auto"
                                 Text="أضف ملاحظات إضافية حول الإشعار..."/>
                    </GroupBox>
                </StackPanel>
            </ScrollViewer>
        </Border>

        <!-- Action Buttons -->
        <Border Grid.Row="2" Background="{StaticResource SurfaceColor}" CornerRadius="12" Padding="20" Margin="0,20,0,0">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="10"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="💡" FontSize="16" Margin="0,0,10,0"/>
                    <TextBlock Text="تأكد من صحة البيانات قبل الحفظ" 
                               FontSize="12" 
                               Foreground="#7F8C8D"/>
                </StackPanel>

                <Button Grid.Column="1" Content="💾 حفظ التغييرات" 
                        Style="{StaticResource ModernButtonStyle}"
                        Margin="0,0,10,0"
                        Click="SaveBtn_Click"/>

                <Button Grid.Column="2" Content="❌ إلغاء" 
                        Style="{StaticResource SecondaryButtonStyle}"
                        Click="CancelBtn_Click"/>
            </Grid>
        </Border>
    </Grid>
</Window>
