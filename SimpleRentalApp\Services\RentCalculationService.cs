using Microsoft.EntityFrameworkCore;
using SimpleRentalApp.Data;
using SimpleRentalApp.Models;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace SimpleRentalApp.Services
{
    /// <summary>
    /// خدمة حساب الإيجار الحالي والمبالغ المتعلقة بالعقود
    /// </summary>
    public class RentCalculationService
    {
        private readonly RentalDbContext _context;

        public RentCalculationService(RentalDbContext context)
        {
            _context = context;
        }

        public RentCalculationService()
        {
            _context = new RentalDbContext();
        }

        /// <summary>
        /// حساب الإيجار الحالي للعقد بناءً على آخر زيادة مسجلة
        /// </summary>
        /// <param name="contractId">معرف العقد</param>
        /// <returns>الإيجار الحالي</returns>
        public async Task<decimal> GetCurrentRentAmountAsync(int contractId)
        {
            try
            {
                var contract = await _context.Contracts
                    .Include(c => c.Property)
                    .Include(c => c.RentIncreases.OrderByDescending(r => r.IncreaseDate))
                    .FirstOrDefaultAsync(c => c.Id == contractId);

                if (contract == null)
                    return 0;

                return GetCurrentRentAmount(contract);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب الإيجار الحالي للعقد {contractId}: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// حساب الإيجار الحالي للعقد (نسخة محلية)
        /// </summary>
        /// <param name="contract">العقد مع بيانات الزيادات</param>
        /// <returns>الإيجار الحالي</returns>
        public decimal GetCurrentRentAmount(Contract contract)
        {
            try
            {
                // إذا كان الإيجار الأولي 0، حاول الحصول على القيمة من العقار
                decimal baseRent = contract.InitialRentAmount;
                if (baseRent == 0 && contract.Property != null)
                {
                    baseRent = contract.Property.MonthlyRent;
                }

                // إذا لم تكن هناك زيادات مسجلة، أرجع الإيجار الأساسي
                if (contract.RentIncreases == null || !contract.RentIncreases.Any())
                    return baseRent;

                // الحصول على آخر زيادة مسجلة (أحدث قيمة إيجار)
                var latestIncrease = contract.RentIncreases
                    .OrderByDescending(r => r.IncreaseDate)
                    .FirstOrDefault();

                if (latestIncrease != null)
                {
                    return latestIncrease.NewAmount;
                }

                return baseRent;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب الإيجار الحالي: {ex.Message}");
                return contract.InitialRentAmount;
            }
        }

        /// <summary>
        /// حساب الإيجار السنوي الحالي للعقد
        /// </summary>
        /// <param name="contractId">معرف العقد</param>
        /// <returns>الإيجار السنوي</returns>
        public async Task<decimal> GetCurrentAnnualRentAsync(int contractId)
        {
            var monthlyRent = await GetCurrentRentAmountAsync(contractId);
            return monthlyRent * 12;
        }

        /// <summary>
        /// حساب ضريبة النظافة (10.5% من الإيجار السنوي)
        /// </summary>
        /// <param name="contractId">معرف العقد</param>
        /// <returns>مبلغ ضريبة النظافة</returns>
        public async Task<decimal> GetCleaningTaxAmountAsync(int contractId)
        {
            var annualRent = await GetCurrentAnnualRentAsync(contractId);
            return Math.Round(annualRent * 0.105m, 2);
        }

        /// <summary>
        /// حساب ضريبة النظافة للعقد (نسخة محلية)
        /// </summary>
        /// <param name="contract">العقد</param>
        /// <returns>مبلغ ضريبة النظافة</returns>
        public decimal GetCleaningTaxAmount(Contract contract)
        {
            var monthlyRent = GetCurrentRentAmount(contract);
            var annualRent = monthlyRent * 12;
            return Math.Round(annualRent * 0.105m, 2);
        }

        /// <summary>
        /// الحصول على تاريخ آخر زيادة إيجار للعقد
        /// </summary>
        /// <param name="contractId">معرف العقد</param>
        /// <returns>تاريخ آخر زيادة أو null إذا لم توجد زيادات</returns>
        public async Task<DateTime?> GetLastRentIncreaseeDateAsync(int contractId)
        {
            try
            {
                var lastIncrease = await _context.RentIncreases
                    .Where(r => r.ContractId == contractId)
                    .OrderByDescending(r => r.IncreaseDate)
                    .FirstOrDefaultAsync();

                return lastIncrease?.IncreaseDate;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الحصول على تاريخ آخر زيادة للعقد {contractId}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// حساب إجمالي الزيادات على العقد
        /// </summary>
        /// <param name="contractId">معرف العقد</param>
        /// <returns>إجمالي مبلغ الزيادات</returns>
        public async Task<decimal> GetTotalRentIncreasesAsync(int contractId)
        {
            try
            {
                var contract = await _context.Contracts
                    .Include(c => c.RentIncreases)
                    .FirstOrDefaultAsync(c => c.Id == contractId);

                if (contract == null || contract.RentIncreases == null || !contract.RentIncreases.Any())
                    return 0;

                var currentRent = GetCurrentRentAmount(contract);
                var initialRent = contract.InitialRentAmount > 0 ? contract.InitialRentAmount : 
                    (contract.Property?.MonthlyRent ?? 0);

                return currentRent - initialRent;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب إجمالي الزيادات للعقد {contractId}: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// حساب نسبة الزيادة الإجمالية على العقد
        /// </summary>
        /// <param name="contractId">معرف العقد</param>
        /// <returns>نسبة الزيادة كنسبة مئوية</returns>
        public async Task<decimal> GetTotalRentIncreasePercentageAsync(int contractId)
        {
            try
            {
                var contract = await _context.Contracts
                    .Include(c => c.Property)
                    .Include(c => c.RentIncreases)
                    .FirstOrDefaultAsync(c => c.Id == contractId);

                if (contract == null)
                    return 0;

                var currentRent = GetCurrentRentAmount(contract);
                var initialRent = contract.InitialRentAmount > 0 ? contract.InitialRentAmount : 
                    (contract.Property?.MonthlyRent ?? 0);

                if (initialRent == 0)
                    return 0;

                var increaseAmount = currentRent - initialRent;
                return Math.Round((increaseAmount / initialRent) * 100, 2);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب نسبة الزيادة للعقد {contractId}: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// التحقق من صحة بيانات الإيجار للعقد
        /// </summary>
        /// <param name="contractId">معرف العقد</param>
        /// <returns>تقرير عن حالة بيانات الإيجار</returns>
        public async Task<RentValidationResult> ValidateContractRentDataAsync(int contractId)
        {
            try
            {
                var contract = await _context.Contracts
                    .Include(c => c.Property)
                    .Include(c => c.RentIncreases.OrderBy(r => r.IncreaseDate))
                    .FirstOrDefaultAsync(c => c.Id == contractId);

                var result = new RentValidationResult
                {
                    ContractId = contractId,
                    IsValid = true,
                    Issues = new List<string>()
                };

                if (contract == null)
                {
                    result.IsValid = false;
                    result.Issues.Add("العقد غير موجود");
                    return result;
                }

                // التحقق من الإيجار الأولي
                if (contract.InitialRentAmount <= 0)
                {
                    if (contract.Property?.MonthlyRent > 0)
                    {
                        result.Issues.Add("الإيجار الأولي 0 - يمكن إصلاحه من إيجار العقار");
                    }
                    else
                    {
                        result.IsValid = false;
                        result.Issues.Add("الإيجار الأولي 0 وإيجار العقار غير محدد");
                    }
                }

                // التحقق من تسلسل الزيادات
                if (contract.RentIncreases != null && contract.RentIncreases.Any())
                {
                    var increases = contract.RentIncreases.OrderBy(r => r.IncreaseDate).ToList();
                    for (int i = 0; i < increases.Count; i++)
                    {
                        var increase = increases[i];
                        
                        if (i == 0)
                        {
                            // أول زيادة يجب أن تبدأ من الإيجار الأولي
                            var expectedPrevious = contract.InitialRentAmount > 0 ? 
                                contract.InitialRentAmount : contract.Property?.MonthlyRent ?? 0;
                            
                            if (Math.Abs(increase.PreviousAmount - expectedPrevious) > 0.01m)
                            {
                                result.Issues.Add($"الزيادة الأولى: المبلغ السابق غير متطابق ({increase.PreviousAmount} vs {expectedPrevious})");
                            }
                        }
                        else
                        {
                            // الزيادات التالية يجب أن تبدأ من المبلغ الجديد للزيادة السابقة
                            var previousIncrease = increases[i - 1];
                            if (Math.Abs(increase.PreviousAmount - previousIncrease.NewAmount) > 0.01m)
                            {
                                result.Issues.Add($"الزيادة {i + 1}: المبلغ السابق غير متطابق مع الزيادة السابقة");
                            }
                        }
                    }
                }

                result.CurrentRent = GetCurrentRentAmount(contract);
                result.InitialRent = contract.InitialRentAmount;
                result.TotalIncreases = result.CurrentRent - result.InitialRent;

                if (result.Issues.Any())
                {
                    result.IsValid = false;
                }

                return result;
            }
            catch (Exception ex)
            {
                return new RentValidationResult
                {
                    ContractId = contractId,
                    IsValid = false,
                    Issues = new List<string> { $"خطأ في التحقق: {ex.Message}" }
                };
            }
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }

    /// <summary>
    /// نتيجة التحقق من صحة بيانات الإيجار
    /// </summary>
    public class RentValidationResult
    {
        public int ContractId { get; set; }
        public bool IsValid { get; set; }
        public List<string> Issues { get; set; } = new List<string>();
        public decimal CurrentRent { get; set; }
        public decimal InitialRent { get; set; }
        public decimal TotalIncreases { get; set; }
    }
}
