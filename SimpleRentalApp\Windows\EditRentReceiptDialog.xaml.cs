using SimpleRentalApp.Models;
using SimpleRentalApp.Services;
using System;
using System.Globalization;
using System.Windows;

namespace SimpleRentalApp.Windows
{
    public partial class EditRentReceiptDialog : Window
    {
        private readonly RentReceipt _receipt;
        private readonly Property _property;
        private readonly Customer? _customer;

        public EditRentReceiptDialog(RentReceipt receipt, Property property, Customer? customer)
        {
            InitializeComponent();
            _receipt = receipt;
            _property = property;
            _customer = customer;
            
            InitializeForm();
        }

        private void InitializeForm()
        {
            try
            {
                // Set property info
                PropertyInfoText.Text = $"المحل: {_property.Name} - المكتري: {_customer?.FullName ?? "غير محدد"}";

                // Load existing receipt data
                ReceiptNumberTextBox.Text = _receipt.ReceiptNumber;
                PaymentDatePicker.SelectedDate = _receipt.PaymentDate;
                AmountTextBox.Text = _receipt.Amount.ToString("0");
                NotesTextBox.Text = _receipt.Notes;
                IsPrintedCheckBox.IsChecked = _receipt.IsPrinted;

                // Set payment period dates
                PeriodStartDatePicker.SelectedDate = _receipt.PeriodStartDate;
                PeriodEndDatePicker.SelectedDate = _receipt.PeriodEndDate;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات التوصيل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string GetPaymentPeriodText()
        {
            var startDate = PeriodStartDatePicker.SelectedDate ?? DateTime.Now;
            var endDate = PeriodEndDatePicker.SelectedDate ?? DateTime.Now;

            return $"من {startDate:dd/MM/yyyy} إلى {endDate:dd/MM/yyyy}";
        }

        private async void SaveBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validate inputs
                if (!ValidateInputs())
                    return;

                // Update receipt data
                _receipt.ReceiptNumber = ReceiptNumberTextBox.Text.Trim();
                _receipt.PaymentDate = PaymentDatePicker.SelectedDate ?? DateTime.Now;
                _receipt.Amount = decimal.Parse(AmountTextBox.Text.Trim());
                _receipt.PaymentPeriod = GetPaymentPeriodText();
                _receipt.Notes = NotesTextBox.Text.Trim();
                _receipt.IsPrinted = IsPrintedCheckBox.IsChecked ?? false;

                // Update period dates
                _receipt.PeriodStartDate = PeriodStartDatePicker.SelectedDate ?? DateTime.Now;
                _receipt.PeriodEndDate = PeriodEndDatePicker.SelectedDate ?? DateTime.Now;

                // Save to database
                await DatabaseService.Instance.UpdateRentReceiptAsync(_receipt);

                MessageBox.Show("تم تحديث التوصيل بنجاح! ✅", "نجح",
                    MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث التوصيل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInputs()
        {
            // Validate receipt number
            if (string.IsNullOrWhiteSpace(ReceiptNumberTextBox.Text))
            {
                MessageBox.Show("رقم التوصيل مطلوب", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            // Validate payment date
            if (!PaymentDatePicker.SelectedDate.HasValue)
            {
                MessageBox.Show("تاريخ الدفع مطلوب", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            // Validate amount
            if (!decimal.TryParse(AmountTextBox.Text.Trim(), out decimal amount) || amount <= 0)
            {
                MessageBox.Show("يرجى إدخال مبلغ صحيح أكبر من صفر", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                AmountTextBox.Focus();
                return false;
            }

            // Validate payment period
            if (!PeriodStartDatePicker.SelectedDate.HasValue || !PeriodEndDatePicker.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى تحديد فترة الأداء (من وإلى)", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (PeriodStartDatePicker.SelectedDate > PeriodEndDatePicker.SelectedDate)
            {
                MessageBox.Show("تاريخ البداية يجب أن يكون قبل تاريخ النهاية", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }



        private void CancelBtn_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
