<Window x:Class="SimpleRentalApp.Windows.PrintCleaningTaxReceiptWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="طباعة وصل ضريبة النظافة" 
        Height="700" 
        Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">

    <Window.Resources>
        <Style TargetType="Button">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Height" Value="40"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header with buttons -->
        <Border Grid.Row="0" 
                Background="#2E7D32" 
                Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Vertical">
                    <TextBlock Text="🧹 طباعة وصل ضريبة النظافة" 
                              FontSize="18" 
                              FontWeight="Bold" 
                              Foreground="White"/>
                    <TextBlock Name="StatusText" 
                              Text="جاهز للطباعة" 
                              FontSize="12" 
                              Foreground="LightGray" 
                              Margin="0,5,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" 
                           Orientation="Horizontal" 
                           VerticalAlignment="Center">
                    <Button Name="PreviewPrintButton"
                            Content="👁️ معاينة وطباعة"
                            Width="150"
                            Background="#4CAF50"
                            Foreground="White"
                            Click="PreviewPrintButton_Click"/>

                    <Button Name="PrintButton"
                            Content="🖨️ طباعة مباشرة"
                            Width="140"
                            Background="#FF9800"
                            Foreground="White"
                            Click="PrintButton_Click"/>

                    <Button Name="SaveAsPdfButton"
                            Content="📄 حفظ PDF"
                            Width="120"
                            Background="#2196F3"
                            Foreground="White"
                            Click="SaveAsPdfButton_Click"/>

                    <Button Name="CloseButton"
                            Content="❌ إغلاق"
                            Width="100"
                            Background="#F44336"
                            Foreground="White"
                            Click="CloseButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Receipt Preview -->
        <ScrollViewer Grid.Row="1" 
                      Background="White"
                      Margin="20,20,20,20"
                      Padding="20"
                      VerticalScrollBarVisibility="Auto">
            <Border Name="ReceiptBorder"
                    Background="White"
                    BorderBrush="#E0E0E0"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="20"
                    MaxWidth="500">
                
                <StackPanel Name="ReceiptContent">
                    <!-- Header -->
                    <Border Background="#2E7D32" 
                            CornerRadius="8,8,0,0" 
                            Padding="15,10" 
                            Margin="0,0,0,15">
                        <StackPanel>
                            <TextBlock Text="🧹 وصل دفع ضريبة النظافة" 
                                      FontSize="16" 
                                      FontWeight="Bold" 
                                      Foreground="White" 
                                      TextAlignment="Center"/>
                            <TextBlock Name="ReceiptNumberText" 
                                      FontSize="12" 
                                      FontWeight="Bold" 
                                      Foreground="White" 
                                      TextAlignment="Center" 
                                      Margin="0,5,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- Customer and Property Info -->
                    <Border Background="#F8F9FA" 
                            BorderBrush="#E0E0E0" 
                            BorderThickness="1" 
                            CornerRadius="6" 
                            Padding="15" 
                            Margin="0,0,0,15">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="المكتري:" FontWeight="Bold" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Name="CustomerNameText" Margin="0,0,0,5"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="المحل:" FontWeight="Bold" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Name="PropertyNameText" Margin="0,0,0,5"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="العنوان:" FontWeight="Bold" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Name="PropertyAddressText" TextWrapping="Wrap" Margin="0,0,0,5"/>


                        </Grid>
                    </Border>

                    <!-- Payment Details -->
                    <Border Background="#E8F5E8" 
                            BorderBrush="#4CAF50" 
                            BorderThickness="1" 
                            CornerRadius="6" 
                            Padding="15" 
                            Margin="0,0,0,15">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="تاريخ الدفع:" FontWeight="Bold" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Name="PaymentDateText" Margin="0,0,0,5"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="السنة الضريبية:" FontWeight="Bold" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Name="TaxYearText" Margin="0,0,0,5"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="المبلغ:" FontWeight="Bold" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Name="AmountText" FontWeight="Bold" FontSize="14" Foreground="#2E7D32" Margin="0,0,0,5"/>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="المبلغ بالحروف:" FontWeight="Bold" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="3" Grid.Column="1" Name="AmountInWordsText" TextWrapping="Wrap" Margin="0,0,0,5"/>

                            <TextBlock Grid.Row="4" Grid.Column="0" Text="نوع الدفعة:" FontWeight="Bold" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="4" Grid.Column="1" Name="PaymentTypeText" Margin="0,0,0,5"/>

                            <TextBlock Grid.Row="5" Grid.Column="0" Text="المتبقي من السنة:" FontWeight="Bold" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="5" Grid.Column="1" Name="RemainingAmountText" Margin="0,0,0,5" Foreground="#F44336" FontWeight="Bold"/>

                            <StackPanel Grid.Row="6" Grid.Column="0" Grid.ColumnSpan="2" Name="NotesSection" Visibility="Collapsed" Margin="0,10,0,0">
                                <TextBlock Text="ملاحظات:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <Border Background="White" BorderBrush="#DDD" BorderThickness="1" Padding="8">
                                    <TextBlock Name="NotesText" TextWrapping="Wrap"/>
                                </Border>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- Legal Notice -->
                    <Border Name="LegalNoticeBorder"
                            BorderThickness="1"
                            CornerRadius="6"
                            Padding="15"
                            Margin="0,0,0,15">
                        <StackPanel>
                            <TextBlock Text="⚠️ إشعار هام" FontWeight="Bold" FontSize="12" Margin="0,0,0,5"/>
                            <TextBlock Name="PaymentStatusNotice" FontSize="10" Margin="0,0,0,3" TextWrapping="Wrap"/>
                            <TextBlock Text="الموعد النهائي لدفع ضريبة النظافة: 31 مايو من كل سنة" FontSize="10" FontWeight="Bold" Foreground="#D32F2F"/>
                        </StackPanel>
                    </Border>

                    <!-- Footer -->
                    <Border BorderBrush="#E0E0E0" 
                            BorderThickness="0,1,0,0" 
                            Padding="0,15,0,0">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="توقيع المؤجر" FontWeight="Bold" FontSize="10" TextAlignment="Center"/>
                                <Border Height="30" BorderBrush="#DDD" BorderThickness="0,0,0,1" Margin="10,10,10,0"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1">
                                <TextBlock Text="ختم المؤجر" FontSize="10" TextAlignment="Center" FontWeight="Bold"/>
                                <Border Height="30" BorderBrush="#DDD" BorderThickness="0,0,0,1" Margin="10,10,10,0"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                </StackPanel>
            </Border>
        </ScrollViewer>
    </Grid>
</Window>
