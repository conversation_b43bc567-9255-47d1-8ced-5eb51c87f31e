# 📋 **دليل استيراد البيانات إلى Supabase**

## 🚀 **الخطوات السريعة**

### **الخطوة 1: إنشاء الجداول**
1. اذهب إلى **SQL Editor** في Supabase
2. انسخ محتوى ملف `create_tables.sql`
3. الصق الكود واضغط **Run**
4. تأكد من ظهور رسالة "تم إنشاء جميع الجداول والفهارس والسياسات بنجاح!"

### **الخطوة 2: استيراد البيانات**

## 🎯 **الطريقة المبسطة (موصى بها)**
استخدم الملفات المبسطة بدون ID لتجنب مشاكل التوافق:

#### **1. customers_simple.csv**
- اذهب إلى **Table Editor** → **customers**
- اضغط **Insert** → **Import data from CSV**
- اختر ملف `customers_simple.csv`
- تأكد من تطابق الأعمدة
- اضغط **Import**

#### **2. properties_simple.csv**
- اذهب إلى **Table Editor** → **properties**
- اضغط **Insert** → **Import data from CSV**
- اختر ملف `properties_simple.csv`
- اضغط **Import**

#### **3. contracts_simple.csv**
- اذهب إلى **Table Editor** → **contracts**
- اضغط **Insert** → **Import data from CSV**
- اختر ملف `contracts_simple.csv`
- اضغط **Import**

#### **4. payments_simple.csv**
- اذهب إلى **Table Editor** → **payments**
- اضغط **Insert** → **Import data from CSV**
- اختر ملف `payments_simple.csv`
- اضغط **Import**

## 📁 **الطريقة الكاملة (متقدمة)**
إذا كنت تريد استيراد البيانات مع ID محددة:

#### **1. users.csv**
- اذهب إلى **Table Editor** → **users**
- اضغط **Insert** → **Import data from CSV**
- اختر ملف `users.csv`
- تأكد من تطابق الأعمدة
- اضغط **Import**

#### **2. customers.csv**
- اذهب إلى **Table Editor** → **customers**
- اضغط **Insert** → **Import data from CSV**
- اختر ملف `customers.csv`
- اضغط **Import**

#### **3. properties.csv**
- اذهب إلى **Table Editor** → **properties**
- اضغط **Insert** → **Import data from CSV**
- اختر ملف `properties.csv`
- اضغط **Import**

#### **4. contracts.csv**
- اذهب إلى **Table Editor** → **contracts**
- اضغط **Insert** → **Import data from CSV**
- اختر ملف `contracts.csv`
- اضغط **Import**

#### **5. payments.csv**
- اذهب إلى **Table Editor** → **payments**
- اضغط **Insert** → **Import data from CSV**
- اختر ملف `payments.csv`
- اضغط **Import**

#### **6. rent_receipts.csv**
- اذهب إلى **Table Editor** → **rent_receipts**
- اضغط **Insert** → **Import data from CSV**
- اختر ملف `rent_receipts.csv`
- اضغط **Import**

## ✅ **التحقق من نجاح الاستيراد**

### **فحص عدد السجلات:**
```sql
SELECT 
    'users' as table_name, COUNT(*) as count FROM users
UNION ALL
SELECT 
    'customers' as table_name, COUNT(*) as count FROM customers
UNION ALL
SELECT 
    'properties' as table_name, COUNT(*) as count FROM properties
UNION ALL
SELECT 
    'contracts' as table_name, COUNT(*) as count FROM contracts
UNION ALL
SELECT 
    'payments' as table_name, COUNT(*) as count FROM payments
UNION ALL
SELECT 
    'rent_receipts' as table_name, COUNT(*) as count FROM rent_receipts;
```

**النتائج المتوقعة:**
- users: 4
- customers: 15
- properties: 18
- contracts: 15
- payments: 21
- rent_receipts: 15

### **فحص العلاقات:**
```sql
-- التحقق من ربط العقارات بالعملاء
SELECT 
    c.first_name || ' ' || c.last_name as customer_name,
    p.name as property_name,
    p.monthly_rent
FROM properties p
JOIN customers c ON p.customer_id = c.id
WHERE p.customer_id IS NOT NULL
ORDER BY c.first_name;
```

### **فحص البيانات المالية:**
```sql
-- إجمالي الإيجارات والأداءات
SELECT 
    SUM(monthly_rent) as total_monthly_rent,
    COUNT(*) as rented_properties
FROM properties 
WHERE status = 2; -- مؤجر

SELECT 
    SUM(rent_amount + cleaning_tax_amount) as total_payments,
    COUNT(*) as payment_count
FROM payments 
WHERE status = 2; -- مدفوع
```

## 🔧 **حل المشاكل الشائعة**

### **مشكلة: خطأ في Foreign Key**
**السبب:** استيراد الجداول بترتيب خاطئ
**الحل:** احذف البيانات وأعد الاستيراد بالترتيب الصحيح

### **مشكلة: خطأ في ترميز النص العربي**
**السبب:** ترميز الملف ليس UTF-8
**الحل:** 
1. افتح الملف في محرر نصوص
2. احفظه بترميز UTF-8
3. أعد الاستيراد

### **مشكلة: خطأ في التواريخ**
**السبب:** تنسيق التاريخ غير صحيح
**الحل:** تأكد من أن التواريخ بصيغة YYYY-MM-DD HH:MM:SS

### **مشكلة: خطأ في الأرقام العشرية**
**السبب:** استخدام فاصلة بدلاً من نقطة
**الحل:** استبدل الفواصل بنقاط في الأرقام

## 🔄 **إعادة تعيين البيانات**

إذا كنت تريد البدء من جديد:

```sql
-- حذف جميع البيانات (احذر!)
TRUNCATE TABLE rent_receipts CASCADE;
TRUNCATE TABLE payments CASCADE;
TRUNCATE TABLE contracts CASCADE;
TRUNCATE TABLE properties CASCADE;
TRUNCATE TABLE customers CASCADE;
TRUNCATE TABLE users CASCADE;

-- إعادة تعيين المعرفات التلقائية
ALTER SEQUENCE users_id_seq RESTART WITH 1;
ALTER SEQUENCE customers_id_seq RESTART WITH 1;
ALTER SEQUENCE properties_id_seq RESTART WITH 1;
ALTER SEQUENCE contracts_id_seq RESTART WITH 1;
ALTER SEQUENCE payments_id_seq RESTART WITH 1;
ALTER SEQUENCE rent_receipts_id_seq RESTART WITH 1;
```

## 📊 **استعلامات مفيدة للاختبار**

### **العملاء وعقاراتهم:**
```sql
SELECT 
    c.first_name || ' ' || c.last_name as customer,
    p.name as property,
    p.monthly_rent,
    p.status
FROM customers c
LEFT JOIN properties p ON c.id = p.customer_id
ORDER BY c.first_name;
```

### **الأداءات المستحقة:**
```sql
SELECT 
    c.first_name || ' ' || c.last_name as customer,
    p.name as property,
    pay.rent_amount + pay.cleaning_tax_amount as total_amount,
    pay.due_date
FROM payments pay
JOIN customers c ON pay.customer_id = c.id
JOIN properties p ON pay.property_id = p.id
WHERE pay.status = 1 -- غير مدفوع
ORDER BY pay.due_date;
```

### **إحصائيات شهرية:**
```sql
SELECT 
    DATE_TRUNC('month', payment_date) as month,
    COUNT(*) as payment_count,
    SUM(rent_amount + cleaning_tax_amount) as total_amount
FROM payments
WHERE status = 2 -- مدفوع
GROUP BY DATE_TRUNC('month', payment_date)
ORDER BY month;
```

## 🎯 **نصائح للنجاح**

1. **اتبع الترتيب**: استورد الجداول بالترتيب المحدد
2. **تحقق من الترميز**: استخدم UTF-8 دائماً
3. **راجع البيانات**: تأكد من صحة البيانات قبل الاستيراد
4. **اختبر الاتصال**: تأكد من عمل التطبيق مع البيانات الجديدة
5. **احفظ نسخة احتياطية**: قبل أي تعديل كبير

## 📞 **الدعم**

إذا واجهت مشاكل:
1. تحقق من سجلات الأخطاء في Supabase
2. راجع تنسيق البيانات في ملفات CSV
3. تأكد من وجود جميع الحقول المطلوبة
4. تحقق من صحة المراجع الخارجية
