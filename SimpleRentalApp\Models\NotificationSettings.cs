using System;
using System.ComponentModel.DataAnnotations;

namespace SimpleRentalApp.Models
{
    /// <summary>
    /// إعدادات الإشعارات
    /// </summary>
    public class NotificationSettings
    {
        [Key]
        public int Id { get; set; }
        
        /// <summary>
        /// عدد أيام التأخير للكراء الشهري قبل إرسال إشعار متأخر
        /// </summary>
        public int RentLateDays { get; set; } = 3;
        
        /// <summary>
        /// عدد أيام التأخير لضريبة النظافة قبل إرسال إشعار متأخر
        /// </summary>
        public int CleaningTaxLateDays { get; set; } = 7;
        
        /// <summary>
        /// الحد الأقصى لعدد الإشعارات المتأخرة في الشهر لكل مكتري
        /// </summary>
        public int MaxLateRemindersPerMonth { get; set; } = 3;
        
        /// <summary>
        /// فترة الانتظار بين الإشعارات المتأخرة (بالأيام)
        /// </summary>
        public int DaysBetweenLateReminders { get; set; } = 2;
        
        /// <summary>
        /// تفعيل الإشعارات التلقائية
        /// </summary>
        public bool AutoNotificationsEnabled { get; set; } = true;
        
        /// <summary>
        /// تفعيل إشعارات الكراء الشهري
        /// </summary>
        public bool RentNotificationsEnabled { get; set; } = true;
        
        /// <summary>
        /// تفعيل إشعارات ضريبة النظافة
        /// </summary>
        public bool CleaningTaxNotificationsEnabled { get; set; } = true;
        
        /// <summary>
        /// تفعيل الإشعارات المتأخرة
        /// </summary>
        public bool LateRemindersEnabled { get; set; } = true;
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public DateTime UpdatedDate { get; set; } = DateTime.Now;
    }
}
