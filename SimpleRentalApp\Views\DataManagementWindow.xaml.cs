using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Win32;
using SimpleRentalApp.Data;
using SimpleRentalApp.Services;

namespace SimpleRentalApp.Views
{
    public partial class DataManagementWindow : Window
    {
        private readonly DataExportImportService _dataService;

        public DataManagementWindow()
        {
            InitializeComponent();
            
            // تهيئة خدمة البيانات
            var context = new RentalDbContext();
            _dataService = new DataExportImportService(context);
            
            // عرض مسار التصدير
            ExportPathText.Text = _dataService.GetExportDirectory();
        }

        /// <summary>
        /// تصدير البيانات كـ JSON
        /// </summary>
        private async void ExportJsonBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ExportJsonBtn.IsEnabled = false;
                ExportJsonBtn.Content = "⏳ جاري التصدير...";

                var result = await _dataService.ExportAllDataAsync();

                if (result.Success)
                {
                    MessageBox.Show(
                        $"{result.Message}\n\nعدد السجلات: {result.RecordCount}",
                        "نجح التصدير ✅",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information
                    );

                    // عرض خيار فتح المجلد
                    var openFolder = MessageBox.Show(
                        "هل تريد فتح مجلد التصدير؟",
                        "فتح المجلد",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question
                    );

                    if (openFolder == MessageBoxResult.Yes)
                    {
                        Process.Start("explorer.exe", _dataService.GetExportDirectory());
                    }
                }
                else
                {
                    MessageBox.Show(
                        result.Message,
                        "فشل التصدير ❌",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error
                    );
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"حدث خطأ غير متوقع:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
            finally
            {
                ExportJsonBtn.IsEnabled = true;
                ExportJsonBtn.Content = "📄 تصدير كـ JSON";
            }
        }

        /// <summary>
        /// تصدير قاعدة البيانات
        /// </summary>
        private async void ExportDbBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ExportDbBtn.IsEnabled = false;
                ExportDbBtn.Content = "⏳ جاري التصدير...";

                var result = await _dataService.ExportDatabaseAsync();

                if (result.Success)
                {
                    var fileSizeMB = result.FileSize / (1024.0 * 1024.0);
                    
                    MessageBox.Show(
                        $"{result.Message}\n\nحجم الملف: {fileSizeMB:F2} MB",
                        "نجح التصدير ✅",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information
                    );

                    // عرض خيار فتح المجلد
                    var openFolder = MessageBox.Show(
                        "هل تريد فتح مجلد التصدير؟",
                        "فتح المجلد",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question
                    );

                    if (openFolder == MessageBoxResult.Yes)
                    {
                        Process.Start("explorer.exe", _dataService.GetExportDirectory());
                    }
                }
                else
                {
                    MessageBox.Show(
                        result.Message,
                        "فشل التصدير ❌",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error
                    );
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"حدث خطأ غير متوقع:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
            finally
            {
                ExportDbBtn.IsEnabled = true;
                ExportDbBtn.Content = "🗄️ تصدير قاعدة البيانات";
            }
        }

        /// <summary>
        /// استيراد البيانات من JSON
        /// </summary>
        private async void ImportJsonBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Title = "اختر ملف JSON للاستيراد",
                    Filter = "JSON Files (*.json)|*.json|All Files (*.*)|*.*",
                    InitialDirectory = _dataService.GetExportDirectory()
                };

                if (openFileDialog.ShowDialog() != true)
                    return;

                // تأكيد الاستيراد
                var confirmMessage = ReplaceExistingCheckBox.IsChecked == true
                    ? "⚠️ تحذير: سيتم حذف جميع البيانات الحالية واستبدالها بالبيانات المستوردة.\n\nهل أنت متأكد من المتابعة؟"
                    : "سيتم دمج البيانات المستوردة مع البيانات الحالية.\n\nهل تريد المتابعة؟";

                var confirm = MessageBox.Show(
                    confirmMessage,
                    "تأكيد الاستيراد",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning
                );

                if (confirm != MessageBoxResult.Yes)
                    return;

                ImportJsonBtn.IsEnabled = false;
                ImportJsonBtn.Content = "⏳ جاري الاستيراد...";

                var result = await _dataService.ImportDataAsync(
                    openFileDialog.FileName, 
                    ReplaceExistingCheckBox.IsChecked == true
                );

                if (result.Success)
                {
                    MessageBox.Show(
                        result.Message,
                        "نجح الاستيراد ✅",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information
                    );

                    // إعادة تحميل البيانات في النوافذ المفتوحة
                    RefreshApplicationData();
                }
                else
                {
                    MessageBox.Show(
                        result.Message,
                        "فشل الاستيراد ❌",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error
                    );
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"حدث خطأ غير متوقع:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
            finally
            {
                ImportJsonBtn.IsEnabled = true;
                ImportJsonBtn.Content = "📄 استيراد من JSON";
            }
        }

        /// <summary>
        /// استيراد قاعدة البيانات
        /// </summary>
        private async void ImportDbBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Title = "اختر ملف قاعدة البيانات للاستيراد",
                    Filter = "SQLite Database (*.db)|*.db|All Files (*.*)|*.*",
                    InitialDirectory = _dataService.GetExportDirectory()
                };

                if (openFileDialog.ShowDialog() != true)
                    return;

                // تأكيد الاستيراد
                var confirm = MessageBox.Show(
                    "⚠️ تحذير: سيتم استبدال قاعدة البيانات الحالية بالكامل.\n\nسيتم إنشاء نسخة احتياطية تلقائية قبل الاستيراد.\n\nهل أنت متأكد من المتابعة؟",
                    "تأكيد استيراد قاعدة البيانات",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning
                );

                if (confirm != MessageBoxResult.Yes)
                    return;

                ImportDbBtn.IsEnabled = false;
                ImportDbBtn.Content = "⏳ جاري الاستيراد...";

                var result = await _dataService.ImportDatabaseAsync(openFileDialog.FileName);

                if (result.Success)
                {
                    MessageBox.Show(
                        result.Message,
                        "نجح الاستيراد ✅",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information
                    );

                    // إعادة تشغيل التطبيق
                    var restart = MessageBox.Show(
                        "يجب إعادة تشغيل التطبيق لتطبيق التغييرات.\n\nهل تريد إعادة التشغيل الآن؟",
                        "إعادة التشغيل",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question
                    );

                    if (restart == MessageBoxResult.Yes)
                    {
                        // إعادة تشغيل التطبيق
                        var exePath = System.Diagnostics.Process.GetCurrentProcess().MainModule?.FileName;
                        if (!string.IsNullOrEmpty(exePath))
                        {
                            System.Diagnostics.Process.Start(exePath);
                        }
                        Application.Current.Shutdown();
                    }
                }
                else
                {
                    MessageBox.Show(
                        result.Message,
                        "فشل الاستيراد ❌",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error
                    );
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"حدث خطأ غير متوقع:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
            finally
            {
                ImportDbBtn.IsEnabled = true;
                ImportDbBtn.Content = "🗄️ استيراد قاعدة البيانات";
            }
        }

        /// <summary>
        /// فتح مجلد التصدير
        /// </summary>
        private void OpenExportFolderBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Process.Start("explorer.exe", _dataService.GetExportDirectory());
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"فشل في فتح المجلد:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
            }
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void CloseBtn_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        /// <summary>
        /// إعادة تحميل البيانات في التطبيق
        /// </summary>
        private void RefreshApplicationData()
        {
            try
            {
                // يمكن إضافة كود لإعادة تحميل البيانات في النوافذ المفتوحة
                // أو إرسال إشعار للنوافذ الأخرى لتحديث بياناتها
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ فقط دون إظهار رسالة للمستخدم
                System.Diagnostics.Debug.WriteLine($"خطأ في إعادة تحميل البيانات: {ex.Message}");
            }
        }
    }
}
