using System;
using System.ComponentModel.DataAnnotations;

namespace SimpleRentalApp.Models
{
    /// <summary>
    /// نموذج قوالب الرسائل
    /// </summary>
    public class MessageTemplate
    {
        [Key]
        public int Id { get; set; }
        
        public WhatsAppNotificationType Type { get; set; }
        
        public string Name { get; set; } = string.Empty;
        
        public string Template { get; set; } = string.Empty;
        
        public bool IsActive { get; set; } = true;
        
        public bool IsDefault { get; set; } = false;
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public DateTime UpdatedDate { get; set; } = DateTime.Now;
        
        // متغيرات القالب المتاحة
        public string AvailableVariables { get; set; } = string.Empty;
    }
    
    /// <summary>
    /// فئة مساعدة لمتغيرات القوالب
    /// </summary>
    public static class TemplateVariables
    {
        public const string CustomerName = "[اسم المكتري]";
        public const string PropertyName = "[اسم المحل]";
        public const string Month = "[الشهر]";
        public const string Amount = "[المبلغ]";
        public const string DueDate = "[تاريخ الأداء]";
        public const string PaymentDay = "[يوم الأداء]";
        public const string Year = "[السنة]";
        public const string PhoneNumber = "[رقم الهاتف]";
        public const string CurrentDate = "[التاريخ الحالي]";
        
        /// <summary>
        /// جلب جميع المتغيرات المتاحة
        /// </summary>
        public static string[] GetAllVariables()
        {
            return new[]
            {
                CustomerName,
                PropertyName,
                Month,
                Amount,
                DueDate,
                PaymentDay,
                Year,
                PhoneNumber,
                CurrentDate
            };
        }
        
        /// <summary>
        /// جلب المتغيرات حسب نوع الإشعار
        /// </summary>
        public static string[] GetVariablesForType(WhatsAppNotificationType type)
        {
            return type switch
            {
                WhatsAppNotificationType.MonthlyRent => new[]
                {
                    CustomerName, PropertyName, Month, Amount, DueDate, PaymentDay, CurrentDate
                },
                WhatsAppNotificationType.CleaningTax => new[]
                {
                    CustomerName, PropertyName, Year, Amount, DueDate, CurrentDate
                },
                WhatsAppNotificationType.ContractExpiry => new[]
                {
                    CustomerName, PropertyName, DueDate, CurrentDate
                },
                WhatsAppNotificationType.RentOverdue => new[]
                {
                    CustomerName, PropertyName, Amount, DueDate, CurrentDate
                },
                WhatsAppNotificationType.CleaningTaxOverdue => new[]
                {
                    CustomerName, PropertyName, Amount, DueDate, CurrentDate
                },
                WhatsAppNotificationType.GeneralReminder => new[]
                {
                    CustomerName, PropertyName, Month, Year, Amount, DueDate, PaymentDay, CurrentDate
                },
                _ => GetAllVariables()
            };
        }
    }
}
