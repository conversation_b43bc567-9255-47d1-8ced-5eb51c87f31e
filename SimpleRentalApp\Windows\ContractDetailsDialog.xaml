<Window x:Class="SimpleRentalApp.Windows.ContractDetailsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تفاصيل العقد" 
        Height="800" Width="700"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        Background="#F8F9FA"
        WindowStyle="None"
        AllowsTransparency="True"
        ResizeMode="NoResize">
    
    <Border Background="White" CornerRadius="16" Margin="20">
        <Border.Effect>
            <DropShadowEffect Color="#424242" 
                            Direction="270" 
                            ShadowDepth="12" 
                            BlurRadius="24" 
                            Opacity="0.3"/>
        </Border.Effect>
        
        <Grid Margin="30">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Modern Header -->
            <Border Grid.Row="0" 
                    Background="#1976D2" 
                    CornerRadius="12" 
                    Padding="25,20"
                    Margin="0,0,0,25">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <TextBlock Text="📋" FontSize="28" Margin="0,0,15,0"/>
                        <TextBlock Text="تفاصيل العقد" 
                                   FontSize="22" 
                                   FontWeight="Bold"
                                   Foreground="White"/>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <TextBlock Name="ContractNumberTextBlock"
                                   Text="عقد رقم: --" 
                                   FontSize="16" 
                                   Foreground="#E3F2FD"
                                   Margin="0,0,20,0"/>
                        <Border Name="StatusBorder"
                                CornerRadius="15"
                                Padding="12,6">
                            <TextBlock Name="StatusTextBlock"
                                       Text="الحالة"
                                       FontSize="12"
                                       FontWeight="Bold"
                                       Foreground="White"/>
                        </Border>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- Content -->
            <ScrollViewer Grid.Row="1"
                         VerticalScrollBarVisibility="Auto"
                         PanningMode="VerticalOnly"
                         CanContentScroll="True">
                <StackPanel>
                    
                    <!-- Customer and Property Info -->
                    <Border Background="#F8F9FA" 
                            BorderBrush="#DEE2E6" 
                            BorderThickness="1" 
                            CornerRadius="12" 
                            Padding="20"
                            Margin="0,0,0,20">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- Customer Info -->
                            <StackPanel Grid.Column="0" Margin="0,0,15,0">
                                <TextBlock Text="👤 معلومات المكتري" 
                                           FontWeight="Bold" 
                                           FontSize="16"
                                           Foreground="#495057"
                                           Margin="0,0,0,15"/>
                                <TextBlock Name="CustomerNameTextBlock"
                                           Text="الاسم: --"
                                           FontSize="14"
                                           Margin="0,0,0,8"/>
                                <TextBlock Name="CustomerPhoneTextBlock"
                                           Text="الهاتف: --"
                                           FontSize="14"
                                           Margin="0,0,0,8"/>
                                <TextBlock Name="CustomerEmailTextBlock"
                                           Text="البريد: --"
                                           FontSize="14"/>
                            </StackPanel>
                            
                            <!-- Property Info -->
                            <StackPanel Grid.Column="1" Margin="15,0,0,0">
                                <TextBlock Text="🏢 معلومات العقار" 
                                           FontWeight="Bold" 
                                           FontSize="16"
                                           Foreground="#495057"
                                           Margin="0,0,0,15"/>
                                <TextBlock Name="PropertyNameTextBlock"
                                           Text="اسم العقار: --"
                                           FontSize="14"
                                           Margin="0,0,0,8"/>
                                <TextBlock Name="PropertyAddressTextBlock"
                                           Text="العنوان: --"
                                           FontSize="14"
                                           Margin="0,0,0,8"/>
                                <TextBlock Name="PropertyTypeTextBlock"
                                           Text="النوع: --"
                                           FontSize="14"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- Contract Dates -->
                    <Border Background="#E3F2FD" 
                            BorderBrush="#2196F3" 
                            BorderThickness="2" 
                            CornerRadius="12" 
                            Padding="20"
                            Margin="0,0,0,20">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                <TextBlock Text="📅 تاريخ البداية" 
                                           FontWeight="Bold" 
                                           FontSize="14"
                                           Foreground="#1976D2"
                                           Margin="0,0,0,10"/>
                                <TextBlock Name="StartDateTextBlock"
                                           Text="--/--/----"
                                           FontSize="16"
                                           FontWeight="Bold"
                                           Foreground="#1565C0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" Margin="10,0,10,0">
                                <TextBlock Text="📅 تاريخ النهاية" 
                                           FontWeight="Bold" 
                                           FontSize="14"
                                           Foreground="#1976D2"
                                           Margin="0,0,0,10"/>
                                <TextBlock Name="EndDateTextBlock"
                                           Text="--/--/----"
                                           FontSize="16"
                                           FontWeight="Bold"
                                           Foreground="#1565C0"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2" Margin="10,0,0,0">
                                <TextBlock Text="⏱️ مدة العقد" 
                                           FontWeight="Bold" 
                                           FontSize="14"
                                           Foreground="#1976D2"
                                           Margin="0,0,0,10"/>
                                <TextBlock Name="DurationTextBlock"
                                           Text="-- شهر"
                                           FontSize="16"
                                           FontWeight="Bold"
                                           Foreground="#1565C0"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- Rent Information -->
                    <Border Background="#E8F5E8" 
                            BorderBrush="#4CAF50" 
                            BorderThickness="2" 
                            CornerRadius="12" 
                            Padding="20"
                            Margin="0,0,0,20">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                <TextBlock Text="💰 الإيجار الأولي" 
                                           FontWeight="Bold" 
                                           FontSize="14"
                                           Foreground="#2E7D32"
                                           Margin="0,0,0,10"/>
                                <TextBlock Name="InitialRentTextBlock"
                                           Text="0 درهم"
                                           FontSize="18"
                                           FontWeight="Bold"
                                           Foreground="#1B5E20"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" Margin="10,0,10,0">
                                <TextBlock Text="💵 الإيجار الحالي" 
                                           FontWeight="Bold" 
                                           FontSize="14"
                                           Foreground="#2E7D32"
                                           Margin="0,0,0,10"/>
                                <TextBlock Name="CurrentRentTextBlock"
                                           Text="0 درهم"
                                           FontSize="18"
                                           FontWeight="Bold"
                                           Foreground="#1B5E20"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2" Margin="10,0,0,0">
                                <TextBlock Text="📈 إجمالي الزيادة" 
                                           FontWeight="Bold" 
                                           FontSize="14"
                                           Foreground="#2E7D32"
                                           Margin="0,0,0,10"/>
                                <TextBlock Name="TotalIncreaseTextBlock"
                                           Text="0 درهم"
                                           FontSize="18"
                                           FontWeight="Bold"
                                           Foreground="#1B5E20"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- Rent Increase Details -->
                    <Border Background="#FFF3E0" 
                            BorderBrush="#FF9800" 
                            BorderThickness="1" 
                            CornerRadius="12" 
                            Padding="20"
                            Margin="0,0,0,20">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,15,0">
                                <TextBlock Text="📊 تفاصيل زيادات الإيجار" 
                                           FontWeight="Bold" 
                                           FontSize="16"
                                           Foreground="#E65100"
                                           Margin="0,0,0,15"/>
                                <TextBlock Name="IncreaseCountTextBlock"
                                           Text="عدد الزيادات: 0"
                                           FontSize="14"
                                           Margin="0,0,0,8"/>
                                <TextBlock Name="IncreasePercentageTextBlock"
                                           Text="نسبة الزيادة: 0%"
                                           FontSize="14"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" Margin="15,0,0,0">
                                <TextBlock Text="⏰ الوقت المتبقي" 
                                           FontWeight="Bold" 
                                           FontSize="16"
                                           Foreground="#E65100"
                                           Margin="0,0,0,15"/>
                                <TextBlock Name="RemainingTimeTextBlock"
                                           Text="متبقي: -- يوم"
                                           FontSize="14"
                                           FontWeight="Bold"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- Rent Increases History -->
                    <Border Name="RentIncreasesSection"
                            Background="#FFF8E1"
                            BorderBrush="#FFC107"
                            BorderThickness="2"
                            CornerRadius="12"
                            Padding="20"
                            Margin="0,0,0,20"
                            Visibility="Collapsed">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                <TextBlock Text="📈" FontSize="20" Margin="0,0,10,0"/>
                                <TextBlock Text="تاريخ زيادات الإيجار"
                                           FontWeight="Bold"
                                           FontSize="16"
                                           Foreground="#F57C00"/>
                            </StackPanel>

                            <ScrollViewer Name="RentIncreasesScrollViewer"
                                        MaxHeight="200"
                                        VerticalScrollBarVisibility="Auto"
                                        PanningMode="VerticalOnly"
                                        CanContentScroll="True">
                                <StackPanel Name="RentIncreasesPanel">
                                    <!-- Rent increases will be loaded here -->
                                </StackPanel>
                            </ScrollViewer>
                        </StackPanel>
                    </Border>

                    <!-- Contract Augments -->
                    <Border Name="AugmentsSection"
                            Background="#FFF3E0"
                            BorderBrush="#FF9800"
                            BorderThickness="2"
                            CornerRadius="12"
                            Padding="20"
                            Margin="0,0,0,20"
                            Visibility="Collapsed">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                <TextBlock Text="➕" FontSize="20" Margin="0,0,10,0"/>
                                <TextBlock Text="الزيادات الإضافية"
                                           FontWeight="Bold"
                                           FontSize="16"
                                           Foreground="#E65100"/>
                            </StackPanel>

                            <ScrollViewer Name="AugmentsScrollViewer"
                                        MaxHeight="200"
                                        VerticalScrollBarVisibility="Auto"
                                        PanningMode="VerticalOnly"
                                        CanContentScroll="True">
                                <StackPanel Name="AugmentsPanel">
                                    <!-- Augments will be loaded here -->
                                </StackPanel>
                            </ScrollViewer>
                        </StackPanel>
                    </Border>

                    <!-- Notes -->
                    <Border Background="#F8F9FA"
                            BorderBrush="#DEE2E6"
                            BorderThickness="1"
                            CornerRadius="12"
                            Padding="20"
                            Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="📝 ملاحظات"
                                       FontWeight="Bold"
                                       FontSize="16"
                                       Foreground="#495057"
                                       Margin="0,0,0,15"/>
                            <TextBlock Name="NotesTextBlock"
                                       Text="لا توجد ملاحظات"
                                       FontSize="14"
                                       TextWrapping="Wrap"
                                       Foreground="#6C757D"/>
                        </StackPanel>
                    </Border>

                </StackPanel>
            </ScrollViewer>

            <!-- Action Buttons -->
            <Border Grid.Row="2" 
                    Background="#F8F9FA" 
                    CornerRadius="12" 
                    Padding="20"
                    Margin="0,25,0,0">
                <StackPanel Orientation="Horizontal" 
                            HorizontalAlignment="Center">
                    <Button Name="EditButton"
                            Content="✏️ تعديل العقد"
                            Width="140"
                            Height="45"
                            FontSize="14"
                            FontWeight="Bold"
                            Background="#FF9800"
                            Foreground="White"
                            BorderThickness="0"
                            Margin="0,0,15,0"
                            Click="EditButton_Click"/>
                    
                    <Button Name="AttachmentsButton"
                            Content="📎 المرفقات"
                            Width="140"
                            Height="45"
                            FontSize="14"
                            FontWeight="Bold"
                            Background="#9C27B0"
                            Foreground="White"
                            BorderThickness="0"
                            Margin="0,0,15,0"
                            Click="AttachmentsButton_Click"/>
                    
                    <Button Name="CloseButton"
                            Content="❌ إغلاق"
                            Width="100"
                            Height="45"
                            FontSize="14"
                            Background="#6C757D"
                            Foreground="White"
                            BorderThickness="0"
                            Click="CloseButton_Click"/>
                </StackPanel>
            </Border>
        </Grid>
    </Border>
</Window>
