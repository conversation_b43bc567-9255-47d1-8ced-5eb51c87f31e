using Microsoft.EntityFrameworkCore;
using SimpleRentalApp.Data;
using SimpleRentalApp.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SimpleRentalApp.Services
{
    /// <summary>
    /// خدمة إشعارات الواتساب الحقيقية
    /// </summary>
    public class RealWhatsAppNotificationService
    {
        private readonly RentalDbContext _context;
        private readonly NotificationTemplateService _templateService;
        private readonly WhatsAppSenderService _whatsAppSender;

        public RealWhatsAppNotificationService(RentalDbContext context)
        {
            _context = context;
            _templateService = new NotificationTemplateService(context);
            _whatsAppSender = new WhatsAppSenderService();
        }

        public RealWhatsAppNotificationService()
        {
            _context = new RentalDbContext();
            _templateService = new NotificationTemplateService(_context);
            _whatsAppSender = new WhatsAppSenderService();
        }

        /// <summary>
        /// إنشاء إشعارات الإيجار الشهري للعقود النشطة
        /// </summary>
        public async Task<List<WhatsAppNotification>> GenerateMonthlyRentNotificationsAsync(int month, int year)
        {
            try
            {
                var notifications = new List<WhatsAppNotification>();
                
                // جلب العقود النشطة
                var activeContracts = await _context.Contracts
                    .Include(c => c.Customer)
                    .Include(c => c.Property)
                    .Where(c => c.IsActive && 
                               c.StartDate <= DateTime.Now && 
                               (c.EndDate == null || c.EndDate >= DateTime.Now))
                    .ToListAsync();

                foreach (var contract in activeContracts)
                {
                    // التحقق من عدم وجود إشعار مماثل
                    var uniqueKey = $"RENT_{contract.Id}_{month}_{year}";
                    var existingNotification = await _context.WhatsAppNotifications
                        .FirstOrDefaultAsync(n => n.UniqueKey == uniqueKey);

                    if (existingNotification != null)
                        continue;

                    // التحقق من وجود رقم هاتف
                    if (string.IsNullOrWhiteSpace(contract.Customer?.Phone))
                        continue;

                    var notification = new WhatsAppNotification
                    {
                        Type = WhatsAppNotificationType.MonthlyRent,
                        CustomerId = contract.CustomerId,
                        PropertyId = contract.PropertyId,
                        ContractId = contract.Id,
                        PhoneNumber = contract.Customer.Phone,
                        Amount = contract.Property.MonthlyRent,
                        Month = month,
                        Year = year,
                        DueDate = new DateTime(year, month, contract.PaymentDay ?? 1),
                        Priority = WhatsAppNotificationPriority.Normal,
                        UniqueKey = uniqueKey,
                        IsAutoGenerated = true,
                        MessageContent = await GenerateMessageContentAsync(WhatsAppNotificationType.MonthlyRent, contract)
                    };

                    notifications.Add(notification);
                }

                if (notifications.Any())
                {
                    _context.WhatsAppNotifications.AddRange(notifications);
                    await _context.SaveChangesAsync();
                }

                return notifications;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء إشعارات الإيجار الشهري: {ex.Message}");
                return new List<WhatsAppNotification>();
            }
        }

        /// <summary>
        /// إنشاء إشعارات ضريبة النظافة
        /// </summary>
        public async Task<List<WhatsAppNotification>> GenerateCleaningTaxNotificationsAsync(int year)
        {
            try
            {
                var notifications = new List<WhatsAppNotification>();
                
                // جلب العقود النشطة
                var activeContracts = await _context.Contracts
                    .Include(c => c.Customer)
                    .Include(c => c.Property)
                    .Where(c => c.IsActive)
                    .ToListAsync();

                foreach (var contract in activeContracts)
                {
                    // التحقق من عدم وجود إشعار مماثل
                    var uniqueKey = $"CLEANING_TAX_{contract.PropertyId}_{year}";
                    var existingNotification = await _context.WhatsAppNotifications
                        .FirstOrDefaultAsync(n => n.UniqueKey == uniqueKey);

                    if (existingNotification != null)
                        continue;

                    // التحقق من وجود رقم هاتف
                    if (string.IsNullOrWhiteSpace(contract.Customer?.Phone))
                        continue;

                    // حساب ضريبة النظافة (10.5% من الإيجار السنوي)
                    var annualRent = contract.Property.MonthlyRent * 12;
                    var cleaningTaxAmount = annualRent * 0.105m;

                    var notification = new WhatsAppNotification
                    {
                        Type = WhatsAppNotificationType.CleaningTax,
                        CustomerId = contract.CustomerId,
                        PropertyId = contract.PropertyId,
                        ContractId = contract.Id,
                        PhoneNumber = contract.Customer.Phone,
                        Amount = cleaningTaxAmount,
                        Year = year,
                        DueDate = new DateTime(year, 5, 31), // آخر أجل 31 مايو
                        Priority = WhatsAppNotificationPriority.High,
                        UniqueKey = uniqueKey,
                        IsAutoGenerated = true,
                        MessageContent = await GenerateMessageContentAsync(WhatsAppNotificationType.CleaningTax, contract, cleaningTaxAmount)
                    };

                    notifications.Add(notification);
                }

                if (notifications.Any())
                {
                    _context.WhatsAppNotifications.AddRange(notifications);
                    await _context.SaveChangesAsync();
                }

                return notifications;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء إشعارات ضريبة النظافة: {ex.Message}");
                return new List<WhatsAppNotification>();
            }
        }

        /// <summary>
        /// إنشاء إشعارات التأخير
        /// </summary>
        public async Task<List<WhatsAppNotification>> GenerateOverdueNotificationsAsync()
        {
            try
            {
                var notifications = new List<WhatsAppNotification>();
                var today = DateTime.Today;

                // البحث عن الإشعارات المتأخرة
                var overdueNotifications = await _context.WhatsAppNotifications
                    .Include(n => n.Customer)
                    .Include(n => n.Property)
                    .Include(n => n.Contract)
                    .Where(n => n.DueDate.HasValue && 
                               n.DueDate.Value.Date < today &&
                               n.Status == WhatsAppNotificationStatus.Pending)
                    .ToListAsync();

                foreach (var overdueNotification in overdueNotifications)
                {
                    // التحقق من عدم وجود إشعار تأخير مماثل
                    var uniqueKey = $"OVERDUE_{overdueNotification.Id}_{today:yyyyMMdd}";
                    var existingOverdueNotification = await _context.WhatsAppNotifications
                        .FirstOrDefaultAsync(n => n.UniqueKey == uniqueKey);

                    if (existingOverdueNotification != null)
                        continue;

                    var daysOverdue = (today - overdueNotification.DueDate.Value.Date).Days;

                    var newNotificationType = overdueNotification.Type switch
                    {
                        WhatsAppNotificationType.MonthlyRent => WhatsAppNotificationType.RentOverdue,
                        WhatsAppNotificationType.CleaningTax => WhatsAppNotificationType.CleaningTaxOverdue,
                        _ => WhatsAppNotificationType.GeneralReminder
                    };

                    var overdueNotificationNew = new WhatsAppNotification
                    {
                        Type = newNotificationType,
                        CustomerId = overdueNotification.CustomerId,
                        PropertyId = overdueNotification.PropertyId,
                        ContractId = overdueNotification.ContractId,
                        PhoneNumber = overdueNotification.PhoneNumber,
                        Amount = overdueNotification.Amount,
                        Month = overdueNotification.Month,
                        Year = overdueNotification.Year,
                        DueDate = overdueNotification.DueDate,
                        Priority = WhatsAppNotificationPriority.Urgent,
                        UniqueKey = uniqueKey,
                        IsAutoGenerated = true,
                        Notes = $"متأخر {daysOverdue} يوم",
                        MessageContent = await GenerateOverdueMessageContentAsync(newNotificationType, overdueNotification, daysOverdue)
                    };

                    notifications.Add(overdueNotificationNew);
                }

                if (notifications.Any())
                {
                    _context.WhatsAppNotifications.AddRange(notifications);
                    await _context.SaveChangesAsync();
                }

                return notifications;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء إشعارات التأخير: {ex.Message}");
                return new List<WhatsAppNotification>();
            }
        }

        /// <summary>
        /// إرسال إشعار واحد
        /// </summary>
        public async Task<bool> SendNotificationAsync(int notificationId)
        {
            try
            {
                var notification = await _context.WhatsAppNotifications
                    .Include(n => n.Customer)
                    .Include(n => n.Property)
                    .FirstOrDefaultAsync(n => n.Id == notificationId);

                if (notification == null)
                    return false;

                // تحديث عدد المحاولات
                notification.AttemptCount++;
                notification.LastAttemptDate = DateTime.Now;

                // إرسال الرسالة
                bool success = await _whatsAppSender.SendMessageWithOptionsAsync(
                    notification.PhoneNumber,
                    notification.MessageContent,
                    useApp: false,
                    showConfirmation: false);

                if (success)
                {
                    notification.Status = WhatsAppNotificationStatus.Sent;
                    notification.SentDate = DateTime.Now;
                    notification.ErrorMessage = null;
                }
                else
                {
                    notification.Status = WhatsAppNotificationStatus.Failed;
                    notification.ErrorMessage = "فشل في الإرسال عبر الواتساب";
                }

                await _context.SaveChangesAsync();
                return success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إرسال الإشعار: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إرسال إشعارات متعددة
        /// </summary>
        public async Task<(int success, int failed)> SendMultipleNotificationsAsync(List<int> notificationIds)
        {
            int successCount = 0;
            int failedCount = 0;

            foreach (var id in notificationIds)
            {
                bool success = await SendNotificationAsync(id);
                if (success)
                    successCount++;
                else
                    failedCount++;

                // فاصل زمني بين الرسائل لتجنب الحظر
                await Task.Delay(2000);
            }

            return (successCount, failedCount);
        }

        /// <summary>
        /// إنشاء محتوى الرسالة
        /// </summary>
        private async Task<string> GenerateMessageContentAsync(WhatsAppNotificationType type, Contract contract, decimal? customAmount = null)
        {
            try
            {
                // محاولة استخدام القالب
                var template = await _templateService.GetTemplateForNotificationTypeAsync(type);
                
                if (template != null)
                {
                    return await _templateService.ApplyTemplateAsync(template, new
                    {
                        CustomerName = contract.Customer?.FullName ?? "المكتري",
                        PropertyName = contract.Property?.Name ?? "المحل",
                        PropertyAddress = contract.Property?.Address ?? "",
                        Amount = customAmount ?? contract.Property.MonthlyRent,
                        DueDate = DateTime.Now.AddDays(30).ToString("dd/MM/yyyy"),
                        Month = DateTime.Now.ToString("MMMM yyyy"),
                        Year = DateTime.Now.Year
                    });
                }

                // رسالة افتراضية
                return GenerateDefaultMessage(type, contract, customAmount);
            }
            catch
            {
                return GenerateDefaultMessage(type, contract, customAmount);
            }
        }

        /// <summary>
        /// إنشاء رسالة افتراضية
        /// </summary>
        private string GenerateDefaultMessage(WhatsAppNotificationType type, Contract contract, decimal? customAmount = null)
        {
            var amount = customAmount ?? contract.Property.MonthlyRent;
            var customerName = contract.Customer?.FullName ?? "المكتري";
            var propertyName = contract.Property?.Name ?? "المحل";

            return type switch
            {
                WhatsAppNotificationType.MonthlyRent => 
                    $"السلام عليكم {customerName}،\n\n" +
                    $"نذكركم بوجوب أداء كراء محل {propertyName}.\n" +
                    $"المبلغ المستحق: {amount:N0} درهم\n\n" +
                    $"شكراً لتعاونكم.",

                WhatsAppNotificationType.CleaningTax => 
                    $"مرحباً {customerName}،\n\n" +
                    $"المرجو تسديد ضريبة النظافة عن المحل {propertyName}.\n" +
                    $"المبلغ المستحق: {amount:N0} درهم\n\n" +
                    $"مع تحياتنا.",

                _ => $"إشعار للسيد/ة {customerName} بخصوص المحل {propertyName}. المبلغ المستحق: {amount:N0} درهم."
            };
        }

        /// <summary>
        /// إنشاء رسالة تأخير
        /// </summary>
        private async Task<string> GenerateOverdueMessageContentAsync(WhatsAppNotificationType type, WhatsAppNotification originalNotification, int daysOverdue)
        {
            var customerName = originalNotification.Customer?.FullName ?? "المكتري";
            var propertyName = originalNotification.Property?.Name ?? "المحل";

            return type switch
            {
                WhatsAppNotificationType.RentOverdue => 
                    $"⚠️ تنبيه مهم ⚠️\n\n" +
                    $"السيد/ة {customerName}،\n\n" +
                    $"نلفت انتباهكم إلى وجود دفعة متأخرة تخص المحل {propertyName}.\n" +
                    $"المبلغ المتأخر: {originalNotification.Amount:N0} درهم\n" +
                    $"عدد أيام التأخير: {daysOverdue} يوم\n\n" +
                    $"المرجو الأداء في أقرب وقت ممكن.",

                WhatsAppNotificationType.CleaningTaxOverdue => 
                    $"🚨 تنبيه عاجل - ضريبة النظافة 🚨\n\n" +
                    $"السيد/ة {customerName}،\n\n" +
                    $"ضريبة النظافة للمحل {propertyName} متأخرة عن موعد الاستحقاق.\n" +
                    $"المبلغ المتأخر: {originalNotification.Amount:N0} درهم\n" +
                    $"عدد أيام التأخير: {daysOverdue} يوم\n\n" +
                    $"المرجو التسديد فوراً.",

                _ => $"تذكير: توجد دفعة متأخرة للسيد/ة {customerName} بمبلغ {originalNotification.Amount:N0} درهم."
            };
        }

        public void Dispose()
        {
            _context?.Dispose();
            _templateService?.Dispose();
        }
    }
}
