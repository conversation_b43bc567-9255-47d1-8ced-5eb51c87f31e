@echo off
echo ===================================
echo تطبيق تدبير الكراء مع المزامنة السحابية
echo Rental Management App with Cloud Sync
echo ===================================
echo.

echo التحقق من .NET SDK...
dotnet --version
if %errorlevel% neq 0 (
    echo خطأ: .NET SDK غير مثبت
    echo يرجى تثبيت .NET 8.0 SDK من: https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo.
echo استعادة الحزم...
dotnet restore
if %errorlevel% neq 0 (
    echo فشل في استعادة الحزم
    pause
    exit /b 1
)

echo.
echo بناء التطبيق...
dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo فشل في بناء التطبيق، جاري المحاولة مع Debug...
    dotnet build --configuration Debug
    if %errorlevel% neq 0 (
        echo فشل في بناء التطبيق
        pause
        exit /b 1
    )
)

echo.
echo تشغيل التطبيق...
echo الميزات المتاحة:
echo   - إدارة العملاء والعقارات
echo   - إدارة العقود والمدفوعات  
echo   - المزامنة السحابية (Supabase, Firebase, Azure SQL)
echo   - التشفير والأمان المتقدم
echo   - مراقب الأداء
echo   - النسخ الاحتياطية
echo.

dotnet run --configuration Release
if %errorlevel% neq 0 (
    echo جاري المحاولة مع Debug...
    dotnet run --configuration Debug
)

echo.
echo انتهى تشغيل التطبيق
pause
