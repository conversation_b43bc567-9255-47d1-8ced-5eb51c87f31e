﻿#pragma checksum "..\..\..\..\Windows\MessagePreviewDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C97B76B485A2D5F99136926857666AA39886774C"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleRentalApp.Windows {
    
    
    /// <summary>
    /// MessagePreviewDialog
    /// </summary>
    public partial class MessagePreviewDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 97 "..\..\..\..\Windows\MessagePreviewDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SubtitleTextBlock;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\..\Windows\MessagePreviewDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CustomerNameTextBlock;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\Windows\MessagePreviewDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PhoneNumberTextBlock;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\..\Windows\MessagePreviewDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RestoreTemplateBtn;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\..\..\Windows\MessagePreviewDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CopyTextBtn;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\Windows\MessagePreviewDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CharCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\..\Windows\MessagePreviewDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MessageTextBox;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\..\Windows\MessagePreviewDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel VariablesPanel;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\..\Windows\MessagePreviewDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SendBtn;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\..\Windows\MessagePreviewDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PreviewBtn;
        
        #line default
        #line hidden
        
        
        #line 176 "..\..\..\..\Windows\MessagePreviewDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelBtn;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleRentalApp;V1.0.0.0;component/windows/messagepreviewdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\MessagePreviewDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.SubtitleTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.CustomerNameTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.PhoneNumberTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.RestoreTemplateBtn = ((System.Windows.Controls.Button)(target));
            
            #line 130 "..\..\..\..\Windows\MessagePreviewDialog.xaml"
            this.RestoreTemplateBtn.Click += new System.Windows.RoutedEventHandler(this.RestoreTemplateBtn_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.CopyTextBtn = ((System.Windows.Controls.Button)(target));
            
            #line 133 "..\..\..\..\Windows\MessagePreviewDialog.xaml"
            this.CopyTextBtn.Click += new System.Windows.RoutedEventHandler(this.CopyTextBtn_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.CharCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.MessageTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 146 "..\..\..\..\Windows\MessagePreviewDialog.xaml"
            this.MessageTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.MessageTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.VariablesPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 9:
            this.SendBtn = ((System.Windows.Controls.Button)(target));
            
            #line 168 "..\..\..\..\Windows\MessagePreviewDialog.xaml"
            this.SendBtn.Click += new System.Windows.RoutedEventHandler(this.SendBtn_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.PreviewBtn = ((System.Windows.Controls.Button)(target));
            
            #line 172 "..\..\..\..\Windows\MessagePreviewDialog.xaml"
            this.PreviewBtn.Click += new System.Windows.RoutedEventHandler(this.PreviewBtn_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.CancelBtn = ((System.Windows.Controls.Button)(target));
            
            #line 176 "..\..\..\..\Windows\MessagePreviewDialog.xaml"
            this.CancelBtn.Click += new System.Windows.RoutedEventHandler(this.CancelBtn_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

