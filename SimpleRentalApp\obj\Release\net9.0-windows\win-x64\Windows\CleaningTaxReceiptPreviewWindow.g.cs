﻿#pragma checksum "..\..\..\..\..\Windows\CleaningTaxReceiptPreviewWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "E3D9990CB75E45BE86CE7A61FE2F1712DBA839A0"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleRentalApp.Windows {
    
    
    /// <summary>
    /// CleaningTaxReceiptPreviewWindow
    /// </summary>
    public partial class CleaningTaxReceiptPreviewWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 101 "..\..\..\..\..\Windows\CleaningTaxReceiptPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PropertyInfoTextBlock;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\..\..\Windows\CleaningTaxReceiptPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ReceiptTitleTextBlock;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\..\..\Windows\CleaningTaxReceiptPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportPdfButton;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\..\..\Windows\CleaningTaxReceiptPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ZoomInButton;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\..\..\Windows\CleaningTaxReceiptPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ZoomOutButton;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\..\..\Windows\CleaningTaxReceiptPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetZoomButton;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\..\..\Windows\CleaningTaxReceiptPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer PreviewScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\..\..\Windows\CleaningTaxReceiptPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border ReceiptPreviewBorder;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\..\..\Windows\CleaningTaxReceiptPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ContentPresenter ReceiptContentPresenter;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\..\..\Windows\CleaningTaxReceiptPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintFromPreviewButton;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\..\..\Windows\CleaningTaxReceiptPreviewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClosePreviewButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleRentalApp;V1.0.0.0;component/windows/cleaningtaxreceiptpreviewwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\CleaningTaxReceiptPreviewWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.PropertyInfoTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.ReceiptTitleTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.ExportPdfButton = ((System.Windows.Controls.Button)(target));
            
            #line 151 "..\..\..\..\..\Windows\CleaningTaxReceiptPreviewWindow.xaml"
            this.ExportPdfButton.Click += new System.Windows.RoutedEventHandler(this.ExportPdfButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.ZoomInButton = ((System.Windows.Controls.Button)(target));
            
            #line 156 "..\..\..\..\..\Windows\CleaningTaxReceiptPreviewWindow.xaml"
            this.ZoomInButton.Click += new System.Windows.RoutedEventHandler(this.ZoomInButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ZoomOutButton = ((System.Windows.Controls.Button)(target));
            
            #line 162 "..\..\..\..\..\Windows\CleaningTaxReceiptPreviewWindow.xaml"
            this.ZoomOutButton.Click += new System.Windows.RoutedEventHandler(this.ZoomOutButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.ResetZoomButton = ((System.Windows.Controls.Button)(target));
            
            #line 168 "..\..\..\..\..\Windows\CleaningTaxReceiptPreviewWindow.xaml"
            this.ResetZoomButton.Click += new System.Windows.RoutedEventHandler(this.ResetZoomButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.PreviewScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 8:
            this.ReceiptPreviewBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 9:
            this.ReceiptContentPresenter = ((System.Windows.Controls.ContentPresenter)(target));
            return;
            case 10:
            this.PrintFromPreviewButton = ((System.Windows.Controls.Button)(target));
            
            #line 207 "..\..\..\..\..\Windows\CleaningTaxReceiptPreviewWindow.xaml"
            this.PrintFromPreviewButton.Click += new System.Windows.RoutedEventHandler(this.PrintFromPreviewButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.ClosePreviewButton = ((System.Windows.Controls.Button)(target));
            
            #line 214 "..\..\..\..\..\Windows\CleaningTaxReceiptPreviewWindow.xaml"
            this.ClosePreviewButton.Click += new System.Windows.RoutedEventHandler(this.ClosePreviewButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

