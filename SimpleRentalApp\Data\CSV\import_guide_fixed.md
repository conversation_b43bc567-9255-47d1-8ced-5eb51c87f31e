# 🔧 **دليل استيراد البيانات - الحل المُصحح**

## ❌ **المشكلة:**
```
Failed to import data: insert or update on table "properties" violates foreign key constraint "properties_customer_id_fkey"
```

## ✅ **الحل:**

### **الطريقة 1: الاستيراد بدون Foreign Keys (الأسهل)**

#### **الخطوة 1: إنشاء الجداول**
```sql
-- في SQL Editor في Supabase
-- انسخ والصق من ملف create_tables.sql
```

#### **الخطوة 2: استيراد البيانات بالترتيب الصحيح**

**1. استورد العملاء أولاً:**
- اذهب إلى **Table Editor** → **customers**
- اضغط **Insert** → **Import data from CSV**
- اختر ملف `customers_simple.csv`
- اضغط **Import**

**2. استورد العقارات بدون customer_id:**
- اذهب إلى **Table Editor** → **properties**
- اضغط **Insert** → **Import data from CSV**
- اختر ملف `properties_no_fk.csv` (بدون عمود customer_id)
- اضغط **Import**

**3. ربط العقارات بالعملاء:**
- اذهب إلى **SQL Editor**
- انسخ والصق محتوى ملف `update_property_customers.sql`
- اضغط **Run**

**4. استورد العقود:**
- اذهب إلى **Table Editor** → **contracts**
- اضغط **Insert** → **Import data from CSV**
- اختر ملف `contracts_simple.csv`
- اضغط **Import**

**5. استورد الأداءات:**
- اذهب إلى **Table Editor** → **payments**
- اضغط **Insert** → **Import data from CSV**
- اختر ملف `payments_simple.csv`
- اضغط **Import**

### **الطريقة 2: تعطيل Foreign Key مؤقتاً (متقدمة)**

#### **الخطوة 1: تعطيل Foreign Key Constraints**
```sql
-- في SQL Editor
ALTER TABLE properties DROP CONSTRAINT IF EXISTS properties_customer_id_fkey;
ALTER TABLE contracts DROP CONSTRAINT IF EXISTS contracts_customer_id_fkey;
ALTER TABLE contracts DROP CONSTRAINT IF EXISTS contracts_property_id_fkey;
ALTER TABLE payments DROP CONSTRAINT IF EXISTS payments_customer_id_fkey;
ALTER TABLE payments DROP CONSTRAINT IF EXISTS payments_property_id_fkey;
```

#### **الخطوة 2: استيراد جميع البيانات**
- استورد `customers.csv`
- استورد `properties.csv`
- استورد `contracts.csv`
- استورد `payments.csv`

#### **الخطوة 3: إعادة تفعيل Foreign Key Constraints**
```sql
-- في SQL Editor
ALTER TABLE properties ADD CONSTRAINT properties_customer_id_fkey 
    FOREIGN KEY (customer_id) REFERENCES customers(id);

ALTER TABLE contracts ADD CONSTRAINT contracts_customer_id_fkey 
    FOREIGN KEY (customer_id) REFERENCES customers(id);

ALTER TABLE contracts ADD CONSTRAINT contracts_property_id_fkey 
    FOREIGN KEY (property_id) REFERENCES properties(id);

ALTER TABLE payments ADD CONSTRAINT payments_customer_id_fkey 
    FOREIGN KEY (customer_id) REFERENCES customers(id);

ALTER TABLE payments ADD CONSTRAINT payments_property_id_fkey 
    FOREIGN KEY (property_id) REFERENCES properties(id);
```

## 🔍 **التحقق من نجاح الاستيراد**

### **فحص عدد السجلات:**
```sql
SELECT 
    'customers' as table_name, COUNT(*) as count FROM customers
UNION ALL
SELECT 
    'properties' as table_name, COUNT(*) as count FROM properties
UNION ALL
SELECT 
    'contracts' as table_name, COUNT(*) as count FROM contracts
UNION ALL
SELECT 
    'payments' as table_name, COUNT(*) as count FROM payments;
```

**النتائج المتوقعة:**
- customers: 15
- properties: 18
- contracts: 15
- payments: 15

### **فحص ربط العقارات بالعملاء:**
```sql
SELECT 
    p.name as property_name,
    CASE 
        WHEN c.id IS NOT NULL THEN c.first_name || ' ' || c.last_name 
        ELSE 'متاح للإيجار' 
    END as customer_name,
    p.monthly_rent
FROM properties p
LEFT JOIN customers c ON p.customer_id = c.id
ORDER BY p.id;
```

### **فحص العقود:**
```sql
SELECT 
    c.first_name || ' ' || c.last_name as customer_name,
    p.name as property_name,
    con.start_date,
    con.end_date,
    con.initial_rent_amount
FROM contracts con
JOIN customers c ON con.customer_id = c.id
JOIN properties p ON con.property_id = p.id
ORDER BY con.start_date;
```

## 🚨 **نصائح لتجنب المشاكل:**

1. **اتبع الترتيب:** العملاء → العقارات → العقود → الأداءات
2. **تحقق من البيانات:** تأكد من عدم وجود قيم فارغة في الحقول المطلوبة
3. **استخدم UTF-8:** تأكد من ترميز الملفات للنصوص العربية
4. **راجع الأخطاء:** اقرأ رسائل الخطأ بعناية
5. **احفظ نسخة احتياطية:** قبل أي تعديل كبير

## 📞 **إذا استمرت المشاكل:**

### **امسح البيانات وابدأ من جديد:**
```sql
-- حذف جميع البيانات (احذر!)
TRUNCATE TABLE payments CASCADE;
TRUNCATE TABLE contracts CASCADE;
TRUNCATE TABLE properties CASCADE;
TRUNCATE TABLE customers CASCADE;

-- إعادة تعيين المعرفات التلقائية
ALTER SEQUENCE customers_id_seq RESTART WITH 1;
ALTER SEQUENCE properties_id_seq RESTART WITH 1;
ALTER SEQUENCE contracts_id_seq RESTART WITH 1;
ALTER SEQUENCE payments_id_seq RESTART WITH 1;
```

### **ثم اتبع الطريقة 1 من البداية**

**🎯 الطريقة 1 هي الأسهل والأكثر أماناً للمبتدئين!**
