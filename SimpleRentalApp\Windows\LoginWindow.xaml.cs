using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using SimpleRentalApp.Services;

namespace SimpleRentalApp.Windows;

public partial class LoginWindow : Window
{
    public bool LoginSuccessful { get; private set; } = false;

    public LoginWindow()
    {
        InitializeComponent();

        // Set focus to username textbox
        UsernameTextBox.Focus();

        // Handle Enter key press
        KeyDown += LoginWindow_KeyDown;
        UsernameTextBox.KeyDown += TextBox_KeyDown;
        PasswordBox.KeyDown += PasswordBox_KeyDown;

        // Handle window closing
        Closing += LoginWindow_Closing;
    }

    private void LoginWindow_Closing(object? sender, System.ComponentModel.CancelEventArgs e)
    {
        // If login was not successful, exit the application
        if (!LoginSuccessful)
        {
            Application.Current.Shutdown();
        }
    }

    private void LoginWindow_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
    {
        if (e.Key == Key.Enter)
        {
            LoginButton_Click(sender, e);
        }
    }

    private void TextBox_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
    {
        if (e.Key == Key.Enter)
        {
            PasswordBox.Focus();
        }
    }

    private void PasswordBox_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
    {
        if (e.Key == Key.Enter)
        {
            LoginButton_Click(sender, e);
        }
    }

    private async void LoginButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // Disable button and show loading
            LoginButton.IsEnabled = false;
            LoginButton.Content = "⏳ جاري التحقق...";
            StatusTextBlock.Visibility = Visibility.Collapsed;

            var username = UsernameTextBox.Text.Trim();
            var password = PasswordBox.Password;

            // Show debug info
            ShowSuccess($"محاولة تسجيل دخول: {username}");
            await Task.Delay(500);

            // Validation
            if (string.IsNullOrEmpty(username))
            {
                ShowError("يرجى إدخال اسم المستخدم");
                return;
            }

            if (string.IsNullOrEmpty(password))
            {
                ShowError("يرجى إدخال كلمة المرور");
                return;
            }

            // Show attempting login
            ShowSuccess("جاري التحقق من البيانات...");
            await Task.Delay(500);

            // Attempt login
            var success = await AuthService.Instance.LoginAsync(username, password);

            if (success)
            {
                ShowSuccess("تم تسجيل الدخول بنجاح! ✅");
                LoginSuccessful = true;

                // Wait a moment
                await Task.Delay(1000);

                // Show main window immediately
                var mainWindow = new MainWindow();
                mainWindow.Show();

                // Close login window
                this.Close();
            }
            else
            {
                ShowError("اسم المستخدم أو كلمة المرور غير صحيحة");
                PasswordBox.Clear();
                PasswordBox.Focus();
            }
        }
        catch (Exception ex)
        {
            ShowError($"خطأ في تسجيل الدخول: {ex.Message}");
            MessageBox.Show($"تفاصيل الخطأ: {ex}", "خطأ تفصيلي", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            // Re-enable button
            LoginButton.IsEnabled = true;
            LoginButton.Content = "🔐 تسجيل الدخول";
        }
    }

    private void ShowError(string message)
    {
        StatusTextBlock.Text = message;
        StatusTextBlock.Foreground = Brushes.Red;
        StatusTextBlock.Visibility = Visibility.Visible;
    }

    private void ShowSuccess(string message)
    {
        StatusTextBlock.Text = message;
        StatusTextBlock.Foreground = Brushes.Green;
        StatusTextBlock.Visibility = Visibility.Visible;
    }
}
