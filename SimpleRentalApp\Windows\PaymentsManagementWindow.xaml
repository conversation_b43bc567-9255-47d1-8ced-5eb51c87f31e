<Window x:Class="SimpleRentalApp.Windows.PaymentsManagementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="💰 إدارة توصيلات الكراء" Height="800" Width="1400"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        MinHeight="600" MinWidth="1000"
        WindowState="Maximized">

    <Window.Resources>
        <!-- Drop Shadow Effect -->
        <DropShadowEffect x:Key="DropShadowEffect"
                          Color="Black" Opacity="0.1"
                          ShadowDepth="2" BlurRadius="8"/>

        <!-- Modern ComboBox Style -->
        <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ComboBox">
                        <Grid>
                            <ToggleButton Name="ToggleButton"
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        Grid.Column="2"
                                        Focusable="false"
                                        IsChecked="{Binding Path=IsDropDownOpen,Mode=TwoWay,RelativeSource={RelativeSource TemplatedParent}}"
                                        ClickMode="Press">
                                <ToggleButton.Template>
                                    <ControlTemplate TargetType="ToggleButton">
                                        <Border Background="{TemplateBinding Background}"
                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                CornerRadius="4">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition/>
                                                    <ColumnDefinition Width="20"/>
                                                </Grid.ColumnDefinitions>
                                                <Path Grid.Column="1"
                                                      HorizontalAlignment="Center"
                                                      VerticalAlignment="Center"
                                                      Data="M 0 0 L 4 4 L 8 0 Z"
                                                      Fill="#666"/>
                                            </Grid>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#F0F8FF"/>
                                                <Setter Property="BorderBrush" Value="#2196F3"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </ToggleButton.Template>
                            </ToggleButton>
                            <ContentPresenter Name="ContentSite"
                                            IsHitTestVisible="False"
                                            Content="{TemplateBinding SelectionBoxItem}"
                                            ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                            ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                                            Margin="{TemplateBinding Padding}"
                                            VerticalAlignment="Center"
                                            HorizontalAlignment="Left"
                                            TextElement.Foreground="Black"/>
                            <TextBox x:Name="PART_EditableTextBox"
                                   Style="{x:Null}"
                                   Template="{DynamicResource ComboBoxTextBox}"
                                   HorizontalAlignment="Left"
                                   VerticalAlignment="Center"
                                   Margin="3,3,23,3"
                                   Focusable="True"
                                   Background="Transparent"
                                   Visibility="Hidden"
                                   IsReadOnly="{TemplateBinding IsReadOnly}"/>
                            <Popup Name="Popup"
                                 Placement="Bottom"
                                 IsOpen="{TemplateBinding IsDropDownOpen}"
                                 AllowsTransparency="True"
                                 Focusable="False"
                                 PopupAnimation="Slide">
                                <Grid Name="DropDown"
                                    SnapsToDevicePixels="True"
                                    MinWidth="{TemplateBinding ActualWidth}"
                                    MaxHeight="{TemplateBinding MaxDropDownHeight}">
                                    <Border x:Name="DropDownBorder"
                                          Background="White"
                                          BorderThickness="1"
                                          BorderBrush="#DDD"
                                          CornerRadius="4"
                                          Effect="{StaticResource DropShadowEffect}"/>
                                    <ScrollViewer Margin="4,6,4,6" SnapsToDevicePixels="True">
                                        <StackPanel IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Contained"
                                                   TextElement.Foreground="Black"/>
                                    </ScrollViewer>
                                </Grid>
                            </Popup>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="HasItems" Value="false">
                                <Setter TargetName="DropDownBorder" Property="MinHeight" Value="95"/>
                            </Trigger>
                            <Trigger Property="IsGrouping" Value="true">
                                <Setter Property="ScrollViewer.CanContentScroll" Value="false"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsEditable" Value="true">
                    <Setter Property="IsTabStop" Value="false"/>
                    <Setter Property="Padding" Value="2"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- ComboBox Item Style -->
        <Style x:Key="ModernComboBoxItemStyle" TargetType="ComboBoxItem">
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ComboBoxItem">
                        <Border Name="Border"
                                Background="{TemplateBinding Background}"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter ContentTemplate="{TemplateBinding ContentTemplate}"
                                            Content="{TemplateBinding Content}"
                                            TextElement.Foreground="Black"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsHighlighted" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#E3F2FD"/>
                                <Setter Property="Foreground" Value="Black"/>
                            </Trigger>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#BBDEFB"/>
                                <Setter Property="Foreground" Value="Black"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="6"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Success Button Style -->
        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#4CAF50"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#45A049"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Warning Button Style -->
        <Style x:Key="WarningButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#FF9800"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#F57C00"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Danger Button Style -->
        <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#F44336"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#D32F2F"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header with Property Selection -->
        <Border Grid.Row="0" Background="White" Padding="30,25"
                BorderBrush="#E0E0E0" BorderThickness="0,0,0,2">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Title Row -->
                <Grid Grid.Row="0" Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="💰 إدارة توصيلات الكراء"
                                   FontSize="32" FontWeight="Bold"
                                   Foreground="#1976D2" Margin="0,0,0,8"/>
                        <TextBlock Name="SubtitleText" Text="إنشاء وإدارة توصيلات الكراء لكل محل"
                                   FontSize="16" Foreground="#666"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Button Name="RefreshBtn" Content="🔄 تحديث"
                                Style="{StaticResource ModernButtonStyle}"
                                Click="RefreshBtn_Click"/>
                        <Button Name="ExportBtn" Content="📄 تصدير"
                                Style="{StaticResource WarningButtonStyle}"
                                Click="ExportBtn_Click"/>
                    </StackPanel>
                </Grid>

                <!-- Property Selection Row -->
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="300"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="🏢 اختيار المحل:"
                               FontSize="16" FontWeight="Medium"
                               VerticalAlignment="Center" Margin="0,0,15,0"/>

                    <ComboBox Name="PropertySelectionComboBox" Grid.Column="1"
                              Style="{StaticResource ModernComboBoxStyle}"
                              ItemContainerStyle="{StaticResource ModernComboBoxItemStyle}"
                              SelectionChanged="PropertySelectionComboBox_SelectionChanged"/>

                    <!-- Property Info Panel -->
                    <StackPanel Name="PropertyInfoPanel" Grid.Column="2"
                                Orientation="Horizontal" Margin="20,0,0,0"
                                Visibility="Collapsed">
                        <Border Background="#E3F2FD" CornerRadius="8" Padding="15,8" Margin="0,0,10,0">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="👤 المكتري:" FontWeight="Medium" Margin="0,0,8,0"/>
                                <TextBlock Name="CustomerNameText" FontWeight="Bold" Foreground="#1976D2"/>
                            </StackPanel>
                        </Border>
                        <Border Background="#E8F5E8" CornerRadius="8" Padding="15,8" Margin="0,0,10,0">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="💰 الإيجار:" FontWeight="Medium" Margin="0,0,8,0"/>
                                <TextBlock Name="MonthlyRentText" FontWeight="Bold" Foreground="#4CAF50"/>
                            </StackPanel>
                        </Border>
                        <Border Background="#FFF3E0" CornerRadius="8" Padding="15,8">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📞 الهاتف:" FontWeight="Medium" Margin="0,0,8,0"/>
                                <TextBlock Name="PhoneNumberText" FontWeight="Bold" Foreground="#FF9800"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>

                    <Button Name="AddReceiptBtn" Grid.Column="3" Content="➕ إضافة توصيل جديد"
                            Style="{StaticResource SuccessButtonStyle}"
                            Click="AddReceiptBtn_Click" IsEnabled="False"
                            FontSize="14" Padding="20,10"/>
                </Grid>
            </Grid>
        </Border>

        <!-- Statistics and Filters -->
        <Border Grid.Row="1" Background="White" Padding="30,20"
                BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Statistics Cards -->
                <Grid Grid.Row="0" Name="StatsGrid" Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <!-- Stats cards will be added programmatically -->
                </Grid>

                <!-- Filters Row -->
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="250"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="150"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="150"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="🔍 بحث:" VerticalAlignment="Center"
                               Margin="0,0,10,0" FontWeight="Medium" FontSize="14"/>
                    <TextBox Name="SearchTextBox" Grid.Column="1" Padding="10,8"
                             TextChanged="SearchTextBox_TextChanged"
                             FontSize="13" BorderBrush="#DDD" BorderThickness="1"/>

                    <TextBlock Grid.Column="2" Text="📅 الشهر:" VerticalAlignment="Center"
                               Margin="20,0,10,0" FontWeight="Medium" FontSize="14"/>
                    <ComboBox Name="MonthFilterComboBox" Grid.Column="3"
                              Style="{StaticResource ModernComboBoxStyle}"
                              ItemContainerStyle="{StaticResource ModernComboBoxItemStyle}"
                              SelectionChanged="MonthFilterComboBox_SelectionChanged"
                              FontSize="13"/>

                    <TextBlock Grid.Column="4" Text="📊 السنة:" VerticalAlignment="Center"
                               Margin="20,0,10,0" FontWeight="Medium" FontSize="14"/>
                    <ComboBox Name="YearFilterComboBox" Grid.Column="5"
                              Style="{StaticResource ModernComboBoxStyle}"
                              ItemContainerStyle="{StaticResource ModernComboBoxItemStyle}"
                              SelectionChanged="YearFilterComboBox_SelectionChanged"
                              FontSize="13"
                              ToolTip="اختر السنة لتصفية التوصيلات (النطاق: 2015 - 2030+)"/>

                    <Button Name="ClearFiltersBtn" Grid.Column="7" Content="🗑️ مسح الفلاتر"
                            Style="{StaticResource WarningButtonStyle}"
                            Click="ClearFiltersBtn_Click" FontSize="13"/>
                </Grid>
            </Grid>
        </Border>

        <!-- Receipts DataGrid -->
        <Border Grid.Row="2" Background="White" Margin="30,0,30,0"
                CornerRadius="12" BorderBrush="#E0E0E0" BorderThickness="1"
                Effect="{StaticResource DropShadowEffect}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Table Header -->
                <Border Grid.Row="0" Background="#F8F9FA"
                        CornerRadius="12,12,0,0" Padding="20,15">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="📋 توصيلات الكراء"
                                   FontSize="18" FontWeight="Bold"
                                   Foreground="#333" VerticalAlignment="Center"/>

                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <TextBlock Name="ReceiptCountText" Text="0 توصيل"
                                       FontSize="14" Foreground="#666"
                                       VerticalAlignment="Center" Margin="0,0,15,0"/>
                            <Button Name="PrintAllBtn" Content="🖨️ طباعة الكل"
                                    Style="{StaticResource ModernButtonStyle}"
                                    Click="PrintAllBtn_Click" IsEnabled="False"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- DataGrid -->
                <DataGrid Name="ReceiptsDataGrid" Grid.Row="1"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="None"
                          HeadersVisibility="Column"
                          Background="Transparent"
                          BorderThickness="0"
                          RowBackground="White"
                          AlternatingRowBackground="#FAFBFC"
                          FontSize="13"
                          RowHeight="55"
                          SelectionChanged="ReceiptsDataGrid_SelectionChanged"
                          MouseDoubleClick="ReceiptsDataGrid_MouseDoubleClick">

                    <DataGrid.ColumnHeaderStyle>
                        <Style TargetType="DataGridColumnHeader">
                            <Setter Property="Background" Value="#F1F3F4"/>
                            <Setter Property="Foreground" Value="#333"/>
                            <Setter Property="FontWeight" Value="Bold"/>
                            <Setter Property="FontSize" Value="13"/>
                            <Setter Property="Padding" Value="15,12"/>
                            <Setter Property="BorderBrush" Value="#E0E0E0"/>
                            <Setter Property="BorderThickness" Value="0,0,1,1"/>
                        </Style>
                    </DataGrid.ColumnHeaderStyle>

                    <DataGrid.Columns>
                        <DataGridTextColumn Header="📄 رقم التوصيل" Binding="{Binding ReceiptNumber}" Width="140">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="Foreground" Value="#1976D2"/>
                                    <Setter Property="Padding" Value="15,0"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="📅 التاريخ" Binding="{Binding PaymentDateDisplay}" Width="120">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="15,0"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="💰 المبلغ" Binding="{Binding AmountDisplay}" Width="130">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="Foreground" Value="#4CAF50"/>
                                    <Setter Property="Padding" Value="15,0"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="📆 الشهر المؤدى" Binding="{Binding PaymentPeriod}" Width="180">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="15,0"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="📝 الملاحظات" Binding="{Binding Notes}" Width="*">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="15,0"/>
                                    <Setter Property="TextWrapping" Value="Wrap"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="🖨️ حالة الطباعة" Binding="{Binding PrintStatusDisplay}" Width="120">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="15,0"/>
                                    <Setter Property="FontWeight" Value="Medium"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>

        <!-- Action Buttons -->
        <Border Grid.Row="3" Background="White" Padding="30,20"
                BorderBrush="#E0E0E0" BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Left side - Info -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Name="SelectionInfoText" Text="اختر توصيل للقيام بالعمليات"
                               FontSize="14" Foreground="#666" VerticalAlignment="Center"/>
                </StackPanel>

                <!-- Right side - Action Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Name="PreviewReceiptBtn" Content="👁️ معاينة"
                            Style="{StaticResource ModernButtonStyle}"
                            Click="PreviewReceiptBtn_Click" IsEnabled="False"
                            ToolTip="معاينة التوصيل قبل الطباعة"/>

                    <Button Name="PrintReceiptBtn" Content="🖨️ طباعة"
                            Style="{StaticResource SuccessButtonStyle}"
                            Click="PrintReceiptBtn_Click" IsEnabled="False"
                            ToolTip="طباعة التوصيل"/>

                    <Button Name="EditReceiptBtn" Content="✏️ تعديل"
                            Style="{StaticResource ModernButtonStyle}"
                            Click="EditReceiptBtn_Click" IsEnabled="False"
                            ToolTip="تعديل بيانات التوصيل"/>

                    <Button Name="DuplicateReceiptBtn" Content="📋 نسخ"
                            Style="{StaticResource WarningButtonStyle}"
                            Click="DuplicateReceiptBtn_Click" IsEnabled="False"
                            ToolTip="نسخ التوصيل وإنشاء توصيل جديد"/>

                    <Button Name="DeleteReceiptBtn" Content="🗑️ حذف"
                            Style="{StaticResource DangerButtonStyle}"
                            Click="DeleteReceiptBtn_Click" IsEnabled="False"
                            ToolTip="حذف التوصيل نهائياً"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
