using RentalManagement.Models;
using System.Globalization;
using System.Windows.Data;

namespace RentalManagement.Helpers
{
    public class PropertyTypeConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is PropertyType type)
            {
                return type switch
                {
                    PropertyType.Apartment => "شقة",
                    PropertyType.Shop => "محل",
                    _ => "غير محدد"
                };
            }
            return "غير محدد";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
