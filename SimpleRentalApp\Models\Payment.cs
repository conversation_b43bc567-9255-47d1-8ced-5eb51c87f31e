using System.ComponentModel.DataAnnotations;

namespace SimpleRentalApp.Models;

public enum PaymentStatus
{
    Unpaid = 1,
    Paid = 2,
    Overdue = 3,
    Partial = 4
}

public enum PaymentType
{
    Rent = 1,
    CleaningTax = 2,
    Combined = 3, // إيجار + ضريبة نظافة
    Deposit = 4,
    Other = 5
}

public class Payment
{
    public int Id { get; set; }
    
    [Required]
    public int PropertyId { get; set; }
    
    [Required]
    public int CustomerId { get; set; }
    
    public PaymentType Type { get; set; } = PaymentType.Combined;
    
    [Required]
    public decimal RentAmount { get; set; }
    
    public decimal CleaningTaxAmount { get; set; } = 0; // مبلغ ضريبة النظافة

    public int? TaxYear { get; set; } // السنة الضريبية لضريبة النظافة

    public decimal TotalAmount => RentAmount + CleaningTaxAmount;
    
    [Required]
    public DateTime DueDate { get; set; }
    
    public DateTime? PaymentDate { get; set; }
    
    public PaymentStatus Status { get; set; } = PaymentStatus.Unpaid;
    
    [StringLength(200)]
    public string Notes { get; set; } = string.Empty;

    [StringLength(50)]
    public string ReceiptNumber { get; set; } = string.Empty; // رقم التوصيل

    [StringLength(100)]
    public string PaymentMethod { get; set; } = "نقداً";

    public DateTime CreatedDate { get; set; } = DateTime.Now;
    public DateTime UpdatedDate { get; set; } = DateTime.Now;

    // Navigation properties
    public virtual Property Property { get; set; } = null!;
    public virtual Customer Customer { get; set; } = null!;
    public virtual List<RentReceipt> Receipts { get; set; } = new();
    
    // Calculated properties
    public bool IsOverdue => Status == PaymentStatus.Unpaid && DueDate < DateTime.Now;
    
    public string StatusDisplay => Status switch
    {
        PaymentStatus.Unpaid => "غير مدفوع",
        PaymentStatus.Paid => "مدفوع",
        PaymentStatus.Overdue => "متأخر",
        PaymentStatus.Partial => "مدفوع جزئياً",
        _ => "غير محدد"
    };
    
    public string TypeDisplay => Type switch
    {
        PaymentType.Rent => "إيجار",
        PaymentType.CleaningTax => "ضريبة نظافة",
        PaymentType.Combined => "إيجار + ضريبة نظافة",
        PaymentType.Deposit => "تأمين",
        PaymentType.Other => "أخرى",
        _ => "غير محدد"
    };
}
