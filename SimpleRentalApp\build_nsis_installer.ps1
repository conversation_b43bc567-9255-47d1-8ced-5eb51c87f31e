# Build NSIS Installer for Rental Management System
# بناء ملف التثبيت NSIS لنظام تدبير الكراء

Write-Host "========================================" -ForegroundColor Green
Write-Host "    Building NSIS Installer" -ForegroundColor Green
Write-Host "    بناء ملف التثبيت NSIS" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Step 1: Check for NSIS
Write-Host "[1/5] Checking for NSIS..." -ForegroundColor Cyan
Write-Host "[1/5] التحقق من وجود NSIS..." -ForegroundColor Cyan

$nsisPath = $null
$possiblePaths = @(
    "${env:ProgramFiles}\NSIS\makensis.exe",
    "${env:ProgramFiles(x86)}\NSIS\makensis.exe",
    "C:\Program Files\NSIS\makensis.exe",
    "C:\Program Files (x86)\NSIS\makensis.exe"
)

foreach ($path in $possiblePaths) {
    if (Test-Path $path) {
        $nsisPath = $path
        break
    }
}

if (-not $nsisPath) {
    Write-Host "Error: NSIS not found!" -ForegroundColor Red
    Write-Host "خطأ: لم يتم العثور على NSIS!" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please download and install NSIS from:" -ForegroundColor Yellow
    Write-Host "يرجى تحميل وتثبيت NSIS من:" -ForegroundColor Yellow
    Write-Host "https://nsis.sourceforge.io/Download" -ForegroundColor White
    Write-Host ""
    Write-Host "Make sure to install the Unicode version" -ForegroundColor Yellow
    Write-Host "تأكد من تثبيت الإصدار الذي يدعم Unicode" -ForegroundColor Yellow
    exit 1
}

Write-Host "Found NSIS at: $nsisPath" -ForegroundColor Green
Write-Host "تم العثور على NSIS في: $nsisPath" -ForegroundColor Green
Write-Host ""

# Step 2: Check required files
Write-Host "[2/5] Checking required files..." -ForegroundColor Cyan
Write-Host "[2/5] التحقق من الملفات المطلوبة..." -ForegroundColor Cyan

$requiredFiles = @{
    "RentalManagement_Installer.nsi" = "NSIS script file"
    "dist_new\SimpleRentalApp.exe" = "Application executable"
}

$missingFiles = @()
foreach ($file in $requiredFiles.Keys) {
    if (-not (Test-Path $file)) {
        $missingFiles += $file
        Write-Host "Missing: $file" -ForegroundColor Red
    } else {
        Write-Host "Found: $file" -ForegroundColor Green
    }
}

if ($missingFiles.Count -gt 0) {
    Write-Host ""
    Write-Host "Error: Missing required files!" -ForegroundColor Red
    Write-Host "خطأ: ملفات مطلوبة غير موجودة!" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please run first:" -ForegroundColor Yellow
    Write-Host "يرجى تشغيل أولاً:" -ForegroundColor Yellow
    Write-Host "powershell -ExecutionPolicy Bypass -File simple_redistribute.ps1" -ForegroundColor White
    exit 1
}

if (-not (Test-Path "app_icon.ico")) {
    Write-Host "Warning: Icon file not found" -ForegroundColor Yellow
    Write-Host "تحذير: ملف الأيقونة غير موجود" -ForegroundColor Yellow
    Write-Host "Default icon will be used" -ForegroundColor Yellow
}

Write-Host "All required files found" -ForegroundColor Green
Write-Host "جميع الملفات المطلوبة موجودة" -ForegroundColor Green
Write-Host ""

# Step 3: Build installer
Write-Host "[3/5] Building installer..." -ForegroundColor Cyan
Write-Host "[3/5] بناء ملف التثبيت..." -ForegroundColor Cyan

try {
    $process = Start-Process -FilePath $nsisPath -ArgumentList "RentalManagement_Installer.nsi" -Wait -PassThru -NoNewWindow
    
    if ($process.ExitCode -ne 0) {
        Write-Host "Error building installer (Exit code: $($process.ExitCode))" -ForegroundColor Red
        Write-Host "خطأ في بناء ملف التثبيت" -ForegroundColor Red
        exit 1
    }
}
catch {
    Write-Host "Error running NSIS: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "خطأ في تشغيل NSIS: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 4: Verify created installer
Write-Host "[4/5] Verifying created installer..." -ForegroundColor Cyan
Write-Host "[4/5] التحقق من ملف التثبيت المنشأ..." -ForegroundColor Cyan

$installerFile = "RentalManagement_Setup_v1.0.exe"
if (Test-Path $installerFile) {
    $fileInfo = Get-Item $installerFile
    $fileSizeMB = [math]::Round($fileInfo.Length / 1MB, 2)
    
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "Installer created successfully!" -ForegroundColor Green
    Write-Host "تم إنشاء ملف التثبيت بنجاح!" -ForegroundColor Green
    Write-Host ""
    Write-Host "File name: $installerFile" -ForegroundColor Cyan
    Write-Host "اسم الملف: $installerFile" -ForegroundColor Cyan
    Write-Host "File size: $fileSizeMB MB" -ForegroundColor Cyan
    Write-Host "حجم الملف: $fileSizeMB ميجابايت" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "File ready for distribution!" -ForegroundColor Green
    Write-Host "الملف جاهز للتوزيع!" -ForegroundColor Green
} else {
    Write-Host "Error: Installer file was not created" -ForegroundColor Red
    Write-Host "خطأ: لم يتم إنشاء ملف التثبيت" -ForegroundColor Red
    exit 1
}

# Step 5: Create installation info
Write-Host "[5/5] Creating installation info..." -ForegroundColor Cyan
Write-Host "[5/5] إنشاء معلومات التثبيت..." -ForegroundColor Cyan

$installerInfo = @"
Rental Management System - Installer Information
===============================================

File name: $installerFile
Version: 1.0.0
Developer: Hafid Abdo
Created: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
File size: $fileSizeMB MB

Features:
- Automatic installation with Arabic interface
- Start menu and desktop shortcuts creation
- System registration
- Uninstaller tool
- Full Arabic language support

System Requirements:
- Windows 10 or later (64-bit)
- Administrator privileges for installation
- 200 MB free space

Usage:
1. Run RentalManagement_Setup_v1.0.exe
2. Follow the installation wizard
3. Launch the program from Start menu or desktop

Login Credentials:
Username: hafid
Password: hafidos159357

Important Notes:
1. Test the installer before distribution
2. The installer fully supports Arabic language
3. Arabic shortcuts will be created
4. Can be uninstalled from Control Panel

The installer is ready for professional distribution!
"@

$installerInfo | Out-File -FilePath "INSTALLER_INFO.txt" -Encoding UTF8

Write-Host ""
Write-Host "Important notes:" -ForegroundColor Yellow
Write-Host "ملاحظات مهمة:" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. Test the installer before distribution" -ForegroundColor White
Write-Host "   تأكد من اختبار ملف التثبيت قبل التوزيع" -ForegroundColor White
Write-Host ""
Write-Host "2. The installer fully supports Arabic language" -ForegroundColor White
Write-Host "   ملف التثبيت يدعم اللغة العربية بالكامل" -ForegroundColor White
Write-Host ""
Write-Host "3. Arabic shortcuts will be created" -ForegroundColor White
Write-Host "   سيتم إنشاء اختصارات باللغة العربية" -ForegroundColor White
Write-Host ""
Write-Host "4. Can be uninstalled from Control Panel" -ForegroundColor White
Write-Host "   يمكن إلغاء التثبيت من لوحة التحكم" -ForegroundColor White
Write-Host ""

Write-Host "NSIS Installer build completed successfully!" -ForegroundColor Green
Write-Host "تم بناء ملف التثبيت NSIS بنجاح!" -ForegroundColor Green
