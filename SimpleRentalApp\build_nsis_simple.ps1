# Simple NSIS Builder for Rental Management System

Write-Host "========================================" -ForegroundColor Green
Write-Host "    Building NSIS Installer" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Check for NSIS
Write-Host "[1/4] Checking for NSIS..." -ForegroundColor Cyan

$nsisPath = $null
$possiblePaths = @(
    "${env:ProgramFiles}\NSIS\makensis.exe",
    "${env:ProgramFiles(x86)}\NSIS\makensis.exe"
)

foreach ($path in $possiblePaths) {
    if (Test-Path $path) {
        $nsisPath = $path
        break
    }
}

if (-not $nsisPath) {
    Write-Host "Error: NSIS not found!" -ForegroundColor Red
    Write-Host "Please download and install NSIS from:" -ForegroundColor Yellow
    Write-Host "https://nsis.sourceforge.io/Download" -ForegroundColor White
    Write-Host "Make sure to install the Unicode version" -ForegroundColor Yellow
    exit 1
}

Write-Host "Found NSIS at: $nsisPath" -ForegroundColor Green
Write-Host ""

# Check required files
Write-Host "[2/4] Checking required files..." -ForegroundColor Cyan

if (-not (Test-Path "RentalManagement_Installer.nsi")) {
    Write-Host "Error: NSIS script file not found!" -ForegroundColor Red
    Write-Host "Required: RentalManagement_Installer.nsi" -ForegroundColor Yellow
    exit 1
}

if (-not (Test-Path "dist_new\SimpleRentalApp.exe")) {
    Write-Host "Error: Application files not found!" -ForegroundColor Red
    Write-Host "Please run first:" -ForegroundColor Yellow
    Write-Host "powershell -ExecutionPolicy Bypass -File simple_redistribute.ps1" -ForegroundColor White
    exit 1
}

Write-Host "All required files found" -ForegroundColor Green
Write-Host ""

# Build installer
Write-Host "[3/4] Building installer..." -ForegroundColor Cyan

try {
    $process = Start-Process -FilePath $nsisPath -ArgumentList "RentalManagement_Installer.nsi" -Wait -PassThru -NoNewWindow
    
    if ($process.ExitCode -ne 0) {
        Write-Host "Error building installer (Exit code: $($process.ExitCode))" -ForegroundColor Red
        exit 1
    }
}
catch {
    Write-Host "Error running NSIS: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Verify result
Write-Host "[4/4] Verifying result..." -ForegroundColor Cyan

$installerFile = "RentalManagement_Setup_v1.0.exe"
if (Test-Path $installerFile) {
    $fileInfo = Get-Item $installerFile
    $fileSizeMB = [math]::Round($fileInfo.Length / 1MB, 2)
    
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "SUCCESS: Installer created!" -ForegroundColor Green
    Write-Host ""
    Write-Host "File: $installerFile" -ForegroundColor Cyan
    Write-Host "Size: $fileSizeMB MB" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Ready for distribution!" -ForegroundColor Green
} else {
    Write-Host "Error: Installer file was not created" -ForegroundColor Red
    exit 1
}

# Create info file
$info = @"
NSIS Installer Build Information
===============================

File: $installerFile
Size: $fileSizeMB MB
Created: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
Developer: Hafid Abdo
Version: 1.0.0

Features:
- Arabic interface
- Automatic installation
- Start menu shortcuts
- Desktop shortcut
- Uninstaller
- System registration

Requirements:
- Windows 10+ (64-bit)
- Admin privileges
- 200 MB space

Usage:
1. Run the installer
2. Follow the wizard
3. Launch from Start menu

Login:
Username: hafid
Password: hafidos159357

Ready for distribution!
"@

$info | Out-File -FilePath "INSTALLER_BUILD_INFO.txt" -Encoding UTF8

Write-Host "Build completed successfully!" -ForegroundColor Green
