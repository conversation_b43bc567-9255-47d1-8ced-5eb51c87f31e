﻿#pragma checksum "..\..\..\..\..\Windows\EditWhatsAppNotificationWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "428158BB3F183E1CF2DA603EC21DE1A0904443A5"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleRentalApp.Windows {
    
    
    /// <summary>
    /// EditWhatsAppNotificationWindow
    /// </summary>
    public partial class EditWhatsAppNotificationWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 167 "..\..\..\..\..\Windows\EditWhatsAppNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CustomerNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\..\..\Windows\EditWhatsAppNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PropertyNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\..\..\Windows\EditWhatsAppNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PhoneNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\..\..\Windows\EditWhatsAppNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\..\..\Windows\EditWhatsAppNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DueDatePicker;
        
        #line default
        #line hidden
        
        
        #line 217 "..\..\..\..\..\Windows\EditWhatsAppNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TemplateComboBox;
        
        #line default
        #line hidden
        
        
        #line 225 "..\..\..\..\..\Windows\EditWhatsAppNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MessageTextBox;
        
        #line default
        #line hidden
        
        
        #line 243 "..\..\..\..\..\Windows\EditWhatsAppNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PreviewTextBlock;
        
        #line default
        #line hidden
        
        
        #line 253 "..\..\..\..\..\Windows\EditWhatsAppNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CharacterCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 255 "..\..\..\..\..\Windows\EditWhatsAppNotificationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MessageLengthTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleRentalApp;V1.0.0.0;component/windows/editwhatsappnotificationwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\EditWhatsAppNotificationWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 10 "..\..\..\..\..\Windows\EditWhatsAppNotificationWindow.xaml"
            ((SimpleRentalApp.Windows.EditWhatsAppNotificationWindow)(target)).KeyDown += new System.Windows.Input.KeyEventHandler(this.Window_KeyDown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.CustomerNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.PropertyNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.PhoneNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 183 "..\..\..\..\..\Windows\EditWhatsAppNotificationWindow.xaml"
            this.PhoneNumberTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.PhoneNumberTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.AmountTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 190 "..\..\..\..\..\Windows\EditWhatsAppNotificationWindow.xaml"
            this.AmountTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.AmountTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.DueDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 197 "..\..\..\..\..\Windows\EditWhatsAppNotificationWindow.xaml"
            this.DueDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DueDatePicker_SelectedDateChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.TemplateComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 218 "..\..\..\..\..\Windows\EditWhatsAppNotificationWindow.xaml"
            this.TemplateComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.TemplateComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 220 "..\..\..\..\..\Windows\EditWhatsAppNotificationWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshTemplatesBtn_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.MessageTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 229 "..\..\..\..\..\Windows\EditWhatsAppNotificationWindow.xaml"
            this.MessageTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.MessageTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.PreviewTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.CharacterCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.MessageLengthTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            
            #line 280 "..\..\..\..\..\Windows\EditWhatsAppNotificationWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveBtn_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 284 "..\..\..\..\..\Windows\EditWhatsAppNotificationWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CancelBtn_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

