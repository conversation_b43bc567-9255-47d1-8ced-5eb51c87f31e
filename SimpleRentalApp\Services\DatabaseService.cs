using Microsoft.EntityFrameworkCore;
using SimpleRentalApp.Data;
using SimpleRentalApp.Models;
using System.Data.Common;
using System.IO;

namespace SimpleRentalApp.Services;

public class DatabaseService : IDisposable
{
    private static DatabaseService? _instance;
    public static DatabaseService Instance => _instance ??= new DatabaseService();

    private DatabaseService() { }

    public void Dispose()
    {
        // تنظيف الموارد إذا لزم الأمر
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// إنشاء سياق قاعدة البيانات
    /// </summary>
    public RentalDbContext CreateContext()
    {
        return new RentalDbContext();
    }
    
    public async Task InitializeDatabaseAsync()
    {
        using var context = new RentalDbContext();

        try
        {
            // تأكد من وجود قاعدة البيانات وإنشاؤها إذا لم تكن موجودة
            await context.Database.EnsureCreatedAsync();

            // تطبيق أي migrations معلقة
            var pendingMigrations = await context.Database.GetPendingMigrationsAsync();
            if (pendingMigrations.Any())
            {
                await context.Database.MigrateAsync();
            }

            // إنشاء البيانات الأساسية فقط إذا لم تكن موجودة
            await EnsureBasicDataAsync();

            // Update database schema for receipt number uniqueness
            await UpdateReceiptNumberConstraintAsync();
        }
        catch (Exception)
        {
            // في حالة فشل التحديث، أعد إنشاء قاعدة البيانات
            await context.Database.EnsureDeletedAsync();
            await context.Database.EnsureCreatedAsync();
            await EnsureBasicDataAsync();
        }
    }

    private async Task UpdateReceiptNumberConstraintAsync()
    {
        using var context = new RentalDbContext();
        try
        {
            var connection = context.Database.GetDbConnection();
            await connection.OpenAsync();

            // Check if the old unique constraint exists
            var checkConstraintSql = @"
                SELECT name FROM sqlite_master
                WHERE type='index' AND name LIKE '%ReceiptNumber%' AND sql LIKE '%UNIQUE%'";

            using var checkCommand = connection.CreateCommand();
            checkCommand.CommandText = checkConstraintSql;
            var existingConstraint = await checkCommand.ExecuteScalarAsync();

            if (existingConstraint != null)
            {
                // Drop the old unique constraint on ReceiptNumber only
                var dropConstraintSql = $"DROP INDEX IF EXISTS {existingConstraint}";
                using var dropCommand = connection.CreateCommand();
                dropCommand.CommandText = dropConstraintSql;
                await dropCommand.ExecuteNonQueryAsync();
            }

            // Create new composite unique constraint
            var createConstraintSql = @"
                CREATE UNIQUE INDEX IF NOT EXISTS IX_RentReceipts_ReceiptNumber_ContractId
                ON RentReceipts (ReceiptNumber, ContractId)";
            using var createCommand = connection.CreateCommand();
            createCommand.CommandText = createConstraintSql;
            await createCommand.ExecuteNonQueryAsync();
        }
        catch (Exception ex)
        {
            // Log error but don't fail initialization
            System.Diagnostics.Debug.WriteLine($"Error updating receipt number constraint: {ex.Message}");
        }
    }

    private async Task EnsureBasicDataAsync()
    {
        using var context = new RentalDbContext();

        // تحقق من وجود المستخدمين
        if (!await context.Users.AnyAsync())
        {
            // إنشاء المستخدم الافتراضي فقط
            var defaultUser = new User
            {
                Username = "hafid",
                FullName = "حفيظ عبدو",
                PasswordHash = "hafidos159357",
                Email = "<EMAIL>",
                Role = UserRole.Admin,
                CreatedDate = DateTime.Now
            };
            context.Users.Add(defaultUser);
            await context.SaveChangesAsync();
        }

        // إنشاء بيانات تجريبية فقط إذا كانت قاعدة البيانات فارغة تماماً
        if (!await context.Properties.AnyAsync() && !await context.Customers.AnyAsync())
        {
            await GeneratePaymentsAsync();
            await GenerateContractsAsync();
            await GenerateRentIncreasesAsync();
        }
    }
    
    private async Task GeneratePaymentsAsync()
    {
        using var context = new RentalDbContext();
        
        // Check if payments already exist
        if (await context.Payments.AnyAsync())
            return;
            
        var rentedProperties = await context.Properties
            .Where(p => p.Status == PropertyStatus.Rented && p.CustomerId.HasValue)
            .ToListAsync();
            
        var payments = new List<Payment>();
        
        foreach (var property in rentedProperties)
        {
            // Generate payments for the last 3 months and next 2 months
            for (int i = -3; i <= 2; i++)
            {
                var dueDate = DateTime.Now.AddMonths(i).Date;
                var status = i < 0 ? PaymentStatus.Paid : 
                           i == 0 ? PaymentStatus.Unpaid : 
                           PaymentStatus.Unpaid;
                           
                if (i < -1) status = PaymentStatus.Paid;
                else if (i == -1) status = PaymentStatus.Overdue;
                
                var payment = new Payment
                {
                    PropertyId = property.Id,
                    CustomerId = property.CustomerId!.Value,
                    Type = PaymentType.Combined,
                    RentAmount = property.MonthlyRent,
                    CleaningTaxAmount = property.CleaningTaxPerPayment,
                    DueDate = dueDate,
                    PaymentDate = status == PaymentStatus.Paid ? dueDate.AddDays(Random.Shared.Next(1, 5)) : null,
                    Status = status,
                    PaymentMethod = "نقداً",
                    CreatedDate = DateTime.Now.AddDays(-30 + (i * 7))
                };
                
                payments.Add(payment);
            }
        }
        
        context.Payments.AddRange(payments);
        await context.SaveChangesAsync();
    }

    private async Task GenerateContractsAsync()
    {
        using var context = new RentalDbContext();

        // Check if contracts already exist
        if (await context.Contracts.AnyAsync())
            return;

        var properties = await context.Properties.Include(p => p.Customer).ToListAsync();

        foreach (var property in properties.Where(p => p.CustomerId.HasValue))
        {
            // Create a contract for each property
            var startDate = DateTime.Today.AddMonths(-Random.Shared.Next(1, 24)); // Random start date in the past 2 years
            var contractDuration = Random.Shared.Next(6, 36); // 6 months to 3 years
            var endDate = startDate.AddMonths(contractDuration);

            var contract = new Contract
            {
                CustomerId = property.CustomerId!.Value,
                PropertyId = property.Id,
                StartDate = startDate,
                EndDate = endDate,
                InitialRentAmount = property.MonthlyRent,
                RentIncreaseCount = Random.Shared.Next(0, 3),
                RentIncreasePercentage = Random.Shared.Next(0, 15),
                Notes = $"عقد إيجار {property.Name} - تم إنشاؤه تلقائياً",
                CreatedDate = startDate
            };

            context.Contracts.Add(contract);
        }

        await context.SaveChangesAsync();
    }

    private async Task GenerateRentIncreasesAsync()
    {
        using var context = new RentalDbContext();

        // Check if rent increases already exist
        if (await context.RentIncreases.AnyAsync())
            return;

        var contracts = await context.Contracts.ToListAsync();

        foreach (var contract in contracts.Where(c => c.RentIncreaseCount > 0))
        {
            var currentAmount = contract.InitialRentAmount;
            var increaseDate = contract.StartDate.AddMonths(6); // First increase after 6 months

            for (int i = 0; i < contract.RentIncreaseCount; i++)
            {
                var previousAmount = currentAmount;
                var newAmount = previousAmount + (previousAmount * (contract.RentIncreasePercentage / 100));

                var rentIncrease = new RentIncrease
                {
                    ContractId = contract.Id,
                    IncreaseDate = increaseDate,
                    PreviousAmount = previousAmount,
                    NewAmount = newAmount,
                    IncreasePercentage = contract.RentIncreasePercentage,
                    Reason = $"زيادة دورية رقم {i + 1} حسب شروط العقد",
                    CreatedDate = increaseDate
                };

                context.RentIncreases.Add(rentIncrease);

                currentAmount = newAmount;
                increaseDate = increaseDate.AddYears(1); // Next increase after 1 year
            }
        }

        await context.SaveChangesAsync();
    }

    // User methods
    public async Task<User?> GetUserByUsernameAsync(string username)
    {
        using var context = new RentalDbContext();
        return await context.Users.FirstOrDefaultAsync(u => u.Username == username);
    }

    public async Task<List<User>> GetUsersAsync()
    {
        using var context = new RentalDbContext();
        return await context.Users.OrderBy(u => u.FullName).ToListAsync();
    }

    public async Task<User> SaveUserAsync(User user)
    {
        using var context = new RentalDbContext();

        if (user.Id == 0)
        {
            context.Users.Add(user);
        }
        else
        {
            context.Users.Update(user);
        }

        await context.SaveChangesAsync();
        return user;
    }

    // Customer methods
    public async Task<List<Customer>> GetCustomersAsync()
    {
        using var context = new RentalDbContext();
        return await context.Customers
            .Include(c => c.Properties)
            .Include(c => c.Payments)
            .OrderBy(c => c.FirstName)
            .ToListAsync();
    }
    
    public async Task<Customer?> GetCustomerAsync(int id)
    {
        using var context = new RentalDbContext();
        return await context.Customers
            .Include(c => c.Properties)
            .Include(c => c.Payments)
            .FirstOrDefaultAsync(c => c.Id == id);
    }
    
    public async Task<Customer> SaveCustomerAsync(Customer customer)
    {
        using var context = new RentalDbContext();
        
        if (customer.Id == 0)
        {
            context.Customers.Add(customer);
        }
        else
        {
            context.Customers.Update(customer);
        }
        
        await context.SaveChangesAsync();
        return customer;
    }
    
    public async Task DeleteCustomerAsync(int id)
    {
        using var context = new RentalDbContext();
        var customer = await context.Customers.FindAsync(id);
        if (customer != null)
        {
            context.Customers.Remove(customer);
            await context.SaveChangesAsync();
        }
    }

    // Contract methods
    public async Task<List<Contract>> GetContractsAsync()
    {
        using var context = new RentalDbContext();
        return await context.Contracts
            .Include(c => c.Customer)
            .Include(c => c.Property)
            .Include(c => c.Attachments)
            .Include(c => c.Augments.Where(a => a.IsActive))
            .OrderByDescending(c => c.CreatedDate)
            .ToListAsync();
    }

    public async Task<Contract?> GetContractByIdAsync(int contractId)
    {
        using var context = new RentalDbContext();
        return await context.Contracts
            .Include(c => c.Customer)
            .Include(c => c.Property)
            .Include(c => c.Attachments)
            .Include(c => c.RentIncreases.OrderBy(r => r.IncreaseDate))
            .Include(c => c.Augments.Where(a => a.IsActive).OrderByDescending(a => a.CreatedDate))
            .FirstOrDefaultAsync(c => c.Id == contractId);
    }

    public async Task<Contract> SaveContractAsync(Contract contract)
    {
        using var context = new RentalDbContext();

        if (contract.Id == 0)
        {
            context.Contracts.Add(contract);
        }
        else
        {
            context.Contracts.Update(contract);
        }

        await context.SaveChangesAsync();
        return contract;
    }

    public async Task DeleteContractAsync(int contractId)
    {
        using var context = new RentalDbContext();
        var contract = await context.Contracts.FindAsync(contractId);
        if (contract != null)
        {
            context.Contracts.Remove(contract);
            await context.SaveChangesAsync();
        }
    }

    public async Task<List<Contract>> GetExpiringContractsAsync(int daysAhead = 30)
    {
        using var context = new RentalDbContext();
        var cutoffDate = DateTime.Today.AddDays(daysAhead);

        return await context.Contracts
            .Include(c => c.Customer)
            .Include(c => c.Property)
            .Where(c => c.EndDate <= cutoffDate && c.EndDate >= DateTime.Today)
            .OrderBy(c => c.EndDate)
            .ToListAsync();
    }

    // Contract Attachment methods
    public async Task<ContractAttachment> SaveAttachmentAsync(ContractAttachment attachment)
    {
        using var context = new RentalDbContext();

        if (attachment.Id == 0)
        {
            context.ContractAttachments.Add(attachment);
        }
        else
        {
            context.ContractAttachments.Update(attachment);
        }

        await context.SaveChangesAsync();
        return attachment;
    }

    public async Task DeleteAttachmentAsync(int attachmentId)
    {
        using var context = new RentalDbContext();
        var attachment = await context.ContractAttachments.FindAsync(attachmentId);
        if (attachment != null)
        {
            // Delete physical file
            if (System.IO.File.Exists(attachment.FilePath))
            {
                System.IO.File.Delete(attachment.FilePath);
            }

            context.ContractAttachments.Remove(attachment);
            await context.SaveChangesAsync();
        }
    }

    // Contract Augment methods
    public async Task<List<ContractAugment>> GetContractAugmentsAsync(int contractId)
    {
        using var context = new RentalDbContext();
        return await context.ContractAugments
            .Include(a => a.Contract)
            .Where(a => a.ContractId == contractId && a.IsActive)
            .OrderByDescending(a => a.CreatedDate)
            .ToListAsync();
    }

    public async Task<ContractAugment> SaveContractAugmentAsync(ContractAugment augment)
    {
        using var context = new RentalDbContext();

        if (augment.Id == 0)
        {
            context.ContractAugments.Add(augment);
        }
        else
        {
            context.ContractAugments.Update(augment);
        }

        await context.SaveChangesAsync();
        return augment;
    }

    public async Task DeleteContractAugmentAsync(int augmentId)
    {
        using var context = new RentalDbContext();
        var augment = await context.ContractAugments.FindAsync(augmentId);
        if (augment != null)
        {
            // Soft delete - mark as inactive instead of removing
            augment.IsActive = false;
            context.ContractAugments.Update(augment);
            await context.SaveChangesAsync();
        }
    }

    public async Task<decimal> GetContractTotalAugmentsAsync(int contractId)
    {
        using var context = new RentalDbContext();
        return await context.ContractAugments
            .Where(a => a.ContractId == contractId && a.IsActive)
            .SumAsync(a => a.CalculatedAmount);
    }

    // Rent Receipt methods
    public async Task<List<RentReceipt>> GetContractReceiptsAsync(int contractId)
    {
        using var context = new RentalDbContext();
        return await context.RentReceipts
            .Include(r => r.Contract)
            .ThenInclude(c => c.Customer)
            .Include(r => r.Contract)
            .ThenInclude(c => c.Property)
            .Where(r => r.ContractId == contractId)
            .OrderByDescending(r => r.CreatedDate)
            .ToListAsync();
    }

    public async Task<RentReceipt> SaveRentReceiptAsync(RentReceipt receipt)
    {
        using var context = new RentalDbContext();

        try
        {
            // Ensure database schema is up to date
            await EnsureReceiptSchemaAsync(context);

            if (receipt.Id == 0)
            {
                // Generate receipt number if not provided
                if (string.IsNullOrEmpty(receipt.ReceiptNumber))
                {
                    var lastSequence = await GetLastReceiptSequenceAsync();
                    receipt.ReceiptNumber = ReceiptNumberGenerator.GenerateSequentialReceiptNumber(lastSequence);
                }

                // If PaymentId is provided but ContractId is 0, try to find a valid contract
                if (receipt.PaymentId.HasValue && receipt.ContractId == 0)
                {
                    var payment = await context.Payments
                        .Include(p => p.Property)
                        .Include(p => p.Customer)
                        .FirstOrDefaultAsync(p => p.Id == receipt.PaymentId.Value);

                    if (payment != null)
                    {
                        // Try to find an active contract for this customer and property
                        var contract = await context.Contracts
                            .Where(c => c.CustomerId == payment.CustomerId &&
                                       c.PropertyId == payment.PropertyId &&
                                       c.StartDate <= DateTime.Now &&
                                       c.EndDate >= DateTime.Now)
                            .FirstOrDefaultAsync();

                        if (contract != null)
                        {
                            receipt.ContractId = contract.Id;
                        }
                        else
                        {
                            // If no active contract found, create a temporary one or use the first available
                            var anyContract = await context.Contracts
                                .Where(c => c.CustomerId == payment.CustomerId && c.PropertyId == payment.PropertyId)
                                .FirstOrDefaultAsync();

                            if (anyContract != null)
                            {
                                receipt.ContractId = anyContract.Id;
                            }
                            else
                            {
                                // Create a minimal contract for this receipt
                                var newContract = new Contract
                                {
                                    CustomerId = payment.CustomerId,
                                    PropertyId = payment.PropertyId,
                                    StartDate = DateTime.Now.AddMonths(-1),
                                    EndDate = DateTime.Now.AddYears(1),
                                    InitialRentAmount = payment.RentAmount,
                                    RentIncreaseCount = 0,
                                    RentIncreasePercentage = 0,
                                    Notes = "عقد تلقائي لإيصال الكراء",
                                    CreatedDate = DateTime.Now
                                };

                                context.Contracts.Add(newContract);
                                await context.SaveChangesAsync();
                                receipt.ContractId = newContract.Id;
                            }
                        }
                    }
                }

                context.RentReceipts.Add(receipt);
            }
            else
            {
                context.RentReceipts.Update(receipt);
            }

            await context.SaveChangesAsync();
            return receipt;
        }
        catch (Exception ex)
        {
            throw new Exception($"خطأ في حفظ الإيصال: {ex.Message}", ex);
        }
    }

    public async Task<RentReceipt> UpdateRentReceiptAsync(RentReceipt receipt)
    {
        using var context = new RentalDbContext();

        try
        {
            context.RentReceipts.Update(receipt);
            await context.SaveChangesAsync();
            return receipt;
        }
        catch (Exception ex)
        {
            throw new Exception($"خطأ في تحديث الإيصال: {ex.Message}", ex);
        }
    }

    private async Task EnsureReceiptSchemaAsync(RentalDbContext context)
    {
        try
        {
            // Check if new columns exist, if not add them
            var connection = context.Database.GetDbConnection();
            await connection.OpenAsync();

            var command = connection.CreateCommand();
            command.CommandText = "PRAGMA table_info(RentReceipts)";

            var columns = new List<string>();
            using (var reader = await command.ExecuteReaderAsync())
            {
                while (await reader.ReadAsync())
                {
                    columns.Add(reader.GetString(1)); // Column name is at index 1
                }
            }

            // Add missing columns
            if (!columns.Contains("PeriodStartDate"))
            {
                command.CommandText = "ALTER TABLE RentReceipts ADD COLUMN PeriodStartDate TEXT";
                await command.ExecuteNonQueryAsync();
            }

            if (!columns.Contains("PeriodEndDate"))
            {
                command.CommandText = "ALTER TABLE RentReceipts ADD COLUMN PeriodEndDate TEXT";
                await command.ExecuteNonQueryAsync();
            }

            if (!columns.Contains("ShowLegalNotices"))
            {
                command.CommandText = "ALTER TABLE RentReceipts ADD COLUMN ShowLegalNotices INTEGER DEFAULT 1";
                await command.ExecuteNonQueryAsync();
            }

            await connection.CloseAsync();
        }
        catch (Exception ex)
        {
            // If schema update fails, continue with existing schema
            System.Diagnostics.Debug.WriteLine($"Schema update warning: {ex.Message}");
        }
    }

    public async Task<RentReceipt?> GetReceiptByIdAsync(int receiptId)
    {
        using var context = new RentalDbContext();
        return await context.RentReceipts
            .Include(r => r.Contract)
            .ThenInclude(c => c.Customer)
            .Include(r => r.Contract)
            .ThenInclude(c => c.Property)
            .Include(r => r.Payment)
            .ThenInclude(p => p!.Customer)
            .Include(r => r.Payment)
            .ThenInclude(p => p!.Property)
            .FirstOrDefaultAsync(r => r.Id == receiptId);
    }

    public async Task<int> GetLastReceiptSequenceAsync()
    {
        using var context = new RentalDbContext();
        var currentYear = DateTime.Now.Year;
        var yearPrefix = $"R{currentYear}-";

        var lastReceipt = await context.RentReceipts
            .Where(r => r.ReceiptNumber.StartsWith(yearPrefix))
            .OrderByDescending(r => r.ReceiptNumber)
            .FirstOrDefaultAsync();

        if (lastReceipt == null) return 0;

        var numberPart = lastReceipt.ReceiptNumber.Substring(yearPrefix.Length);
        return int.TryParse(numberPart, out int sequence) ? sequence : 0;
    }

    public async Task<int> GetLastPropertyReceiptSequenceAsync(int propertyId)
    {
        using var context = new RentalDbContext();
        var currentYear = DateTime.Now.Year;
        var propertyPrefix = $"R{currentYear}-{propertyId:D3}-";

        var lastReceipt = await context.RentReceipts
            .Where(r => r.ReceiptNumber.StartsWith(propertyPrefix))
            .OrderByDescending(r => r.ReceiptNumber)
            .FirstOrDefaultAsync();

        if (lastReceipt == null) return 0;

        var numberPart = lastReceipt.ReceiptNumber.Substring(propertyPrefix.Length);
        return int.TryParse(numberPart, out int sequence) ? sequence : 0;
    }

    public async Task MarkReceiptAsPrintedAsync(int receiptId)
    {
        using var context = new RentalDbContext();
        var receipt = await context.RentReceipts.FindAsync(receiptId);
        if (receipt != null)
        {
            receipt.IsPrinted = true;
            receipt.PrintedDate = DateTime.Now;
            context.RentReceipts.Update(receipt);
            await context.SaveChangesAsync();
        }
    }

    public async Task<List<RentReceipt>> GetRentReceiptsAsync()
    {
        using var context = new RentalDbContext();
        return await context.RentReceipts
            .Include(r => r.Contract)
            .ThenInclude(c => c.Property)
            .Include(r => r.Contract)
            .ThenInclude(c => c.Customer)
            .OrderByDescending(r => r.PaymentDate)
            .ToListAsync();
    }

    public async Task<List<RentReceipt>> GetRentReceiptsForPropertyAsync(int propertyId)
    {
        using var context = new RentalDbContext();
        return await context.RentReceipts
            .Include(r => r.Contract)
            .ThenInclude(c => c.Property)
            .Include(r => r.Contract)
            .ThenInclude(c => c.Customer)
            .Where(r => r.Contract.PropertyId == propertyId)
            .OrderByDescending(r => r.PaymentDate)
            .ToListAsync();
    }

    public async Task DeleteRentReceiptAsync(int id)
    {
        using var context = new RentalDbContext();
        var receipt = await context.RentReceipts.FindAsync(id);
        if (receipt != null)
        {
            context.RentReceipts.Remove(receipt);
            await context.SaveChangesAsync();
        }
    }

    public async Task<string> GetNextReceiptNumberForPropertyAsync(int propertyId)
    {
        using var context = new RentalDbContext();

        // Get all receipts for this property with R-XXX format
        var propertyReceipts = await context.RentReceipts
            .Where(r => r.Contract != null && r.Contract.PropertyId == propertyId)
            .Where(r => r.ReceiptNumber.StartsWith("R-"))
            .ToListAsync();

        if (propertyReceipts.Count == 0)
            return "R-001";

        // Extract numbers and find the highest
        var maxNumber = 0;
        foreach (var receipt in propertyReceipts)
        {
            var numberPart = receipt.ReceiptNumber.Substring(2); // Remove "R-"
            if (int.TryParse(numberPart, out int number) && number > maxNumber)
            {
                maxNumber = number;
            }
        }

        return $"R-{(maxNumber + 1):D3}";
    }

    // CleaningTaxReceipt methods
    public async Task<List<CleaningTaxReceipt>> GetCleaningTaxReceiptsAsync()
    {
        using var context = new RentalDbContext();
        return await context.CleaningTaxReceipts
            .Include(r => r.Property)
                .ThenInclude(p => p.Customer)
            .Include(r => r.Payment)
            .OrderByDescending(r => r.CreatedDate)
            .ToListAsync();
    }

    public async Task<List<CleaningTaxReceipt>> GetCleaningTaxReceiptsForPropertyAsync(int propertyId)
    {
        using var context = new RentalDbContext();
        return await context.CleaningTaxReceipts
            .Include(r => r.Property)
                .ThenInclude(p => p.Customer)
            .Include(r => r.Payment)
            .Where(r => r.PropertyId == propertyId)
            .OrderByDescending(r => r.CreatedDate)
            .ToListAsync();
    }

    public async Task<CleaningTaxReceipt?> GetCleaningTaxReceiptByIdAsync(int id)
    {
        using var context = new RentalDbContext();
        return await context.CleaningTaxReceipts
            .Include(r => r.Property)
                .ThenInclude(p => p.Customer)
            .Include(r => r.Payment)
            .FirstOrDefaultAsync(r => r.Id == id);
    }

    public async Task<CleaningTaxReceipt> SaveCleaningTaxReceiptAsync(CleaningTaxReceipt receipt)
    {
        using var context = new RentalDbContext();

        if (receipt.Id == 0)
        {
            context.CleaningTaxReceipts.Add(receipt);
        }
        else
        {
            context.CleaningTaxReceipts.Update(receipt);
        }

        await context.SaveChangesAsync();
        return receipt;
    }

    public async Task DeleteCleaningTaxReceiptAsync(int id)
    {
        using var context = new RentalDbContext();
        var receipt = await context.CleaningTaxReceipts.FindAsync(id);
        if (receipt != null)
        {
            context.CleaningTaxReceipts.Remove(receipt);
            await context.SaveChangesAsync();
        }
    }

    public async Task<string> GetNextCleaningTaxReceiptNumberAsync(int propertyId, int taxYear)
    {
        using var context = new RentalDbContext();

        // Get all cleaning tax receipts for this property and tax year
        var propertyReceipts = await context.CleaningTaxReceipts
            .Where(r => r.PropertyId == propertyId && r.TaxYear == taxYear)
            .Where(r => r.ReceiptNumber.StartsWith($"TSC{taxYear}-"))
            .ToListAsync();

        if (propertyReceipts.Count == 0)
            return $"TSC{taxYear}-001";

        // Extract numbers and find the highest
        var maxNumber = 0;
        var prefix = $"TSC{taxYear}-";

        foreach (var receipt in propertyReceipts)
        {
            var numberPart = receipt.ReceiptNumber.Substring(prefix.Length);
            if (int.TryParse(numberPart, out int number) && number > maxNumber)
            {
                maxNumber = number;
            }
        }

        return $"TSC{taxYear}-{(maxNumber + 1):D3}";
    }

    public async Task<Contract?> GetActiveContractForPropertyAsync(int propertyId)
    {
        using var context = new RentalDbContext();
        var currentDate = DateTime.Now;

        return await context.Contracts
            .Include(c => c.Property)
            .Include(c => c.Customer)
            .Where(c => c.PropertyId == propertyId &&
                       c.StartDate <= currentDate &&
                       c.EndDate >= currentDate)
            .OrderByDescending(c => c.StartDate)
            .FirstOrDefaultAsync();
    }

    public async Task MarkCleaningTaxReceiptAsPrintedAsync(int receiptId)
    {
        // For now, just return as we don't have CleaningTaxReceipt table yet
        // TODO: Implement when CleaningTaxReceipt table is added to database
        await Task.CompletedTask;
    }

    // Property methods
    public async Task<List<Property>> GetPropertiesAsync()
    {
        using var context = new RentalDbContext();
        return await context.Properties
            .Include(p => p.Customer)
            .Include(p => p.Payments)
            .OrderBy(p => p.Name)
            .ToListAsync();
    }
    
    public async Task<Property?> GetPropertyAsync(int id)
    {
        using var context = new RentalDbContext();
        return await context.Properties
            .Include(p => p.Customer)
            .Include(p => p.Payments)
            .FirstOrDefaultAsync(p => p.Id == id);
    }
    
    public async Task<Property> SavePropertyAsync(Property property)
    {
        using var context = new RentalDbContext();
        
        if (property.Id == 0)
        {
            context.Properties.Add(property);
        }
        else
        {
            context.Properties.Update(property);
        }
        
        await context.SaveChangesAsync();
        return property;
    }
    
    public async Task DeletePropertyAsync(int id)
    {
        using var context = new RentalDbContext();
        var property = await context.Properties.FindAsync(id);
        if (property != null)
        {
            context.Properties.Remove(property);
            await context.SaveChangesAsync();
        }
    }
    
    // Payment methods
    public async Task<List<Payment>> GetPaymentsAsync()
    {
        using var context = new RentalDbContext();
        return await context.Payments
            .Include(p => p.Property)
            .Include(p => p.Customer)
            .OrderByDescending(p => p.DueDate)
            .ToListAsync();
    }

    public async Task<Payment?> GetPaymentByIdAsync(int paymentId)
    {
        using var context = new RentalDbContext();
        return await context.Payments
            .Include(p => p.Customer)
            .Include(p => p.Property)
            .Include(p => p.Receipts)
            .FirstOrDefaultAsync(p => p.Id == paymentId);
    }
    
    public async Task<Payment?> GetPaymentAsync(int id)
    {
        using var context = new RentalDbContext();
        return await context.Payments
            .Include(p => p.Property)
            .Include(p => p.Customer)
            .FirstOrDefaultAsync(p => p.Id == id);
    }
    
    public async Task<Payment> SavePaymentAsync(Payment payment)
    {
        using var context = new RentalDbContext();
        
        if (payment.Id == 0)
        {
            context.Payments.Add(payment);
        }
        else
        {
            context.Payments.Update(payment);
        }
        
        await context.SaveChangesAsync();
        return payment;
    }
    

    
    // Dashboard statistics
    public async Task<DashboardStats> GetDashboardStatsAsync()
    {
        using var context = new RentalDbContext();
        
        var totalProperties = await context.Properties.CountAsync();
        var rentedProperties = await context.Properties.CountAsync(p => p.Status == PropertyStatus.Rented);
        var totalCustomers = await context.Customers.CountAsync();
        var overduePayments = await context.Payments.CountAsync(p => p.Status == PaymentStatus.Overdue);
        var monthlyRevenue = await context.Payments
            .Where(p => p.Status == PaymentStatus.Paid && p.PaymentDate.HasValue &&
                       p.PaymentDate.Value.Month == DateTime.Now.Month &&
                       p.PaymentDate.Value.Year == DateTime.Now.Year)
            .SumAsync(p => p.RentAmount + p.CleaningTaxAmount);

        var totalCleaningTax = await context.Payments
            .Where(p => p.Status == PaymentStatus.Paid && p.PaymentDate.HasValue)
            .SumAsync(p => p.CleaningTaxAmount);
        
        return new DashboardStats
        {
            TotalProperties = totalProperties,
            RentedProperties = rentedProperties,
            AvailableProperties = totalProperties - rentedProperties,
            TotalCustomers = totalCustomers,
            OverduePayments = overduePayments,
            MonthlyRevenue = monthlyRevenue,
            TotalCleaningTaxCollected = totalCleaningTax
        };
    }

    // Additional Payment methods for PropertyPaymentsWindow
    public async Task<List<Payment>> GetPaymentsByPropertyIdAsync(int propertyId)
    {
        using var context = new RentalDbContext();
        return await context.Payments
            .Include(p => p.Property)
            .Include(p => p.Customer)
            .Where(p => p.PropertyId == propertyId)
            .OrderByDescending(p => p.PaymentDate)
            .ToListAsync();
    }

    /// <summary>
    /// توليد رقم توصيل تلقائي للمحل بصيغة R-XXX
    /// </summary>
    /// <param name="propertyId">معرف المحل</param>
    /// <returns>رقم التوصيل الجديد</returns>
    public async Task<string> GenerateReceiptNumberAsync(int propertyId)
    {
        using var context = new RentalDbContext();

        // جلب آخر رقم توصيل للمحل
        var lastPayment = await context.Payments
            .Where(p => p.PropertyId == propertyId && !string.IsNullOrEmpty(p.ReceiptNumber))
            .OrderByDescending(p => p.Id)
            .FirstOrDefaultAsync();

        int nextNumber = 1;

        if (lastPayment != null && !string.IsNullOrEmpty(lastPayment.ReceiptNumber))
        {
            // استخراج الرقم من آخر توصيل (مثال: R-005 -> 5)
            var parts = lastPayment.ReceiptNumber.Split('-');
            if (parts.Length == 2 && int.TryParse(parts[1], out int lastNumber))
            {
                nextNumber = lastNumber + 1;
            }
        }

        // تكوين رقم التوصيل الجديد بصيغة R-XXX
        return $"R-{nextNumber:D3}";
    }



    public async Task<string> GenerateReceiptNumberAsync()
    {
        using var context = new RentalDbContext();
        var year = DateTime.Now.Year;
        var lastPayment = await context.Payments
            .OrderByDescending(p => p.Id)
            .FirstOrDefaultAsync();

        int nextNumber = 1;
        if (lastPayment != null)
        {
            nextNumber = lastPayment.Id + 1;
        }

        return $"R{year}-{nextNumber:D3}";
    }

    public async Task<List<Property>> GetAllPropertiesAsync()
    {
        using var context = new RentalDbContext();
        return await context.Properties.ToListAsync();
    }

    public async Task<Property?> GetPropertyByIdAsync(int id)
    {
        using var context = new RentalDbContext();
        return await context.Properties.FindAsync(id);
    }

    public async Task<Customer?> GetCustomerByIdAsync(int id)
    {
        using var context = new RentalDbContext();
        return await context.Customers.FindAsync(id);
    }

    public async Task AddPaymentAsync(Payment payment)
    {
        using var context = new RentalDbContext();
        context.Payments.Add(payment);
        await context.SaveChangesAsync();
    }

    public async Task UpdatePaymentAsync(Payment payment)
    {
        using var context = new RentalDbContext();
        context.Payments.Update(payment);
        await context.SaveChangesAsync();
    }

    public async Task DeletePaymentAsync(int paymentId)
    {
        using var context = new RentalDbContext();
        var payment = await context.Payments.FindAsync(paymentId);
        if (payment != null)
        {
            context.Payments.Remove(payment);
            await context.SaveChangesAsync();
        }
    }

    /// <summary>
    /// الحصول على مسار قاعدة البيانات
    /// </summary>
    public string GetDatabasePath()
    {
        // قاعدة البيانات موجودة في مجلد التطبيق الحالي
        var currentDirectory = Directory.GetCurrentDirectory();
        var dbPath = Path.Combine(currentDirectory, "rental_management.db");

        // إذا لم توجد في المجلد الحالي، ابحث في مجلد bin
        if (!File.Exists(dbPath))
        {
            var binPath = Path.Combine(currentDirectory, "bin", "Debug", "net9.0-windows", "rental_management.db");
            if (File.Exists(binPath))
            {
                return binPath;
            }
        }

        return dbPath;
    }
}

public class DashboardStats
{
    public int TotalProperties { get; set; }
    public int RentedProperties { get; set; }
    public int AvailableProperties { get; set; }
    public int TotalCustomers { get; set; }
    public int OverduePayments { get; set; }
    public decimal MonthlyRevenue { get; set; }
    public decimal TotalCleaningTaxCollected { get; set; }
}
