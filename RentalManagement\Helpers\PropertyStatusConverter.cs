using RentalManagement.Models;
using System.Globalization;
using System.Windows.Data;

namespace RentalManagement.Helpers
{
    public class PropertyStatusConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is PropertyStatus status)
            {
                return status switch
                {
                    PropertyStatus.Available => "متاح",
                    PropertyStatus.Rented => "مكتراة",
                    _ => "غير محدد"
                };
            }
            return "غير محدد";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
