-- ===================================================================
-- إعداد Supabase للمزامنة السحابية - تدبير الكراء
-- ===================================================================

-- تفعيل Row Level Security
ALTER TABLE IF EXISTS sync_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS backups ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS sync_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS sync_conflicts ENABLE ROW LEVEL SECURITY;

-- إن<PERSON>اء جدول بيانات المزامنة
CREATE TABLE IF NOT EXISTS sync_data (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    entity_type VARCHAR(100) NOT NULL,
    entity_id VARCHAR(100) NOT NULL,
    json_data TEXT NOT NULL,
    operation VARCHAR(50) NOT NULL,
    timestamp TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    user_id VARCHAR(100),
    device_id VARCHAR(100),
    hash VARCHAR(500),
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- إنشاء الفهارس
CREATE INDEX IF NOT EXISTS idx_sync_data_entity_type_id ON sync_data (entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_sync_data_timestamp ON sync_data (timestamp);
CREATE INDEX IF NOT EXISTS idx_sync_data_operation ON sync_data (operation);
CREATE INDEX IF NOT EXISTS idx_sync_data_user_id ON sync_data (user_id);
CREATE INDEX IF NOT EXISTS idx_sync_data_device_id ON sync_data (device_id);

-- إنشاء جدول النسخ الاحتياطية
CREATE TABLE IF NOT EXISTS backups (
    id VARCHAR(100) PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    size BIGINT NOT NULL,
    data TEXT NOT NULL,
    type VARCHAR(50) NOT NULL,
    version VARCHAR(20) NOT NULL,
    description TEXT,
    user_id VARCHAR(100),
    device_id VARCHAR(100)
);

-- إنشاء الفهارس للنسخ الاحتياطية
CREATE INDEX IF NOT EXISTS idx_backups_created_at ON backups (created_at);
CREATE INDEX IF NOT EXISTS idx_backups_type ON backups (type);
CREATE INDEX IF NOT EXISTS idx_backups_user_id ON backups (user_id);

-- إنشاء جدول سجل المزامنة
CREATE TABLE IF NOT EXISTS sync_log (
    id BIGSERIAL PRIMARY KEY,
    timestamp TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    operation VARCHAR(50) NOT NULL,
    entity_type VARCHAR(100),
    entity_id VARCHAR(100),
    status VARCHAR(50) NOT NULL, -- Success, Failed, Conflict
    message TEXT,
    user_id VARCHAR(100),
    device_id VARCHAR(100),
    duration INTEGER, -- بالميلي ثانية
    error_details TEXT
);

-- إنشاء الفهارس لسجل المزامنة
CREATE INDEX IF NOT EXISTS idx_sync_log_timestamp ON sync_log (timestamp);
CREATE INDEX IF NOT EXISTS idx_sync_log_status ON sync_log (status);
CREATE INDEX IF NOT EXISTS idx_sync_log_operation ON sync_log (operation);
CREATE INDEX IF NOT EXISTS idx_sync_log_entity_type ON sync_log (entity_type);

-- إنشاء جدول التعارضات
CREATE TABLE IF NOT EXISTS sync_conflicts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    entity_type VARCHAR(100) NOT NULL,
    entity_id VARCHAR(100) NOT NULL,
    local_data TEXT NOT NULL,
    remote_data TEXT NOT NULL,
    conflict_type VARCHAR(50) NOT NULL, -- DataConflict, TimestampConflict, etc.
    detected_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    resolved_at TIMESTAMPTZ,
    resolution VARCHAR(50), -- ServerWins, ClientWins, Manual, Merged
    resolved_by VARCHAR(100),
    is_resolved BOOLEAN DEFAULT FALSE NOT NULL,
    resolution_notes TEXT
);

-- إنشاء الفهارس للتعارضات
CREATE INDEX IF NOT EXISTS idx_sync_conflicts_entity_type_id ON sync_conflicts (entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_sync_conflicts_detected_at ON sync_conflicts (detected_at);
CREATE INDEX IF NOT EXISTS idx_sync_conflicts_is_resolved ON sync_conflicts (is_resolved);

-- إنشاء جدول إعدادات المزامنة
CREATE TABLE IF NOT EXISTS sync_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id VARCHAR(100) NOT NULL,
    device_id VARCHAR(100) NOT NULL,
    service_type VARCHAR(50) NOT NULL,
    auto_sync_enabled BOOLEAN DEFAULT TRUE NOT NULL,
    sync_interval_minutes INTEGER DEFAULT 30 NOT NULL,
    enable_encryption BOOLEAN DEFAULT TRUE NOT NULL,
    conflict_resolution VARCHAR(50) DEFAULT 'ServerWins' NOT NULL,
    enable_real_time_sync BOOLEAN DEFAULT FALSE NOT NULL,
    last_sync_time TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- إنشاء الفهارس لإعدادات المزامنة
CREATE INDEX IF NOT EXISTS idx_sync_settings_user_id ON sync_settings (user_id);
CREATE INDEX IF NOT EXISTS idx_sync_settings_device_id ON sync_settings (device_id);

-- إنشاء دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- إنشاء مشغلات لتحديث updated_at
DROP TRIGGER IF EXISTS update_sync_data_updated_at ON sync_data;
CREATE TRIGGER update_sync_data_updated_at
    BEFORE UPDATE ON sync_data
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_sync_settings_updated_at ON sync_settings;
CREATE TRIGGER update_sync_settings_updated_at
    BEFORE UPDATE ON sync_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- إنشاء دالة لإدراج بيانات المزامنة
CREATE OR REPLACE FUNCTION insert_sync_data(
    p_entity_type VARCHAR(100),
    p_entity_id VARCHAR(100),
    p_json_data TEXT,
    p_operation VARCHAR(50),
    p_user_id VARCHAR(100) DEFAULT NULL,
    p_device_id VARCHAR(100) DEFAULT NULL,
    p_hash VARCHAR(500) DEFAULT NULL,
    p_metadata JSONB DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    new_id UUID;
BEGIN
    INSERT INTO sync_data (entity_type, entity_id, json_data, operation, user_id, device_id, hash, metadata)
    VALUES (p_entity_type, p_entity_id, p_json_data, p_operation, p_user_id, p_device_id, p_hash, p_metadata)
    RETURNING id INTO new_id;
    
    RETURN new_id;
END;
$$ LANGUAGE plpgsql;

-- إنشاء دالة لجلب البيانات المحدثة
CREATE OR REPLACE FUNCTION get_updated_data(
    p_last_sync_time TIMESTAMPTZ,
    p_entity_type VARCHAR(100) DEFAULT NULL
)
RETURNS TABLE (
    entity_type VARCHAR(100),
    entity_id VARCHAR(100),
    json_data TEXT,
    operation VARCHAR(50),
    timestamp TIMESTAMPTZ,
    user_id VARCHAR(100),
    device_id VARCHAR(100),
    hash VARCHAR(500),
    metadata JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        sd.entity_type,
        sd.entity_id,
        sd.json_data,
        sd.operation,
        sd.timestamp,
        sd.user_id,
        sd.device_id,
        sd.hash,
        sd.metadata
    FROM sync_data sd
    WHERE sd.timestamp > p_last_sync_time
    AND (p_entity_type IS NULL OR sd.entity_type = p_entity_type)
    ORDER BY sd.timestamp ASC;
END;
$$ LANGUAGE plpgsql;

-- إنشاء دالة لتسجيل عملية المزامنة
CREATE OR REPLACE FUNCTION log_sync_operation(
    p_operation VARCHAR(50),
    p_entity_type VARCHAR(100) DEFAULT NULL,
    p_entity_id VARCHAR(100) DEFAULT NULL,
    p_status VARCHAR(50),
    p_message TEXT DEFAULT NULL,
    p_user_id VARCHAR(100) DEFAULT NULL,
    p_device_id VARCHAR(100) DEFAULT NULL,
    p_duration INTEGER DEFAULT NULL,
    p_error_details TEXT DEFAULT NULL
)
RETURNS BIGINT AS $$
DECLARE
    new_id BIGINT;
BEGIN
    INSERT INTO sync_log (operation, entity_type, entity_id, status, message, user_id, device_id, duration, error_details)
    VALUES (p_operation, p_entity_type, p_entity_id, p_status, p_message, p_user_id, p_device_id, p_duration, p_error_details)
    RETURNING id INTO new_id;
    
    RETURN new_id;
END;
$$ LANGUAGE plpgsql;

-- إنشاء عرض لإحصائيات المزامنة
CREATE OR REPLACE VIEW sync_statistics AS
SELECT 
    entity_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN operation = 'Create' THEN 1 END) as created_records,
    COUNT(CASE WHEN operation = 'Update' THEN 1 END) as updated_records,
    COUNT(CASE WHEN operation = 'Delete' THEN 1 END) as deleted_records,
    MAX(timestamp) as last_sync_time,
    MIN(timestamp) as first_sync_time
FROM sync_data
GROUP BY entity_type;

-- إعداد Row Level Security Policies

-- سياسة للوصول إلى بيانات المزامنة (المستخدمون يمكنهم الوصول لبياناتهم فقط)
CREATE POLICY "Users can access their own sync data" ON sync_data
    FOR ALL USING (auth.uid()::text = user_id);

-- سياسة للوصول إلى النسخ الاحتياطية
CREATE POLICY "Users can access their own backups" ON backups
    FOR ALL USING (auth.uid()::text = user_id);

-- سياسة للوصول إلى سجل المزامنة
CREATE POLICY "Users can access their own sync logs" ON sync_log
    FOR ALL USING (auth.uid()::text = user_id);

-- سياسة للوصول إلى التعارضات
CREATE POLICY "Users can access their own conflicts" ON sync_conflicts
    FOR ALL USING (auth.uid()::text IN (
        SELECT user_id FROM sync_data WHERE entity_type = sync_conflicts.entity_type AND entity_id = sync_conflicts.entity_id
    ));

-- سياسة للوصول إلى إعدادات المزامنة
CREATE POLICY "Users can access their own sync settings" ON sync_settings
    FOR ALL USING (auth.uid()::text = user_id);

-- إنشاء فهارس مركبة للأداء
CREATE INDEX IF NOT EXISTS idx_sync_data_composite 
ON sync_data (entity_type, entity_id, timestamp)
WHERE operation IN ('Create', 'Update', 'Delete');

-- تفعيل Real-time subscriptions
ALTER PUBLICATION supabase_realtime ADD TABLE sync_data;
ALTER PUBLICATION supabase_realtime ADD TABLE sync_conflicts;

-- إنشاء دالة للتنظيف التلقائي للبيانات القديمة
CREATE OR REPLACE FUNCTION cleanup_old_sync_data()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- حذف البيانات الأقدم من 90 يوم
    DELETE FROM sync_log 
    WHERE timestamp < NOW() - INTERVAL '90 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- حذف التعارضات المحلولة الأقدم من 30 يوم
    DELETE FROM sync_conflicts 
    WHERE is_resolved = TRUE AND resolved_at < NOW() - INTERVAL '30 days';
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- جدولة التنظيف التلقائي (يتطلب pg_cron extension)
-- SELECT cron.schedule('cleanup-sync-data', '0 2 * * *', 'SELECT cleanup_old_sync_data();');

COMMENT ON TABLE sync_data IS 'جدول بيانات المزامنة الرئيسي';
COMMENT ON TABLE backups IS 'جدول النسخ الاحتياطية';
COMMENT ON TABLE sync_log IS 'سجل عمليات المزامنة';
COMMENT ON TABLE sync_conflicts IS 'جدول التعارضات في المزامنة';
COMMENT ON TABLE sync_settings IS 'إعدادات المزامنة للمستخدمين';

-- إنهاء الإعداد
SELECT 'تم إكمال إعداد Supabase للمزامنة السحابية بنجاح!' as status;
