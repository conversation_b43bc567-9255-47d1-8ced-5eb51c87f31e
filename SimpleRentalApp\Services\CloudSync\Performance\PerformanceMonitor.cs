using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace SimpleRentalApp.Services.CloudSync.Performance
{
    /// <summary>
    /// مراقب أداء المزامنة
    /// </summary>
    public class PerformanceMonitor : IDisposable
    {
        private readonly ConcurrentDictionary<string, PerformanceMetric> _metrics;
        private readonly Timer _reportingTimer;
        private readonly PerformanceCounters _counters;
        private readonly object _lockObject = new();
        private bool _isDisposed;

        public event EventHandler<PerformanceReportEventArgs>? PerformanceReport;
        public event EventHandler<PerformanceAlertEventArgs>? PerformanceAlert;

        public PerformanceMonitor(TimeSpan? reportingInterval = null)
        {
            _metrics = new ConcurrentDictionary<string, PerformanceMetric>();
            _counters = new PerformanceCounters();
            
            var interval = reportingInterval ?? TimeSpan.FromMinutes(1);
            _reportingTimer = new Timer(GenerateReport, null, interval, interval);
        }

        /// <summary>
        /// بدء قياس العملية
        /// </summary>
        public IDisposable StartOperation(string operationName, string? category = null)
        {
            return new OperationTracker(this, operationName, category);
        }

        /// <summary>
        /// تسجيل مقياس أداء
        /// </summary>
        public void RecordMetric(string name, double value, string? unit = null, string? category = null)
        {
            var key = $"{category ?? "General"}_{name}";
            
            _metrics.AddOrUpdate(key, 
                new PerformanceMetric(name, category, unit),
                (k, existing) =>
                {
                    existing.AddValue(value);
                    return existing;
                }).AddValue(value);
        }

        /// <summary>
        /// تسجيل عداد
        /// </summary>
        public void IncrementCounter(string name, string? category = null, long increment = 1)
        {
            var key = $"{category ?? "General"}_{name}";
            _counters.Increment(key, increment);
        }

        /// <summary>
        /// تسجيل وقت العملية
        /// </summary>
        internal void RecordOperationTime(string operationName, string? category, TimeSpan duration)
        {
            RecordMetric($"{operationName}_Duration", duration.TotalMilliseconds, "ms", category);
            IncrementCounter($"{operationName}_Count", category);
            
            // تحقق من التنبيهات
            CheckPerformanceAlerts(operationName, duration, category);
        }

        /// <summary>
        /// الحصول على إحصائيات الأداء
        /// </summary>
        public PerformanceStats GetStats()
        {
            lock (_lockObject)
            {
                var stats = new PerformanceStats
                {
                    GeneratedAt = DateTime.UtcNow,
                    Metrics = _metrics.Values.Select(m => m.GetSnapshot()).ToList(),
                    Counters = _counters.GetSnapshot(),
                    SystemInfo = GetSystemInfo()
                };

                return stats;
            }
        }

        /// <summary>
        /// إعادة تعيين الإحصائيات
        /// </summary>
        public void Reset()
        {
            lock (_lockObject)
            {
                _metrics.Clear();
                _counters.Reset();
            }
        }

        /// <summary>
        /// الحصول على أفضل وأسوأ العمليات أداءً
        /// </summary>
        public PerformanceAnalysis GetPerformanceAnalysis()
        {
            var analysis = new PerformanceAnalysis();
            
            foreach (var metric in _metrics.Values)
            {
                var snapshot = metric.GetSnapshot();
                
                if (snapshot.Name.EndsWith("_Duration"))
                {
                    var operationName = snapshot.Name.Replace("_Duration", "");
                    
                    if (snapshot.Average > analysis.SlowestOperation?.AverageTime)
                    {
                        analysis.SlowestOperation = new OperationPerformance
                        {
                            Name = operationName,
                            Category = snapshot.Category,
                            AverageTime = snapshot.Average,
                            MaxTime = snapshot.Maximum,
                            MinTime = snapshot.Minimum,
                            TotalCalls = snapshot.Count
                        };
                    }
                    
                    if (analysis.FastestOperation == null || snapshot.Average < analysis.FastestOperation.AverageTime)
                    {
                        analysis.FastestOperation = new OperationPerformance
                        {
                            Name = operationName,
                            Category = snapshot.Category,
                            AverageTime = snapshot.Average,
                            MaxTime = snapshot.Maximum,
                            MinTime = snapshot.Minimum,
                            TotalCalls = snapshot.Count
                        };
                    }
                }
            }
            
            return analysis;
        }

        /// <summary>
        /// فحص تنبيهات الأداء
        /// </summary>
        private void CheckPerformanceAlerts(string operationName, TimeSpan duration, string? category)
        {
            // تنبيه إذا استغرقت العملية أكثر من 10 ثوان
            if (duration.TotalSeconds > 10)
            {
                OnPerformanceAlert(new PerformanceAlertEventArgs
                {
                    AlertType = PerformanceAlertType.SlowOperation,
                    OperationName = operationName,
                    Category = category,
                    Value = duration.TotalSeconds,
                    Threshold = 10,
                    Message = $"العملية {operationName} استغرقت {duration.TotalSeconds:F2} ثانية",
                    Timestamp = DateTime.UtcNow
                });
            }
            
            // تنبيه إذا كان هناك الكثير من العمليات البطيئة
            var key = $"{category ?? "General"}_{operationName}_Duration";
            if (_metrics.TryGetValue(key, out var metric))
            {
                var snapshot = metric.GetSnapshot();
                if (snapshot.Count > 10 && snapshot.Average > 5000) // أكثر من 5 ثوان في المتوسط
                {
                    OnPerformanceAlert(new PerformanceAlertEventArgs
                    {
                        AlertType = PerformanceAlertType.HighAverageTime,
                        OperationName = operationName,
                        Category = category,
                        Value = snapshot.Average,
                        Threshold = 5000,
                        Message = $"متوسط وقت العملية {operationName} هو {snapshot.Average:F2} ميلي ثانية",
                        Timestamp = DateTime.UtcNow
                    });
                }
            }
        }

        /// <summary>
        /// إنشاء تقرير الأداء
        /// </summary>
        private void GenerateReport(object? state)
        {
            if (_isDisposed) return;

            try
            {
                var stats = GetStats();
                var analysis = GetPerformanceAnalysis();
                
                OnPerformanceReport(new PerformanceReportEventArgs
                {
                    Stats = stats,
                    Analysis = analysis,
                    GeneratedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                OnPerformanceAlert(new PerformanceAlertEventArgs
                {
                    AlertType = PerformanceAlertType.MonitoringError,
                    Message = $"خطأ في إنشاء تقرير الأداء: {ex.Message}",
                    Timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// الحصول على معلومات النظام
        /// </summary>
        private SystemInfo GetSystemInfo()
        {
            var process = Process.GetCurrentProcess();
            
            return new SystemInfo
            {
                ProcessorCount = Environment.ProcessorCount,
                WorkingSet = process.WorkingSet64,
                PrivateMemorySize = process.PrivateMemorySize64,
                VirtualMemorySize = process.VirtualMemorySize64,
                ThreadCount = process.Threads.Count,
                HandleCount = process.HandleCount,
                TotalProcessorTime = process.TotalProcessorTime,
                UserProcessorTime = process.UserProcessorTime
            };
        }

        #region Events

        protected virtual void OnPerformanceReport(PerformanceReportEventArgs e)
        {
            PerformanceReport?.Invoke(this, e);
        }

        protected virtual void OnPerformanceAlert(PerformanceAlertEventArgs e)
        {
            PerformanceAlert?.Invoke(this, e);
        }

        #endregion

        #region IDisposable

        public void Dispose()
        {
            if (_isDisposed) return;

            _isDisposed = true;
            _reportingTimer?.Dispose();
        }

        #endregion

        /// <summary>
        /// متتبع العمليات
        /// </summary>
        private class OperationTracker : IDisposable
        {
            private readonly PerformanceMonitor _monitor;
            private readonly string _operationName;
            private readonly string? _category;
            private readonly Stopwatch _stopwatch;

            public OperationTracker(PerformanceMonitor monitor, string operationName, string? category)
            {
                _monitor = monitor;
                _operationName = operationName;
                _category = category;
                _stopwatch = Stopwatch.StartNew();
            }

            public void Dispose()
            {
                _stopwatch.Stop();
                _monitor.RecordOperationTime(_operationName, _category, _stopwatch.Elapsed);
            }
        }
    }

    /// <summary>
    /// مقياس الأداء
    /// </summary>
    public class PerformanceMetric
    {
        private readonly List<double> _values = new();
        private readonly object _lock = new();

        public string Name { get; }
        public string? Category { get; }
        public string? Unit { get; }

        public PerformanceMetric(string name, string? category = null, string? unit = null)
        {
            Name = name;
            Category = category;
            Unit = unit;
        }

        public void AddValue(double value)
        {
            lock (_lock)
            {
                _values.Add(value);
                
                // الاحتفاظ بآخر 1000 قيمة فقط لتوفير الذاكرة
                if (_values.Count > 1000)
                {
                    _values.RemoveAt(0);
                }
            }
        }

        public MetricSnapshot GetSnapshot()
        {
            lock (_lock)
            {
                if (_values.Count == 0)
                {
                    return new MetricSnapshot
                    {
                        Name = Name,
                        Category = Category,
                        Unit = Unit,
                        Count = 0
                    };
                }

                return new MetricSnapshot
                {
                    Name = Name,
                    Category = Category,
                    Unit = Unit,
                    Count = _values.Count,
                    Sum = _values.Sum(),
                    Average = _values.Average(),
                    Minimum = _values.Min(),
                    Maximum = _values.Max(),
                    Latest = _values.Last()
                };
            }
        }
    }

    /// <summary>
    /// العدادات
    /// </summary>
    public class PerformanceCounters
    {
        private readonly ConcurrentDictionary<string, long> _counters = new();

        public void Increment(string name, long increment = 1)
        {
            _counters.AddOrUpdate(name, increment, (k, v) => v + increment);
        }

        public Dictionary<string, long> GetSnapshot()
        {
            return new Dictionary<string, long>(_counters);
        }

        public void Reset()
        {
            _counters.Clear();
        }
    }

    #region Data Models

    public class PerformanceStats
    {
        public DateTime GeneratedAt { get; set; }
        public List<MetricSnapshot> Metrics { get; set; } = new();
        public Dictionary<string, long> Counters { get; set; } = new();
        public SystemInfo SystemInfo { get; set; } = new();
    }

    public class MetricSnapshot
    {
        public string Name { get; set; } = "";
        public string? Category { get; set; }
        public string? Unit { get; set; }
        public int Count { get; set; }
        public double Sum { get; set; }
        public double Average { get; set; }
        public double Minimum { get; set; }
        public double Maximum { get; set; }
        public double Latest { get; set; }
    }

    public class SystemInfo
    {
        public int ProcessorCount { get; set; }
        public long WorkingSet { get; set; }
        public long PrivateMemorySize { get; set; }
        public long VirtualMemorySize { get; set; }
        public int ThreadCount { get; set; }
        public int HandleCount { get; set; }
        public TimeSpan TotalProcessorTime { get; set; }
        public TimeSpan UserProcessorTime { get; set; }
    }

    public class PerformanceAnalysis
    {
        public OperationPerformance? SlowestOperation { get; set; }
        public OperationPerformance? FastestOperation { get; set; }
    }

    public class OperationPerformance
    {
        public string Name { get; set; } = "";
        public string? Category { get; set; }
        public double AverageTime { get; set; }
        public double MaxTime { get; set; }
        public double MinTime { get; set; }
        public int TotalCalls { get; set; }
    }

    public class PerformanceReportEventArgs : EventArgs
    {
        public PerformanceStats Stats { get; set; } = new();
        public PerformanceAnalysis Analysis { get; set; } = new();
        public DateTime GeneratedAt { get; set; }
    }

    public class PerformanceAlertEventArgs : EventArgs
    {
        public PerformanceAlertType AlertType { get; set; }
        public string? OperationName { get; set; }
        public string? Category { get; set; }
        public double Value { get; set; }
        public double Threshold { get; set; }
        public string Message { get; set; } = "";
        public DateTime Timestamp { get; set; }
    }

    public enum PerformanceAlertType
    {
        SlowOperation,
        HighAverageTime,
        HighMemoryUsage,
        MonitoringError
    }

    #endregion
}
