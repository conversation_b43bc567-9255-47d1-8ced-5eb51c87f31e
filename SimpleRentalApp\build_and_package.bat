@echo off
chcp 65001 > nul
echo ========================================
echo   بناء وتعبئة تطبيق تدبير الكراء
echo ========================================
echo.

echo [1/4] نشر التطبيق...
call publish.bat
if %ERRORLEVEL% neq 0 (
    echo فشل في نشر التطبيق!
    pause
    exit /b 1
)

echo.
echo [2/4] التحقق من وجود NSIS...
where makensis >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo تحذير: NSIS غير مثبت - سيتم تخطي إنشاء المثبت
    echo يمكنك استخدام الملفات في مجلد dist مباشرة
    goto :skip_installer
)

echo.
echo [3/4] إنشاء المثبت...
call create_installer.bat
if %ERRORLEVEL% neq 0 (
    echo فشل في إنشاء المثبت!
    pause
    exit /b 1
)

:skip_installer
echo.
echo [4/4] إنشاء حزمة ZIP...
if exist "تدبير_الكراء_v1.0.0.zip" del "تدبير_الكراء_v1.0.0.zip"

powershell -Command "Compress-Archive -Path 'dist\*' -DestinationPath 'تدبير_الكراء_v1.0.0.zip' -Force"

if %ERRORLEVEL% neq 0 (
    echo فشل في إنشاء ملف ZIP!
    pause
    exit /b 1
)

echo.
echo ========================================
echo تم الانتهاء من التعبئة بنجاح!
echo ========================================
echo.
echo الملفات المتوفرة:
echo - مجلد dist: يحتوي على ملف التطبيق المباشر
echo - تدبير_الكراء_v1.0.0.zip: حزمة مضغوطة للتوزيع
if exist "تدبير_الكراء_مثبت_v1.0.0.exe" echo - تدبير_الكراء_مثبت_v1.0.0.exe: مثبت تلقائي
echo.
echo يمكنك الآن توزيع هذه الملفات للمستخدمين
echo.
pause
