🏠 Rental Management System (تدبير الكراء)
================================================

A comprehensive desktop application for managing rental properties, 
tenants, contracts, and payments.

📋 APPLICATION INFO
------------------
- Name: Rental Management System (تدبير الكراء)
- Version: 1.0.0
- Developer: Hafid Abdo
- Release Date: December 2024
- Platform: Windows Desktop Application (WPF)

🎯 KEY FEATURES
--------------
✅ Property Management - Add, edit, and manage rental properties
✅ Tenant Management - Comprehensive tenant information system
✅ Contract Management - Create and manage rental contracts
✅ Payment Tracking - Record and track rental payments
✅ Cleaning Tax Management - Handle annual cleaning tax (10.5%)
✅ WhatsApp Notifications - Send automated reminders to tenants
✅ Receipt Printing - Generate A6 size receipts in Arabic
✅ Reports & Analytics - Comprehensive financial reports
✅ Data Export/Import - Backup and restore functionality

💻 SYSTEM REQUIREMENTS
---------------------
- Operating System: Windows 10 or newer
- Processor: Intel/AMD 64-bit
- Memory: 4 GB RAM (minimum)
- Storage: 500 MB free disk space
- .NET Runtime: Included (no separate installation needed)

🚀 QUICK START
--------------
1. Double-click RentalManagement.exe to launch
2. The database will be created automatically on first run
3. Start by adding properties and tenants
4. Create rental contracts
5. Record payments and generate receipts

📚 MAIN MODULES
--------------

🏢 Properties Management
- Add property details (name, address, description)
- Edit and update property information
- Link properties to tenants

👥 Tenants Management  
- Store tenant personal information
- Contact details and phone numbers
- Track tenant history

📄 Contracts Management
- Create rental agreements
- Set rental amounts and payment dates
- Track contract status (active, expired, expiring)
- Handle rent increases

💰 Payments Management
- Record rental payments
- Generate receipt numbers (R-001, R-002...)
- Print A6 size receipts
- Convert amounts to Arabic words

🧹 Cleaning Tax Management
- Calculate annual cleaning tax (10.5% of yearly rent)
- Track tax payments
- Generate tax receipts (TSC2024-001...)
- Handle partial payments

📱 WhatsApp Notifications
- Send monthly rental reminders
- Overdue payment notifications
- Customizable message templates
- Track notification history

📊 Reports & Analytics
- Financial summaries
- Payment history reports
- Contract status reports
- Export data to various formats

🔧 TECHNICAL DETAILS
-------------------
- Framework: .NET 9.0 WPF
- Database: SQLite with Entity Framework
- PDF Generation: QuestPDF library
- UI Design: Modern Arabic-friendly interface
- Deployment: Single File Application

📁 FILE LOCATIONS
----------------
- Database: Same folder as application (rental_management.db)
- Backups: Documents\RentalBackups
- Receipts: Documents\RentalReceipts

🛠️ TROUBLESHOOTING
------------------

Problem: Application won't start
Solution: Ensure Windows is updated, restart computer

Problem: Database error
Solution: Delete "rental_management.db" file and restart app

Problem: Can't print receipts
Solution: Ensure printer is installed and working

Problem: Arabic text not displaying correctly
Solution: Install Arabic language fonts in Windows

📞 SUPPORT
----------
For assistance:
- Developer: Hafid Abdo
- Email: <EMAIL>

⚠️ IMPORTANT NOTES
------------------
- Always backup your data regularly
- Don't delete the database file unless certain
- Use correct phone numbers for WhatsApp notifications
- Ensure data accuracy when entering information

🎯 BEST PRACTICES
----------------
1. Enter data accurately from the beginning
2. Use clear names for properties
3. Update tenant information regularly
4. Print receipts immediately after payment
5. Review reports monthly
6. Keep regular backups

✅ Installation Complete!
========================
Enjoy using the Rental Management System for efficient 
property management.

---
© 2025 Hafid Abdo - All Rights Reserved
