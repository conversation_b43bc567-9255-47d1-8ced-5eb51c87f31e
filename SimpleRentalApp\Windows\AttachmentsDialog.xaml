<Window x:Class="SimpleRentalApp.Windows.AttachmentsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة مرفقات العقد" 
        Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        Background="#F8F9FA"
        WindowStyle="None"
        AllowsTransparency="True"
        ResizeMode="NoResize">
    
    <Border Background="White" CornerRadius="12" Margin="20">
        <Border.Effect>
            <DropShadowEffect Color="#424242" 
                            Direction="270" 
                            ShadowDepth="12" 
                            BlurRadius="24" 
                            Opacity="0.3"/>
        </Border.Effect>
        
        <Grid Margin="30">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Modern Header -->
            <Border Grid.Row="0" 
                    Background="#9C27B0" 
                    CornerRadius="8" 
                    Padding="20,15"
                    Margin="0,0,0,20">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <TextBlock Text="📎" FontSize="24" Margin="0,0,10,0"/>
                    <TextBlock Name="HeaderTextBlock"
                               Text="إدارة مرفقات العقد" 
                               FontSize="18" 
                               FontWeight="Bold"
                               Foreground="White"/>
                </StackPanel>
            </Border>

            <!-- Action Bar -->
            <Border Grid.Row="1" 
                    Background="#F8F9FA" 
                    BorderBrush="#DEE2E6" 
                    BorderThickness="1" 
                    CornerRadius="8" 
                    Padding="15"
                    Margin="0,0,0,15">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button Name="AddAttachmentButton"
                            Content="📁 إضافة مرفق"
                            Height="40"
                            Padding="20,0"
                            Background="#4CAF50"
                            Foreground="White"
                            BorderThickness="0"
                            FontWeight="Bold"
                            FontSize="14"
                            Click="AddAttachmentButton_Click"/>
                </StackPanel>
            </Border>

            <!-- Attachments List -->
            <Border Grid.Row="2" 
                    Background="White" 
                    BorderBrush="#E0E0E0" 
                    BorderThickness="1" 
                    CornerRadius="8">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Name="AttachmentsPanel" Margin="15">
                        <!-- Attachments will be loaded here -->
                    </StackPanel>
                </ScrollViewer>
            </Border>

            <!-- Close Button -->
            <Border Grid.Row="3" 
                    Background="#F8F9FA" 
                    CornerRadius="8" 
                    Padding="20"
                    Margin="0,20,0,0">
                <Button Name="CloseButton"
                        Content="❌ إغلاق"
                        Width="120"
                        Height="45"
                        FontSize="14"
                        Background="#6C757D"
                        Foreground="White"
                        BorderThickness="0"
                        HorizontalAlignment="Center"
                        Click="CloseButton_Click"/>
            </Border>
        </Grid>
    </Border>
</Window>
