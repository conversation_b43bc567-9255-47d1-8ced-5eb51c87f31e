using System;
using System.ComponentModel.DataAnnotations;

namespace SimpleRentalApp.Models
{
    public class RentChangeLog
    {
        public int Id { get; set; }

        [Required]
        public int PropertyId { get; set; }
        public Property Property { get; set; } = null!;

        public int? ContractId { get; set; }
        public Contract? Contract { get; set; }

        [Required]
        public decimal OldRentAmount { get; set; }

        [Required]
        public decimal NewRentAmount { get; set; }

        [Required]
        public DateTime ChangeDate { get; set; }

        [Required]
        [StringLength(200)]
        public string ChangeReason { get; set; } = string.Empty;

        [StringLength(500)]
        public string Notes { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string ChangedBy { get; set; } = "النظام";

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Computed properties
        public decimal ChangeAmount => NewRentAmount - OldRentAmount;

        public decimal ChangePercentage => OldRentAmount > 0 ? 
            (ChangeAmount / OldRentAmount) * 100 : 0;

        public string ChangeAmountDisplay => $"{ChangeAmount:N0} درهم";

        public string ChangePercentageDisplay => $"{ChangePercentage:F1}%";

        public string ChangeDateDisplay => ChangeDate.ToString("dd/MM/yyyy");

        public string ChangeTypeDisplay => ChangeAmount >= 0 ? "زيادة" : "تخفيض";

        public string OldCleaningTax => $"{OldRentAmount * 12 * 0.105m:N0} درهم";

        public string NewCleaningTax => $"{NewRentAmount * 12 * 0.105m:N0} درهم";

        public string CleaningTaxChange => $"{(NewRentAmount - OldRentAmount) * 12 * 0.105m:N0} درهم";
    }
}
