rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // قواعد بيانات المزامنة
    match /sync_data/{document} {
      allow read, write: if request.auth != null && 
        (resource.data.user_id == request.auth.uid || 
         request.auth.token.role == 'admin');
      
      allow create: if request.auth != null && 
        request.resource.data.user_id == request.auth.uid &&
        validateSyncData(request.resource.data);
      
      allow update: if request.auth != null && 
        (resource.data.user_id == request.auth.uid || 
         request.auth.token.role == 'admin') &&
        validateSyncData(request.resource.data);
    }
    
    // قواعد النسخ الاحتياطية
    match /backups/{document} {
      allow read, write: if request.auth != null && 
        (resource.data.user_id == request.auth.uid || 
         request.auth.token.role == 'admin');
      
      allow create: if request.auth != null && 
        request.resource.data.user_id == request.auth.uid &&
        validateBackupData(request.resource.data);
    }
    
    // قواعد سجل المزامنة
    match /sync_log/{document} {
      allow read: if request.auth != null && 
        (resource.data.user_id == request.auth.uid || 
         request.auth.token.role == 'admin');
      
      allow create: if request.auth != null && 
        request.resource.data.user_id == request.auth.uid &&
        validateSyncLogData(request.resource.data);
      
      // منع التعديل والحذف لضمان سلامة السجل
      allow update, delete: if false;
    }
    
    // قواعد التعارضات
    match /sync_conflicts/{document} {
      allow read, write: if request.auth != null && 
        (resource.data.user_id == request.auth.uid || 
         request.auth.token.role == 'admin');
      
      allow create: if request.auth != null && 
        validateConflictData(request.resource.data);
      
      allow update: if request.auth != null && 
        (resource.data.user_id == request.auth.uid || 
         request.auth.token.role == 'admin') &&
        validateConflictData(request.resource.data);
    }
    
    // قواعد إعدادات المزامنة
    match /sync_settings/{userId}/devices/{deviceId} {
      allow read, write: if request.auth != null && 
        (userId == request.auth.uid || 
         request.auth.token.role == 'admin');
      
      allow create, update: if request.auth != null && 
        userId == request.auth.uid &&
        validateSyncSettingsData(request.resource.data);
    }
    
    // قواعد المستخدمين
    match /users/{userId} {
      allow read, write: if request.auth != null && 
        (userId == request.auth.uid || 
         request.auth.token.role == 'admin');
      
      allow create: if request.auth != null && 
        userId == request.auth.uid &&
        validateUserData(request.resource.data);
      
      allow update: if request.auth != null && 
        userId == request.auth.uid &&
        validateUserData(request.resource.data);
    }
    
    // قواعد الأجهزة
    match /users/{userId}/devices/{deviceId} {
      allow read, write: if request.auth != null && 
        userId == request.auth.uid;
      
      allow create, update: if request.auth != null && 
        userId == request.auth.uid &&
        validateDeviceData(request.resource.data);
    }
    
    // منع الوصول لأي مجموعات أخرى
    match /{document=**} {
      allow read, write: if false;
    }
  }
  
  // دوال التحقق من صحة البيانات
  function validateSyncData(data) {
    return data.keys().hasAll(['entity_type', 'entity_id', 'json_data', 'operation', 'timestamp']) &&
           data.entity_type is string && data.entity_type.size() > 0 &&
           data.entity_id is string && data.entity_id.size() > 0 &&
           data.json_data is string && data.json_data.size() > 0 &&
           data.operation in ['Create', 'Update', 'Delete'] &&
           data.timestamp is timestamp &&
           data.timestamp <= request.time;
  }
  
  function validateBackupData(data) {
    return data.keys().hasAll(['name', 'created_at', 'size', 'data', 'type', 'version']) &&
           data.name is string && data.name.size() > 0 &&
           data.created_at is timestamp && data.created_at <= request.time &&
           data.size is number && data.size >= 0 &&
           data.data is string && data.data.size() > 0 &&
           data.type in ['Full', 'Incremental'] &&
           data.version is string && data.version.size() > 0;
  }
  
  function validateSyncLogData(data) {
    return data.keys().hasAll(['timestamp', 'operation', 'status']) &&
           data.timestamp is timestamp && data.timestamp <= request.time &&
           data.operation is string && data.operation.size() > 0 &&
           data.status in ['Success', 'Failed', 'Conflict'];
  }
  
  function validateConflictData(data) {
    return data.keys().hasAll(['entity_type', 'entity_id', 'local_data', 'remote_data', 'conflict_type', 'detected_at']) &&
           data.entity_type is string && data.entity_type.size() > 0 &&
           data.entity_id is string && data.entity_id.size() > 0 &&
           data.local_data is string && data.local_data.size() > 0 &&
           data.remote_data is string && data.remote_data.size() > 0 &&
           data.conflict_type is string && data.conflict_type.size() > 0 &&
           data.detected_at is timestamp && data.detected_at <= request.time;
  }
  
  function validateSyncSettingsData(data) {
    return data.keys().hasAll(['service_type', 'auto_sync_enabled', 'sync_interval_minutes', 'enable_encryption', 'conflict_resolution']) &&
           data.service_type in ['supabase', 'firebase', 'azuresql'] &&
           data.auto_sync_enabled is bool &&
           data.sync_interval_minutes is number && data.sync_interval_minutes >= 5 && data.sync_interval_minutes <= 1440 &&
           data.enable_encryption is bool &&
           data.conflict_resolution in ['ServerWins', 'ClientWins', 'Manual', 'MergeChanges'];
  }
  
  function validateUserData(data) {
    return data.keys().hasAll(['email', 'name']) &&
           data.email is string && data.email.matches('.*@.*\\..*') &&
           data.name is string && data.name.size() > 0;
  }
  
  function validateDeviceData(data) {
    return data.keys().hasAll(['name', 'last_seen']) &&
           data.name is string && data.name.size() > 0 &&
           data.last_seen is timestamp && data.last_seen <= request.time;
  }
}
