# إنشاء أيقونة لتطبيق تدبير الكراء
# Create icon for Rental Management Application

Add-Type -AssemblyName System.Drawing
Add-Type -AssemblyName System.Windows.Forms

Write-Host "Creating application icon..." -ForegroundColor Green

# Create a 256x256 bitmap
$size = 256
$bitmap = New-Object System.Drawing.Bitmap($size, $size)
$graphics = [System.Drawing.Graphics]::FromImage($bitmap)

# Set high quality rendering
$graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
$graphics.TextRenderingHint = [System.Drawing.Text.TextRenderingHint]::AntiAlias

# Create gradient background (blue to light blue)
$brush = New-Object System.Drawing.Drawing2D.LinearGradientBrush(
    (New-Object System.Drawing.Point(0, 0)),
    (New-Object System.Drawing.Point($size, $size)),
    [System.Drawing.Color]::FromArgb(41, 128, 185),  # Blue
    [System.Drawing.Color]::FromArgb(52, 152, 219)   # Light Blue
)

# Fill background with gradient
$graphics.FillRectangle($brush, 0, 0, $size, $size)

# Create building/house shape (representing rental properties)
$whitePen = New-Object System.Drawing.Pen([System.Drawing.Color]::White, 4)
$whiteBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)

# Draw building outline
$buildingRect = New-Object System.Drawing.Rectangle(50, 80, 156, 120)
$graphics.FillRectangle($whiteBrush, $buildingRect)
$graphics.DrawRectangle($whitePen, $buildingRect)

# Draw roof (triangle)
$roofPoints = @(
    (New-Object System.Drawing.Point(128, 40)),  # Top point
    (New-Object System.Drawing.Point(40, 80)),   # Left point
    (New-Object System.Drawing.Point(216, 80))   # Right point
)
$graphics.FillPolygon($whiteBrush, $roofPoints)
$graphics.DrawPolygon($whitePen, $roofPoints)

# Draw windows
$windowBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(41, 128, 185))
$window1 = New-Object System.Drawing.Rectangle(70, 100, 25, 25)
$window2 = New-Object System.Drawing.Rectangle(130, 100, 25, 25)
$window3 = New-Object System.Drawing.Rectangle(70, 140, 25, 25)
$window4 = New-Object System.Drawing.Rectangle(130, 140, 25, 25)

$graphics.FillRectangle($windowBrush, $window1)
$graphics.FillRectangle($windowBrush, $window2)
$graphics.FillRectangle($windowBrush, $window3)
$graphics.FillRectangle($windowBrush, $window4)

# Draw door
$door = New-Object System.Drawing.Rectangle(170, 150, 20, 50)
$graphics.FillRectangle($windowBrush, $door)

# Add currency symbol (درهم) or key symbol
$font = New-Object System.Drawing.Font("Arial", 24, [System.Drawing.FontStyle]::Bold)
$textBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
$text = "₹"  # Currency symbol
$textSize = $graphics.MeasureString($text, $font)
$textX = ($size - $textSize.Width) / 2
$textY = 210
$graphics.DrawString($text, $font, $textBrush, $textX, $textY)

# Clean up graphics
$graphics.Dispose()

# Save as PNG first
$pngPath = "app_icon.png"
$bitmap.Save($pngPath, [System.Drawing.Imaging.ImageFormat]::Png)
Write-Host "PNG icon created: $pngPath" -ForegroundColor Cyan

# Convert to ICO format
try {
    # Create ICO file with multiple sizes
    $icoPath = "app_icon.ico"
    
    # Create different sizes for ICO
    $sizes = @(16, 32, 48, 64, 128, 256)
    $iconImages = @()
    
    foreach ($iconSize in $sizes) {
        $smallBitmap = New-Object System.Drawing.Bitmap($iconSize, $iconSize)
        $smallGraphics = [System.Drawing.Graphics]::FromImage($smallBitmap)
        $smallGraphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
        $smallGraphics.DrawImage($bitmap, 0, 0, $iconSize, $iconSize)
        $smallGraphics.Dispose()
        $iconImages += $smallBitmap
    }
    
    # Save as ICO (simplified approach)
    $bitmap.Save($icoPath, [System.Drawing.Imaging.ImageFormat]::Icon)
    Write-Host "ICO icon created: $icoPath" -ForegroundColor Cyan
    
    # Clean up
    foreach ($img in $iconImages) {
        $img.Dispose()
    }
}
catch {
    Write-Host "Could not create ICO file directly. PNG file created successfully." -ForegroundColor Yellow
    Write-Host "You can convert PNG to ICO using online tools or specialized software." -ForegroundColor Yellow
}

# Clean up
$bitmap.Dispose()
$brush.Dispose()
$whitePen.Dispose()
$whiteBrush.Dispose()
$windowBrush.Dispose()
$font.Dispose()
$textBrush.Dispose()

Write-Host "Icon creation completed!" -ForegroundColor Green
Write-Host "Files created:" -ForegroundColor Cyan
Write-Host "- app_icon.png (PNG format)" -ForegroundColor White
if (Test-Path "app_icon.ico") {
    Write-Host "- app_icon.ico (ICO format)" -ForegroundColor White
}

Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. If ICO file was not created, convert PNG to ICO using:" -ForegroundColor White
Write-Host "   - Online converter: https://convertio.co/png-ico/" -ForegroundColor White
Write-Host "   - Or use specialized software" -ForegroundColor White
Write-Host "2. Add the ICO file to your project" -ForegroundColor White
Write-Host "3. Update the project file to reference the icon" -ForegroundColor White
