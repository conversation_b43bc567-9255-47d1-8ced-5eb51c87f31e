using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;
using Moq;
using SimpleRentalApp.Services.CloudSync;
using SimpleRentalApp.Data;
using SimpleRentalApp.Models;
using SimpleRentalApp.Services.CloudSync.Providers;

namespace SimpleRentalApp.Tests.CloudSync
{
    /// <summary>
    /// اختبارات معالجة التعارضات
    /// </summary>
    public class ConflictResolutionTests
    {
        private readonly Mock<ISyncService> _mockSyncService;
        private readonly Mock<DatabaseService> _mockDatabaseService;
        private readonly SyncManager _syncManager;
        private readonly SyncConfiguration _testConfig;

        public ConflictResolutionTests()
        {
            _mockSyncService = new Mock<ISyncService>();
            _mockDatabaseService = new Mock<DatabaseService>();
            _syncManager = new SyncManager(_mockDatabaseService.Object);
            
            _testConfig = new SyncConfiguration
            {
                ServiceType = "test",
                AutoSync = false,
                SyncIntervalMinutes = 30,
                EnableEncryption = true,
                ConflictResolution = ConflictResolutionStrategy.ServerWins,
                EnableRealTimeSync = false,
                DatabaseUrl = "https://test.example.com",
                ApiKey = "test-api-key",
                EncryptionKey = "test-encryption-key-32-characters-long"
            };
        }

        [Fact]
        public async Task HandleConflicts_ServerWins_ShouldResolveCorrectly()
        {
            // Arrange
            _testConfig.ConflictResolution = ConflictResolutionStrategy.ServerWins;
            
            var localData = new SyncData
            {
                EntityType = "Customer",
                EntityId = "1",
                JsonData = "{\"id\": 1, \"name\": \"Local Customer\"}",
                Operation = SyncOperation.Update,
                Timestamp = DateTime.UtcNow.AddMinutes(-5)
            };

            var remoteData = new SyncData
            {
                EntityType = "Customer",
                EntityId = "1",
                JsonData = "{\"id\": 1, \"name\": \"Remote Customer\"}",
                Operation = SyncOperation.Update,
                Timestamp = DateTime.UtcNow
            };

            var conflict = new SyncConflict
            {
                EntityType = "Customer",
                EntityId = "1",
                LocalData = localData,
                RemoteData = remoteData,
                ConflictType = ConflictType.DataConflict,
                DetectedAt = DateTime.UtcNow
            };

            var conflicts = new List<SyncConflict> { conflict };

            _mockSyncService.Setup(s => s.ResolveConflictsAsync(It.IsAny<List<SyncConflict>>()))
                .ReturnsAsync(new ConflictResolutionResult
                {
                    Success = true,
                    ResolvedConflicts = conflicts,
                    UnresolvedConflicts = new List<SyncConflict>()
                });

            // Act
            var result = await _syncManager.HandleConflictsAsync(conflicts);

            // Assert
            Assert.True(result.Success);
            Assert.Equal(1, result.ResolvedConflicts.Count);
            Assert.Empty(result.UnresolvedConflicts);
        }

        [Fact]
        public async Task HandleConflicts_ClientWins_ShouldResolveCorrectly()
        {
            // Arrange
            _testConfig.ConflictResolution = ConflictResolutionStrategy.ClientWins;
            
            var localData = new SyncData
            {
                EntityType = "Property",
                EntityId = "2",
                JsonData = "{\"id\": 2, \"name\": \"Local Property\"}",
                Operation = SyncOperation.Update,
                Timestamp = DateTime.UtcNow
            };

            var remoteData = new SyncData
            {
                EntityType = "Property",
                EntityId = "2",
                JsonData = "{\"id\": 2, \"name\": \"Remote Property\"}",
                Operation = SyncOperation.Update,
                Timestamp = DateTime.UtcNow.AddMinutes(-10)
            };

            var conflict = new SyncConflict
            {
                EntityType = "Property",
                EntityId = "2",
                LocalData = localData,
                RemoteData = remoteData,
                ConflictType = ConflictType.DataConflict,
                DetectedAt = DateTime.UtcNow
            };

            var conflicts = new List<SyncConflict> { conflict };

            _mockSyncService.Setup(s => s.ResolveConflictsAsync(It.IsAny<List<SyncConflict>>()))
                .ReturnsAsync(new ConflictResolutionResult
                {
                    Success = true,
                    ResolvedConflicts = conflicts,
                    UnresolvedConflicts = new List<SyncConflict>()
                });

            // Act
            var result = await _syncManager.HandleConflictsAsync(conflicts);

            // Assert
            Assert.True(result.Success);
            Assert.Equal(1, result.ResolvedConflicts.Count);
            Assert.Empty(result.UnresolvedConflicts);
        }

        [Fact]
        public async Task HandleConflicts_Manual_ShouldLeaveConflictsUnresolved()
        {
            // Arrange
            _testConfig.ConflictResolution = ConflictResolutionStrategy.Manual;
            
            var localData = new SyncData
            {
                EntityType = "Contract",
                EntityId = "3",
                JsonData = "{\"id\": 3, \"name\": \"Local Contract\"}",
                Operation = SyncOperation.Update,
                Timestamp = DateTime.UtcNow
            };

            var remoteData = new SyncData
            {
                EntityType = "Contract",
                EntityId = "3",
                JsonData = "{\"id\": 3, \"name\": \"Remote Contract\"}",
                Operation = SyncOperation.Update,
                Timestamp = DateTime.UtcNow
            };

            var conflict = new SyncConflict
            {
                EntityType = "Contract",
                EntityId = "3",
                LocalData = localData,
                RemoteData = remoteData,
                ConflictType = ConflictType.DataConflict,
                DetectedAt = DateTime.UtcNow
            };

            var conflicts = new List<SyncConflict> { conflict };

            _mockSyncService.Setup(s => s.ResolveConflictsAsync(It.IsAny<List<SyncConflict>>()))
                .ReturnsAsync(new ConflictResolutionResult
                {
                    Success = false,
                    ResolvedConflicts = new List<SyncConflict>(),
                    UnresolvedConflicts = conflicts
                });

            // Act
            var result = await _syncManager.HandleConflictsAsync(conflicts);

            // Assert
            Assert.False(result.Success);
            Assert.Empty(result.ResolvedConflicts);
            Assert.Equal(1, result.UnresolvedConflicts.Count);
        }

        [Fact]
        public async Task HandleConflicts_MergeChanges_ShouldResolveCorrectly()
        {
            // Arrange
            _testConfig.ConflictResolution = ConflictResolutionStrategy.MergeChanges;
            
            var localData = new SyncData
            {
                EntityType = "Payment",
                EntityId = "4",
                JsonData = "{\"id\": 4, \"amount\": 1000, \"date\": \"2025-01-01\"}",
                Operation = SyncOperation.Update,
                Timestamp = DateTime.UtcNow
            };

            var remoteData = new SyncData
            {
                EntityType = "Payment",
                EntityId = "4",
                JsonData = "{\"id\": 4, \"amount\": 1200, \"receipt\": \"R2025-001\"}",
                Operation = SyncOperation.Update,
                Timestamp = DateTime.UtcNow
            };

            var conflict = new SyncConflict
            {
                EntityType = "Payment",
                EntityId = "4",
                LocalData = localData,
                RemoteData = remoteData,
                ConflictType = ConflictType.DataConflict,
                DetectedAt = DateTime.UtcNow
            };

            var conflicts = new List<SyncConflict> { conflict };

            _mockSyncService.Setup(s => s.ResolveConflictsAsync(It.IsAny<List<SyncConflict>>()))
                .ReturnsAsync(new ConflictResolutionResult
                {
                    Success = true,
                    ResolvedConflicts = conflicts,
                    UnresolvedConflicts = new List<SyncConflict>()
                });

            // Act
            var result = await _syncManager.HandleConflictsAsync(conflicts);

            // Assert
            Assert.True(result.Success);
            Assert.Equal(1, result.ResolvedConflicts.Count);
            Assert.Empty(result.UnresolvedConflicts);
        }

        [Fact]
        public async Task HandleConflicts_MultipleConflicts_ShouldResolveCorrectly()
        {
            // Arrange
            _testConfig.ConflictResolution = ConflictResolutionStrategy.ServerWins;
            
            var conflicts = new List<SyncConflict>
            {
                new SyncConflict
                {
                    EntityType = "Customer",
                    EntityId = "1",
                    LocalData = new SyncData { EntityType = "Customer", EntityId = "1" },
                    RemoteData = new SyncData { EntityType = "Customer", EntityId = "1" },
                    ConflictType = ConflictType.DataConflict
                },
                new SyncConflict
                {
                    EntityType = "Property",
                    EntityId = "2",
                    LocalData = new SyncData { EntityType = "Property", EntityId = "2" },
                    RemoteData = new SyncData { EntityType = "Property", EntityId = "2" },
                    ConflictType = ConflictType.TimestampConflict
                },
                new SyncConflict
                {
                    EntityType = "Contract",
                    EntityId = "3",
                    LocalData = new SyncData { EntityType = "Contract", EntityId = "3" },
                    RemoteData = new SyncData { EntityType = "Contract", EntityId = "3" },
                    ConflictType = ConflictType.OperationConflict
                }
            };

            _mockSyncService.Setup(s => s.ResolveConflictsAsync(It.IsAny<List<SyncConflict>>()))
                .ReturnsAsync(new ConflictResolutionResult
                {
                    Success = true,
                    ResolvedConflicts = conflicts,
                    UnresolvedConflicts = new List<SyncConflict>()
                });

            // Act
            var result = await _syncManager.HandleConflictsAsync(conflicts);

            // Assert
            Assert.True(result.Success);
            Assert.Equal(3, result.ResolvedConflicts.Count);
            Assert.Empty(result.UnresolvedConflicts);
        }

        [Fact]
        public async Task HandleConflicts_MixedResolution_ShouldResolvePartially()
        {
            // Arrange
            _testConfig.ConflictResolution = ConflictResolutionStrategy.ServerWins;
            
            var conflicts = new List<SyncConflict>
            {
                new SyncConflict
                {
                    EntityType = "Customer",
                    EntityId = "1",
                    LocalData = new SyncData { EntityType = "Customer", EntityId = "1" },
                    RemoteData = new SyncData { EntityType = "Customer", EntityId = "1" },
                    ConflictType = ConflictType.DataConflict
                },
                new SyncConflict
                {
                    EntityType = "Property",
                    EntityId = "2",
                    LocalData = new SyncData { EntityType = "Property", EntityId = "2" },
                    RemoteData = new SyncData { EntityType = "Property", EntityId = "2" },
                    ConflictType = ConflictType.TimestampConflict
                }
            };

            _mockSyncService.Setup(s => s.ResolveConflictsAsync(It.IsAny<List<SyncConflict>>()))
                .ReturnsAsync(new ConflictResolutionResult
                {
                    Success = true,
                    ResolvedConflicts = new List<SyncConflict> { conflicts[0] },
                    UnresolvedConflicts = new List<SyncConflict> { conflicts[1] }
                });

            // Act
            var result = await _syncManager.HandleConflictsAsync(conflicts);

            // Assert
            Assert.True(result.Success);
            Assert.Equal(1, result.ResolvedConflicts.Count);
            Assert.Equal(1, result.UnresolvedConflicts.Count);
            Assert.Equal("Customer", result.ResolvedConflicts[0].EntityType);
            Assert.Equal("Property", result.UnresolvedConflicts[0].EntityType);
        }

        [Fact]
        public async Task HandleConflicts_WithNullConflicts_ShouldReturnEmptyResult()
        {
            // Act
            var result = await _syncManager.HandleConflictsAsync(null);

            // Assert
            Assert.False(result.Success);
            Assert.Empty(result.ResolvedConflicts);
            Assert.Empty(result.UnresolvedConflicts);
        }

        [Fact]
        public async Task HandleConflicts_WithEmptyConflicts_ShouldReturnEmptyResult()
        {
            // Act
            var result = await _syncManager.HandleConflictsAsync(new List<SyncConflict>());

            // Assert
            Assert.False(result.Success);
            Assert.Empty(result.ResolvedConflicts);
            Assert.Empty(result.UnresolvedConflicts);
        }

        [Fact]
        public async Task HandleConflicts_WithServiceError_ShouldReturnFailureResult()
        {
            // Arrange
            var conflicts = new List<SyncConflict>
            {
                new SyncConflict
                {
                    EntityType = "Customer",
                    EntityId = "1",
                    LocalData = new SyncData { EntityType = "Customer", EntityId = "1" },
                    RemoteData = new SyncData { EntityType = "Customer", EntityId = "1" },
                    ConflictType = ConflictType.DataConflict
                }
            };

            _mockSyncService.Setup(s => s.ResolveConflictsAsync(It.IsAny<List<SyncConflict>>()))
                .ThrowsAsync(new Exception("Test error"));

            // Act
            var result = await _syncManager.HandleConflictsAsync(conflicts);

            // Assert
            Assert.False(result.Success);
            Assert.Empty(result.ResolvedConflicts);
            Assert.Equal(1, result.UnresolvedConflicts.Count);
            Assert.Contains("فشل في حل التعارضات", result.Message);
        }

        [Theory]
        [InlineData(ConflictResolutionStrategy.ServerWins)]
        [InlineData(ConflictResolutionStrategy.ClientWins)]
        [InlineData(ConflictResolutionStrategy.Manual)]
        [InlineData(ConflictResolutionStrategy.MergeChanges)]
        public void ConflictResolutionStrategy_AllValues_ShouldBeValid(ConflictResolutionStrategy strategy)
        {
            // Arrange
            _testConfig.ConflictResolution = strategy;

            // Act & Assert
            Assert.Equal(strategy, _testConfig.ConflictResolution);
        }

        [Theory]
        [InlineData(ConflictType.DataConflict)]
        [InlineData(ConflictType.TimestampConflict)]
        [InlineData(ConflictType.OperationConflict)]
        [InlineData(ConflictType.DeleteConflict)]
        public void ConflictType_AllValues_ShouldBeValid(ConflictType type)
        {
            // Arrange
            var conflict = new SyncConflict { ConflictType = type };

            // Act & Assert
            Assert.Equal(type, conflict.ConflictType);
        }

        [Fact]
        public void SyncConflict_IsResolved_ShouldBeInitiallyFalse()
        {
            // Arrange
            var conflict = new SyncConflict();

            // Act & Assert
            Assert.False(conflict.IsResolved);
            Assert.Null(conflict.ResolvedAt);
            Assert.Null(conflict.Resolution);
        }

        [Fact]
        public void ConflictResolutionResult_Creation_ShouldInitializeCollections()
        {
            // Act
            var result = new ConflictResolutionResult();

            // Assert
            Assert.NotNull(result.ResolvedConflicts);
            Assert.NotNull(result.UnresolvedConflicts);
            Assert.Empty(result.ResolvedConflicts);
            Assert.Empty(result.UnresolvedConflicts);
            Assert.False(result.Success);
        }
    }
}
