using Microsoft.EntityFrameworkCore;
using RentalManagement.Data;
using RentalManagement.Helpers;
using RentalManagement.Models;
using RentalManagement.Services;
using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;
using System.Windows.Input;

namespace RentalManagement.ViewModels
{
    public class PropertyDialogViewModel : BaseViewModel
    {
        private Property _property;
        private PropertyType _type;
        private string _location = string.Empty;
        private decimal _rentAmount;
        private PropertyStatus _status;
        private string _description = string.Empty;
        private Customer? _selectedCustomer;

        public PropertyDialogViewModel() : this(new Property())
        {
            Title = "إضافة عقار جديد";
        }

        public PropertyDialogViewModel(Property property)
        {
            _property = property ?? new Property();
            Title = property.Id == 0 ? "إضافة عقار جديد" : "تعديل بيانات العقار";
            
            Customers = new ObservableCollection<Customer>();
            
            // Initialize properties
            Type = _property.Type;
            Location = _property.Location;
            RentAmount = _property.RentAmount;
            Status = _property.Status;
            Description = _property.Description;

            // Initialize commands
            SaveCommand = new RelayCommand(Save, CanSave);
            CancelCommand = new RelayCommand(Cancel);

            LoadCustomers();
        }

        public Property Property => _property;

        public ObservableCollection<Customer> Customers { get; }

        [Required(ErrorMessage = "نوع العقار مطلوب")]
        public PropertyType Type
        {
            get => _type;
            set
            {
                if (SetProperty(ref _type, value))
                {
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        [Required(ErrorMessage = "الموقع مطلوب")]
        [StringLength(200, ErrorMessage = "الموقع لا يجب أن يتجاوز 200 حرف")]
        public string Location
        {
            get => _location;
            set
            {
                if (SetProperty(ref _location, value))
                {
                    ValidateProperty(value);
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        [Required(ErrorMessage = "مبلغ الكراء مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "مبلغ الكراء يجب أن يكون أكبر من صفر")]
        public decimal RentAmount
        {
            get => _rentAmount;
            set
            {
                if (SetProperty(ref _rentAmount, value))
                {
                    ValidateProperty(value);
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        public PropertyStatus Status
        {
            get => _status;
            set
            {
                if (SetProperty(ref _status, value))
                {
                    // If status is Available, clear customer selection
                    if (value == PropertyStatus.Available)
                    {
                        SelectedCustomer = null;
                    }
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        [StringLength(500, ErrorMessage = "الوصف لا يجب أن يتجاوز 500 حرف")]
        public string Description
        {
            get => _description;
            set
            {
                if (SetProperty(ref _description, value))
                {
                    ValidateProperty(value);
                }
            }
        }

        public Customer? SelectedCustomer
        {
            get => _selectedCustomer;
            set => SetProperty(ref _selectedCustomer, value);
        }

        // Commands
        public ICommand SaveCommand { get; }
        public ICommand CancelCommand { get; }

        // Events
        public event EventHandler<bool>? DialogClosed;

        private bool CanSave()
        {
            return !string.IsNullOrWhiteSpace(Location) &&
                   RentAmount > 0 &&
                   IsValid();
        }

        private void Save()
        {
            if (!CanSave()) return;

            try
            {
                ClearError();

                // Update property properties
                _property.Type = Type;
                _property.Location = Location.Trim();
                _property.RentAmount = RentAmount;
                _property.Status = Status;
                _property.Description = Description.Trim();
                _property.CustomerId = SelectedCustomer?.Id;

                if (_property.Id == 0)
                {
                    _property.CreatedDate = DateTime.Now;
                }

                DialogClosed?.Invoke(this, true);
            }
            catch (Exception ex)
            {
                SetError($"خطأ في حفظ البيانات: {ex.Message}");
            }
        }

        private void Cancel()
        {
            DialogClosed?.Invoke(this, false);
        }

        private async void LoadCustomers()
        {
            try
            {
                using var context = DatabaseService.Instance.CreateContext();
                var customers = await context.Customers
                    .OrderBy(c => c.FullName)
                    .ToListAsync();

                Customers.Clear();
                foreach (var customer in customers)
                {
                    Customers.Add(customer);
                }

                // Set selected customer if property has one
                if (_property.CustomerId.HasValue)
                {
                    SelectedCustomer = Customers.FirstOrDefault(c => c.Id == _property.CustomerId.Value);
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في تحميل الزبائن: {ex.Message}");
            }
        }

        private void ValidateProperty(object? value, [System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            if (propertyName == null) return;

            var context = new ValidationContext(this) { MemberName = propertyName };
            var results = new List<ValidationResult>();
            
            Validator.TryValidateProperty(value, context, results);
            
            if (results.Any())
            {
                SetError(results.First().ErrorMessage ?? "خطأ في التحقق من صحة البيانات");
            }
            else
            {
                ClearError();
            }
        }

        private bool IsValid()
        {
            var context = new ValidationContext(this);
            var results = new List<ValidationResult>();
            return Validator.TryValidateObject(this, context, results, true);
        }
    }
}
