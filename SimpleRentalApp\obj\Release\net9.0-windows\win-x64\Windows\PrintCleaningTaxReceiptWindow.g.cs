﻿#pragma checksum "..\..\..\..\..\Windows\PrintCleaningTaxReceiptWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "6B17AC0B14F5D7F2F0C36FEAFACA1A6E1EDB5413"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleRentalApp.Windows {
    
    
    /// <summary>
    /// PrintCleaningTaxReceiptWindow
    /// </summary>
    public partial class PrintCleaningTaxReceiptWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 45 "..\..\..\..\..\Windows\PrintCleaningTaxReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\..\Windows\PrintCleaningTaxReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PreviewPrintButton;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\..\Windows\PrintCleaningTaxReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintButton;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\..\Windows\PrintCleaningTaxReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveAsPdfButton;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\..\Windows\PrintCleaningTaxReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\..\Windows\PrintCleaningTaxReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border ReceiptBorder;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\..\Windows\PrintCleaningTaxReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ReceiptContent;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\..\..\Windows\PrintCleaningTaxReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ReceiptNumberText;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\..\..\Windows\PrintCleaningTaxReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CustomerNameText;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\..\Windows\PrintCleaningTaxReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PropertyNameText;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\..\Windows\PrintCleaningTaxReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PropertyAddressText;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\..\Windows\PrintCleaningTaxReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaymentDateText;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\..\..\Windows\PrintCleaningTaxReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TaxYearText;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\..\..\Windows\PrintCleaningTaxReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AmountText;
        
        #line default
        #line hidden
        
        
        #line 184 "..\..\..\..\..\Windows\PrintCleaningTaxReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AmountInWordsText;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\..\..\Windows\PrintCleaningTaxReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaymentTypeText;
        
        #line default
        #line hidden
        
        
        #line 190 "..\..\..\..\..\Windows\PrintCleaningTaxReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RemainingAmountText;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\..\..\Windows\PrintCleaningTaxReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel NotesSection;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\..\..\Windows\PrintCleaningTaxReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NotesText;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\..\..\Windows\PrintCleaningTaxReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border LegalNoticeBorder;
        
        #line default
        #line hidden
        
        
        #line 209 "..\..\..\..\..\Windows\PrintCleaningTaxReceiptWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaymentStatusNotice;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleRentalApp;V1.0.0.0;component/windows/printcleaningtaxreceiptwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\PrintCleaningTaxReceiptWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.PreviewPrintButton = ((System.Windows.Controls.Button)(target));
            
            #line 60 "..\..\..\..\..\Windows\PrintCleaningTaxReceiptWindow.xaml"
            this.PreviewPrintButton.Click += new System.Windows.RoutedEventHandler(this.PreviewPrintButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.PrintButton = ((System.Windows.Controls.Button)(target));
            
            #line 67 "..\..\..\..\..\Windows\PrintCleaningTaxReceiptWindow.xaml"
            this.PrintButton.Click += new System.Windows.RoutedEventHandler(this.PrintButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.SaveAsPdfButton = ((System.Windows.Controls.Button)(target));
            
            #line 74 "..\..\..\..\..\Windows\PrintCleaningTaxReceiptWindow.xaml"
            this.SaveAsPdfButton.Click += new System.Windows.RoutedEventHandler(this.SaveAsPdfButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 81 "..\..\..\..\..\Windows\PrintCleaningTaxReceiptWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.ReceiptBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 7:
            this.ReceiptContent = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 8:
            this.ReceiptNumberText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.CustomerNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.PropertyNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.PropertyAddressText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.PaymentDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.TaxYearText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.AmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.AmountInWordsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.PaymentTypeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.RemainingAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.NotesSection = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 19:
            this.NotesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.LegalNoticeBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 21:
            this.PaymentStatusNotice = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

