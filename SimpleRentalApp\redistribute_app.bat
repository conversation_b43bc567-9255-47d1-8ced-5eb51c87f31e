@echo off
chcp 65001 > nul
echo ========================================
echo    إعادة توزيع تطبيق تدبير الكراء
echo    Redistributing Rental Management App
echo ========================================
echo.

echo [1/8] تنظيف المشروع...
echo [1/8] Cleaning project...
dotnet clean --configuration Release
if errorlevel 1 (
    echo خطأ في تنظيف المشروع
    echo Error cleaning project
    pause
    exit /b 1
)

echo [2/8] بناء المشروع...
echo [2/8] Building project...
dotnet build --configuration Release
if errorlevel 1 (
    echo خطأ في بناء المشروع
    echo Error building project
    pause
    exit /b 1
)

echo [3/8] إنشاء نشر محمول...
echo [3/8] Creating portable release...
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output dist_portable
if errorlevel 1 (
    echo خطأ في النشر المحمول
    echo Error in portable publishing
    pause
    exit /b 1
)

echo [4/8] نسخ الأيقونة والملفات الإضافية...
echo [4/8] Copying icon and additional files...
copy app_icon.ico dist_portable\ > nul 2>&1
copy rental_icon.png dist_portable\ > nul 2>&1
copy README.md dist_portable\ > nul 2>&1

echo [5/8] إنشاء ملف تشغيل عربي...
echo [5/8] Creating Arabic launcher...
echo @echo off > dist_portable\تشغيل_التطبيق.bat
echo chcp 65001 ^> nul >> dist_portable\تشغيل_التطبيق.bat
echo title نظام تدبير الكراء >> dist_portable\تشغيل_التطبيق.bat
echo echo ======================================== >> dist_portable\تشغيل_التطبيق.bat
echo echo       نظام تدبير الكراء >> dist_portable\تشغيل_التطبيق.bat
echo echo    Rental Management System >> dist_portable\تشغيل_التطبيق.bat
echo echo ======================================== >> dist_portable\تشغيل_التطبيق.bat
echo echo. >> dist_portable\تشغيل_التطبيق.bat
echo echo المطور: حفيظ عبدو >> dist_portable\تشغيل_التطبيق.bat
echo echo Developer: Hafid Abdo >> dist_portable\تشغيل_التطبيق.bat
echo echo الإصدار: 1.0.0 مع الأيقونة الجديدة >> dist_portable\تشغيل_التطبيق.bat
echo echo Version: 1.0.0 with New Icon >> dist_portable\تشغيل_التطبيق.bat
echo echo. >> dist_portable\تشغيل_التطبيق.bat
echo echo بدء تشغيل النظام... >> dist_portable\تشغيل_التطبيق.bat
echo echo Starting system... >> dist_portable\تشغيل_التطبيق.bat
echo echo. >> dist_portable\تشغيل_التطبيق.bat
echo start SimpleRentalApp.exe >> dist_portable\تشغيل_التطبيق.bat

echo [6/8] إنشاء دليل المستخدم...
echo [6/8] Creating user guide...
echo نظام تدبير الكراء - الإصدار الجديد مع الأيقونة المحسنة > dist_portable\دليل_المستخدم.txt
echo ================================================== >> dist_portable\دليل_المستخدم.txt
echo. >> dist_portable\دليل_المستخدم.txt
echo مرحباً بك في نظام تدبير الكراء المطور من طرف حفيظ عبدو >> dist_portable\دليل_المستخدم.txt
echo. >> dist_portable\دليل_المستخدم.txt
echo الجديد في هذا الإصدار: >> dist_portable\دليل_المستخدم.txt
echo ✓ أيقونة جديدة احترافية >> dist_portable\دليل_المستخدم.txt
echo ✓ تحسينات في الأداء >> dist_portable\دليل_المستخدم.txt
echo ✓ إصلاح الأخطاء >> dist_portable\دليل_المستخدم.txt
echo. >> dist_portable\دليل_المستخدم.txt
echo طريقة التشغيل: >> dist_portable\دليل_المستخدم.txt
echo 1. تشغيل ملف "تشغيل_التطبيق.bat" >> dist_portable\دليل_المستخدم.txt
echo 2. أو تشغيل "SimpleRentalApp.exe" مباشرة >> dist_portable\دليل_المستخدم.txt
echo. >> dist_portable\دليل_المستخدم.txt
echo بيانات تسجيل الدخول: >> dist_portable\دليل_المستخدم.txt
echo اسم المستخدم: hafid >> dist_portable\دليل_المستخدم.txt
echo كلمة المرور: hafidos159357 >> dist_portable\دليل_المستخدم.txt
echo. >> dist_portable\دليل_المستخدم.txt
echo للدعم الفني يرجى التواصل مع المطور >> dist_portable\دليل_المستخدم.txt

echo [7/8] إنشاء ملف مضغوط للتوزيع...
echo [7/8] Creating distribution archive...
powershell -Command "Compress-Archive -Path 'dist_portable\*' -DestinationPath 'RentalManagement_v1.0_NewIcon.zip' -Force"

echo [8/8] إنشاء معلومات الإصدار...
echo [8/8] Creating release information...
echo نظام تدبير الكراء - معلومات الإصدار > RELEASE_INFO.txt
echo ======================================= >> RELEASE_INFO.txt
echo. >> RELEASE_INFO.txt
echo الإصدار: 1.0.0 >> RELEASE_INFO.txt
echo تاريخ الإصدار: %date% >> RELEASE_INFO.txt
echo المطور: حفيظ عبدو >> RELEASE_INFO.txt
echo. >> RELEASE_INFO.txt
echo الجديد في هذا الإصدار: >> RELEASE_INFO.txt
echo - أيقونة جديدة احترافية تعكس طبيعة التطبيق >> RELEASE_INFO.txt
echo - تحسينات في واجهة المستخدم >> RELEASE_INFO.txt
echo - إصلاح مشاكل البناء والنشر >> RELEASE_INFO.txt
echo - تحسين ملفات التوزيع >> RELEASE_INFO.txt
echo. >> RELEASE_INFO.txt
echo الملفات المتاحة: >> RELEASE_INFO.txt
echo - RentalManagement_v1.0_NewIcon.zip (النسخة المحمولة) >> RELEASE_INFO.txt
echo - dist_portable\ (مجلد النسخة المحمولة) >> RELEASE_INFO.txt
echo. >> RELEASE_INFO.txt
echo متطلبات النظام: >> RELEASE_INFO.txt
echo - Windows 10 أو أحدث >> RELEASE_INFO.txt
echo - .NET 9.0 Runtime (مضمن) >> RELEASE_INFO.txt
echo - 4 GB RAM >> RELEASE_INFO.txt
echo - 200 MB مساحة فارغة >> RELEASE_INFO.txt

echo.
echo ========================================
echo تم إعادة توزيع التطبيق بنجاح!
echo Application redistributed successfully!
echo.
echo الملفات المنشأة:
echo Files created:
echo - dist_portable\ (مجلد النسخة المحمولة)
echo - RentalManagement_v1.0_NewIcon.zip (ملف مضغوط)
echo - RELEASE_INFO.txt (معلومات الإصدار)
echo.
echo حجم الملف المضغوط:
echo Compressed file size:
for %%A in ("RentalManagement_v1.0_NewIcon.zip") do echo %%~zA bytes
echo.
echo التطبيق جاهز للتوزيع!
echo Application ready for distribution!
echo ========================================
echo.
pause
