using Microsoft.EntityFrameworkCore;
using SimpleRentalApp.Data;
using SimpleRentalApp.Models;
using SimpleRentalApp.Services;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Media;
using System.Windows.Media.Effects;

namespace SimpleRentalApp.Windows
{
    public partial class AdvancedNotificationsWindow : Window
    {
        private readonly RentalDbContext _context;
        private readonly AdvancedNotificationService _notificationService;
        private readonly WhatsAppSenderService _whatsAppSender;
        private readonly NotificationTemplateService _templateService;
        private ObservableCollection<NotificationDisplayInfo> _allNotifications;
        private ObservableCollection<NotificationDisplayInfo> _filteredNotifications;
        private CollectionViewSource _notificationsViewSource;
        private ICollectionView _notificationsView;
        private bool _isDarkMode = false;

        public AdvancedNotificationsWindow()
        {
            InitializeComponent();
            _context = new RentalDbContext();
            _notificationService = new AdvancedNotificationService(_context);
            _whatsAppSender = new WhatsAppSenderService();
            _templateService = new NotificationTemplateService(_context);
            _allNotifications = new ObservableCollection<NotificationDisplayInfo>();
            _filteredNotifications = new ObservableCollection<NotificationDisplayInfo>();

            InitializeFilters();
            LoadNotificationsAsync();
            UpdateLastUpdateTime();
        }

        private void InitializeFilters()
        {
            // Type Filter
            TypeFilterComboBox.Items.Add(new ComboBoxItem { Content = "جميع الأنواع", Tag = null });
            TypeFilterComboBox.Items.Add(new ComboBoxItem { Content = "إيجار شهري", Tag = NotificationType.MonthlyRent });
            TypeFilterComboBox.Items.Add(new ComboBoxItem { Content = "ضريبة النظافة", Tag = NotificationType.CleaningTax });
            TypeFilterComboBox.Items.Add(new ComboBoxItem { Content = "تأخير إيجار", Tag = NotificationType.RentOverdue });
            TypeFilterComboBox.Items.Add(new ComboBoxItem { Content = "تأخير ضريبة النظافة", Tag = NotificationType.CleaningTaxOverdue });
            TypeFilterComboBox.Items.Add(new ComboBoxItem { Content = "انتهاء العقد", Tag = NotificationType.ContractExpiry });
            TypeFilterComboBox.Items.Add(new ComboBoxItem { Content = "تذكير دفع", Tag = NotificationType.PaymentReminder });
            TypeFilterComboBox.SelectedIndex = 0;

            // Status Filter
            StatusFilterComboBox.Items.Add(new ComboBoxItem { Content = "جميع الحالات", Tag = null });
            StatusFilterComboBox.Items.Add(new ComboBoxItem { Content = "بانتظار الإرسال", Tag = NotificationStatus.Pending });
            StatusFilterComboBox.Items.Add(new ComboBoxItem { Content = "تم الإرسال", Tag = NotificationStatus.Sent });
            StatusFilterComboBox.Items.Add(new ComboBoxItem { Content = "فشل الإرسال", Tag = NotificationStatus.Failed });
            StatusFilterComboBox.Items.Add(new ComboBoxItem { Content = "ملغي", Tag = NotificationStatus.Cancelled });
            StatusFilterComboBox.Items.Add(new ComboBoxItem { Content = "مجدول", Tag = NotificationStatus.Scheduled });
            StatusFilterComboBox.SelectedIndex = 0;

            // Priority Filter
            PriorityFilterComboBox.Items.Add(new ComboBoxItem { Content = "جميع الأولويات", Tag = null });
            PriorityFilterComboBox.Items.Add(new ComboBoxItem { Content = "منخفضة", Tag = NotificationPriority.Low });
            PriorityFilterComboBox.Items.Add(new ComboBoxItem { Content = "عادية", Tag = NotificationPriority.Normal });
            PriorityFilterComboBox.Items.Add(new ComboBoxItem { Content = "عالية", Tag = NotificationPriority.High });
            PriorityFilterComboBox.Items.Add(new ComboBoxItem { Content = "عاجلة", Tag = NotificationPriority.Urgent });
            PriorityFilterComboBox.SelectedIndex = 0;
        }

        private async void LoadNotificationsAsync()
        {
            try
            {
                StatusTextBlock.Text = "جاري تحميل الإشعارات...";
                
                var notifications = await _notificationService.GetNotificationsAsync();
                
                _allNotifications.Clear();
                foreach (var notification in notifications)
                {
                    _allNotifications.Add(notification);
                }

                ApplyFilters();
                UpdateCountDisplay();
                UpdateLastUpdateTime();
                
                StatusTextBlock.Text = $"تم تحميل {notifications.Count} إشعار بنجاح";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإشعارات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "خطأ في تحميل الإشعارات";
            }
        }

        private void ApplyFilters()
        {
            try
            {
                var filtered = _allNotifications.AsEnumerable();

                // Type filter
                if (TypeFilterComboBox.SelectedItem is ComboBoxItem typeItem && typeItem.Tag != null)
                {
                    var selectedType = (NotificationType)typeItem.Tag;
                    filtered = filtered.Where(n => n.Type == selectedType);
                }

                // Status filter
                if (StatusFilterComboBox.SelectedItem is ComboBoxItem statusItem && statusItem.Tag != null)
                {
                    var selectedStatus = (NotificationStatus)statusItem.Tag;
                    filtered = filtered.Where(n => n.Status == selectedStatus);
                }

                // Priority filter
                if (PriorityFilterComboBox.SelectedItem is ComboBoxItem priorityItem && priorityItem.Tag != null)
                {
                    var selectedPriority = (NotificationPriority)priorityItem.Tag;
                    filtered = filtered.Where(n => n.Priority == selectedPriority);
                }

                // Search filter
                var searchText = SearchTextBox.Text?.Trim();
                if (!string.IsNullOrEmpty(searchText) && searchText != "البحث بالاسم أو المحل..." && 
                    SearchTextBox.Foreground != Brushes.Gray)
                {
                    filtered = filtered.Where(n => 
                        n.CustomerName.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                        n.PropertyName.Contains(searchText, StringComparison.OrdinalIgnoreCase));
                }

                _filteredNotifications.Clear();
                foreach (var notification in filtered.OrderByDescending(n => n.DueDate))
                {
                    _filteredNotifications.Add(notification);
                }

                NotificationsDataGrid.ItemsSource = _filteredNotifications;
                UpdateCountDisplay();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق التصفية: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateCountDisplay()
        {
            var totalCount = _allNotifications.Count;
            var filteredCount = _filteredNotifications.Count;
            
            if (totalCount == filteredCount)
            {
                CountTextBlock.Text = $"({totalCount} إشعار)";
            }
            else
            {
                CountTextBlock.Text = $"({filteredCount} من أصل {totalCount} إشعار)";
            }
        }

        private void UpdateLastUpdateTime()
        {
            LastUpdateTextBlock.Text = DateTime.Now.ToString("HH:mm:ss");
        }

        // Event Handlers
        private async void GenerateMonthlyRentBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "هل تريد توليد إشعارات الإيجار الشهري للشهر الحالي؟\n\nسيتم إنشاء إشعارات لجميع العقود النشطة.",
                    "توليد إشعارات الإيجار الشهري",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    StatusTextBlock.Text = "جاري توليد إشعارات الإيجار الشهري...";
                    
                    var currentDate = DateTime.Now;
                    var notifications = await _notificationService.GenerateMonthlyRentNotificationsAsync(
                        currentDate.Year, currentDate.Month);

                    MessageBox.Show($"تم توليد {notifications.Count} إشعار إيجار شهري بنجاح!", "تم التوليد",
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    LoadNotificationsAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في توليد إشعارات الإيجار الشهري: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "خطأ في توليد الإشعارات";
            }
        }

        private async void GenerateCleaningTaxBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "هل تريد توليد إشعارات ضريبة النظافة للسنة الحالية؟\n\nسيتم إنشاء إشعارات لجميع العقود النشطة.",
                    "توليد إشعارات ضريبة النظافة",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    StatusTextBlock.Text = "جاري توليد إشعارات ضريبة النظافة...";
                    
                    var currentYear = DateTime.Now.Year;
                    var notifications = await _notificationService.GenerateCleaningTaxNotificationsAsync(currentYear);

                    MessageBox.Show($"تم توليد {notifications.Count} إشعار ضريبة نظافة بنجاح!", "تم التوليد",
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    LoadNotificationsAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في توليد إشعارات ضريبة النظافة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "خطأ في توليد الإشعارات";
            }
        }

        private async void GenerateOverdueBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "هل تريد توليد إشعارات التأخير للمدفوعات المستحقة؟\n\nسيتم فحص جميع الإشعارات المتأخرة وإنشاء تنبيهات مناسبة.",
                    "توليد إشعارات التأخير",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    StatusTextBlock.Text = "جاري توليد إشعارات التأخير...";
                    
                    var notifications = await _notificationService.GenerateOverdueNotificationsAsync();

                    MessageBox.Show($"تم توليد {notifications.Count} إشعار تأخير بنجاح!", "تم التوليد",
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    LoadNotificationsAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في توليد إشعارات التأخير: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "خطأ في توليد الإشعارات";
            }
        }

        private async void AutoUpdateBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                StatusTextBlock.Text = "جاري التحديث التلقائي...";
                
                var currentDate = DateTime.Now;
                
                // Generate all types of notifications
                var monthlyRent = await _notificationService.GenerateMonthlyRentNotificationsAsync(
                    currentDate.Year, currentDate.Month);
                var cleaningTax = await _notificationService.GenerateCleaningTaxNotificationsAsync(currentDate.Year);
                var overdue = await _notificationService.GenerateOverdueNotificationsAsync();

                var totalGenerated = monthlyRent.Count + cleaningTax.Count + overdue.Count;

                MessageBox.Show(
                    $"تم التحديث التلقائي بنجاح!\n\n" +
                    $"• إشعارات الإيجار الشهري: {monthlyRent.Count}\n" +
                    $"• إشعارات ضريبة النظافة: {cleaningTax.Count}\n" +
                    $"• إشعارات التأخير: {overdue.Count}\n\n" +
                    $"إجمالي الإشعارات الجديدة: {totalGenerated}",
                    "تم التحديث التلقائي",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);

                LoadNotificationsAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التحديث التلقائي: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "خطأ في التحديث التلقائي";
            }
        }

        private void StatisticsBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var pendingCount = _allNotifications.Count(n => n.Status == NotificationStatus.Pending);
                var sentCount = _allNotifications.Count(n => n.Status == NotificationStatus.Sent);
                var failedCount = _allNotifications.Count(n => n.Status == NotificationStatus.Failed);
                var overdueCount = _allNotifications.Count(n => n.IsOverdue);

                var rentCount = _allNotifications.Count(n => n.Type == NotificationType.MonthlyRent);
                var cleaningTaxCount = _allNotifications.Count(n => n.Type == NotificationType.CleaningTax);
                var overdueRentCount = _allNotifications.Count(n => n.Type == NotificationType.RentOverdue);
                var overdueCleaningTaxCount = _allNotifications.Count(n => n.Type == NotificationType.CleaningTaxOverdue);

                MessageBox.Show(
                    $"📊 إحصائيات الإشعارات\n\n" +
                    $"📈 حسب الحالة:\n" +
                    $"• بانتظار الإرسال: {pendingCount}\n" +
                    $"• تم الإرسال: {sentCount}\n" +
                    $"• فشل الإرسال: {failedCount}\n" +
                    $"• متأخرة: {overdueCount}\n\n" +
                    $"📋 حسب النوع:\n" +
                    $"• إيجار شهري: {rentCount}\n" +
                    $"• ضريبة النظافة: {cleaningTaxCount}\n" +
                    $"• تأخير إيجار: {overdueRentCount}\n" +
                    $"• تأخير ضريبة النظافة: {overdueCleaningTaxCount}\n\n" +
                    $"📊 الإجمالي: {_allNotifications.Count} إشعار",
                    "إحصائيات الإشعارات",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض الإحصائيات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // Filter event handlers
        private void TypeFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_allNotifications != null)
                ApplyFilters();
        }

        private void StatusFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_allNotifications != null)
                ApplyFilters();
        }

        private void PriorityFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_allNotifications != null)
                ApplyFilters();
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_allNotifications != null)
                ApplyFilters();
        }

        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (SearchTextBox.Text == "البحث بالاسم أو المحل..." && SearchTextBox.Foreground == Brushes.Gray)
            {
                SearchTextBox.Text = "";
                SearchTextBox.Foreground = Brushes.Black;
            }
        }

        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                SearchTextBox.Text = "البحث بالاسم أو المحل...";
                SearchTextBox.Foreground = Brushes.Gray;
            }
        }

        private void ClearFiltersBtn_Click(object sender, RoutedEventArgs e)
        {
            TypeFilterComboBox.SelectedIndex = 0;
            StatusFilterComboBox.SelectedIndex = 0;
            PriorityFilterComboBox.SelectedIndex = 0;
            SearchTextBox.Text = "البحث بالاسم أو المحل...";
            SearchTextBox.Foreground = Brushes.Gray;
            ApplyFilters();
        }

        private void SelectAllBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button)
                {
                    bool selectAll = button.Content.ToString().Contains("تحديد الكل");

                    foreach (var notification in _filteredNotifications)
                    {
                        notification.IsSelected = selectAll;
                    }

                    // تحديث نص الزر
                    button.Content = selectAll ? "❌ إلغاء التحديد" : "☑️ تحديد الكل";

                    // تحديث العرض
                    NotificationsDataGrid.Items.Refresh();

                    var selectedCount = _filteredNotifications.Count(n => n.IsSelected);
                    StatusTextBlock.Text = selectAll ?
                        $"تم تحديد {selectedCount} إشعار" :
                        "تم إلغاء تحديد جميع الإشعارات";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديد الإشعارات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // Action button handlers
        private async void SendNotificationBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is NotificationDisplayInfo notification)
                {
                    // التحقق من وجود رقم الهاتف
                    if (string.IsNullOrWhiteSpace(notification.PhoneNumber) || notification.PhoneNumber == "غير محدد")
                    {
                        MessageBox.Show($"لا يوجد رقم هاتف للمكتري {notification.CustomerName}.\n\nيرجى إضافة رقم الهاتف أولاً.",
                            "رقم هاتف مفقود",
                            MessageBoxButton.OK,
                            MessageBoxImage.Warning);
                        return;
                    }

                    // إنشاء الرسالة
                    string message = await GenerateNotificationMessage(notification);

                    if (string.IsNullOrWhiteSpace(message))
                    {
                        MessageBox.Show("فشل في إنشاء نص الرسالة.", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }

                    // فتح نافذة معاينة وتعديل الرسالة
                    var previewDialog = new MessagePreviewDialog(notification, message, _templateService);
                    previewDialog.Owner = this;

                    var dialogResult = previewDialog.ShowDialog();

                    if (dialogResult == true && previewDialog.SendMessage)
                    {
                        StatusTextBlock.Text = "جاري فتح الواتساب...";
                        button.IsEnabled = false;

                        // استخدام الرسالة المعدلة من نافذة المعاينة
                        var finalMessage = previewDialog.FinalMessage;

                        // إرسال الرسالة عبر الواتساب
                        bool success = await _whatsAppSender.SendMessageWithOptionsAsync(
                            notification.PhoneNumber,
                            finalMessage,
                            useApp: false,
                            showConfirmation: true);

                        if (success)
                        {
                            // تحديث حالة الإشعار
                            await UpdateNotificationStatus(notification, NotificationStatus.Sent);
                            StatusTextBlock.Text = "تم فتح الواتساب بنجاح!";
                            LoadNotificationsAsync();
                        }
                        else
                        {
                            StatusTextBlock.Text = "فشل في فتح الواتساب";
                        }

                        button.IsEnabled = true;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إرسال الإشعار: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "فشل في إرسال الإشعار";
            }
        }

        private void EditTemplateBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is NotificationDisplayInfo notification)
                {
                    var templateWindow = new MessageTemplatesWindow();
                    templateWindow.Owner = this;
                    templateWindow.ShowDialog();

                    // إعادة تحميل الإشعارات بعد تعديل القوالب
                    LoadNotificationsAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة القوالب: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PreviewMessageBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is NotificationDisplayInfo notification)
                {
                    var previewWindow = new MessagePreviewWindow(notification);
                    previewWindow.Owner = this;
                    previewWindow.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معاينة الرسالة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void RetryNotificationBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is NotificationDisplayInfo notification)
                {
                    var result = MessageBox.Show(
                        $"هل تريد إعادة محاولة إرسال الإشعار للمكتري {notification.CustomerName}؟",
                        "إعادة المحاولة",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        StatusTextBlock.Text = "جاري إعادة المحاولة...";

                        // هنا يمكن إضافة منطق إعادة الإرسال
                        await Task.Delay(1000);

                        MessageBox.Show("تم إعادة المحاولة بنجاح!", "تم",
                            MessageBoxButton.OK, MessageBoxImage.Information);

                        LoadNotificationsAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعادة المحاولة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void EditNotificationBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is NotificationDisplayInfo notification)
                {
                    var editWindow = new EditNotificationWindow(notification);
                    editWindow.Owner = this;
                    if (editWindow.ShowDialog() == true)
                    {
                        LoadNotificationsAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل الإشعار: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void CancelNotificationBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is NotificationDisplayInfo notification)
                {
                    var result = MessageBox.Show(
                        $"هل تريد إلغاء الإشعار للمكتري {notification.CustomerName}؟\n\nلن يتم إرسال هذا الإشعار.",
                        "تأكيد الإلغاء",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        StatusTextBlock.Text = "جاري إلغاء الإشعار...";

                        // هنا يمكن إضافة منطق الإلغاء
                        await Task.Delay(500);

                        MessageBox.Show("تم إلغاء الإشعار بنجاح!", "تم الإلغاء",
                            MessageBoxButton.OK, MessageBoxImage.Information);

                        LoadNotificationsAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إلغاء الإشعار: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void DeleteNotificationBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is NotificationDisplayInfo notification)
                {
                    var result = MessageBox.Show(
                        $"هل تريد حذف الإشعار للمكتري {notification.CustomerName} نهائياً؟\n\nلا يمكن التراجع عن هذا الإجراء.",
                        "تأكيد الحذف",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        StatusTextBlock.Text = "جاري حذف الإشعار...";

                        // هنا يمكن إضافة منطق الحذف
                        await Task.Delay(500);

                        MessageBox.Show("تم حذف الإشعار بنجاح!", "تم الحذف",
                            MessageBoxButton.OK, MessageBoxImage.Information);

                        LoadNotificationsAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الإشعار: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void WhatsAppSettingsBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var settingsWindow = new WhatsAppSettingsWindow();
                settingsWindow.Owner = this;
                if (settingsWindow.ShowDialog() == true)
                {
                    StatusTextBlock.Text = "تم حفظ إعدادات الواتساب بنجاح";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة إعدادات الواتساب: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void MessageTemplatesBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var templateWindow = new MessageTemplatesWindow();
                templateWindow.Owner = this;
                templateWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة قوالب الرسائل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BulkSendBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var selectedNotifications = _filteredNotifications.Where(n => n.IsSelected).ToList();

                if (!selectedNotifications.Any())
                {
                    MessageBox.Show("يرجى تحديد إشعار واحد على الأقل للإرسال", "لا توجد إشعارات محددة",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var result = MessageBox.Show(
                    $"هل تريد إرسال {selectedNotifications.Count} إشعار محدد؟\n\nسيتم إرسال جميع الإشعارات المحددة دفعة واحدة.",
                    "تأكيد الإرسال الجماعي",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    StatusTextBlock.Text = $"جاري إرسال {selectedNotifications.Count} إشعار...";

                    int successCount = 0;
                    int failureCount = 0;

                    foreach (var notification in selectedNotifications)
                    {
                        try
                        {
                            // التحقق من رقم الهاتف
                            if (string.IsNullOrWhiteSpace(notification.PhoneNumber) || notification.PhoneNumber == "غير محدد")
                            {
                                failureCount++;
                                continue;
                            }

                            // إنشاء الرسالة
                            string message = await GenerateNotificationMessage(notification);

                            if (string.IsNullOrWhiteSpace(message))
                            {
                                failureCount++;
                                continue;
                            }

                            // إرسال الرسالة
                            bool success = await _whatsAppSender.SendMessageWithOptionsAsync(
                                notification.PhoneNumber,
                                message,
                                useApp: false,
                                showConfirmation: false);

                            if (success)
                            {
                                await UpdateNotificationStatus(notification, NotificationStatus.Sent);
                                successCount++;
                            }
                            else
                            {
                                failureCount++;
                            }

                            // انتظار قصير بين الرسائل
                            await Task.Delay(1000);
                        }
                        catch
                        {
                            failureCount++;
                        }
                    }

                    MessageBox.Show(
                        $"تم الإرسال الجماعي!\n\n" +
                        $"✅ نجح: {successCount}\n" +
                        $"❌ فشل: {failureCount}\n" +
                        $"📊 الإجمالي: {selectedNotifications.Count}",
                        "نتائج الإرسال الجماعي",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);

                    LoadNotificationsAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الإرسال الجماعي: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ThemeToggleBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _isDarkMode = !_isDarkMode;

                if (sender is Button button)
                {
                    if (_isDarkMode)
                    {
                        // تطبيق الوضع الليلي
                        this.Background = new SolidColorBrush(Color.FromRgb(33, 37, 41));
                        button.Content = "☀️ الوضع النهاري";

                        // تحديث ألوان العناصر
                        foreach (Border border in FindVisualChildren<Border>(this))
                        {
                            if (border.Background is SolidColorBrush brush && brush.Color == Colors.White)
                            {
                                border.Background = new SolidColorBrush(Color.FromRgb(52, 58, 64));
                            }
                        }

                        foreach (TextBlock textBlock in FindVisualChildren<TextBlock>(this))
                        {
                            if (textBlock.Foreground is SolidColorBrush brush &&
                                (brush.Color == Color.FromRgb(44, 62, 80) || brush.Color == Colors.Black))
                            {
                                textBlock.Foreground = new SolidColorBrush(Colors.White);
                            }
                        }
                    }
                    else
                    {
                        // تطبيق الوضع النهاري
                        this.Background = new SolidColorBrush(Color.FromRgb(245, 245, 245));
                        button.Content = "🌙 الوضع الليلي";

                        // إعادة تعيين الألوان الأصلية
                        foreach (Border border in FindVisualChildren<Border>(this))
                        {
                            if (border.Background is SolidColorBrush brush && brush.Color == Color.FromRgb(52, 58, 64))
                            {
                                border.Background = new SolidColorBrush(Colors.White);
                            }
                        }

                        foreach (TextBlock textBlock in FindVisualChildren<TextBlock>(this))
                        {
                            if (textBlock.Foreground is SolidColorBrush brush && brush.Color == Colors.White)
                            {
                                textBlock.Foreground = new SolidColorBrush(Color.FromRgb(44, 62, 80));
                            }
                        }
                    }
                }

                StatusTextBlock.Text = _isDarkMode ? "تم تفعيل الوضع الليلي" : "تم تفعيل الوضع النهاري";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تبديل الوضع: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // Helper method to find visual children
        private static IEnumerable<T> FindVisualChildren<T>(DependencyObject depObj) where T : DependencyObject
        {
            if (depObj != null)
            {
                for (int i = 0; i < VisualTreeHelper.GetChildrenCount(depObj); i++)
                {
                    DependencyObject child = VisualTreeHelper.GetChild(depObj, i);
                    if (child != null && child is T)
                    {
                        yield return (T)child;
                    }

                    foreach (T childOfChild in FindVisualChildren<T>(child))
                    {
                        yield return childOfChild;
                    }
                }
            }
        }

        private async void AutoGenerateBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "هل تريد توليد الإشعارات تلقائياً؟\n\n" +
                    "سيتم إنشاء إشعارات للعقود والدفعات المستحقة:\n" +
                    "• إشعارات الإيجار الشهري\n" +
                    "• إشعارات ضريبة النظافة\n" +
                    "• إشعارات التأخير\n\n" +
                    "قد تستغرق هذه العملية بعض الوقت.",
                    "تأكيد التوليد التلقائي",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    StatusTextBlock.Text = "جاري توليد الإشعارات تلقائياً...";

                    if (sender is Button button)
                    {
                        button.IsEnabled = false;
                        button.Content = "⏳ جاري التوليد...";
                    }

                    // محاكاة عملية التوليد التلقائي
                    await Task.Delay(3000);

                    // هنا يمكن إضافة منطق التوليد الفعلي
                    int generatedCount = await GenerateAutomaticNotifications();

                    MessageBox.Show(
                        $"تم توليد {generatedCount} إشعار جديد بنجاح!\n\n" +
                        "تم إنشاء الإشعارات بناءً على:\n" +
                        "✅ العقود النشطة\n" +
                        "✅ الدفعات المستحقة\n" +
                        "✅ التأخيرات الحالية",
                        "تم التوليد التلقائي",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);

                    LoadNotificationsAsync();

                    if (sender is Button btn)
                    {
                        btn.IsEnabled = true;
                        btn.Content = "🤖 توليد تلقائي";
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التوليد التلقائي: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);

                if (sender is Button btn)
                {
                    btn.IsEnabled = true;
                    btn.Content = "🤖 توليد تلقائي";
                }
            }
        }

        private async Task<int> GenerateAutomaticNotifications()
        {
            // محاكاة توليد الإشعارات
            await Task.Delay(1000);

            // هنا يمكن إضافة منطق التوليد الفعلي:
            // 1. البحث عن العقود النشطة
            // 2. التحقق من الدفعات المستحقة
            // 3. إنشاء إشعارات للتأخيرات
            // 4. إنشاء إشعارات ضريبة النظافة

            Random random = new Random();
            return random.Next(5, 15); // محاكاة عدد الإشعارات المولدة
        }

        /// <summary>
        /// إنشاء نص الرسالة للإشعار باستخدام خدمة القوالب المحسنة
        /// </summary>
        private async Task<string> GenerateNotificationMessage(NotificationDisplayInfo notification)
        {
            try
            {
                // تحديث المبلغ أولاً إذا لزم الأمر
                await UpdateNotificationAmountIfNeeded(notification);

                // استخدام خدمة القوالب الجديدة
                var message = await _templateService.ApplyTemplateToNotificationAsync(notification.Notification);

                if (!string.IsNullOrWhiteSpace(message))
                {
                    return message;
                }

                // في حالة فشل خدمة القوالب، استخدم الطريقة القديمة كبديل
                return await GenerateNotificationMessageFallback(notification);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء رسالة الإشعار: {ex.Message}");
                return await GenerateNotificationMessageFallback(notification);
            }
        }

        /// <summary>
        /// تحديث مبلغ الإشعار إذا كان قديماً
        /// </summary>
        private async Task UpdateNotificationAmountIfNeeded(NotificationDisplayInfo notification)
        {
            try
            {
                if (notification.Notification.Contract != null)
                {
                    var rentCalculationService = new RentCalculationService(_context);
                    var currentRentAmount = rentCalculationService.GetCurrentRentAmount(notification.Notification.Contract);

                    // تحديث المبلغ إذا كان مختلفاً
                    if (notification.Type == NotificationType.MonthlyRent &&
                        Math.Abs(notification.Amount - currentRentAmount) > 0.01m)
                    {
                        notification.Notification.Amount = currentRentAmount;
                        notification.Amount = currentRentAmount;
                        notification.AmountDisplay = $"{currentRentAmount:N0} درهم";

                        // حفظ التحديث في قاعدة البيانات
                        _context.AdvancedNotifications.Update(notification.Notification);
                        await _context.SaveChangesAsync();
                    }
                    else if (notification.Type == NotificationType.CleaningTax)
                    {
                        var newAnnualTax = currentRentAmount * 12 * 0.105m;
                        if (Math.Abs(notification.Amount - newAnnualTax) > 0.01m)
                        {
                            notification.Notification.Amount = newAnnualTax;
                            notification.Amount = newAnnualTax;
                            notification.AmountDisplay = $"{newAnnualTax:N0} درهم";

                            // حفظ التحديث في قاعدة البيانات
                            _context.AdvancedNotifications.Update(notification.Notification);
                            await _context.SaveChangesAsync();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث مبلغ الإشعار: {ex.Message}");
            }
        }

        /// <summary>
        /// الطريقة البديلة لإنشاء الرسالة (الطريقة القديمة)
        /// </summary>
        private async Task<string> GenerateNotificationMessageFallback(NotificationDisplayInfo notification)
        {
            try
            {
                // البحث عن قالب مناسب للإشعار
                var template = await _context.MessageTemplates
                    .FirstOrDefaultAsync(t => t.Type == GetMessageTemplateType(notification.Type));

                string messageTemplate;

                if (template != null)
                {
                    messageTemplate = template.Template;
                }
                else
                {
                    // استخدام قالب افتراضي حسب نوع الإشعار
                    messageTemplate = GetDefaultTemplate(notification.Type);
                }

                // استبدال المتغيرات في القالب
                return ProcessMessageTemplate(messageTemplate, notification);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الطريقة البديلة لإنشاء رسالة الإشعار: {ex.Message}");
                return GetDefaultTemplate(notification.Type);
            }
        }

        /// <summary>
        /// ربط نوع الإشعار بنوع قالب الرسالة (تم تبسيط هذه الوظيفة)
        /// </summary>
        private NotificationType GetMessageTemplateType(NotificationType notificationType)
        {
            // النظام يستخدم NotificationType مباشرة في MessageTemplate
            return notificationType;
        }

        /// <summary>
        /// الحصول على قالب افتراضي حسب نوع الإشعار
        /// </summary>
        private string GetDefaultTemplate(NotificationType type)
        {
            return type switch
            {
                NotificationType.MonthlyRent =>
                    "السلام عليكم {CustomerName}\n\n" +
                    "نذكركم بأن إيجار {PropertyName} مستحق الدفع.\n\n" +
                    "المبلغ: {Amount} درهم\n" +
                    "تاريخ الاستحقاق: {DueDate}\n\n" +
                    "يرجى التواصل معنا لتسوية المبلغ.\n\n" +
                    "شكراً لكم",

                NotificationType.CleaningTax =>
                    "السلام عليكم {CustomerName}\n\n" +
                    "نذكركم بأن ضريبة النظافة لـ {PropertyName} مستحقة الدفع.\n\n" +
                    "المبلغ: {Amount} درهم\n" +
                    "السنة: {Year}\n\n" +
                    "يرجى التواصل معنا لتسوية المبلغ.\n\n" +
                    "شكراً لكم",

                NotificationType.RentOverdue =>
                    "السلام عليكم {CustomerName}\n\n" +
                    "⚠️ تذكير هام: إيجار {PropertyName} متأخر عن موعد الاستحقاق.\n\n" +
                    "المبلغ: {Amount} درهم\n" +
                    "تاريخ الاستحقاق: {DueDate}\n" +
                    "عدد الأيام المتأخرة: {OverdueDays}\n\n" +
                    "يرجى المبادرة بالدفع لتجنب أي إجراءات إضافية.\n\n" +
                    "شكراً لكم",

                NotificationType.PaymentReminder =>
                    "السلام عليكم {CustomerName}\n\n" +
                    "📌 تذكير ودي بالدفع المستحق لـ {PropertyName}.\n\n" +
                    "المبلغ: {Amount} درهم\n\n" +
                    "نقدر تفهمكم وتعاونكم.\n\n" +
                    "شكراً لكم",

                _ =>
                    "السلام عليكم {CustomerName}\n\n" +
                    "لديكم إشعار متعلق بـ {PropertyName}.\n\n" +
                    "يرجى التواصل معنا.\n\n" +
                    "شكراً لكم"
            };
        }

        /// <summary>
        /// معالجة قالب الرسالة واستبدال المتغيرات
        /// </summary>
        private string ProcessMessageTemplate(string template, NotificationDisplayInfo notification)
        {
            try
            {
                return template
                    .Replace("{CustomerName}", notification.CustomerName)
                    .Replace("{PropertyName}", notification.PropertyName)
                    .Replace("{Amount}", notification.Amount.ToString("N0"))
                    .Replace("{DueDate}", notification.DueDate.ToString("dd/MM/yyyy"))
                    .Replace("{Year}", notification.DueDate.Year.ToString())
                    .Replace("{OverdueDays}", notification.IsOverdue ?
                        (DateTime.Now - notification.DueDate).Days.ToString() : "0")
                    .Replace("{Today}", DateTime.Now.ToString("dd/MM/yyyy"));
            }
            catch (Exception)
            {
                return template; // إرجاع القالب الأصلي في حالة الخطأ
            }
        }

        /// <summary>
        /// تحديث حالة الإشعار في قاعدة البيانات
        /// </summary>
        private async Task UpdateNotificationStatus(NotificationDisplayInfo notification, NotificationStatus newStatus)
        {
            try
            {
                var dbNotification = await _context.AdvancedNotifications
                    .FirstOrDefaultAsync(n => n.Id == notification.Id);

                if (dbNotification != null)
                {
                    dbNotification.Status = newStatus;
                    dbNotification.SentDate = newStatus == NotificationStatus.Sent ? DateTime.Now : null;
                    dbNotification.LastSentDate = DateTime.Now;
                    dbNotification.AttemptCount = (dbNotification.AttemptCount ?? 0) + 1;
                    dbNotification.UpdatedDate = DateTime.Now;

                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث حالة الإشعار: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث مبالغ الإشعارات لتعكس الإيجار الحالي
        /// </summary>
        private async void UpdateAmountsBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                StatusTextBlock.Text = "جاري تحديث المبالغ...";

                var button = sender as Button;
                if (button != null)
                    button.IsEnabled = false;

                // تحديث المبالغ باستخدام خدمة الإشعارات
                var updatedCount = await _notificationService.UpdateNotificationAmountsAsync();

                if (updatedCount > 0)
                {
                    // إعادة تحميل الإشعارات لعرض المبالغ المحدثة
                    LoadNotificationsAsync();

                    StatusTextBlock.Text = $"تم تحديث {updatedCount} إشعار بنجاح ✅";

                    MessageBox.Show(
                        $"تم تحديث {updatedCount} إشعار لتعكس الإيجار الحالي.\n\n" +
                        "الآن تعرض الإشعارات المبالغ الصحيحة بناءً على آخر زيادات الإيجار.",
                        "تم التحديث بنجاح",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                }
                else
                {
                    StatusTextBlock.Text = "جميع المبالغ محدثة بالفعل ✅";

                    MessageBox.Show(
                        "جميع مبالغ الإشعارات محدثة بالفعل.\n\nلا توجد تغييرات مطلوبة.",
                        "لا توجد تحديثات",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "خطأ في تحديث المبالغ ❌";
                MessageBox.Show($"حدث خطأ أثناء تحديث المبالغ:\n\n{ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                var button = sender as Button;
                if (button != null)
                    button.IsEnabled = true;
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            _templateService?.Dispose();
            base.OnClosed(e);
        }
    }
}
