using Microsoft.EntityFrameworkCore;
using SimpleRentalApp.Data;
using SimpleRentalApp.Models;
using SimpleRentalApp.Services;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Media;

namespace SimpleRentalApp.Windows
{
    public partial class AdvancedNotificationsWindow : Window
    {
        private readonly RentalDbContext _context;
        private readonly AdvancedNotificationService _notificationService;
        private ObservableCollection<NotificationDisplayInfo> _allNotifications;
        private ObservableCollection<NotificationDisplayInfo> _filteredNotifications;
        private CollectionViewSource _notificationsViewSource;
        private ICollectionView _notificationsView;

        public AdvancedNotificationsWindow()
        {
            InitializeComponent();
            _context = new RentalDbContext();
            _notificationService = new AdvancedNotificationService(_context);
            _allNotifications = new ObservableCollection<NotificationDisplayInfo>();
            _filteredNotifications = new ObservableCollection<NotificationDisplayInfo>();
            
            InitializeFilters();
            LoadNotificationsAsync();
            UpdateLastUpdateTime();
        }

        private void InitializeFilters()
        {
            // Type Filter
            TypeFilterComboBox.Items.Add(new ComboBoxItem { Content = "جميع الأنواع", Tag = null });
            TypeFilterComboBox.Items.Add(new ComboBoxItem { Content = "إيجار شهري", Tag = NotificationType.MonthlyRent });
            TypeFilterComboBox.Items.Add(new ComboBoxItem { Content = "ضريبة النظافة", Tag = NotificationType.CleaningTax });
            TypeFilterComboBox.Items.Add(new ComboBoxItem { Content = "تأخير إيجار", Tag = NotificationType.RentOverdue });
            TypeFilterComboBox.Items.Add(new ComboBoxItem { Content = "تأخير ضريبة النظافة", Tag = NotificationType.CleaningTaxOverdue });
            TypeFilterComboBox.Items.Add(new ComboBoxItem { Content = "انتهاء العقد", Tag = NotificationType.ContractExpiry });
            TypeFilterComboBox.Items.Add(new ComboBoxItem { Content = "تذكير دفع", Tag = NotificationType.PaymentReminder });
            TypeFilterComboBox.SelectedIndex = 0;

            // Status Filter
            StatusFilterComboBox.Items.Add(new ComboBoxItem { Content = "جميع الحالات", Tag = null });
            StatusFilterComboBox.Items.Add(new ComboBoxItem { Content = "بانتظار الإرسال", Tag = NotificationStatus.Pending });
            StatusFilterComboBox.Items.Add(new ComboBoxItem { Content = "تم الإرسال", Tag = NotificationStatus.Sent });
            StatusFilterComboBox.Items.Add(new ComboBoxItem { Content = "فشل الإرسال", Tag = NotificationStatus.Failed });
            StatusFilterComboBox.Items.Add(new ComboBoxItem { Content = "ملغي", Tag = NotificationStatus.Cancelled });
            StatusFilterComboBox.Items.Add(new ComboBoxItem { Content = "مجدول", Tag = NotificationStatus.Scheduled });
            StatusFilterComboBox.SelectedIndex = 0;

            // Priority Filter
            PriorityFilterComboBox.Items.Add(new ComboBoxItem { Content = "جميع الأولويات", Tag = null });
            PriorityFilterComboBox.Items.Add(new ComboBoxItem { Content = "منخفضة", Tag = NotificationPriority.Low });
            PriorityFilterComboBox.Items.Add(new ComboBoxItem { Content = "عادية", Tag = NotificationPriority.Normal });
            PriorityFilterComboBox.Items.Add(new ComboBoxItem { Content = "عالية", Tag = NotificationPriority.High });
            PriorityFilterComboBox.Items.Add(new ComboBoxItem { Content = "عاجلة", Tag = NotificationPriority.Urgent });
            PriorityFilterComboBox.SelectedIndex = 0;
        }

        private async void LoadNotificationsAsync()
        {
            try
            {
                StatusTextBlock.Text = "جاري تحميل الإشعارات...";
                
                var notifications = await _notificationService.GetNotificationsAsync();
                
                _allNotifications.Clear();
                foreach (var notification in notifications)
                {
                    _allNotifications.Add(notification);
                }

                ApplyFilters();
                UpdateCountDisplay();
                UpdateLastUpdateTime();
                
                StatusTextBlock.Text = $"تم تحميل {notifications.Count} إشعار بنجاح";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإشعارات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "خطأ في تحميل الإشعارات";
            }
        }

        private void ApplyFilters()
        {
            try
            {
                var filtered = _allNotifications.AsEnumerable();

                // Type filter
                if (TypeFilterComboBox.SelectedItem is ComboBoxItem typeItem && typeItem.Tag != null)
                {
                    var selectedType = (NotificationType)typeItem.Tag;
                    filtered = filtered.Where(n => n.Type == selectedType);
                }

                // Status filter
                if (StatusFilterComboBox.SelectedItem is ComboBoxItem statusItem && statusItem.Tag != null)
                {
                    var selectedStatus = (NotificationStatus)statusItem.Tag;
                    filtered = filtered.Where(n => n.Status == selectedStatus);
                }

                // Priority filter
                if (PriorityFilterComboBox.SelectedItem is ComboBoxItem priorityItem && priorityItem.Tag != null)
                {
                    var selectedPriority = (NotificationPriority)priorityItem.Tag;
                    filtered = filtered.Where(n => n.Priority == selectedPriority);
                }

                // Search filter
                var searchText = SearchTextBox.Text?.Trim();
                if (!string.IsNullOrEmpty(searchText) && searchText != "البحث بالاسم أو المحل..." && 
                    SearchTextBox.Foreground != Brushes.Gray)
                {
                    filtered = filtered.Where(n => 
                        n.CustomerName.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                        n.PropertyName.Contains(searchText, StringComparison.OrdinalIgnoreCase));
                }

                _filteredNotifications.Clear();
                foreach (var notification in filtered.OrderByDescending(n => n.DueDate))
                {
                    _filteredNotifications.Add(notification);
                }

                NotificationsDataGrid.ItemsSource = _filteredNotifications;
                UpdateCountDisplay();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق التصفية: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateCountDisplay()
        {
            var totalCount = _allNotifications.Count;
            var filteredCount = _filteredNotifications.Count;
            
            if (totalCount == filteredCount)
            {
                CountTextBlock.Text = $"({totalCount} إشعار)";
            }
            else
            {
                CountTextBlock.Text = $"({filteredCount} من أصل {totalCount} إشعار)";
            }
        }

        private void UpdateLastUpdateTime()
        {
            LastUpdateTextBlock.Text = DateTime.Now.ToString("HH:mm:ss");
        }

        // Event Handlers
        private async void GenerateMonthlyRentBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "هل تريد توليد إشعارات الإيجار الشهري للشهر الحالي؟\n\nسيتم إنشاء إشعارات لجميع العقود النشطة.",
                    "توليد إشعارات الإيجار الشهري",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    StatusTextBlock.Text = "جاري توليد إشعارات الإيجار الشهري...";
                    
                    var currentDate = DateTime.Now;
                    var notifications = await _notificationService.GenerateMonthlyRentNotificationsAsync(
                        currentDate.Year, currentDate.Month);

                    MessageBox.Show($"تم توليد {notifications.Count} إشعار إيجار شهري بنجاح!", "تم التوليد",
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    LoadNotificationsAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في توليد إشعارات الإيجار الشهري: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "خطأ في توليد الإشعارات";
            }
        }

        private async void GenerateCleaningTaxBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "هل تريد توليد إشعارات ضريبة النظافة للسنة الحالية؟\n\nسيتم إنشاء إشعارات لجميع العقود النشطة.",
                    "توليد إشعارات ضريبة النظافة",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    StatusTextBlock.Text = "جاري توليد إشعارات ضريبة النظافة...";
                    
                    var currentYear = DateTime.Now.Year;
                    var notifications = await _notificationService.GenerateCleaningTaxNotificationsAsync(currentYear);

                    MessageBox.Show($"تم توليد {notifications.Count} إشعار ضريبة نظافة بنجاح!", "تم التوليد",
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    LoadNotificationsAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في توليد إشعارات ضريبة النظافة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "خطأ في توليد الإشعارات";
            }
        }

        private async void GenerateOverdueBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "هل تريد توليد إشعارات التأخير للمدفوعات المستحقة؟\n\nسيتم فحص جميع الإشعارات المتأخرة وإنشاء تنبيهات مناسبة.",
                    "توليد إشعارات التأخير",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    StatusTextBlock.Text = "جاري توليد إشعارات التأخير...";
                    
                    var notifications = await _notificationService.GenerateOverdueNotificationsAsync();

                    MessageBox.Show($"تم توليد {notifications.Count} إشعار تأخير بنجاح!", "تم التوليد",
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    LoadNotificationsAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في توليد إشعارات التأخير: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "خطأ في توليد الإشعارات";
            }
        }

        private async void AutoUpdateBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                StatusTextBlock.Text = "جاري التحديث التلقائي...";
                
                var currentDate = DateTime.Now;
                
                // Generate all types of notifications
                var monthlyRent = await _notificationService.GenerateMonthlyRentNotificationsAsync(
                    currentDate.Year, currentDate.Month);
                var cleaningTax = await _notificationService.GenerateCleaningTaxNotificationsAsync(currentDate.Year);
                var overdue = await _notificationService.GenerateOverdueNotificationsAsync();

                var totalGenerated = monthlyRent.Count + cleaningTax.Count + overdue.Count;

                MessageBox.Show(
                    $"تم التحديث التلقائي بنجاح!\n\n" +
                    $"• إشعارات الإيجار الشهري: {monthlyRent.Count}\n" +
                    $"• إشعارات ضريبة النظافة: {cleaningTax.Count}\n" +
                    $"• إشعارات التأخير: {overdue.Count}\n\n" +
                    $"إجمالي الإشعارات الجديدة: {totalGenerated}",
                    "تم التحديث التلقائي",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);

                LoadNotificationsAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التحديث التلقائي: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "خطأ في التحديث التلقائي";
            }
        }

        private void StatisticsBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var pendingCount = _allNotifications.Count(n => n.Status == NotificationStatus.Pending);
                var sentCount = _allNotifications.Count(n => n.Status == NotificationStatus.Sent);
                var failedCount = _allNotifications.Count(n => n.Status == NotificationStatus.Failed);
                var overdueCount = _allNotifications.Count(n => n.IsOverdue);

                var rentCount = _allNotifications.Count(n => n.Type == NotificationType.MonthlyRent);
                var cleaningTaxCount = _allNotifications.Count(n => n.Type == NotificationType.CleaningTax);
                var overdueRentCount = _allNotifications.Count(n => n.Type == NotificationType.RentOverdue);
                var overdueCleaningTaxCount = _allNotifications.Count(n => n.Type == NotificationType.CleaningTaxOverdue);

                MessageBox.Show(
                    $"📊 إحصائيات الإشعارات\n\n" +
                    $"📈 حسب الحالة:\n" +
                    $"• بانتظار الإرسال: {pendingCount}\n" +
                    $"• تم الإرسال: {sentCount}\n" +
                    $"• فشل الإرسال: {failedCount}\n" +
                    $"• متأخرة: {overdueCount}\n\n" +
                    $"📋 حسب النوع:\n" +
                    $"• إيجار شهري: {rentCount}\n" +
                    $"• ضريبة النظافة: {cleaningTaxCount}\n" +
                    $"• تأخير إيجار: {overdueRentCount}\n" +
                    $"• تأخير ضريبة النظافة: {overdueCleaningTaxCount}\n\n" +
                    $"📊 الإجمالي: {_allNotifications.Count} إشعار",
                    "إحصائيات الإشعارات",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض الإحصائيات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // Filter event handlers
        private void TypeFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_allNotifications != null)
                ApplyFilters();
        }

        private void StatusFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_allNotifications != null)
                ApplyFilters();
        }

        private void PriorityFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_allNotifications != null)
                ApplyFilters();
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_allNotifications != null)
                ApplyFilters();
        }

        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (SearchTextBox.Text == "البحث بالاسم أو المحل..." && SearchTextBox.Foreground == Brushes.Gray)
            {
                SearchTextBox.Text = "";
                SearchTextBox.Foreground = Brushes.Black;
            }
        }

        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                SearchTextBox.Text = "البحث بالاسم أو المحل...";
                SearchTextBox.Foreground = Brushes.Gray;
            }
        }

        private void ClearFiltersBtn_Click(object sender, RoutedEventArgs e)
        {
            TypeFilterComboBox.SelectedIndex = 0;
            StatusFilterComboBox.SelectedIndex = 0;
            PriorityFilterComboBox.SelectedIndex = 0;
            SearchTextBox.Text = "البحث بالاسم أو المحل...";
            SearchTextBox.Foreground = Brushes.Gray;
            ApplyFilters();
        }

        // Action button handlers (to be implemented)
        private void SendNotificationBtn_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("ميزة الإرسال قيد التطوير", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void RetryNotificationBtn_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("ميزة إعادة المحاولة قيد التطوير", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void EditNotificationBtn_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("ميزة التعديل قيد التطوير", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CancelNotificationBtn_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("ميزة الإلغاء قيد التطوير", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void DeleteNotificationBtn_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("ميزة الحذف قيد التطوير", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            base.OnClosed(e);
        }
    }
}
