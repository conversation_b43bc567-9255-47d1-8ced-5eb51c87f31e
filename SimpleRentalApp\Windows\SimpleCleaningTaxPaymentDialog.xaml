<Window x:Class="SimpleRentalApp.Windows.SimpleCleaningTaxPaymentDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة دفعة ضريبة النظافة"
        Height="550"
        Width="450"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        Background="#F5F5F5"
        ResizeMode="NoResize">

    <Window.Resources>
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SaveButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#4CAF50"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#388E3C"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="CancelButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#757575"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#616161"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Height" Value="35"/>
        </Style>

        <Style x:Key="ModernComboBox" TargetType="ComboBox">
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Height" Value="35"/>
        </Style>

        <Style x:Key="ModernDatePicker" TargetType="DatePicker">
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Height" Value="35"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" 
                Background="#4CAF50" 
                CornerRadius="8,8,0,0" 
                Padding="20,15"
                Margin="0,0,0,20">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="💰" FontSize="24" Margin="0,0,10,0"/>
                <TextBlock Text="إضافة دفعة ضريبة النظافة" 
                          FontSize="18" 
                          FontWeight="Bold" 
                          Foreground="White"/>
            </StackPanel>
        </Border>

        <!-- Scrollable Content -->
        <ScrollViewer Grid.Row="1" Grid.RowSpan="2"
                      VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Disabled"
                      PanningMode="VerticalOnly"
                      Margin="0,0,0,15">
            <StackPanel>
                <!-- Property Information -->
                <Border Background="White"
                        BorderBrush="#E0E0E0"
                        BorderThickness="1"
                        CornerRadius="6"
                        Padding="15"
                        Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="معلومات المحل"
                                  FontSize="14"
                                  FontWeight="Bold"
                                  Foreground="#333"
                                  Margin="0,0,0,10"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم المحل:" FontWeight="Bold" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Name="PropertyNameText" Margin="0,0,0,5"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="المكتري:" FontWeight="Bold" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Name="CustomerNameText" Margin="0,0,0,5"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="السنة:" FontWeight="Bold" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Name="YearText" Margin="0,0,0,5"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Payment Form -->
                <Border Background="White"
                        BorderBrush="#E0E0E0"
                        BorderThickness="1"
                        CornerRadius="6"
                        Padding="15">
                    <StackPanel>
                        <TextBlock Text="بيانات الدفعة"
                                  FontSize="14"
                                  FontWeight="Bold"
                                  Foreground="#333"
                                  Margin="0,0,0,15"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Payment Date -->
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="تاريخ الدفع:" FontWeight="Bold" Margin="0,0,10,5"/>
                            <DatePicker Grid.Row="0" Grid.Column="1" Name="PaymentDatePicker" Style="{StaticResource ModernDatePicker}" Margin="0,0,0,10"/>

                            <!-- Amount -->
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="المبلغ:" FontWeight="Bold" Margin="0,0,10,5"/>
                            <TextBox Grid.Row="1" Grid.Column="1" Name="AmountTextBox" Style="{StaticResource ModernTextBox}" Margin="0,0,0,10"/>

                            <!-- Amount in Words -->
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="المبلغ بالحروف:" FontWeight="Bold" Margin="0,0,10,5"/>
                            <TextBox Grid.Row="2" Grid.Column="1" Name="AmountInWordsTextBox" Style="{StaticResource ModernTextBox}" IsReadOnly="True" Background="#F5F5F5" Margin="0,0,0,10"/>

                            <!-- Receipt Number -->
                            <TextBlock Grid.Row="3" Grid.Column="0" Text="رقم التوصيل:" FontWeight="Bold" Margin="0,0,10,5"/>
                            <StackPanel Grid.Row="3" Grid.Column="1" Margin="0,0,0,10">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBox Grid.Column="0" Name="ReceiptNumberTextBox" Style="{StaticResource ModernTextBox}"
                                             ToolTip="رقم التوصيل - مستقل لكل محل وسنة ضريبية"
                                             TextChanged="ReceiptNumberTextBox_TextChanged"/>
                                    <Button Grid.Column="1" Content="🔄" Margin="5,0,0,0" Padding="8"
                                            ToolTip="إنشاء رقم تلقائي جديد لهذا المحل والسنة الضريبية"
                                            Click="GenerateReceiptNumber_Click"
                                            Background="#E3F2FD" Foreground="#1976D2" BorderBrush="#BBDEFB"/>
                                </Grid>
                                <TextBlock Name="ReceiptNumberHintText" FontSize="11" Foreground="Gray" Margin="0,3,0,0"
                                           Text="الرقم التالي المتاح لهذا المحل سيتم إنشاؤه تلقائياً"/>

                                <!-- Generate New Number Button -->
                                <Button Name="GenerateNewNumberBtn" Content="📝 إنشاء رقم جديد تلقائياً"
                                        Margin="0,5,0,0" Padding="8,4" FontSize="12"
                                        Background="#E3F2FD" Foreground="#1976D2" BorderBrush="#BBDEFB"
                                        ToolTip="إنشاء رقم توصيل جديد بناءً على آخر رقم مستخدم لهذا المحل والسنة الضريبية"
                                        Click="GenerateNewNumberBtn_Click"/>
                            </StackPanel>

                            <!-- Payment Method -->
                            <TextBlock Grid.Row="4" Grid.Column="0" Text="طريقة الدفع:" FontWeight="Bold" Margin="0,0,10,5"/>
                            <ComboBox Grid.Row="4" Grid.Column="1" Name="PaymentMethodComboBox" Style="{StaticResource ModernComboBox}" Margin="0,0,0,10"/>

                            <!-- Notes -->
                            <TextBlock Grid.Row="5" Grid.Column="0" Text="ملاحظات:" FontWeight="Bold" Margin="0,0,10,5" VerticalAlignment="Top"/>
                            <TextBox Grid.Row="5" Grid.Column="1" Name="NotesTextBox" Style="{StaticResource ModernTextBox}" Height="60" TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>
                        </Grid>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <StackPanel Grid.Row="3"
                   Orientation="Horizontal"
                   HorizontalAlignment="Center"
                   Margin="0,15,0,0">
            <Button Name="SaveButton" 
                   Content="💾 حفظ الدفعة" 
                   Style="{StaticResource SaveButton}" 
                   Click="SaveButton_Click"
                   MinWidth="120"/>
            <Button Name="CancelButton" 
                   Content="❌ إلغاء" 
                   Style="{StaticResource CancelButton}" 
                   Click="CancelButton_Click"
                   MinWidth="120"/>
        </StackPanel>
    </Grid>
</Window>
