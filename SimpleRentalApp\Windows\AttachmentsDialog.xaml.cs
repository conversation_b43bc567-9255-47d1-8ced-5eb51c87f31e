using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Microsoft.Win32;
using SimpleRentalApp.Models;
using SimpleRentalApp.Services;

namespace SimpleRentalApp.Windows;

public partial class AttachmentsDialog : Window
{
    private readonly Contract _contract;
    private List<ContractAttachment> _attachments = new();
    
    public AttachmentsDialog(Contract contract)
    {
        InitializeComponent();
        _contract = contract;
        
        HeaderTextBlock.Text = $"مرفقات عقد رقم {contract.Id} - {contract.Customer.FullName}";
        LoadAttachments();
    }

    private async void LoadAttachments()
    {
        try
        {
            var contract = await DatabaseService.Instance.GetContractByIdAsync(_contract.Id);
            if (contract != null)
            {
                _attachments = contract.Attachments.OrderByDescending(a => a.UploadDate).ToList();
                DisplayAttachments();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل المرفقات: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void DisplayAttachments()
    {
        AttachmentsPanel.Children.Clear();

        if (!_attachments.Any())
        {
            var noAttachmentsPanel = new StackPanel 
            { 
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 50, 0, 50)
            };
            
            noAttachmentsPanel.Children.Add(new TextBlock 
            { 
                Text = "📎", 
                FontSize = 48, 
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 15)
            });
            
            noAttachmentsPanel.Children.Add(new TextBlock 
            { 
                Text = "لا توجد مرفقات لهذا العقد", 
                FontSize = 16, 
                HorizontalAlignment = HorizontalAlignment.Center,
                Foreground = Brushes.Gray
            });
            
            AttachmentsPanel.Children.Add(noAttachmentsPanel);
            return;
        }

        foreach (var attachment in _attachments)
        {
            var attachmentCard = CreateAttachmentCard(attachment);
            AttachmentsPanel.Children.Add(attachmentCard);
        }
    }

    private Border CreateAttachmentCard(ContractAttachment attachment)
    {
        var border = new Border
        {
            Background = Brushes.White,
            BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E0E0E0")),
            BorderThickness = new Thickness(1),
            CornerRadius = new CornerRadius(8),
            Margin = new Thickness(0, 5, 0, 5),
            Padding = new Thickness(15)
        };

        var grid = new Grid();
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

        // File Icon
        var iconPanel = new StackPanel { VerticalAlignment = VerticalAlignment.Center };
        iconPanel.Children.Add(new TextBlock 
        { 
            Text = attachment.FileIcon, 
            FontSize = 32,
            HorizontalAlignment = HorizontalAlignment.Center
        });
        Grid.SetColumn(iconPanel, 0);
        grid.Children.Add(iconPanel);

        // File Details
        var detailsPanel = new StackPanel 
        { 
            Margin = new Thickness(15, 0, 15, 0),
            VerticalAlignment = VerticalAlignment.Center
        };
        
        detailsPanel.Children.Add(new TextBlock 
        { 
            Text = attachment.FileName, 
            FontSize = 14, 
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#212121"))
        });
        
        if (!string.IsNullOrEmpty(attachment.Description))
        {
            detailsPanel.Children.Add(new TextBlock 
            { 
                Text = attachment.Description, 
                FontSize = 12, 
                Foreground = Brushes.Gray,
                Margin = new Thickness(0, 2, 0, 0)
            });
        }
        
        detailsPanel.Children.Add(new TextBlock 
        { 
            Text = $"الحجم: {attachment.FileSizeDisplay} | تاريخ الرفع: {attachment.UploadDate:dd/MM/yyyy HH:mm}", 
            FontSize = 11, 
            Foreground = Brushes.Gray,
            Margin = new Thickness(0, 5, 0, 0)
        });
        
        Grid.SetColumn(detailsPanel, 1);
        grid.Children.Add(detailsPanel);

        // Action Buttons
        var buttonsPanel = new StackPanel 
        { 
            Orientation = Orientation.Horizontal,
            VerticalAlignment = VerticalAlignment.Center
        };
        
        var openButton = new Button
        {
            Content = "👁️",
            Width = 35,
            Height = 35,
            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#2196F3")),
            Foreground = Brushes.White,
            BorderThickness = new Thickness(0),
            Margin = new Thickness(2),
            ToolTip = "فتح الملف"
        };
        openButton.Click += (s, e) => OpenAttachment(attachment);
        buttonsPanel.Children.Add(openButton);
        
        var downloadButton = new Button
        {
            Content = "💾",
            Width = 35,
            Height = 35,
            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4CAF50")),
            Foreground = Brushes.White,
            BorderThickness = new Thickness(0),
            Margin = new Thickness(2),
            ToolTip = "تحميل الملف"
        };
        downloadButton.Click += (s, e) => DownloadAttachment(attachment);
        buttonsPanel.Children.Add(downloadButton);
        
        var deleteButton = new Button
        {
            Content = "🗑️",
            Width = 35,
            Height = 35,
            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F44336")),
            Foreground = Brushes.White,
            BorderThickness = new Thickness(0),
            Margin = new Thickness(2),
            ToolTip = "حذف الملف"
        };
        deleteButton.Click += async (s, e) => await DeleteAttachment(attachment);
        buttonsPanel.Children.Add(deleteButton);
        
        Grid.SetColumn(buttonsPanel, 2);
        grid.Children.Add(buttonsPanel);

        border.Child = grid;
        return border;
    }

    private async void AddAttachmentButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "اختر ملف لإرفاقه",
                Filter = "جميع الملفات (*.*)|*.*|ملفات PDF (*.pdf)|*.pdf|صور (*.jpg;*.jpeg;*.png;*.gif)|*.jpg;*.jpeg;*.png;*.gif|مستندات Word (*.doc;*.docx)|*.doc;*.docx|ملفات Excel (*.xls;*.xlsx)|*.xls;*.xlsx",
                Multiselect = false
            };

            if (openFileDialog.ShowDialog() == true)
            {
                var sourceFile = openFileDialog.FileName;
                var fileName = Path.GetFileName(sourceFile);
                var fileExtension = Path.GetExtension(sourceFile).TrimStart('.').ToLower();
                var fileSize = new FileInfo(sourceFile).Length;

                // Create attachments directory if it doesn't exist
                var attachmentsDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Attachments");
                Directory.CreateDirectory(attachmentsDir);

                // Generate unique filename
                var uniqueFileName = $"{Guid.NewGuid()}_{fileName}";
                var destinationPath = Path.Combine(attachmentsDir, uniqueFileName);

                // Copy file
                File.Copy(sourceFile, destinationPath);

                // Create attachment record
                var attachment = new ContractAttachment
                {
                    ContractId = _contract.Id,
                    FileName = fileName,
                    FilePath = destinationPath,
                    FileType = fileExtension,
                    FileSize = fileSize,
                    Description = "", // Could add a dialog to get description
                    UploadDate = DateTime.Now
                };

                // Save to database
                await DatabaseService.Instance.SaveAttachmentAsync(attachment);

                MessageBox.Show("تم إرفاق الملف بنجاح! ✅", "نجح الإرفاق", 
                    MessageBoxButton.OK, MessageBoxImage.Information);

                LoadAttachments(); // Refresh
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إرفاق الملف: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void OpenAttachment(ContractAttachment attachment)
    {
        try
        {
            if (File.Exists(attachment.FilePath))
            {
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = attachment.FilePath,
                    UseShellExecute = true
                });
            }
            else
            {
                MessageBox.Show("الملف غير موجود", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void DownloadAttachment(ContractAttachment attachment)
    {
        try
        {
            var saveFileDialog = new SaveFileDialog
            {
                FileName = attachment.FileName,
                Filter = "جميع الملفات (*.*)|*.*"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                File.Copy(attachment.FilePath, saveFileDialog.FileName, true);
                MessageBox.Show("تم تحميل الملف بنجاح! ✅", "نجح التحميل", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل الملف: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task DeleteAttachment(ContractAttachment attachment)
    {
        var result = MessageBox.Show($"هل تريد حذف الملف '{attachment.FileName}'؟", 
            "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);
        
        if (result == MessageBoxResult.Yes)
        {
            try
            {
                await DatabaseService.Instance.DeleteAttachmentAsync(attachment.Id);
                MessageBox.Show("تم حذف الملف بنجاح! ✅", "نجح الحذف", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                LoadAttachments(); // Refresh
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الملف: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    private void RefreshButton_Click(object sender, RoutedEventArgs e)
    {
        LoadAttachments();
    }

    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }
}
