using System.ComponentModel.DataAnnotations;

namespace SimpleRentalApp.Models;

public class Contract
{
    public int Id { get; set; }
    
    [Required]
    public int CustomerId { get; set; }
    public Customer Customer { get; set; } = null!;
    
    [Required]
    public int PropertyId { get; set; }
    public Property Property { get; set; } = null!;
    
    [Required]
    public DateTime StartDate { get; set; }
    
    [Required]
    public DateTime EndDate { get; set; }

    [Required]
    public decimal InitialRentAmount { get; set; }

    public int RentIncreaseCount { get; set; } = 0;

    public decimal RentIncreasePercentage { get; set; } = 0;

    /// <summary>
    /// يوم الأداء الشهري (1-31). إذا كان null، يتم استخدام يوم بداية العقد
    /// </summary>
    public int? PaymentDay { get; set; }
    
    [StringLength(500)]
    public string Notes { get; set; } = string.Empty;
    
    public DateTime CreatedDate { get; set; } = DateTime.Now;
    public DateTime UpdatedDate { get; set; } = DateTime.Now;

    public bool IsActive { get; set; } = true;

    // Navigation properties
    public List<ContractAttachment> Attachments { get; set; } = new();
    public List<RentIncrease> RentIncreases { get; set; } = new();
    public List<ContractAugment> Augments { get; set; } = new();
    
    // Computed properties
    public TimeSpan Duration => EndDate - StartDate;
    
    public int DurationInDays => (int)Duration.TotalDays;
    
    public int DurationInMonths => (int)(Duration.TotalDays / 30.44); // Average days per month
    
    public int DurationInYears => (int)(Duration.TotalDays / 365.25); // Average days per year
    
    public string DurationDisplay
    {
        get
        {
            if (DurationInYears > 0)
                return $"{DurationInYears} سنة و {DurationInMonths % 12} شهر";
            else if (DurationInMonths > 0)
                return $"{DurationInMonths} شهر";
            else
                return $"{DurationInDays} يوم";
        }
    }
    
    public ContractStatus Status
    {
        get
        {
            var today = DateTime.Today;
            if (today > EndDate)
                return ContractStatus.Expired;
            else if ((EndDate - today).TotalDays <= 30)
                return ContractStatus.ExpiringSoon;
            else
                return ContractStatus.Active;
        }
    }
    
    public string StatusDisplay => Status switch
    {
        ContractStatus.Active => "ساري",
        ContractStatus.ExpiringSoon => "قريب الانتهاء",
        ContractStatus.Expired => "منتهي",
        _ => "غير محدد"
    };
    
    public string StatusColor => Status switch
    {
        ContractStatus.Active => "#4CAF50",
        ContractStatus.ExpiringSoon => "#FF9800",
        ContractStatus.Expired => "#F44336",
        _ => "#9E9E9E"
    };

    public decimal TotalAugmentsAmount => Augments?.Where(a => a.IsActive).Sum(a => a.CalculatedAmount) ?? 0;

    public int ActiveAugmentsCount => Augments?.Count(a => a.IsActive) ?? 0;

    public string AugmentsSummary
    {
        get
        {
            if (ActiveAugmentsCount == 0)
                return "لا توجد زيادات";

            return $"{ActiveAugmentsCount} زيادة بقيمة {TotalAugmentsAmount:N0} درهم";
        }
    }

    public decimal CurrentRentAmount
    {
        get
        {
            if (RentIncreaseCount == 0 || RentIncreasePercentage == 0)
                return InitialRentAmount;

            // Calculate current rent with compound increases
            var currentRent = InitialRentAmount;
            for (int i = 0; i < RentIncreaseCount; i++)
            {
                currentRent += currentRent * (RentIncreasePercentage / 100);
            }
            return Math.Round(currentRent, 2);
        }
    }

    public decimal TotalRentIncrease => CurrentRentAmount - InitialRentAmount;

    public string RentIncreaseDisplay
    {
        get
        {
            if (TotalRentIncrease > 0)
                return $"+{TotalRentIncrease:N0} درهم ({((TotalRentIncrease / InitialRentAmount) * 100):F1}%)";
            else
                return "لا توجد زيادة";
        }
    }

    public int DaysRemaining => Math.Max(0, (int)(EndDate - DateTime.Today).TotalDays);

    public string DaysRemainingDisplay
    {
        get
        {
            if (Status == ContractStatus.Expired)
                return $"انتهى منذ {Math.Abs(DaysRemaining)} يوم";
            else if (DaysRemaining == 0)
                return "ينتهي اليوم";
            else if (DaysRemaining == 1)
                return "ينتهي غداً";
            else
                return $"متبقي {DaysRemaining} يوم";
        }
    }
}

public enum ContractStatus
{
    Active,
    ExpiringSoon,
    Expired
}

public class ContractAttachment
{
    public int Id { get; set; }
    
    [Required]
    public int ContractId { get; set; }
    public Contract Contract { get; set; } = null!;
    
    [Required]
    [StringLength(255)]
    public string FileName { get; set; } = string.Empty;
    
    [Required]
    [StringLength(500)]
    public string FilePath { get; set; } = string.Empty;
    
    [StringLength(100)]
    public string FileType { get; set; } = string.Empty;
    
    public long FileSize { get; set; }
    
    [StringLength(200)]
    public string Description { get; set; } = string.Empty;
    
    public DateTime UploadDate { get; set; } = DateTime.Now;
    
    public string FileSizeDisplay
    {
        get
        {
            if (FileSize < 1024)
                return $"{FileSize} B";
            else if (FileSize < 1024 * 1024)
                return $"{FileSize / 1024:F1} KB";
            else
                return $"{FileSize / (1024 * 1024):F1} MB";
        }
    }
    
    public string FileIcon => FileType.ToLower() switch
    {
        "pdf" => "📄",
        "doc" or "docx" => "📝",
        "xls" or "xlsx" => "📊",
        "jpg" or "jpeg" or "png" or "gif" or "bmp" => "🖼️",
        "zip" or "rar" or "7z" => "📦",
        _ => "📎"
    };
}

public class RentIncrease
{
    public int Id { get; set; }

    [Required]
    public int ContractId { get; set; }
    public Contract Contract { get; set; } = null!;

    [Required]
    public DateTime IncreaseDate { get; set; }

    [Required]
    public decimal PreviousAmount { get; set; }

    [Required]
    public decimal NewAmount { get; set; }

    [Required]
    public decimal IncreasePercentage { get; set; }

    [StringLength(200)]
    public string Reason { get; set; } = string.Empty;

    public DateTime CreatedDate { get; set; } = DateTime.Now;

    // Computed properties
    public decimal IncreaseAmount => NewAmount - PreviousAmount;

    public string IncreaseAmountDisplay => $"{IncreaseAmount:N0} درهم";

    public string IncreasePercentageDisplay => $"{IncreasePercentage:F1}%";

    public string IncreaseDetails => $"من {PreviousAmount:N0} إلى {NewAmount:N0} درهم ({IncreasePercentageDisplay})";
}
