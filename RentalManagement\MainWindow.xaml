﻿<Window x:Class="RentalManagement.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="نظام إدارة المحلات"
        Height="500" Width="700"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="White"
        WindowState="Normal"
        Topmost="True">

    <StackPanel Margin="20">
        <TextBlock Text="مرحباً بك في نظام إدارة المحلات"
                   FontSize="24"
                   FontWeight="Bold"
                   HorizontalAlignment="Center"
                   Margin="0,20,0,30"
                   Foreground="DarkBlue"/>

        <TextBlock Text="التطبيق يعمل بنجاح!"
                   FontSize="18"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,20"
                   Foreground="Green"/>

        <Button Content="اختبار التطبيق"
                FontSize="16"
                Padding="20,10"
                HorizontalAlignment="Center"
                Background="Blue"
                Foreground="White"
                Click="TestButton_Click"
                Margin="0,0,0,20"/>

        <Button Content="خروج"
                FontSize="14"
                Padding="15,8"
                HorizontalAlignment="Center"
                Background="Red"
                Foreground="White"
                Click="ExitButton_Click"/>
    </StackPanel>
</Window>
