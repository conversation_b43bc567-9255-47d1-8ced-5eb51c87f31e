﻿#pragma checksum "..\..\..\..\Windows\AugmentContractWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "E308F95C33DA55A1C2A666AF11864191FC1F0A18"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleRentalApp.Windows {
    
    
    /// <summary>
    /// AugmentContractWindow
    /// </summary>
    public partial class AugmentContractWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 43 "..\..\..\..\Windows\AugmentContractWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ContractInfoTextBlock;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\Windows\AugmentContractWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox AugmentTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\..\Windows\AugmentContractWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BaseRentTextBox;
        
        #line default
        #line hidden
        
        
        #line 121 "..\..\..\..\Windows\AugmentContractWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PercentageTextBox;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\..\Windows\AugmentContractWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CalculatedAmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\Windows\AugmentContractWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker ApplicationDatePicker;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\..\Windows\AugmentContractWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 220 "..\..\..\..\Windows\AugmentContractWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 232 "..\..\..\..\Windows\AugmentContractWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleRentalApp;V1.0.0.0;component/windows/augmentcontractwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\AugmentContractWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ContractInfoTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.AugmentTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 77 "..\..\..\..\Windows\AugmentContractWindow.xaml"
            this.AugmentTypeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.AugmentTypeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.BaseRentTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.PercentageTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 131 "..\..\..\..\Windows\AugmentContractWindow.xaml"
            this.PercentageTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.PercentageTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.CalculatedAmountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.ApplicationDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 7:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 230 "..\..\..\..\Windows\AugmentContractWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 240 "..\..\..\..\Windows\AugmentContractWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

