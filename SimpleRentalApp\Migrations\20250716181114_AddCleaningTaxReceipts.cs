﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace SimpleRentalApp.Migrations
{
    /// <inheritdoc />
    public partial class AddCleaningTaxReceipts : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Customers",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    FirstName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    LastName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Phone = table.Column<string>(type: "TEXT", maxLength: 15, nullable: false),
                    Email = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    NationalId = table.Column<string>(type: "TEXT", maxLength: 20, nullable: false),
                    Address = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Customers", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MessageTemplates",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Type = table.Column<int>(type: "INTEGER", nullable: false),
                    Name = table.Column<string>(type: "TEXT", nullable: false),
                    Template = table.Column<string>(type: "TEXT", nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    IsDefault = table.Column<bool>(type: "INTEGER", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    AvailableVariables = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MessageTemplates", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "NotificationSettings",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    RentLateDays = table.Column<int>(type: "INTEGER", nullable: false),
                    CleaningTaxLateDays = table.Column<int>(type: "INTEGER", nullable: false),
                    MaxLateRemindersPerMonth = table.Column<int>(type: "INTEGER", nullable: false),
                    DaysBetweenLateReminders = table.Column<int>(type: "INTEGER", nullable: false),
                    AutoNotificationsEnabled = table.Column<bool>(type: "INTEGER", nullable: false),
                    RentNotificationsEnabled = table.Column<bool>(type: "INTEGER", nullable: false),
                    CleaningTaxNotificationsEnabled = table.Column<bool>(type: "INTEGER", nullable: false),
                    LateRemindersEnabled = table.Column<bool>(type: "INTEGER", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NotificationSettings", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Users",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Username = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    FullName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    PasswordHash = table.Column<string>(type: "TEXT", maxLength: 255, nullable: false),
                    Email = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Role = table.Column<int>(type: "INTEGER", nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    LastLoginDate = table.Column<DateTime>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Users", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Properties",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Name = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Address = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    Type = table.Column<int>(type: "INTEGER", nullable: false),
                    Status = table.Column<int>(type: "INTEGER", nullable: false),
                    MonthlyRent = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    CleaningTaxPaymentOption = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Rooms = table.Column<int>(type: "INTEGER", nullable: true),
                    Bathrooms = table.Column<int>(type: "INTEGER", nullable: true),
                    Area = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Description = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    CustomerId = table.Column<int>(type: "INTEGER", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Properties", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Properties_Customers_CustomerId",
                        column: x => x.CustomerId,
                        principalTable: "Customers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "Contracts",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    CustomerId = table.Column<int>(type: "INTEGER", nullable: false),
                    PropertyId = table.Column<int>(type: "INTEGER", nullable: false),
                    StartDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    EndDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    InitialRentAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    RentIncreaseCount = table.Column<int>(type: "INTEGER", nullable: false),
                    RentIncreasePercentage = table.Column<decimal>(type: "decimal(5,2)", nullable: false),
                    PaymentDay = table.Column<int>(type: "INTEGER", nullable: true),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Contracts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Contracts_Customers_CustomerId",
                        column: x => x.CustomerId,
                        principalTable: "Customers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Contracts_Properties_PropertyId",
                        column: x => x.PropertyId,
                        principalTable: "Properties",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Payments",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    PropertyId = table.Column<int>(type: "INTEGER", nullable: false),
                    CustomerId = table.Column<int>(type: "INTEGER", nullable: false),
                    Type = table.Column<int>(type: "INTEGER", nullable: false),
                    RentAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    CleaningTaxAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    TaxYear = table.Column<int>(type: "INTEGER", nullable: true),
                    DueDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    PaymentDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    Status = table.Column<int>(type: "INTEGER", nullable: false),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    ReceiptNumber = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    PaymentMethod = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false, defaultValue: "نقداً"),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Payments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Payments_Customers_CustomerId",
                        column: x => x.CustomerId,
                        principalTable: "Customers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Payments_Properties_PropertyId",
                        column: x => x.PropertyId,
                        principalTable: "Properties",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "WhatsAppNotifications",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    CustomerId = table.Column<int>(type: "INTEGER", nullable: false),
                    PropertyId = table.Column<int>(type: "INTEGER", nullable: false),
                    Type = table.Column<int>(type: "INTEGER", nullable: false),
                    Message = table.Column<string>(type: "TEXT", nullable: false),
                    PhoneNumber = table.Column<string>(type: "TEXT", nullable: false),
                    ScheduledDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    SentDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    Status = table.Column<int>(type: "INTEGER", nullable: false),
                    ErrorMessage = table.Column<string>(type: "TEXT", nullable: true),
                    Amount = table.Column<decimal>(type: "TEXT", nullable: false),
                    DueDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    Month = table.Column<string>(type: "TEXT", nullable: false),
                    Year = table.Column<int>(type: "INTEGER", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    IsAutoGenerated = table.Column<bool>(type: "INTEGER", nullable: false),
                    UniqueKey = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WhatsAppNotifications", x => x.Id);
                    table.ForeignKey(
                        name: "FK_WhatsAppNotifications_Customers_CustomerId",
                        column: x => x.CustomerId,
                        principalTable: "Customers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_WhatsAppNotifications_Properties_PropertyId",
                        column: x => x.PropertyId,
                        principalTable: "Properties",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ContractAttachments",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    ContractId = table.Column<int>(type: "INTEGER", nullable: false),
                    FileName = table.Column<string>(type: "TEXT", maxLength: 255, nullable: false),
                    FilePath = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    FileType = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    FileSize = table.Column<long>(type: "INTEGER", nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    UploadDate = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ContractAttachments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ContractAttachments_Contracts_ContractId",
                        column: x => x.ContractId,
                        principalTable: "Contracts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ContractAugments",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    ContractId = table.Column<int>(type: "INTEGER", nullable: false),
                    AugmentType = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Percentage = table.Column<decimal>(type: "decimal(5,2)", nullable: false),
                    BaseRent = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    CalculatedAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    ApplicationDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ContractAugments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ContractAugments_Contracts_ContractId",
                        column: x => x.ContractId,
                        principalTable: "Contracts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "RentChangeLogs",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    PropertyId = table.Column<int>(type: "INTEGER", nullable: false),
                    ContractId = table.Column<int>(type: "INTEGER", nullable: true),
                    OldRentAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    NewRentAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    ChangeDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    ChangeReason = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    ChangedBy = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RentChangeLogs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RentChangeLogs_Contracts_ContractId",
                        column: x => x.ContractId,
                        principalTable: "Contracts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_RentChangeLogs_Properties_PropertyId",
                        column: x => x.PropertyId,
                        principalTable: "Properties",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "RentIncreases",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    ContractId = table.Column<int>(type: "INTEGER", nullable: false),
                    IncreaseDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    PreviousAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    NewAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    IncreasePercentage = table.Column<decimal>(type: "decimal(5,2)", nullable: false),
                    Reason = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RentIncreases", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RentIncreases_Contracts_ContractId",
                        column: x => x.ContractId,
                        principalTable: "Contracts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CleaningTaxReceipts",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    ReceiptNumber = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    PropertyId = table.Column<int>(type: "INTEGER", nullable: false),
                    PaymentId = table.Column<int>(type: "INTEGER", nullable: true),
                    Amount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    PaymentDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    TaxYear = table.Column<int>(type: "INTEGER", nullable: false),
                    PaymentMethod = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    CreatedBy = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    IsPrinted = table.Column<bool>(type: "INTEGER", nullable: false),
                    PrintedDate = table.Column<DateTime>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CleaningTaxReceipts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CleaningTaxReceipts_Payments_PaymentId",
                        column: x => x.PaymentId,
                        principalTable: "Payments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_CleaningTaxReceipts_Properties_PropertyId",
                        column: x => x.PropertyId,
                        principalTable: "Properties",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "RentReceipts",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    ReceiptNumber = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    ContractId = table.Column<int>(type: "INTEGER", nullable: false),
                    PaymentId = table.Column<int>(type: "INTEGER", nullable: true),
                    Amount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    PaymentDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    PeriodStartDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    PeriodEndDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    PaymentPeriod = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    PaymentMethod = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    CreatedBy = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    IsPrinted = table.Column<bool>(type: "INTEGER", nullable: false),
                    PrintedDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    ShowLegalNotices = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RentReceipts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RentReceipts_Contracts_ContractId",
                        column: x => x.ContractId,
                        principalTable: "Contracts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RentReceipts_Payments_PaymentId",
                        column: x => x.PaymentId,
                        principalTable: "Payments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.InsertData(
                table: "Customers",
                columns: new[] { "Id", "Address", "CreatedDate", "Email", "FirstName", "LastName", "NationalId", "Phone", "UpdatedDate" },
                values: new object[,]
                {
                    { 1, "حي الملز، الرياض", new DateTime(2025, 6, 16, 19, 11, 13, 913, DateTimeKind.Local).AddTicks(9048), "<EMAIL>", "أحمد", "محمد علي", "1234567890", "0501234567", new DateTime(2025, 7, 16, 19, 11, 13, 913, DateTimeKind.Local).AddTicks(5727) },
                    { 2, "حي النخيل، الرياض", new DateTime(2025, 6, 21, 19, 11, 13, 913, DateTimeKind.Local).AddTicks(9519), "<EMAIL>", "فاطمة", "عبدالله", "0987654321", "0509876543", new DateTime(2025, 7, 16, 19, 11, 13, 913, DateTimeKind.Local).AddTicks(9509) },
                    { 3, "حي العليا، الرياض", new DateTime(2025, 6, 26, 19, 11, 13, 913, DateTimeKind.Local).AddTicks(9529), "<EMAIL>", "محمد", "أحمد", "1122334455", "0551122334", new DateTime(2025, 7, 16, 19, 11, 13, 913, DateTimeKind.Local).AddTicks(9525) },
                    { 4, "حي الورود، الرياض", new DateTime(2025, 7, 1, 19, 11, 13, 913, DateTimeKind.Local).AddTicks(9538), "<EMAIL>", "سارة", "خالد", "5566778899", "0555566778", new DateTime(2025, 7, 16, 19, 11, 13, 913, DateTimeKind.Local).AddTicks(9535) },
                    { 5, "حي الصحافة، الرياض", new DateTime(2025, 7, 6, 19, 11, 13, 913, DateTimeKind.Local).AddTicks(9547), "<EMAIL>", "عبدالله", "سعد", "9988776655", "0559988776", new DateTime(2025, 7, 16, 19, 11, 13, 913, DateTimeKind.Local).AddTicks(9544) }
                });

            migrationBuilder.InsertData(
                table: "Properties",
                columns: new[] { "Id", "Address", "Area", "Bathrooms", "CleaningTaxPaymentOption", "CreatedDate", "CustomerId", "Description", "MonthlyRent", "Name", "Rooms", "Status", "Type", "UpdatedDate" },
                values: new object[,]
                {
                    { 4, "برج الفيصلية، حي العليا", 200m, null, 12m, new DateTime(2025, 7, 1, 19, 11, 13, 916, DateTimeKind.Local).AddTicks(6454), null, "مكتب فاخر في برج الفيصلية", 11000m, "مكتب برج الفيصلية", null, 1, 3, new DateTime(2025, 7, 16, 19, 11, 13, 916, DateTimeKind.Local).AddTicks(6451) },
                    { 6, "حي الورود، شارع الأمير سلطان", 400m, 4, 2m, new DateTime(2025, 7, 11, 19, 11, 13, 916, DateTimeKind.Local).AddTicks(6474), null, "فيلا فاخرة مع حديقة", 16000m, "فيلا الورود الفاخرة", 6, 1, 4, new DateTime(2025, 7, 16, 19, 11, 13, 916, DateTimeKind.Local).AddTicks(6470) }
                });

            migrationBuilder.InsertData(
                table: "Users",
                columns: new[] { "Id", "CreatedDate", "Email", "FullName", "IsActive", "LastLoginDate", "PasswordHash", "Role", "Username" },
                values: new object[,]
                {
                    { 1, new DateTime(2024, 1, 1, 8, 0, 0, 0, DateTimeKind.Unspecified), "<EMAIL>", "حفيظ عبدو", true, null, "hafidos159357", 1, "hafid" },
                    { 2, new DateTime(2024, 1, 2, 9, 0, 0, 0, DateTimeKind.Unspecified), "<EMAIL>", "أحمد المدير", true, null, "manager123", 2, "manager" },
                    { 3, new DateTime(2024, 1, 3, 10, 0, 0, 0, DateTimeKind.Unspecified), "<EMAIL>", "فاطمة الموظفة", true, null, "employee123", 3, "employee" }
                });

            migrationBuilder.InsertData(
                table: "Properties",
                columns: new[] { "Id", "Address", "Area", "Bathrooms", "CleaningTaxPaymentOption", "CreatedDate", "CustomerId", "Description", "MonthlyRent", "Name", "Rooms", "Status", "Type", "UpdatedDate" },
                values: new object[,]
                {
                    { 1, "حي الملز، شارع الأمير محمد بن عبدالعزيز", 120m, 2, 1m, new DateTime(2025, 6, 16, 19, 11, 13, 916, DateTimeKind.Local).AddTicks(5905), 1, "شقة فاخرة مع إطلالة رائعة", 3500m, "شقة الملز الفاخرة", 3, 2, 1, new DateTime(2025, 7, 16, 19, 11, 13, 915, DateTimeKind.Local).AddTicks(2944) },
                    { 2, "شارع التحلية، وسط الدار البيضاء", 80m, null, 4m, new DateTime(2025, 6, 21, 19, 11, 13, 916, DateTimeKind.Local).AddTicks(6433), 2, "محل تجاري في موقع ممتاز", 7000m, "محل التحلية التجاري", null, 2, 2, new DateTime(2025, 7, 16, 19, 11, 13, 916, DateTimeKind.Local).AddTicks(6360) },
                    { 3, "حي النخيل، شارع الملك فهد", 150m, 3, 2m, new DateTime(2025, 6, 26, 19, 11, 13, 916, DateTimeKind.Local).AddTicks(6445), 3, "شقة عائلية واسعة", 4200m, "شقة النخيل العائلية", 4, 2, 1, new DateTime(2025, 7, 16, 19, 11, 13, 916, DateTimeKind.Local).AddTicks(6439) },
                    { 5, "حي العليا، شارع العروبة", 130m, 2, 3m, new DateTime(2025, 7, 6, 19, 11, 13, 916, DateTimeKind.Local).AddTicks(6464), 4, "شقة حديثة مع جميع المرافق", 5500m, "شقة العليا الحديثة", 3, 2, 1, new DateTime(2025, 7, 16, 19, 11, 13, 916, DateTimeKind.Local).AddTicks(6460) }
                });

            migrationBuilder.CreateIndex(
                name: "IX_CleaningTaxReceipts_PaymentId",
                table: "CleaningTaxReceipts",
                column: "PaymentId");

            migrationBuilder.CreateIndex(
                name: "IX_CleaningTaxReceipts_PropertyId",
                table: "CleaningTaxReceipts",
                column: "PropertyId");

            migrationBuilder.CreateIndex(
                name: "IX_CleaningTaxReceipts_ReceiptNumber_PropertyId_TaxYear",
                table: "CleaningTaxReceipts",
                columns: new[] { "ReceiptNumber", "PropertyId", "TaxYear" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ContractAttachments_ContractId",
                table: "ContractAttachments",
                column: "ContractId");

            migrationBuilder.CreateIndex(
                name: "IX_ContractAugments_ContractId",
                table: "ContractAugments",
                column: "ContractId");

            migrationBuilder.CreateIndex(
                name: "IX_Contracts_CustomerId",
                table: "Contracts",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_Contracts_PropertyId",
                table: "Contracts",
                column: "PropertyId");

            migrationBuilder.CreateIndex(
                name: "IX_Payments_CustomerId",
                table: "Payments",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_Payments_PropertyId",
                table: "Payments",
                column: "PropertyId");

            migrationBuilder.CreateIndex(
                name: "IX_Properties_CustomerId",
                table: "Properties",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_RentChangeLogs_ContractId",
                table: "RentChangeLogs",
                column: "ContractId");

            migrationBuilder.CreateIndex(
                name: "IX_RentChangeLogs_PropertyId",
                table: "RentChangeLogs",
                column: "PropertyId");

            migrationBuilder.CreateIndex(
                name: "IX_RentIncreases_ContractId",
                table: "RentIncreases",
                column: "ContractId");

            migrationBuilder.CreateIndex(
                name: "IX_RentReceipts_ContractId",
                table: "RentReceipts",
                column: "ContractId");

            migrationBuilder.CreateIndex(
                name: "IX_RentReceipts_PaymentId",
                table: "RentReceipts",
                column: "PaymentId");

            migrationBuilder.CreateIndex(
                name: "IX_RentReceipts_ReceiptNumber_ContractId",
                table: "RentReceipts",
                columns: new[] { "ReceiptNumber", "ContractId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Users_Username",
                table: "Users",
                column: "Username",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_WhatsAppNotifications_CustomerId",
                table: "WhatsAppNotifications",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_WhatsAppNotifications_PropertyId",
                table: "WhatsAppNotifications",
                column: "PropertyId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CleaningTaxReceipts");

            migrationBuilder.DropTable(
                name: "ContractAttachments");

            migrationBuilder.DropTable(
                name: "ContractAugments");

            migrationBuilder.DropTable(
                name: "MessageTemplates");

            migrationBuilder.DropTable(
                name: "NotificationSettings");

            migrationBuilder.DropTable(
                name: "RentChangeLogs");

            migrationBuilder.DropTable(
                name: "RentIncreases");

            migrationBuilder.DropTable(
                name: "RentReceipts");

            migrationBuilder.DropTable(
                name: "Users");

            migrationBuilder.DropTable(
                name: "WhatsAppNotifications");

            migrationBuilder.DropTable(
                name: "Contracts");

            migrationBuilder.DropTable(
                name: "Payments");

            migrationBuilder.DropTable(
                name: "Properties");

            migrationBuilder.DropTable(
                name: "Customers");
        }
    }
}
