using System;
using System.Threading.Tasks;
using Xunit;
using SimpleRentalApp.Services.CloudSync.Security;

namespace SimpleRentalApp.Tests.CloudSync
{
    /// <summary>
    /// اختبارات خدمة التشفير
    /// </summary>
    public class EncryptionServiceTests
    {
        private readonly EncryptionService _encryptionService;
        private readonly string _testKey;

        public EncryptionServiceTests()
        {
            _testKey = EncryptionService.GenerateEncryptionKey();
            _encryptionService = new EncryptionService(_testKey);
        }

        [Fact]
        public void GenerateEncryptionKey_ShouldReturnValidKey()
        {
            // Arrange & Act
            var key = EncryptionService.GenerateEncryptionKey();

            // Assert
            Assert.NotNull(key);
            Assert.NotEmpty(key);
            Assert.True(key.Length >= 32); // على الأقل 256 بت
        }

        [Fact]
        public void GenerateDeviceId_ShouldReturnUniqueIds()
        {
            // Arrange & Act
            var deviceId1 = EncryptionService.GenerateDeviceId();
            var deviceId2 = EncryptionService.GenerateDeviceId();

            // Assert
            Assert.NotNull(deviceId1);
            Assert.NotNull(deviceId2);
            Assert.NotEqual(deviceId1, deviceId2);
            Assert.True(deviceId1.Length > 0);
            Assert.True(deviceId2.Length > 0);
        }

        [Theory]
        [InlineData("")]
        [InlineData("Hello World")]
        [InlineData("مرحبا بالعالم")]
        [InlineData("{'name': 'test', 'value': 123}")]
        [InlineData("Very long text that contains multiple sentences and should be encrypted properly without any issues.")]
        public async Task EncryptAsync_DecryptAsync_ShouldReturnOriginalText(string originalText)
        {
            // Act
            var encryptedText = await _encryptionService.EncryptAsync(originalText);
            var decryptedText = await _encryptionService.DecryptAsync(encryptedText);

            // Assert
            Assert.Equal(originalText, decryptedText);
        }

        [Fact]
        public async Task EncryptAsync_ShouldReturnDifferentResultsForSameInput()
        {
            // Arrange
            var originalText = "Test data for encryption";

            // Act
            var encrypted1 = await _encryptionService.EncryptAsync(originalText);
            var encrypted2 = await _encryptionService.EncryptAsync(originalText);

            // Assert
            Assert.NotEqual(encrypted1, encrypted2); // يجب أن تكون مختلفة بسبب IV العشوائي
        }

        [Fact]
        public async Task DecryptAsync_WithInvalidData_ShouldThrowException()
        {
            // Arrange
            var invalidEncryptedData = "invalid_encrypted_data";

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => _encryptionService.DecryptAsync(invalidEncryptedData));
        }

        [Fact]
        public async Task DecryptAsync_WithWrongKey_ShouldThrowException()
        {
            // Arrange
            var originalText = "Test data";
            var encryptedText = await _encryptionService.EncryptAsync(originalText);
            
            var wrongKeyService = new EncryptionService(EncryptionService.GenerateEncryptionKey());

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => wrongKeyService.DecryptAsync(encryptedText));
        }

        [Theory]
        [InlineData("")]
        [InlineData("Simple text")]
        [InlineData("نص عربي")]
        [InlineData("{'json': 'data', 'number': 42}")]
        public void CreateHash_ShouldReturnConsistentHash(string input)
        {
            // Act
            var hash1 = _encryptionService.CreateHash(input);
            var hash2 = _encryptionService.CreateHash(input);

            // Assert
            Assert.Equal(hash1, hash2);
            Assert.NotNull(hash1);
            Assert.NotEmpty(hash1);
        }

        [Fact]
        public void CreateHash_DifferentInputs_ShouldReturnDifferentHashes()
        {
            // Arrange
            var input1 = "First input";
            var input2 = "Second input";

            // Act
            var hash1 = _encryptionService.CreateHash(input1);
            var hash2 = _encryptionService.CreateHash(input2);

            // Assert
            Assert.NotEqual(hash1, hash2);
        }

        [Fact]
        public void VerifyHash_WithCorrectData_ShouldReturnTrue()
        {
            // Arrange
            var originalData = "Test data for hashing";
            var hash = _encryptionService.CreateHash(originalData);

            // Act
            var isValid = _encryptionService.VerifyHash(originalData, hash);

            // Assert
            Assert.True(isValid);
        }

        [Fact]
        public void VerifyHash_WithIncorrectData_ShouldReturnFalse()
        {
            // Arrange
            var originalData = "Original data";
            var modifiedData = "Modified data";
            var hash = _encryptionService.CreateHash(originalData);

            // Act
            var isValid = _encryptionService.VerifyHash(modifiedData, hash);

            // Assert
            Assert.False(isValid);
        }

        [Fact]
        public void VerifyHash_WithInvalidHash_ShouldReturnFalse()
        {
            // Arrange
            var data = "Test data";
            var invalidHash = "invalid_hash";

            // Act
            var isValid = _encryptionService.VerifyHash(data, invalidHash);

            // Assert
            Assert.False(isValid);
        }

        [Fact]
        public async Task EncryptAsync_WithNullInput_ShouldThrowArgumentException()
        {
            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(() => _encryptionService.EncryptAsync(null));
        }

        [Fact]
        public async Task DecryptAsync_WithNullInput_ShouldThrowArgumentException()
        {
            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(() => _encryptionService.DecryptAsync(null));
        }

        [Fact]
        public void CreateHash_WithNullInput_ShouldThrowArgumentException()
        {
            // Act & Assert
            Assert.Throws<ArgumentException>(() => _encryptionService.CreateHash(null));
        }

        [Fact]
        public void VerifyHash_WithNullInputs_ShouldThrowArgumentException()
        {
            // Act & Assert
            Assert.Throws<ArgumentException>(() => _encryptionService.VerifyHash(null, "hash"));
            Assert.Throws<ArgumentException>(() => _encryptionService.VerifyHash("data", null));
        }

        [Fact]
        public async Task EncryptDecrypt_LargeData_ShouldWorkCorrectly()
        {
            // Arrange
            var largeData = new string('A', 10000); // 10KB من البيانات

            // Act
            var encrypted = await _encryptionService.EncryptAsync(largeData);
            var decrypted = await _encryptionService.DecryptAsync(encrypted);

            // Assert
            Assert.Equal(largeData, decrypted);
        }

        [Fact]
        public async Task EncryptDecrypt_UnicodeData_ShouldWorkCorrectly()
        {
            // Arrange
            var unicodeData = "🏠 مرحبا بكم في تطبيق تدبير الكراء 🏢 Welcome to Rental Management 中文测试";

            // Act
            var encrypted = await _encryptionService.EncryptAsync(unicodeData);
            var decrypted = await _encryptionService.DecryptAsync(encrypted);

            // Assert
            Assert.Equal(unicodeData, decrypted);
        }

        [Fact]
        public void Constructor_WithInvalidKey_ShouldThrowArgumentException()
        {
            // Act & Assert
            Assert.Throws<ArgumentException>(() => new EncryptionService(""));
            Assert.Throws<ArgumentException>(() => new EncryptionService(null));
            Assert.Throws<ArgumentException>(() => new EncryptionService("short")); // مفتاح قصير جداً
        }

        [Fact]
        public async Task PerformanceTest_EncryptDecrypt_ShouldBeReasonablyFast()
        {
            // Arrange
            var testData = "Test data for performance measurement";
            var iterations = 100;
            var startTime = DateTime.UtcNow;

            // Act
            for (int i = 0; i < iterations; i++)
            {
                var encrypted = await _encryptionService.EncryptAsync(testData);
                var decrypted = await _encryptionService.DecryptAsync(encrypted);
                Assert.Equal(testData, decrypted);
            }

            var endTime = DateTime.UtcNow;
            var duration = endTime - startTime;

            // Assert
            Assert.True(duration.TotalSeconds < 10, $"العملية استغرقت {duration.TotalSeconds} ثانية، وهو أكثر من المتوقع");
        }
    }
}
