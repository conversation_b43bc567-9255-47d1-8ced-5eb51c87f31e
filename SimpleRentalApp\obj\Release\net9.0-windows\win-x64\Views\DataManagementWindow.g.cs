﻿#pragma checksum "..\..\..\..\..\Views\DataManagementWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "A0E31AE656DD71E9920334C41084A49FD8EAAC43"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleRentalApp.Views {
    
    
    /// <summary>
    /// DataManagementWindow
    /// </summary>
    public partial class DataManagementWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 97 "..\..\..\..\..\Views\DataManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportJsonBtn;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\..\Views\DataManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportDbBtn;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\..\..\Views\DataManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ImportJsonBtn;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\..\..\Views\DataManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ImportDbBtn;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\..\Views\DataManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ReplaceExistingCheckBox;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\..\..\Views\DataManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ExportPathText;
        
        #line default
        #line hidden
        
        
        #line 207 "..\..\..\..\..\Views\DataManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenExportFolderBtn;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\..\..\Views\DataManagementWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseBtn;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleRentalApp;V1.0.0.0;component/views/datamanagementwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\DataManagementWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ExportJsonBtn = ((System.Windows.Controls.Button)(target));
            
            #line 102 "..\..\..\..\..\Views\DataManagementWindow.xaml"
            this.ExportJsonBtn.Click += new System.Windows.RoutedEventHandler(this.ExportJsonBtn_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.ExportDbBtn = ((System.Windows.Controls.Button)(target));
            
            #line 115 "..\..\..\..\..\Views\DataManagementWindow.xaml"
            this.ExportDbBtn.Click += new System.Windows.RoutedEventHandler(this.ExportDbBtn_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.ImportJsonBtn = ((System.Windows.Controls.Button)(target));
            
            #line 145 "..\..\..\..\..\Views\DataManagementWindow.xaml"
            this.ImportJsonBtn.Click += new System.Windows.RoutedEventHandler(this.ImportJsonBtn_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.ImportDbBtn = ((System.Windows.Controls.Button)(target));
            
            #line 158 "..\..\..\..\..\Views\DataManagementWindow.xaml"
            this.ImportDbBtn.Click += new System.Windows.RoutedEventHandler(this.ImportDbBtn_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ReplaceExistingCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 6:
            this.ExportPathText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.OpenExportFolderBtn = ((System.Windows.Controls.Button)(target));
            
            #line 212 "..\..\..\..\..\Views\DataManagementWindow.xaml"
            this.OpenExportFolderBtn.Click += new System.Windows.RoutedEventHandler(this.OpenExportFolderBtn_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.CloseBtn = ((System.Windows.Controls.Button)(target));
            
            #line 219 "..\..\..\..\..\Views\DataManagementWindow.xaml"
            this.CloseBtn.Click += new System.Windows.RoutedEventHandler(this.CloseBtn_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

