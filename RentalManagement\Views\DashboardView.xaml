<UserControl x:Class="RentalManagement.Views.DashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800"
             FlowDirection="RightToLeft">
    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="16">
            <!-- Page Title -->
            <TextBlock Text="لوحة التحكم" 
                       Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                       Margin="0,0,0,24"/>

            <!-- Statistics Cards -->
            <Grid Margin="0,0,0,24">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Total Properties -->
                <materialDesign:Card Grid.Column="0" 
                                   Margin="0,0,8,0"
                                   Padding="16"
                                   materialDesign:ElevationAssist.Elevation="Dp2">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="Home" 
                                                   Width="32" Height="32"
                                                   Foreground="{StaticResource PrimaryBrush}"
                                                   DockPanel.Dock="Left"/>
                            <StackPanel DockPanel.Dock="Right" HorizontalAlignment="Right">
                                <TextBlock Text="{Binding TotalProperties}" 
                                         Style="{StaticResource MaterialDesignHeadline3TextBlock}"
                                         HorizontalAlignment="Right"/>
                                <TextBlock Text="إجمالي العقارات" 
                                         Style="{StaticResource MaterialDesignBody2TextBlock}"
                                         HorizontalAlignment="Right"/>
                            </StackPanel>
                        </DockPanel>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Rented Properties -->
                <materialDesign:Card Grid.Column="1" 
                                   Margin="8,0"
                                   Padding="16"
                                   materialDesign:ElevationAssist.Elevation="Dp2">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="HomeAccount" 
                                                   Width="32" Height="32"
                                                   Foreground="{StaticResource SuccessBrush}"
                                                   DockPanel.Dock="Left"/>
                            <StackPanel DockPanel.Dock="Right" HorizontalAlignment="Right">
                                <TextBlock Text="{Binding RentedProperties}" 
                                         Style="{StaticResource MaterialDesignHeadline3TextBlock}"
                                         HorizontalAlignment="Right"/>
                                <TextBlock Text="المحلات المكتراة"
                                         Style="{StaticResource MaterialDesignBody2TextBlock}"
                                         HorizontalAlignment="Right"/>
                            </StackPanel>
                        </DockPanel>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Available Properties -->
                <materialDesign:Card Grid.Column="2" 
                                   Margin="8,0"
                                   Padding="16"
                                   materialDesign:ElevationAssist.Elevation="Dp2">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="HomeOutline" 
                                                   Width="32" Height="32"
                                                   Foreground="{StaticResource WarningBrush}"
                                                   DockPanel.Dock="Left"/>
                            <StackPanel DockPanel.Dock="Right" HorizontalAlignment="Right">
                                <TextBlock Text="{Binding AvailableProperties}" 
                                         Style="{StaticResource MaterialDesignHeadline3TextBlock}"
                                         HorizontalAlignment="Right"/>
                                <TextBlock Text="العقارات المتاحة" 
                                         Style="{StaticResource MaterialDesignBody2TextBlock}"
                                         HorizontalAlignment="Right"/>
                            </StackPanel>
                        </DockPanel>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Total Customers -->
                <materialDesign:Card Grid.Column="3" 
                                   Margin="8,0,0,0"
                                   Padding="16"
                                   materialDesign:ElevationAssist.Elevation="Dp2">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="AccountGroup" 
                                                   Width="32" Height="32"
                                                   Foreground="{StaticResource AccentBrush}"
                                                   DockPanel.Dock="Left"/>
                            <StackPanel DockPanel.Dock="Right" HorizontalAlignment="Right">
                                <TextBlock Text="{Binding TotalCustomers}" 
                                         Style="{StaticResource MaterialDesignHeadline3TextBlock}"
                                         HorizontalAlignment="Right"/>
                                <TextBlock Text="إجمالي الزبائن" 
                                         Style="{StaticResource MaterialDesignBody2TextBlock}"
                                         HorizontalAlignment="Right"/>
                            </StackPanel>
                        </DockPanel>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <!-- Revenue and Overdue -->
            <Grid Margin="0,0,0,24">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Monthly Revenue -->
                <materialDesign:Card Grid.Column="0" 
                                   Margin="0,0,8,0"
                                   Padding="16"
                                   materialDesign:ElevationAssist.Elevation="Dp2">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="CurrencyUsd" 
                                                   Width="32" Height="32"
                                                   Foreground="{StaticResource SuccessBrush}"
                                                   DockPanel.Dock="Left"/>
                            <StackPanel DockPanel.Dock="Right" HorizontalAlignment="Right">
                                <TextBlock Text="{Binding TotalMonthlyRevenue, StringFormat='{}{0:C}'}" 
                                         Style="{StaticResource MaterialDesignHeadline3TextBlock}"
                                         HorizontalAlignment="Right"/>
                                <TextBlock Text="الإيرادات الشهرية" 
                                         Style="{StaticResource MaterialDesignBody2TextBlock}"
                                         HorizontalAlignment="Right"/>
                            </StackPanel>
                        </DockPanel>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Overdue Payments -->
                <materialDesign:Card Grid.Column="1" 
                                   Margin="8,0,0,0"
                                   Padding="16"
                                   materialDesign:ElevationAssist.Elevation="Dp2">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="AlertCircle" 
                                                   Width="32" Height="32"
                                                   Foreground="{StaticResource ErrorBrush}"
                                                   DockPanel.Dock="Left"/>
                            <StackPanel DockPanel.Dock="Right" HorizontalAlignment="Right">
                                <TextBlock Text="{Binding OverduePaymentsCount}" 
                                         Style="{StaticResource MaterialDesignHeadline3TextBlock}"
                                         HorizontalAlignment="Right"/>
                                <TextBlock Text="الأداءات المتأخرة" 
                                         Style="{StaticResource MaterialDesignBody2TextBlock}"
                                         HorizontalAlignment="Right"/>
                            </StackPanel>
                        </DockPanel>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <!-- Recent Activity -->
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Overdue Payments List -->
                <materialDesign:Card Grid.Column="0" 
                                   Margin="0,0,8,0"
                                   Padding="16"
                                   materialDesign:ElevationAssist.Elevation="Dp2">
                    <StackPanel>
                        <TextBlock Text="الأداءات المتأخرة" 
                                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Margin="0,0,0,16"/>
                        
                        <ListView ItemsSource="{Binding OverduePayments}"
                                MaxHeight="300">
                            <ListView.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="{StaticResource ErrorBrush}" 
                                          CornerRadius="4" 
                                          Padding="8" 
                                          Margin="0,2">
                                        <StackPanel>
                                            <TextBlock Text="{Binding Customer.FullName}" 
                                                     FontWeight="Bold"
                                                     Foreground="White"/>
                                            <TextBlock Text="{Binding Property.Location}" 
                                                     Foreground="White"/>
                                            <TextBlock Text="{Binding Amount, StringFormat='المبلغ: {0:C}'}" 
                                                     Foreground="White"/>
                                            <TextBlock Text="{Binding DueDate, StringFormat='تاريخ الاستحقاق: {0:dd/MM/yyyy}'}" 
                                                     Foreground="White"/>
                                        </StackPanel>
                                    </Border>
                                </DataTemplate>
                            </ListView.ItemTemplate>
                        </ListView>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Recent Payments -->
                <materialDesign:Card Grid.Column="1" 
                                   Margin="8,0,0,0"
                                   Padding="16"
                                   materialDesign:ElevationAssist.Elevation="Dp2">
                    <StackPanel>
                        <TextBlock Text="الأداءات الأخيرة" 
                                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Margin="0,0,0,16"/>
                        
                        <ListView ItemsSource="{Binding RecentPayments}"
                                MaxHeight="300">
                            <ListView.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="{StaticResource SuccessBrush}" 
                                          CornerRadius="4" 
                                          Padding="8" 
                                          Margin="0,2">
                                        <StackPanel>
                                            <TextBlock Text="{Binding Customer.FullName}" 
                                                     FontWeight="Bold"
                                                     Foreground="White"/>
                                            <TextBlock Text="{Binding Property.Location}" 
                                                     Foreground="White"/>
                                            <TextBlock Text="{Binding Amount, StringFormat='المبلغ: {0:C}'}" 
                                                     Foreground="White"/>
                                            <TextBlock Text="{Binding PaymentDate, StringFormat='تاريخ الدفع: {0:dd/MM/yyyy}'}" 
                                                     Foreground="White"/>
                                        </StackPanel>
                                    </Border>
                                </DataTemplate>
                            </ListView.ItemTemplate>
                        </ListView>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>
        </StackPanel>
    </ScrollViewer>
</UserControl>
