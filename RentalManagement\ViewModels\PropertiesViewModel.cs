using Microsoft.EntityFrameworkCore;
using RentalManagement.Data;
using RentalManagement.Helpers;
using RentalManagement.Models;
using RentalManagement.Services;
using System.Collections.ObjectModel;
using System.Windows.Input;

namespace RentalManagement.ViewModels
{
    public class PropertiesViewModel : BaseViewModel
    {
        private Property? _selectedProperty;
        private string _searchText = string.Empty;
        private PropertyStatus? _statusFilter;
        private PropertyType? _typeFilter;

        public PropertiesViewModel()
        {
            Title = "إدارة العقارات";
            Properties = new ObservableCollection<Property>();

            // Initialize commands
            AddPropertyCommand = new RelayCommand(AddProperty);
            EditPropertyCommand = new RelayCommand<Property>(EditProperty);
            DeletePropertyCommand = new RelayCommand<Property>(DeleteProperty);
            ViewPropertyDetailsCommand = new RelayCommand<Property>(ViewPropertyDetails);
            SearchCommand = new RelayCommand(Search);
            RefreshCommand = new RelayCommand(LoadProperties);
            ClearFiltersCommand = new RelayCommand(ClearFilters);

            LoadProperties();
        }

        public ObservableCollection<Property> Properties { get; }

        public Property? SelectedProperty
        {
            get => _selectedProperty;
            set => SetProperty(ref _selectedProperty, value);
        }

        public string SearchText
        {
            get => _searchText;
            set => SetProperty(ref _searchText, value);
        }

        public PropertyStatus? StatusFilter
        {
            get => _statusFilter;
            set
            {
                if (SetProperty(ref _statusFilter, value))
                {
                    Search();
                }
            }
        }

        public PropertyType? TypeFilter
        {
            get => _typeFilter;
            set
            {
                if (SetProperty(ref _typeFilter, value))
                {
                    Search();
                }
            }
        }

        // Commands
        public ICommand AddPropertyCommand { get; }
        public ICommand EditPropertyCommand { get; }
        public ICommand DeletePropertyCommand { get; }
        public ICommand ViewPropertyDetailsCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand RefreshCommand { get; }
        public ICommand ClearFiltersCommand { get; }

        private async void LoadProperties()
        {
            try
            {
                IsBusy = true;
                ClearError();

                using var context = DatabaseService.Instance.CreateContext();
                var properties = await context.Properties
                    .Include(p => p.Customer)
                    .OrderBy(p => p.Location)
                    .ToListAsync();

                Properties.Clear();
                foreach (var property in properties)
                {
                    Properties.Add(property);
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في تحميل العقارات: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async void AddProperty()
        {
            var dialog = new PropertyDialogViewModel();
            var result = await ShowPropertyDialog(dialog);

            if (result == true && dialog.Property != null)
            {
                try
                {
                    IsBusy = true;
                    ClearError();

                    using var context = DatabaseService.Instance.CreateContext();
                    context.Properties.Add(dialog.Property);
                    await context.SaveChangesAsync();

                    Properties.Add(dialog.Property);
                }
                catch (Exception ex)
                {
                    SetError($"خطأ في إضافة العقار: {ex.Message}");
                }
                finally
                {
                    IsBusy = false;
                }
            }
        }

        private async void EditProperty(Property? property)
        {
            if (property == null) return;

            var dialog = new PropertyDialogViewModel(property);
            var result = await ShowPropertyDialog(dialog);

            if (result == true && dialog.Property != null)
            {
                try
                {
                    IsBusy = true;
                    ClearError();

                    using var context = DatabaseService.Instance.CreateContext();
                    context.Properties.Update(dialog.Property);
                    await context.SaveChangesAsync();

                    // Update the property in the collection
                    var index = Properties.IndexOf(property);
                    if (index >= 0)
                    {
                        Properties[index] = dialog.Property;
                        SelectedProperty = dialog.Property;
                    }
                }
                catch (Exception ex)
                {
                    SetError($"خطأ في تعديل العقار: {ex.Message}");
                }
                finally
                {
                    IsBusy = false;
                }
            }
        }

        private async void DeleteProperty(Property? property)
        {
            if (property == null) return;

            // Show confirmation dialog
            var result = System.Windows.MessageBox.Show(
                $"هل أنت متأكد من حذف العقار '{property.Location}'؟\nسيتم حذف جميع البيانات المرتبطة به.",
                "تأكيد الحذف",
                System.Windows.MessageBoxButton.YesNo,
                System.Windows.MessageBoxImage.Warning);

            if (result != System.Windows.MessageBoxResult.Yes) return;

            try
            {
                IsBusy = true;
                ClearError();

                using var context = DatabaseService.Instance.CreateContext();
                var propertyToDelete = await context.Properties.FindAsync(property.Id);
                if (propertyToDelete != null)
                {
                    context.Properties.Remove(propertyToDelete);
                    await context.SaveChangesAsync();
                    Properties.Remove(property);

                    if (SelectedProperty == property)
                        SelectedProperty = null;
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في حذف العقار: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void ViewPropertyDetails(Property? property)
        {
            if (property == null) return;

            // This would show a detailed view of the property
            // For now, just select the property
            SelectedProperty = property;
        }

        private async void Search()
        {
            try
            {
                IsBusy = true;
                ClearError();

                using var context = DatabaseService.Instance.CreateContext();
                var query = context.Properties.Include(p => p.Customer).AsQueryable();

                if (!string.IsNullOrWhiteSpace(SearchText))
                {
                    query = query.Where(p => p.Location.Contains(SearchText) ||
                                           p.Description.Contains(SearchText));
                }

                if (StatusFilter.HasValue)
                {
                    query = query.Where(p => p.Status == StatusFilter.Value);
                }

                if (TypeFilter.HasValue)
                {
                    query = query.Where(p => p.Type == TypeFilter.Value);
                }

                var properties = await query.OrderBy(p => p.Location).ToListAsync();

                Properties.Clear();
                foreach (var property in properties)
                {
                    Properties.Add(property);
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في البحث: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void ClearFilters()
        {
            SearchText = string.Empty;
            StatusFilter = null;
            TypeFilter = null;
            LoadProperties();
        }

        private async Task<bool?> ShowPropertyDialog(PropertyDialogViewModel dialog)
        {
            return await Task.Run(() =>
            {
                bool? result = null;

                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    var dialogWindow = new Views.PropertyDialog(dialog);
                    dialogWindow.Owner = System.Windows.Application.Current.MainWindow;
                    result = dialogWindow.ShowDialog();
                });

                return result;
            });
        }
    }
}
