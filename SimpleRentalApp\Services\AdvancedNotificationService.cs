using Microsoft.EntityFrameworkCore;
using SimpleRentalApp.Data;
using SimpleRentalApp.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SimpleRentalApp.Services
{
    /// <summary>
    /// خدمة إدارة الإشعارات المتطورة
    /// </summary>
    public class AdvancedNotificationService
    {
        private readonly RentalDbContext _context;

        public AdvancedNotificationService(RentalDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// توليد إشعارات الإيجار الشهري للعقود النشطة
        /// </summary>
        public async Task<List<AdvancedNotification>> GenerateMonthlyRentNotificationsAsync(int year, int month)
        {
            var notifications = new List<AdvancedNotification>();

            try
            {
                // الحصول على العقود النشطة
                var activeContracts = await _context.Contracts
                    .Include(c => c.Property)
                        .ThenInclude(p => p.Customer)
                    .Where(c => c.IsActive && 
                               c.StartDate <= DateTime.Now &&
                               (!c.EndDate.HasValue || c.EndDate.Value >= DateTime.Now))
                    .ToListAsync();

                foreach (var contract in activeContracts)
                {
                    // التحقق من عدم وجود إشعار مسبق لنفس الشهر والسنة
                    var existingNotification = await _context.AdvancedNotifications
                        .FirstOrDefaultAsync(n => n.ContractId == contract.Id &&
                                                 n.Type == NotificationType.MonthlyRent &&
                                                 n.PaymentYear == year &&
                                                 n.PaymentMonth == month);

                    if (existingNotification == null)
                    {
                        var dueDate = new DateTime(year, month, contract.PaymentDay ?? 1);
                        
                        var notification = new AdvancedNotification
                        {
                            PropertyId = contract.PropertyId,
                            CustomerId = contract.Property.CustomerId,
                            ContractId = contract.Id,
                            Type = NotificationType.MonthlyRent,
                            Status = NotificationStatus.Pending,
                            Priority = NotificationPriority.Normal,
                            DueDate = dueDate,
                            Amount = contract.CurrentRentAmount,
                            PaymentMonth = month,
                            PaymentYear = year,
                            Message = GenerateMonthlyRentMessage(contract, year, month),
                            CreatedDate = DateTime.Now,
                            UpdatedDate = DateTime.Now
                        };

                        notifications.Add(notification);
                    }
                }

                if (notifications.Any())
                {
                    _context.AdvancedNotifications.AddRange(notifications);
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error generating monthly rent notifications: {ex.Message}");
            }

            return notifications;
        }

        /// <summary>
        /// توليد إشعارات ضريبة النظافة السنوية
        /// </summary>
        public async Task<List<AdvancedNotification>> GenerateCleaningTaxNotificationsAsync(int taxYear)
        {
            var notifications = new List<AdvancedNotification>();

            try
            {
                // الحصول على العقود النشطة
                var activeContracts = await _context.Contracts
                    .Include(c => c.Property)
                        .ThenInclude(p => p.Customer)
                    .Where(c => c.IsActive)
                    .ToListAsync();

                foreach (var contract in activeContracts)
                {
                    // التحقق من عدم وجود إشعار مسبق لنفس السنة الضريبية
                    var existingNotification = await _context.AdvancedNotifications
                        .FirstOrDefaultAsync(n => n.PropertyId == contract.PropertyId &&
                                                 n.Type == NotificationType.CleaningTax &&
                                                 n.TaxYear == taxYear);

                    if (existingNotification == null)
                    {
                        var dueDate = new DateTime(taxYear, 5, 31); // موعد استحقاق ضريبة النظافة
                        var annualTax = contract.CurrentRentAmount * 12 * 0.105m;

                        var notification = new AdvancedNotification
                        {
                            PropertyId = contract.PropertyId,
                            CustomerId = contract.Property.CustomerId ?? contract.CustomerId,
                            ContractId = contract.Id,
                            Type = NotificationType.CleaningTax,
                            Status = NotificationStatus.Pending,
                            Priority = NotificationPriority.High,
                            DueDate = dueDate,
                            Amount = annualTax,
                            TaxYear = taxYear,
                            Message = GenerateCleaningTaxMessage(contract, taxYear, annualTax),
                            CreatedDate = DateTime.Now,
                            UpdatedDate = DateTime.Now
                        };

                        notifications.Add(notification);
                    }
                }

                if (notifications.Any())
                {
                    _context.AdvancedNotifications.AddRange(notifications);
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error generating cleaning tax notifications: {ex.Message}");
            }

            return notifications;
        }

        /// <summary>
        /// توليد إشعارات التأخير
        /// </summary>
        public async Task<List<AdvancedNotification>> GenerateOverdueNotificationsAsync()
        {
            var notifications = new List<AdvancedNotification>();

            try
            {
                var today = DateTime.Today;

                // إشعارات تأخير الإيجار
                var overdueRentNotifications = await _context.AdvancedNotifications
                    .Include(n => n.Property)
                        .ThenInclude(p => p.Customer)
                    .Include(n => n.Contract)
                    .Where(n => n.Type == NotificationType.MonthlyRent &&
                               n.Status == NotificationStatus.Pending &&
                               n.DueDate < today)
                    .ToListAsync();

                foreach (var rentNotification in overdueRentNotifications)
                {
                    var daysOverdue = (today - rentNotification.DueDate).Days;
                    
                    // إنشاء إشعار تأخير إذا لم يكن موجوداً
                    var existingOverdue = await _context.AdvancedNotifications
                        .FirstOrDefaultAsync(n => n.PropertyId == rentNotification.PropertyId &&
                                                 n.Type == NotificationType.RentOverdue &&
                                                 n.PaymentYear == rentNotification.PaymentYear &&
                                                 n.PaymentMonth == rentNotification.PaymentMonth);

                    if (existingOverdue == null && daysOverdue >= 3) // بعد 3 أيام من التأخير
                    {
                        var overdueNotification = new AdvancedNotification
                        {
                            PropertyId = rentNotification.PropertyId,
                            CustomerId = rentNotification.CustomerId,
                            ContractId = rentNotification.ContractId,
                            Type = NotificationType.RentOverdue,
                            Status = NotificationStatus.Pending,
                            Priority = NotificationPriority.Urgent,
                            DueDate = today,
                            Amount = rentNotification.Amount,
                            PaymentMonth = rentNotification.PaymentMonth,
                            PaymentYear = rentNotification.PaymentYear,
                            DaysOverdue = daysOverdue,
                            Message = GenerateOverdueRentMessage(rentNotification, daysOverdue),
                            CreatedDate = DateTime.Now,
                            UpdatedDate = DateTime.Now
                        };

                        notifications.Add(overdueNotification);
                    }
                }

                // إشعارات تأخير ضريبة النظافة
                var overdueCleaningTaxNotifications = await _context.AdvancedNotifications
                    .Include(n => n.Property)
                        .ThenInclude(p => p.Customer)
                    .Include(n => n.Contract)
                    .Where(n => n.Type == NotificationType.CleaningTax &&
                               n.Status == NotificationStatus.Pending &&
                               n.DueDate < today)
                    .ToListAsync();

                foreach (var taxNotification in overdueCleaningTaxNotifications)
                {
                    var daysOverdue = (today - taxNotification.DueDate).Days;
                    
                    var existingOverdue = await _context.AdvancedNotifications
                        .FirstOrDefaultAsync(n => n.PropertyId == taxNotification.PropertyId &&
                                                 n.Type == NotificationType.CleaningTaxOverdue &&
                                                 n.TaxYear == taxNotification.TaxYear);

                    if (existingOverdue == null && daysOverdue >= 7) // بعد أسبوع من التأخير
                    {
                        var overdueNotification = new AdvancedNotification
                        {
                            PropertyId = taxNotification.PropertyId,
                            CustomerId = taxNotification.CustomerId,
                            ContractId = taxNotification.ContractId,
                            Type = NotificationType.CleaningTaxOverdue,
                            Status = NotificationStatus.Pending,
                            Priority = NotificationPriority.Urgent,
                            DueDate = today,
                            Amount = taxNotification.Amount,
                            TaxYear = taxNotification.TaxYear,
                            DaysOverdue = daysOverdue,
                            Message = GenerateOverdueCleaningTaxMessage(taxNotification, daysOverdue),
                            CreatedDate = DateTime.Now,
                            UpdatedDate = DateTime.Now
                        };

                        notifications.Add(overdueNotification);
                    }
                }

                if (notifications.Any())
                {
                    _context.AdvancedNotifications.AddRange(notifications);
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error generating overdue notifications: {ex.Message}");
            }

            return notifications;
        }

        /// <summary>
        /// الحصول على جميع الإشعارات مع التصفية
        /// </summary>
        public async Task<List<NotificationDisplayInfo>> GetNotificationsAsync(
            NotificationType? type = null,
            NotificationStatus? status = null,
            int? propertyId = null,
            int? customerId = null,
            DateTime? fromDate = null,
            DateTime? toDate = null)
        {
            try
            {
                var query = _context.AdvancedNotifications
                    .Include(n => n.Property)
                    .Include(n => n.Customer)
                    .Include(n => n.Contract)
                    .AsQueryable();

                if (type.HasValue)
                    query = query.Where(n => n.Type == type.Value);

                if (status.HasValue)
                    query = query.Where(n => n.Status == status.Value);

                if (propertyId.HasValue)
                    query = query.Where(n => n.PropertyId == propertyId.Value);

                if (customerId.HasValue)
                    query = query.Where(n => n.CustomerId == customerId.Value);

                if (fromDate.HasValue)
                    query = query.Where(n => n.DueDate >= fromDate.Value);

                if (toDate.HasValue)
                    query = query.Where(n => n.DueDate <= toDate.Value);

                var notifications = await query
                    .OrderByDescending(n => n.CreatedDate)
                    .ToListAsync();

                return notifications.Select(n => new NotificationDisplayInfo
                {
                    Id = n.Id,
                    PropertyName = n.Property.Name,
                    CustomerName = n.Customer.Name,
                    PhoneNumber = n.Customer.PhoneNumber,
                    TypeDisplay = n.TypeDisplay,
                    StatusDisplay = n.StatusDisplay,
                    PriorityDisplay = n.PriorityDisplay,
                    AmountDisplay = n.AmountDisplay,
                    DueDateDisplay = n.DueDateDisplay,
                    SentDateDisplay = n.SentDateDisplay,
                    Message = n.Message,
                    Notes = n.Notes,
                    IsOverdue = n.IsOverdue,
                    CanRetry = n.CanRetry,
                    CanSend = n.Status == NotificationStatus.Pending || n.Status == NotificationStatus.Failed,
                    RetryCount = n.RetryCount,
                    MaxRetries = n.MaxRetries,
                    Type = n.Type,
                    Status = n.Status,
                    Priority = n.Priority,
                    DueDate = n.DueDate,
                    SentDate = n.SentDate,
                    Notification = n
                }).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting notifications: {ex.Message}");
                return new List<NotificationDisplayInfo>();
            }
        }

        // Private helper methods for message generation
        private string GenerateMonthlyRentMessage(Contract contract, int year, int month)
        {
            var monthName = new DateTime(year, month, 1).ToString("MMMM yyyy");
            return $"تذكير: إيجار شهر {monthName} للمحل '{contract.Property.Name}' بمبلغ {contract.CurrentRentAmount:N0} درهم مستحق الدفع.";
        }

        private string GenerateCleaningTaxMessage(Contract contract, int taxYear, decimal amount)
        {
            return $"تذكير: ضريبة النظافة لسنة {taxYear} للمحل '{contract.Property.Name}' بمبلغ {amount:N0} درهم مستحقة الدفع قبل 31/05/{taxYear}.";
        }

        private string GenerateOverdueRentMessage(AdvancedNotification rentNotification, int daysOverdue)
        {
            var monthName = new DateTime(rentNotification.PaymentYear!.Value, rentNotification.PaymentMonth!.Value, 1).ToString("MMMM yyyy");
            return $"تنبيه: إيجار شهر {monthName} للمحل '{rentNotification.Property.Name}' متأخر {daysOverdue} يوم. المبلغ المستحق: {rentNotification.Amount:N0} درهم.";
        }

        private string GenerateOverdueCleaningTaxMessage(AdvancedNotification taxNotification, int daysOverdue)
        {
            return $"تنبيه: ضريبة النظافة لسنة {taxNotification.TaxYear} للمحل '{taxNotification.Property.Name}' متأخرة {daysOverdue} يوم. المبلغ المستحق: {taxNotification.Amount:N0} درهم.";
        }
    }
}
