using Microsoft.EntityFrameworkCore;
using RentalManagement.Data;
using RentalManagement.Helpers;
using RentalManagement.Models;
using RentalManagement.Services;
using System.Collections.ObjectModel;
using System.Windows.Input;

namespace RentalManagement.ViewModels
{
    public class CustomersViewModel : BaseViewModel
    {
        private Customer? _selectedCustomer;
        private string _searchText = string.Empty;

        public CustomersViewModel()
        {
            Title = "إدارة الزبائن";
            Customers = new ObservableCollection<Customer>();
            
            // Initialize commands
            AddCustomerCommand = new RelayCommand(AddCustomer);
            EditCustomerCommand = new RelayCommand<Customer>(EditCustomer);
            DeleteCustomerCommand = new RelayCommand<Customer>(DeleteCustomer);
            ViewCustomerDetailsCommand = new RelayCommand<Customer>(ViewCustomerDetails);
            SearchCommand = new RelayCommand(Search);
            RefreshCommand = new RelayCommand(LoadCustomers);

            LoadCustomers();
        }

        public ObservableCollection<Customer> Customers { get; }

        public Customer? SelectedCustomer
        {
            get => _selectedCustomer;
            set => SetProperty(ref _selectedCustomer, value);
        }

        public string SearchText
        {
            get => _searchText;
            set => SetProperty(ref _searchText, value);
        }

        // Commands
        public ICommand AddCustomerCommand { get; }
        public ICommand EditCustomerCommand { get; }
        public ICommand DeleteCustomerCommand { get; }
        public ICommand ViewCustomerDetailsCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand RefreshCommand { get; }

        private async void LoadCustomers()
        {
            try
            {
                IsBusy = true;
                ClearError();

                using var context = DatabaseService.Instance.CreateContext();
                var customers = await context.Customers
                    .OrderBy(c => c.FullName)
                    .ToListAsync();

                Customers.Clear();
                foreach (var customer in customers)
                {
                    Customers.Add(customer);
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في تحميل الزبائن: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async void AddCustomer()
        {
            var dialog = new CustomerDialogViewModel();
            var result = await ShowCustomerDialog(dialog);

            if (result == true && dialog.Customer != null)
            {
                try
                {
                    IsBusy = true;
                    ClearError();

                    using var context = DatabaseService.Instance.CreateContext();
                    context.Customers.Add(dialog.Customer);
                    await context.SaveChangesAsync();

                    Customers.Add(dialog.Customer);
                }
                catch (Exception ex)
                {
                    SetError($"خطأ في إضافة الزبون: {ex.Message}");
                }
                finally
                {
                    IsBusy = false;
                }
            }
        }

        private async void EditCustomer(Customer? customer)
        {
            if (customer == null) return;

            var dialog = new CustomerDialogViewModel(customer);
            var result = await ShowCustomerDialog(dialog);

            if (result == true && dialog.Customer != null)
            {
                try
                {
                    IsBusy = true;
                    ClearError();

                    using var context = DatabaseService.Instance.CreateContext();
                    context.Customers.Update(dialog.Customer);
                    await context.SaveChangesAsync();

                    // Update the customer in the collection
                    var index = Customers.IndexOf(customer);
                    if (index >= 0)
                    {
                        Customers[index] = dialog.Customer;
                        SelectedCustomer = dialog.Customer;
                    }
                }
                catch (Exception ex)
                {
                    SetError($"خطأ في تعديل الزبون: {ex.Message}");
                }
                finally
                {
                    IsBusy = false;
                }
            }
        }

        private async Task<bool?> ShowCustomerDialog(CustomerDialogViewModel dialog)
        {
            return await Task.Run(() =>
            {
                bool? result = null;

                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    var dialogWindow = new Views.CustomerDialog(dialog);
                    dialogWindow.Owner = System.Windows.Application.Current.MainWindow;
                    result = dialogWindow.ShowDialog();
                });

                return result;
            });
        }

        private async void DeleteCustomer(Customer? customer)
        {
            if (customer == null) return;

            // Show confirmation dialog
            var result = System.Windows.MessageBox.Show(
                $"هل أنت متأكد من حذف الزبون '{customer.FullName}'؟\nسيتم حذف جميع البيانات المرتبطة به.",
                "تأكيد الحذف",
                System.Windows.MessageBoxButton.YesNo,
                System.Windows.MessageBoxImage.Warning);

            if (result != System.Windows.MessageBoxResult.Yes) return;

            try
            {
                IsBusy = true;
                ClearError();

                using var context = DatabaseService.Instance.CreateContext();
                var customerToDelete = await context.Customers.FindAsync(customer.Id);
                if (customerToDelete != null)
                {
                    context.Customers.Remove(customerToDelete);
                    await context.SaveChangesAsync();
                    Customers.Remove(customer);

                    if (SelectedCustomer == customer)
                        SelectedCustomer = null;
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في حذف الزبون: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void ViewCustomerDetails(Customer? customer)
        {
            if (customer == null) return;

            // This would show a detailed view of the customer
            // For now, just select the customer
            SelectedCustomer = customer;
        }

        private async void Search()
        {
            try
            {
                IsBusy = true;
                ClearError();

                using var context = DatabaseService.Instance.CreateContext();
                var query = context.Customers.AsQueryable();

                if (!string.IsNullOrWhiteSpace(SearchText))
                {
                    query = query.Where(c => c.FullName.Contains(SearchText) ||
                                           c.NationalId.Contains(SearchText) ||
                                           c.PhoneNumber.Contains(SearchText));
                }

                var customers = await query.OrderBy(c => c.FullName).ToListAsync();

                Customers.Clear();
                foreach (var customer in customers)
                {
                    Customers.Add(customer);
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في البحث: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }
    }
}
