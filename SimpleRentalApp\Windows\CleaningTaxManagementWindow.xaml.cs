using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Microsoft.EntityFrameworkCore;
using SimpleRentalApp.Data;
using SimpleRentalApp.Models;
using SimpleRentalApp.Services;

namespace SimpleRentalApp.Windows
{
    public partial class CleaningTaxManagementWindow : Window
    {
        #region Fields and Configuration
        private List<CleaningTaxPropertyInfo> _propertyInfos = new();
        private int _selectedYear;
        private readonly RentalDbContext _context;

        // Year Configuration - مطابق لنافذة تفاصيل المحل
        private const int DEFAULT_START_YEAR = 2020;           // سنة البداية الافتراضية
        private const int DEFAULT_FUTURE_YEARS = 5;            // عدد السنوات المستقبلية
        private const bool DEFAULT_DESCENDING_ORDER = true;    // ترتيب تنازلي (الأحدث أولاً)

        private static int StartYear => DEFAULT_START_YEAR;
        private static int FutureYears => DEFAULT_FUTURE_YEARS;
        private static bool DescendingOrder => DEFAULT_DESCENDING_ORDER;
        #endregion

        public CleaningTaxManagementWindow()
        {
            InitializeComponent();
            _context = new RentalDbContext();
            InitializeWindow();
        }

        private async void InitializeWindow()
        {
            try
            {
                await LoadYears();
                await LoadData();
                SetupEventHandlers();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadYears()
        {
            try
            {
                // Generate dynamic years list using configuration
                var years = GenerateYearsList(StartYear, FutureYears, DescendingOrder);

                // Set ItemsSource
                YearComboBox.ItemsSource = years;

                // Set default selection to current year
                var currentYear = DateTime.Now.Year;
                _selectedYear = currentYear;

                // Ensure selected year is in the list
                if (years.Contains(_selectedYear))
                {
                    YearComboBox.SelectedItem = _selectedYear;
                }
                else
                {
                    // If current year is not in list, select the first available year
                    if (years.Any())
                    {
                        _selectedYear = years.First();
                        YearComboBox.SelectedItem = _selectedYear;
                    }
                }

                // Log the generated years for debugging
                System.Diagnostics.Debug.WriteLine($"CleaningTaxManagement - Generated years: {string.Join(", ", years)} (Total: {years.Count})");
                System.Diagnostics.Debug.WriteLine($"CleaningTaxManagement - Selected year: {_selectedYear}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in LoadYears: {ex.Message}");
                // Fallback to simple year list
                var currentYear = DateTime.Now.Year;
                var fallbackYears = new List<int> { currentYear + 1, currentYear, currentYear - 1, currentYear - 2 };
                YearComboBox.ItemsSource = fallbackYears;
                YearComboBox.SelectedItem = currentYear;
                _selectedYear = currentYear;
            }
        }

        /// <summary>
        /// Generates a dynamic list of years from start year to current year + future years
        /// This method creates a future-proof year list that automatically adapts to the current year
        /// </summary>
        /// <param name="startYear">Starting year (e.g., 2020 - when the system started)</param>
        /// <param name="futureYears">Number of future years to include (e.g., 5 years ahead)</param>
        /// <param name="descending">Whether to sort in descending order (true = newest first, better UX)</param>
        /// <returns>List of years ready for ComboBox binding</returns>
        private List<int> GenerateYearsList(int startYear = 2020, int futureYears = 5, bool descending = true)
        {
            try
            {
                var currentYear = DateTime.Now.Year;
                var endYear = currentYear + futureYears;
                var years = new List<int>();

                // Validate input parameters
                if (startYear > currentYear + futureYears)
                {
                    System.Diagnostics.Debug.WriteLine($"Warning: Start year {startYear} is beyond end year {endYear}");
                    startYear = currentYear - 2; // Fallback to reasonable start
                }

                if (futureYears < 0)
                {
                    System.Diagnostics.Debug.WriteLine($"Warning: Future years {futureYears} is negative, using 0");
                    futureYears = 0;
                }

                // Generate years from start to end
                for (int year = startYear; year <= endYear; year++)
                {
                    years.Add(year);
                }

                // Sort based on preference (descending by default for better UX)
                if (descending)
                {
                    years.Sort((x, y) => y.CompareTo(x)); // Descending order (2029, 2028, 2027, ...)
                }
                else
                {
                    years.Sort(); // Ascending order (2020, 2021, 2022, ...)
                }

                return years;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error generating years list: {ex.Message}");
                // Return minimal fallback list centered around current year
                var currentYear = DateTime.Now.Year;
                var fallbackYears = new List<int> { currentYear + 1, currentYear, currentYear - 1, currentYear - 2 };
                return fallbackYears;
            }
        }

        private void SetupEventHandlers()
        {
            YearComboBox.SelectionChanged += async (s, e) =>
            {
                if (YearComboBox.SelectedItem is int selectedYear)
                {
                    _selectedYear = selectedYear;
                    await LoadData();
                }
            };

            RefreshButton.Click += async (s, e) => await LoadData();

            // Add double-click event for row details
            PropertiesDataGrid.MouseDoubleClick += (s, e) =>
            {
                if (PropertiesDataGrid.SelectedItem is CleaningTaxPropertyInfo selectedInfo)
                {
                    ShowPropertyDetails(selectedInfo);
                }
            };
        }

        private async Task LoadData()
        {
            try
            {
                StatusTextBlock.Text = "جاري تحميل البيانات...";
                
                var properties = await DatabaseService.Instance.GetPropertiesAsync();
                var payments = await DatabaseService.Instance.GetPaymentsAsync();
                
                // Filter payments for selected year and cleaning tax
                // Use TaxYear if available, otherwise fall back to PaymentDate.Year for backward compatibility
                var yearPayments = payments.Where(p =>
                    ((p.TaxYear.HasValue && p.TaxYear.Value == _selectedYear) ||
                     (!p.TaxYear.HasValue && p.PaymentDate?.Year == _selectedYear)) &&
                    p.CleaningTaxAmount > 0).ToList();

                _propertyInfos = new List<CleaningTaxPropertyInfo>();

                foreach (var property in properties)
                {
                    // جلب المكتري من أحدث عقد للمحل (نشط أو أحدث عقد)
                    Customer? customer = null;

                    // أولاً: البحث عن عقد نشط
                    var activeContract = await _context.Contracts
                        .Include(c => c.Customer)
                        .Where(c => c.PropertyId == property.Id &&
                               c.StartDate <= DateTime.Now &&
                               c.EndDate >= DateTime.Now)
                        .OrderByDescending(c => c.StartDate)
                        .FirstOrDefaultAsync();

                    // إذا لم يوجد عقد نشط، جلب أحدث عقد
                    if (activeContract == null)
                    {
                        activeContract = await _context.Contracts
                            .Include(c => c.Customer)
                            .Where(c => c.PropertyId == property.Id)
                            .OrderByDescending(c => c.StartDate)
                            .FirstOrDefaultAsync();
                    }

                    if (activeContract != null)
                    {
                        customer = activeContract.Customer;
                        System.Diagnostics.Debug.WriteLine($"Property: {property.Name}, Customer from Contract: {customer?.FullName ?? "null"}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"Property: {property.Name}, No contract found");
                    }

                    var propertyPayments = yearPayments.Where(p => p.PropertyId == property.Id).ToList();
                    
                    var annualTax = property.MonthlyRent * 12 * 0.105m;
                    var paidAmount = propertyPayments.Sum(p => p.CleaningTaxAmount);
                    var remainingAmount = Math.Max(0, annualTax - paidAmount);
                    
                    var status = GetPaymentStatus(paidAmount, annualTax);
                    
                    var info = new CleaningTaxPropertyInfo
                    {
                        PropertyId = property.Id,
                        PropertyName = property.Name,
                        CustomerName = customer?.FullName ?? "غير محدد",
                        PaymentOption = property.CleaningTaxPaymentOptionDisplay ?? "غير محدد",
                        AnnualTax = annualTax,
                        PaidAmount = paidAmount,
                        RemainingAmount = remainingAmount,
                        Status = status,
                        HasPayments = propertyPayments.Count > 0
                    };
                    
                    _propertyInfos.Add(info);
                }

                PropertiesDataGrid.ItemsSource = _propertyInfos;
                UpdateStatistics();
                UpdateRowColors();
                
                StatusTextBlock.Text = $"تم تحميل {_propertyInfos.Count} محل للسنة {_selectedYear}";
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "خطأ في تحميل البيانات";
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string GetPaymentStatus(decimal paidAmount, decimal annualTax)
        {
            if (paidAmount <= 0) return "غير مدفوعة";
            if (paidAmount >= annualTax) return "مكتملة";
            return "جزئية";
        }

        private void UpdateStatistics()
        {
            var total = _propertyInfos.Count;
            var paid = _propertyInfos.Count(p => p.Status == "مكتملة");
            var partial = _propertyInfos.Count(p => p.Status == "جزئية");
            var unpaid = _propertyInfos.Count(p => p.Status == "غير مدفوعة");

            TotalPropertiesText.Text = total.ToString();
            PaidPropertiesText.Text = paid.ToString();
            PartialPropertiesText.Text = partial.ToString();
            UnpaidPropertiesText.Text = unpaid.ToString();
        }

        private void UpdateRowColors()
        {
            PropertiesDataGrid.LoadingRow += (s, e) =>
            {
                if (e.Row.DataContext is CleaningTaxPropertyInfo info)
                {
                    switch (info.Status)
                    {
                        case "مكتملة":
                            e.Row.Background = new SolidColorBrush(Color.FromRgb(232, 245, 233)); // Light green
                            break;
                        case "جزئية":
                            e.Row.Background = new SolidColorBrush(Color.FromRgb(255, 248, 225)); // Light orange
                            break;
                        case "غير مدفوعة":
                            if (!info.HasPayments)
                            {
                                e.Row.Background = new SolidColorBrush(Color.FromRgb(255, 235, 238)); // Light red for no data
                                e.Row.ToolTip = "لا توجد بيانات ضريبة نظافة لهذا المحل في السنة المختارة";
                            }
                            else
                            {
                                e.Row.Background = new SolidColorBrush(Color.FromRgb(255, 245, 245)); // Very light red
                            }
                            break;
                    }
                }
            };
        }

        private async void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is int propertyId)
                {
                    var property = await DatabaseService.Instance.GetPropertyByIdAsync(propertyId);
                    if (property == null)
                    {
                        MessageBox.Show("لم يتم العثور على المحل", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }

                    // Get payments for this property in selected year
                    var payments = await DatabaseService.Instance.GetPaymentsAsync();
                    var propertyPayments = payments.Where(p =>
                        p.PropertyId == propertyId &&
                        ((p.TaxYear.HasValue && p.TaxYear.Value == _selectedYear) ||
                         (!p.TaxYear.HasValue && p.PaymentDate?.Year == _selectedYear)) &&
                        p.CleaningTaxAmount > 0).ToList();

                    if (propertyPayments.Count == 0)
                    {
                        MessageBox.Show($"لا توجد دفعات ضريبة نظافة للمحل {property.Name} في عام {_selectedYear}", 
                            "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }

                    // Show receipt selection dialog
                    var dialog = new CleaningTaxReceiptSelectionDialog(property, propertyPayments);
                    dialog.Owner = this;
                    dialog.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة الوصل: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is int propertyId)
                {
                    var property = await DatabaseService.Instance.GetPropertyByIdAsync(propertyId);
                    if (property == null)
                    {
                        MessageBox.Show("لم يتم العثور على المحل", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }

                    var result = MessageBox.Show(
                        $"هل أنت متأكد من حذف جميع دفعات ضريبة النظافة للمحل '{property.Name}' في عام {_selectedYear}؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                        "تأكيد الحذف",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        await DeleteCleaningTaxPayments(propertyId);
                        await LoadData(); // Refresh data
                        MessageBox.Show("تم حذف دفعات ضريبة النظافة بنجاح", "نجح", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task DeleteCleaningTaxPayments(int propertyId)
        {
            var payments = await DatabaseService.Instance.GetPaymentsAsync();
            var paymentsToDelete = payments.Where(p =>
                p.PropertyId == propertyId &&
                ((p.TaxYear.HasValue && p.TaxYear.Value == _selectedYear) ||
                 (!p.TaxYear.HasValue && p.PaymentDate?.Year == _selectedYear)) &&
                p.CleaningTaxAmount > 0).ToList();

            foreach (var payment in paymentsToDelete)
            {
                await DatabaseService.Instance.DeletePaymentAsync(payment.Id);
            }
        }

        // Additional helper methods
        private void ExportToExcel()
        {
            try
            {
                var csv = "اسم المحل,اسم المكتري,طريقة الدفع,الضريبة السنوية,المبلغ المدفوع,المبلغ المتبقي,الحالة\n";

                foreach (var info in _propertyInfos)
                {
                    csv += $"{info.PropertyName},{info.CustomerName},{info.PaymentOption}," +
                           $"{info.AnnualTax},{info.PaidAmount},{info.RemainingAmount},{info.Status}\n";
                }

                var fileName = $"ضريبة_النظافة_{_selectedYear}_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
                var documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
                var filePath = System.IO.Path.Combine(documentsPath, fileName);

                System.IO.File.WriteAllText(filePath, csv, System.Text.Encoding.UTF8);

                MessageBox.Show($"تم تصدير البيانات بنجاح إلى:\n{filePath}", "نجح التصدير",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ShowPropertyDetails(CleaningTaxPropertyInfo info)
        {
            var details = $"تفاصيل المحل: {info.PropertyName}\n\n";
            details += $"المكتري: {info.CustomerName}\n";
            details += $"طريقة الدفع: {info.PaymentOption}\n";
            details += $"الضريبة السنوية: {info.AnnualTaxDisplay}\n";
            details += $"المبلغ المدفوع: {info.PaidAmountDisplay}\n";
            details += $"المبلغ المتبقي: {info.RemainingAmountDisplay}\n";
            details += $"الحالة: {info.StatusDisplay}\n";
            details += $"السنة: {_selectedYear}";

            MessageBox.Show(details, "تفاصيل المحل", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            ExportToExcel();
        }



        private async void ViewDetailsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Debug information
                System.Diagnostics.Debug.WriteLine($"ViewDetailsButton_Click called");

                if (sender is Button button)
                {
                    System.Diagnostics.Debug.WriteLine($"Button found, Tag type: {button.Tag?.GetType().Name ?? "null"}");

                    if (button.Tag is CleaningTaxPropertyInfo propertyInfo)
                    {
                        System.Diagnostics.Debug.WriteLine($"PropertyInfo found, PropertyId: {propertyInfo.PropertyId}");

                        if (propertyInfo.PropertyId <= 0)
                        {
                            MessageBox.Show("معرف المحل غير صحيح", "خطأ",
                                MessageBoxButton.OK, MessageBoxImage.Error);
                            return;
                        }

                        var property = await DatabaseService.Instance.GetPropertyByIdAsync(propertyInfo.PropertyId);
                        if (property == null)
                        {
                            MessageBox.Show($"لم يتم العثور على المحل بالمعرف: {propertyInfo.PropertyId}", "خطأ",
                                MessageBoxButton.OK, MessageBoxImage.Error);
                            return;
                        }

                        System.Diagnostics.Debug.WriteLine($"Property found: {property.Name}");

                        // Show modern property details window with selected year
                        var detailsWindow = new PropertyDetailsWindow(property, _selectedYear);

                        // Set owner only if this window is still open
                        try
                        {
                            if (this.IsLoaded)
                            {
                                detailsWindow.Owner = this;
                            }
                        }
                        catch (InvalidOperationException)
                        {
                            // Window is closed, don't set owner
                        }

                        detailsWindow.ShowDialog();

                        // Refresh data after closing details window
                        await LoadData();
                    }
                    else
                    {
                        MessageBox.Show($"خطأ في نوع البيانات المرفقة بالزر. النوع الفعلي: {button.Tag?.GetType().Name ?? "null"}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
                else
                {
                    MessageBox.Show("خطأ في تحديد الزر المضغوط", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "خطأ في عرض التفاصيل";
                var errorMessage = $"خطأ في عرض التفاصيل:\n\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $"\n\nتفاصيل إضافية: {ex.InnerException.Message}";
                }
                errorMessage += $"\n\nStack Trace:\n{ex.StackTrace}";

                MessageBox.Show(errorMessage, "خطأ تفصيلي",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void AddPaymentForPropertyButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is int propertyId)
                {
                    var property = await DatabaseService.Instance.GetPropertyByIdAsync(propertyId);
                    if (property == null)
                    {
                        MessageBox.Show("لم يتم العثور على المحل", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }

                    // Show simple payment dialog with property details
                    var dialog = new SimpleCleaningTaxPaymentDialog(property, _selectedYear);
                    dialog.Owner = this;
                    if (dialog.ShowDialog() == true)
                    {
                        // Force refresh data and update display
                        await LoadData();

                        // Ensure the DataGrid is refreshed
                        if (PropertiesDataGrid.ItemsSource != null)
                        {
                            PropertiesDataGrid.Items.Refresh();
                        }

                        StatusTextBlock.Text = $"تم إضافة دفعة ضريبة النظافة للمحل {property.Name} بنجاح للسنة {_selectedYear}";

                        // Show success message with details
                        MessageBox.Show($"تم إضافة دفعة ضريبة النظافة بنجاح\n\nالمحل: {property.Name}\nالسنة: {_selectedYear}\n\nتم تحديث البيانات تلقائياً",
                            "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "خطأ في إضافة دفعة ضريبة النظافة";
                MessageBox.Show($"خطأ في إضافة دفعة ضريبة النظافة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            base.OnClosed(e);
        }
    }

    // Data model for the grid
    public class CleaningTaxPropertyInfo
    {
        public int PropertyId { get; set; }
        public string PropertyName { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string PaymentOption { get; set; } = string.Empty;
        public decimal AnnualTax { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public string Status { get; set; } = string.Empty;
        public bool HasPayments { get; set; }

        public string AnnualTaxDisplay => $"{AnnualTax:N0} درهم";
        public string PaidAmountDisplay => $"{PaidAmount:N0} درهم";
        public string RemainingAmountDisplay => $"{RemainingAmount:N0} درهم";
        public string StatusDisplay => Status;
    }
}
