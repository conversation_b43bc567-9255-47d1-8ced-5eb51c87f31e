using System;
using System.ComponentModel.DataAnnotations;
using SimpleRentalApp.Utils;

namespace SimpleRentalApp.Models;

public class CleaningTaxReceipt
{
    public int Id { get; set; }
    
    [Required]
    [StringLength(50)]
    public string ReceiptNumber { get; set; } = string.Empty;
    
    [Required]
    public int PropertyId { get; set; }
    public Property Property { get; set; } = null!;

    public int? PaymentId { get; set; }
    public Payment? Payment { get; set; }
    
    [Required]
    public decimal Amount { get; set; }
    
    [Required]
    public DateTime PaymentDate { get; set; }
    
    [Required]
    public int TaxYear { get; set; }
    
    [StringLength(50)]
    public string PaymentMethod { get; set; } = "نقداً";
    
    [StringLength(500)]
    public string Notes { get; set; } = string.Empty;
    
    [StringLength(100)]
    public string CreatedBy { get; set; } = "النظام";
    
    public DateTime CreatedDate { get; set; } = DateTime.Now;
    
    public bool IsPrinted { get; set; } = false;
    
    public DateTime? PrintedDate { get; set; }

    // Display properties
    public string PaymentDateDisplay => PaymentDate.ToString("dd/MM/yyyy");
    
    public string AmountDisplay => $"{Amount:N0} درهم";
    
    public string AmountInWords => NumberToArabicWords.ConvertCurrencyToWords(Amount);
    
    public string CustomerName => Property?.Customer?.FullName ?? "غير محدد";
    
    public string PropertyName => Property?.Name ?? "غير محدد";
    
    public string PropertyAddress => Property?.Address ?? "غير محدد";


}
