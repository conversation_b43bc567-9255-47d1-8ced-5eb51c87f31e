<Window x:Class="RentalManagement.Views.CustomerDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="{Binding Title}" 
        Height="500" Width="450"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Title -->
        <TextBlock Grid.Row="0"
                   Text="{Binding Title}"
                   Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,24"/>

        <!-- Form Fields -->
        <StackPanel Grid.Row="1">
            <!-- Full Name -->
            <TextBox Text="{Binding FullName, UpdateSourceTrigger=PropertyChanged}"
                     materialDesign:HintAssist.Hint="الاسم الكامل *"
                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                     materialDesign:ValidationAssist.UsePopup="True"
                     Margin="0,0,0,16"/>

            <!-- National ID -->
            <TextBox Text="{Binding NationalId, UpdateSourceTrigger=PropertyChanged}"
                     materialDesign:HintAssist.Hint="رقم البطاقة الوطنية *"
                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                     materialDesign:ValidationAssist.UsePopup="True"
                     Margin="0,0,0,16"/>

            <!-- Phone Number -->
            <TextBox Text="{Binding PhoneNumber, UpdateSourceTrigger=PropertyChanged}"
                     materialDesign:HintAssist.Hint="رقم الهاتف *"
                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                     materialDesign:ValidationAssist.UsePopup="True"
                     Margin="0,0,0,16"/>

            <!-- Address -->
            <TextBox Text="{Binding Address, UpdateSourceTrigger=PropertyChanged}"
                     materialDesign:HintAssist.Hint="العنوان"
                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                     AcceptsReturn="True"
                     TextWrapping="Wrap"
                     MinLines="3"
                     MaxLines="5"
                     VerticalScrollBarVisibility="Auto"/>
        </StackPanel>

        <!-- Error Message -->
        <TextBlock Grid.Row="2"
                   Text="{Binding ErrorMessage}"
                   Foreground="{StaticResource ErrorBrush}"
                   TextWrapping="Wrap"
                   Margin="0,16,0,0"
                   Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"/>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="3"
                    Orientation="Horizontal"
                    HorizontalAlignment="Left"
                    Margin="0,24,0,0">
            <Button Content="حفظ"
                    Command="{Binding SaveCommand}"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    IsDefault="True"
                    Margin="0,0,16,0"
                    Width="100"/>
            
            <Button Content="إلغاء"
                    Command="{Binding CancelCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    IsCancel="True"
                    Width="100"/>
        </StackPanel>
    </Grid>
</Window>
