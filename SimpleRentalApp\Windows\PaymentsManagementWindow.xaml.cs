using Microsoft.EntityFrameworkCore;
using SimpleRentalApp.Data;
using SimpleRentalApp.Models;
using SimpleRentalApp.Services;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;
using System.Windows.Media;

namespace SimpleRentalApp.Windows
{
    public partial class PaymentsManagementWindow : Window
    {
        private ObservableCollection<RentReceiptDisplayInfo> _allReceipts;
        private CollectionViewSource _receiptsViewSource;
        private ICollectionView _receiptsView;
        private List<Property> _properties;
        private List<Customer> _customers;
        private Property? _selectedProperty;
        private Customer? _selectedCustomer;

        // Year Configuration - Dynamic year selection
        private const int DEFAULT_START_YEAR = 2015;           // سنة البداية الافتراضية
        private const int DEFAULT_FUTURE_YEARS = 5;            // عدد السنوات المستقبلية
        private const bool DEFAULT_DESCENDING_ORDER = true;    // ترتيب تنازلي (الأحدث أولاً)

        private static int StartYear => DEFAULT_START_YEAR;
        private static int FutureYears => DEFAULT_FUTURE_YEARS;
        private static bool DescendingOrder => DEFAULT_DESCENDING_ORDER;

        public PaymentsManagementWindow()
        {
            InitializeComponent();
            InitializeData();
            LoadPropertiesAsync();
        }

        private void InitializeData()
        {
            _allReceipts = new ObservableCollection<RentReceiptDisplayInfo>();
            _receiptsViewSource = new CollectionViewSource();
            _receiptsViewSource.Source = _allReceipts;
            _receiptsView = _receiptsViewSource.View;
            ReceiptsDataGrid.ItemsSource = _receiptsView;

            // Initialize filter ComboBoxes
            InitializeFilterComboBoxes();
        }

        private void InitializeFilterComboBoxes()
        {
            // Month filter
            MonthFilterComboBox.Items.Add(new ComboBoxItem { Content = "جميع الشهور", Tag = 0 });
            for (int i = 1; i <= 12; i++)
            {
                var monthName = new DateTime(2024, i, 1).ToString("MMMM");
                MonthFilterComboBox.Items.Add(new ComboBoxItem { Content = monthName, Tag = i });
            }
            MonthFilterComboBox.SelectedIndex = 0;

            // Year filter - Dynamic year generation
            InitializeYearFilter();
        }

        /// <summary>
        /// Initialize year filter with dynamic year range
        /// </summary>
        private void InitializeYearFilter()
        {
            try
            {
                YearFilterComboBox.Items.Clear();
                YearFilterComboBox.Items.Add(new ComboBoxItem { Content = "جميع السنوات", Tag = 0 });

                // Generate dynamic years list using configuration
                var years = GenerateYearsList(StartYear, FutureYears, DescendingOrder);

                foreach (var year in years)
                {
                    YearFilterComboBox.Items.Add(new ComboBoxItem { Content = year.ToString(), Tag = year });
                }

                // Set default selection to current year
                var currentYear = DateTime.Now.Year;
                var currentYearIndex = years.IndexOf(currentYear);

                if (currentYearIndex >= 0)
                {
                    // +1 because of "جميع السنوات" item at index 0
                    YearFilterComboBox.SelectedIndex = currentYearIndex + 1;
                }
                else
                {
                    // If current year not found, select "جميع السنوات"
                    YearFilterComboBox.SelectedIndex = 0;
                }

                System.Diagnostics.Debug.WriteLine($"PaymentsManagement - Generated years: {string.Join(", ", years)} (Total: {years.Count})");
                System.Diagnostics.Debug.WriteLine($"PaymentsManagement - Selected year index: {YearFilterComboBox.SelectedIndex}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in InitializeYearFilter: {ex.Message}");
                // Fallback to simple year list
                YearFilterComboBox.Items.Clear();
                YearFilterComboBox.Items.Add(new ComboBoxItem { Content = "جميع السنوات", Tag = 0 });

                var currentYear = DateTime.Now.Year;
                var fallbackYears = new List<int> { currentYear + 1, currentYear, currentYear - 1, currentYear - 2 };

                foreach (var year in fallbackYears)
                {
                    YearFilterComboBox.Items.Add(new ComboBoxItem { Content = year.ToString(), Tag = year });
                }

                YearFilterComboBox.SelectedIndex = 2; // Current year in fallback list
            }
        }

        /// <summary>
        /// Generates a dynamic list of years from start year to current year + future years
        /// This method creates a future-proof year list that automatically adapts to the current year
        /// </summary>
        /// <param name="startYear">Starting year (e.g., 2015 - when the system started)</param>
        /// <param name="futureYears">Number of future years to include (e.g., 5 years ahead)</param>
        /// <param name="descending">Whether to sort in descending order (true = newest first, better UX)</param>
        /// <returns>List of years ready for ComboBox binding</returns>
        private List<int> GenerateYearsList(int startYear = 2015, int futureYears = 5, bool descending = true)
        {
            try
            {
                var currentYear = DateTime.Now.Year;
                var endYear = currentYear + futureYears;
                var years = new List<int>();

                // Validate input parameters
                if (startYear > currentYear + futureYears)
                {
                    System.Diagnostics.Debug.WriteLine($"Warning: Start year {startYear} is beyond end year {endYear}");
                    startYear = currentYear - 5; // Fallback to reasonable start
                }

                if (futureYears < 0)
                {
                    System.Diagnostics.Debug.WriteLine($"Warning: Future years {futureYears} is negative, using 0");
                    futureYears = 0;
                }

                // Generate years from start to end
                for (int year = startYear; year <= endYear; year++)
                {
                    years.Add(year);
                }

                // Sort based on preference (descending by default for better UX)
                if (descending)
                {
                    years.Sort((x, y) => y.CompareTo(x)); // Descending order (2030, 2029, 2028, ...)
                }
                else
                {
                    years.Sort(); // Ascending order (2015, 2016, 2017, ...)
                }

                return years;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error generating years list: {ex.Message}");
                // Return minimal fallback list centered around current year
                var currentYear = DateTime.Now.Year;
                var fallbackYears = new List<int> { currentYear + 1, currentYear, currentYear - 1, currentYear - 2 };
                return fallbackYears;
            }
        }

        private async Task LoadPropertiesAsync()
        {
            try
            {
                Mouse.OverrideCursor = Cursors.Wait;

                // Load properties and customers
                _properties = await DatabaseService.Instance.GetPropertiesAsync();
                _customers = await DatabaseService.Instance.GetCustomersAsync();

                // Populate property selection ComboBox
                PropertySelectionComboBox.Items.Clear();
                PropertySelectionComboBox.Items.Add(new ComboBoxItem { Content = "اختر محل...", Tag = null });

                foreach (var property in _properties.OrderBy(p => p.Name))
                {
                    PropertySelectionComboBox.Items.Add(new ComboBoxItem
                    {
                        Content = $"{property.Name} - {property.Address}",
                        Tag = property
                    });
                }
                PropertySelectionComboBox.SelectedIndex = 0;

                UpdateSubtitle();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المحلات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                Mouse.OverrideCursor = null;
            }
        }

        private async Task LoadReceiptsForPropertyAsync(Property property)
        {
            try
            {
                Mouse.OverrideCursor = Cursors.Wait;

                // Get active contract for this property
                var activeContract = await DatabaseService.Instance.GetActiveContractForPropertyAsync(property.Id);
                if (activeContract == null)
                {
                    _allReceipts.Clear();
                    UpdateStatistics();
                    UpdateReceiptCount();
                    UpdateButtonStates();

                    MessageBox.Show("لا يوجد عقد نشط لهذا المحل. يرجى إنشاء عقد أولاً لإدارة التوصيلات.", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // Get all rent receipts for this contract
                var receipts = await DatabaseService.Instance.GetContractReceiptsAsync(activeContract.Id);

                // Convert to display format
                _allReceipts.Clear();
                foreach (var receipt in receipts)
                {
                    var displayInfo = new RentReceiptDisplayInfo
                    {
                        Receipt = receipt,
                        ReceiptNumber = receipt.ReceiptNumber,
                        PaymentDateDisplay = receipt.PaymentDate.ToString("yyyy/MM/dd"),
                        AmountDisplay = $"{receipt.Amount:N0} درهم",
                        PaymentPeriod = receipt.PaymentPeriod,
                        Notes = receipt.Notes ?? "",
                        PrintStatusDisplay = receipt.IsPrinted ? "✅ مطبوع" : "⏳ غير مطبوع"
                    };

                    _allReceipts.Add(displayInfo);
                }

                // Update statistics and UI
                UpdateStatistics();
                UpdateReceiptCount();
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل التوصيلات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                Mouse.OverrideCursor = null;
            }
        }

        private void UpdateStatistics()
        {
            if (_selectedProperty == null)
            {
                StatsGrid.Children.Clear();
                return;
            }

            var totalReceipts = _allReceipts.Count;
            var printedReceipts = _allReceipts.Count(r => r.Receipt.IsPrinted);
            var unprintedReceipts = totalReceipts - printedReceipts;
            var totalRevenue = _allReceipts.Sum(r => r.Receipt.Amount);

            StatsGrid.Children.Clear();

            var cards = new[]
            {
                CreateStatCard("💰", "إجمالي الإيرادات", $"{totalRevenue:N0} درهم", "#4CAF50", 0),
                CreateStatCard("📄", "إجمالي التوصيلات", totalReceipts.ToString(), "#2196F3", 1),
                CreateStatCard("🖨️", "مطبوعة", printedReceipts.ToString(), "#4CAF50", 2),
                CreateStatCard("⏳", "غير مطبوعة", unprintedReceipts.ToString(), "#FF9800", 3)
            };

            foreach (var card in cards)
            {
                StatsGrid.Children.Add(card);
            }
        }

        private void UpdateReceiptCount()
        {
            var count = _receiptsView?.Cast<RentReceiptDisplayInfo>().Count() ?? 0;
            ReceiptCountText.Text = $"{count} توصيل";
        }

        private void UpdateButtonStates()
        {
            var hasSelection = ReceiptsDataGrid.SelectedItem != null;
            var hasReceipts = _allReceipts.Count > 0;

            PreviewReceiptBtn.IsEnabled = hasSelection;
            PrintReceiptBtn.IsEnabled = hasSelection;
            EditReceiptBtn.IsEnabled = hasSelection;
            DuplicateReceiptBtn.IsEnabled = hasSelection;
            DeleteReceiptBtn.IsEnabled = hasSelection;
            PrintAllBtn.IsEnabled = hasReceipts;

            if (hasSelection && ReceiptsDataGrid.SelectedItem is RentReceiptDisplayInfo selected)
            {
                SelectionInfoText.Text = $"محدد: {selected.ReceiptNumber} - {selected.AmountDisplay}";
            }
            else
            {
                SelectionInfoText.Text = hasReceipts ? "اختر توصيل للقيام بالعمليات" : "لا توجد توصيلات";
            }
        }

        private Border CreateStatCard(string icon, string title, string value, string color, int column)
        {
            var border = new Border
            {
                Background = Brushes.White,
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(20),
                Margin = new Thickness(column == 0 ? 0 : 10, 0, 0, 0),
                BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E0E0E0")),
                BorderThickness = new Thickness(1)
            };

            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            // Icon
            var iconBorder = new Border
            {
                Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color)),
                CornerRadius = new CornerRadius(25),
                Width = 50,
                Height = 50,
                Margin = new Thickness(0, 0, 15, 0)
            };

            var iconText = new TextBlock
            {
                Text = icon,
                FontSize = 20,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };
            iconBorder.Child = iconText;
            Grid.SetColumn(iconBorder, 0);
            grid.Children.Add(iconBorder);

            // Content
            var contentPanel = new StackPanel { VerticalAlignment = VerticalAlignment.Center };
            var valueText = new TextBlock
            {
                Text = value,
                FontSize = 24,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#333"))
            };
            contentPanel.Children.Add(valueText);

            var titleText = new TextBlock
            {
                Text = title,
                FontSize = 12,
                Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#666"))
            };
            contentPanel.Children.Add(titleText);

            Grid.SetColumn(contentPanel, 1);
            grid.Children.Add(contentPanel);

            border.Child = grid;
            Grid.SetColumn(border, column);
            return border;
        }

        private void UpdateSubtitle()
        {
            if (_selectedProperty == null)
            {
                var currentYear = DateTime.Now.Year;
                var yearRange = $"{StartYear}-{currentYear + FutureYears}";
                SubtitleText.Text = $"اختر محل لعرض وإدارة توصيلات الكراء الخاصة به (النطاق الزمني المتاح: {yearRange})";
                return;
            }

            var totalCount = _allReceipts.Count;
            var filteredCount = _receiptsView?.Cast<RentReceiptDisplayInfo>().Count() ?? 0;

            // Get current filter info
            var filterInfo = GetCurrentFilterInfo();

            if (totalCount == filteredCount)
            {
                SubtitleText.Text = $"إدارة توصيلات الكراء للمحل: {_selectedProperty.Name} - إجمالي {totalCount} توصيل{filterInfo}";
            }
            else
            {
                SubtitleText.Text = $"المحل: {_selectedProperty.Name} - عرض {filteredCount} من أصل {totalCount} توصيل{filterInfo}";
            }
        }

        /// <summary>
        /// Get current filter information for display
        /// </summary>
        private string GetCurrentFilterInfo()
        {
            var filterParts = new List<string>();

            // Year filter
            if (YearFilterComboBox.SelectedItem is ComboBoxItem yearItem && yearItem.Tag is int year && year > 0)
            {
                filterParts.Add($"السنة: {year}");
            }

            // Month filter
            if (MonthFilterComboBox.SelectedItem is ComboBoxItem monthItem && monthItem.Tag is int month && month > 0)
            {
                var monthName = new DateTime(2024, month, 1).ToString("MMMM");
                filterParts.Add($"الشهر: {monthName}");
            }

            // Search filter
            if (!string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                filterParts.Add($"البحث: '{SearchTextBox.Text}'");
            }

            return filterParts.Count > 0 ? $" (مُصفى حسب: {string.Join(", ", filterParts)})" : "";
        }

        // Event Handlers
        private async void RefreshBtn_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedProperty != null)
            {
                await LoadReceiptsForPropertyAsync(_selectedProperty);
            }
            else
            {
                await LoadPropertiesAsync();
            }
        }

        private async void PropertySelectionComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (PropertySelectionComboBox.SelectedItem is ComboBoxItem item && item.Tag is Property property)
            {
                _selectedProperty = property;
                _selectedCustomer = property.CustomerId.HasValue ?
                    _customers.FirstOrDefault(c => c.Id == property.CustomerId.Value) : null;

                // Update property info panel
                PropertyInfoPanel.Visibility = Visibility.Visible;
                CustomerNameText.Text = _selectedCustomer?.FullName ?? "غير محدد";
                MonthlyRentText.Text = $"{property.MonthlyRent:N0} درهم";
                PhoneNumberText.Text = _selectedCustomer?.Phone ?? "غير محدد";

                // Enable add receipt button
                AddReceiptBtn.IsEnabled = true;

                // Load receipts for this property
                await LoadReceiptsForPropertyAsync(property);
            }
            else
            {
                _selectedProperty = null;
                _selectedCustomer = null;
                PropertyInfoPanel.Visibility = Visibility.Collapsed;
                AddReceiptBtn.IsEnabled = false;

                _allReceipts.Clear();
                UpdateStatistics();
                UpdateSubtitle();
                UpdateButtonStates();
            }
        }

        private void ReceiptsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdateButtonStates();
        }

        private void ReceiptsDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (ReceiptsDataGrid.SelectedItem != null)
            {
                PreviewReceiptBtn_Click(sender, e);
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void MonthFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void YearFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void ClearFiltersBtn_Click(object sender, RoutedEventArgs e)
        {
            SearchTextBox.Text = "";
            MonthFilterComboBox.SelectedIndex = 0;

            // Reset year filter to current year (dynamic calculation)
            var currentYear = DateTime.Now.Year;
            var currentYearIndex = -1;

            // Find current year in the ComboBox items
            for (int i = 1; i < YearFilterComboBox.Items.Count; i++) // Start from 1 to skip "جميع السنوات"
            {
                if (YearFilterComboBox.Items[i] is ComboBoxItem item &&
                    item.Tag is int year && year == currentYear)
                {
                    currentYearIndex = i;
                    break;
                }
            }

            // Set selection to current year if found, otherwise "جميع السنوات"
            YearFilterComboBox.SelectedIndex = currentYearIndex >= 0 ? currentYearIndex : 0;

            ApplyFilters();
        }

        /// <summary>
        /// Refresh year filter to include any new years that might have been added
        /// </summary>
        public void RefreshYearFilter()
        {
            try
            {
                var currentSelection = YearFilterComboBox.SelectedItem;
                var currentTag = currentSelection is ComboBoxItem item ? item.Tag : null;

                InitializeYearFilter();

                // Try to restore previous selection
                if (currentTag != null)
                {
                    for (int i = 0; i < YearFilterComboBox.Items.Count; i++)
                    {
                        if (YearFilterComboBox.Items[i] is ComboBoxItem comboItem &&
                            Equals(comboItem.Tag, currentTag))
                        {
                            YearFilterComboBox.SelectedIndex = i;
                            return;
                        }
                    }
                }

                // If previous selection not found, default to current year
                var currentYear = DateTime.Now.Year;
                for (int i = 1; i < YearFilterComboBox.Items.Count; i++)
                {
                    if (YearFilterComboBox.Items[i] is ComboBoxItem comboItem &&
                        comboItem.Tag is int year && year == currentYear)
                    {
                        YearFilterComboBox.SelectedIndex = i;
                        return;
                    }
                }

                // Fallback to "جميع السنوات"
                YearFilterComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error refreshing year filter: {ex.Message}");
            }
        }

        private void ApplyFilters()
        {
            if (_receiptsView == null) return;

            _receiptsView.Filter = item =>
            {
                if (item is not RentReceiptDisplayInfo receipt) return false;

                // Search filter
                var searchText = SearchTextBox.Text?.ToLower() ?? "";
                if (!string.IsNullOrEmpty(searchText))
                {
                    var searchMatch = receipt.ReceiptNumber.ToLower().Contains(searchText) ||
                                    receipt.PaymentPeriod.ToLower().Contains(searchText) ||
                                    receipt.Notes.ToLower().Contains(searchText) ||
                                    receipt.AmountDisplay.ToLower().Contains(searchText);

                    if (!searchMatch) return false;
                }

                // Month filter
                if (MonthFilterComboBox.SelectedItem is ComboBoxItem monthItem &&
                    monthItem.Tag is int month && month > 0)
                {
                    if (receipt.Receipt.PaymentDate.Month != month) return false;
                }

                // Year filter
                if (YearFilterComboBox.SelectedItem is ComboBoxItem yearItem &&
                    yearItem.Tag is int year && year > 0)
                {
                    if (receipt.Receipt.PaymentDate.Year != year) return false;
                }

                return true;
            };

            UpdateSubtitle();
            UpdateReceiptCount();
        }

        // Action Button Event Handlers
        private async void AddReceiptBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_selectedProperty == null)
                {
                    MessageBox.Show("يرجى اختيار محل أولاً", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var dialog = new Windows.AddRentReceiptDialog(_selectedProperty, _selectedCustomer);
                dialog.Owner = this;
                if (dialog.ShowDialog() == true)
                {
                    // Refresh receipts after adding
                    await LoadReceiptsForPropertyAsync(_selectedProperty);

                    // Refresh year filter to include any new years
                    RefreshYearFilter();

                    MessageBox.Show("تم إنشاء التوصيل بنجاح! ✅", "نجح",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة توصيل جديد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PreviewReceiptBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (ReceiptsDataGrid.SelectedItem is not RentReceiptDisplayInfo selectedReceipt)
                {
                    MessageBox.Show("يرجى اختيار توصيل للمعاينة", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var previewWindow = new Windows.ReceiptPreviewWindow(selectedReceipt.Receipt, _selectedProperty, _selectedCustomer);
                previewWindow.Owner = this;
                previewWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معاينة التوصيل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void PrintReceiptBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (ReceiptsDataGrid.SelectedItem is not RentReceiptDisplayInfo selectedReceipt)
                {
                    MessageBox.Show("يرجى اختيار توصيل للطباعة", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                Mouse.OverrideCursor = Cursors.Wait;

                var printService = ModernPrintService.Instance;
                var success = printService.PrintReceiptWithPreview(selectedReceipt.Receipt, _selectedProperty, _selectedCustomer);

                if (success)
                {
                    // Mark as printed
                    selectedReceipt.Receipt.IsPrinted = true;
                    await DatabaseService.Instance.UpdateRentReceiptAsync(selectedReceipt.Receipt);

                    // Refresh display
                    selectedReceipt.PrintStatusDisplay = "✅ مطبوع";
                    await LoadReceiptsForPropertyAsync(_selectedProperty!);

                    MessageBox.Show("تم طباعة التوصيل بنجاح! ✅", "نجحت الطباعة",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التوصيل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                Mouse.OverrideCursor = null;
            }
        }

        private async void EditReceiptBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (ReceiptsDataGrid.SelectedItem is not RentReceiptDisplayInfo selectedReceipt)
                {
                    MessageBox.Show("يرجى اختيار توصيل للتعديل", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var dialog = new Windows.EditRentReceiptDialog(selectedReceipt.Receipt, _selectedProperty!, _selectedCustomer);
                dialog.Owner = this;
                if (dialog.ShowDialog() == true)
                {
                    await LoadReceiptsForPropertyAsync(_selectedProperty!);
                    MessageBox.Show("تم تحديث التوصيل بنجاح! ✅", "نجح",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل التوصيل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void DuplicateReceiptBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (ReceiptsDataGrid.SelectedItem is not RentReceiptDisplayInfo selectedReceipt)
                {
                    MessageBox.Show("يرجى اختيار توصيل للنسخ", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var result = MessageBox.Show(
                    $"هل تريد نسخ التوصيل {selectedReceipt.ReceiptNumber} وإنشاء توصيل جديد؟\n\n" +
                    "سيتم نسخ جميع البيانات عدا رقم التوصيل والتاريخ.",
                    "تأكيد النسخ",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    var dialog = new Windows.AddRentReceiptDialog(_selectedProperty!, _selectedCustomer, selectedReceipt.Receipt);
                    dialog.Owner = this;
                    if (dialog.ShowDialog() == true)
                    {
                        await LoadReceiptsForPropertyAsync(_selectedProperty!);
                        MessageBox.Show("تم نسخ التوصيل بنجاح! ✅", "نجح",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في نسخ التوصيل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void DeleteReceiptBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (ReceiptsDataGrid.SelectedItem is not RentReceiptDisplayInfo selectedReceipt)
                {
                    MessageBox.Show("يرجى اختيار توصيل للحذف", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف التوصيل {selectedReceipt.ReceiptNumber}؟\n\n" +
                    $"المبلغ: {selectedReceipt.AmountDisplay}\n" +
                    $"الشهر: {selectedReceipt.PaymentPeriod}\n" +
                    $"التاريخ: {selectedReceipt.PaymentDateDisplay}\n\n" +
                    "⚠️ تحذير: لا يمكن التراجع عن هذا الإجراء!",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    Mouse.OverrideCursor = Cursors.Wait;

                    await DatabaseService.Instance.DeleteRentReceiptAsync(selectedReceipt.Receipt.Id);
                    await LoadReceiptsForPropertyAsync(_selectedProperty!);

                    MessageBox.Show("تم حذف التوصيل بنجاح! ✅", "نجح",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف التوصيل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                Mouse.OverrideCursor = null;
            }
        }

        private async void PrintAllBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_allReceipts.Count == 0)
                {
                    MessageBox.Show("لا توجد توصيلات للطباعة", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var result = MessageBox.Show(
                    $"هل تريد طباعة جميع التوصيلات ({_allReceipts.Count} توصيل)؟",
                    "تأكيد الطباعة",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    Mouse.OverrideCursor = Cursors.Wait;

                    var printService = ModernPrintService.Instance;
                    var printedCount = 0;

                    foreach (var receiptInfo in _allReceipts)
                    {
                        try
                        {
                            var success = printService.PrintReceiptWithPreview(receiptInfo.Receipt, _selectedProperty, _selectedCustomer);
                            if (success)
                            {
                                receiptInfo.Receipt.IsPrinted = true;
                                await DatabaseService.Instance.UpdateRentReceiptAsync(receiptInfo.Receipt);
                                printedCount++;
                            }
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"خطأ في طباعة التوصيل {receiptInfo.ReceiptNumber}: {ex.Message}",
                                "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                        }
                    }

                    await LoadReceiptsForPropertyAsync(_selectedProperty!);
                    MessageBox.Show($"تم طباعة {printedCount} توصيل بنجاح! ✅", "نجحت الطباعة",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التوصيلات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                Mouse.OverrideCursor = null;
            }
        }

        private async void ExportBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_selectedProperty == null)
                {
                    MessageBox.Show("يرجى اختيار محل أولاً", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (_allReceipts.Count == 0)
                {
                    MessageBox.Show("لا توجد توصيلات للتصدير", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                Mouse.OverrideCursor = Cursors.Wait;

                var exportService = new Services.DataExportImportService();
                var filteredReceipts = _receiptsView?.Cast<RentReceiptDisplayInfo>().Select(r => r.Receipt).ToList() ?? new List<RentReceipt>();

                var result = await exportService.ExportReceiptsToExcelAsync(filteredReceipts, _selectedProperty);

                if (result.Success)
                {
                    var openResult = MessageBox.Show($"تم تصدير التوصيلات بنجاح إلى:\n{result.FilePath}\n\nهل تريد فتح الملف؟",
                        "نجح التصدير", MessageBoxButton.YesNo, MessageBoxImage.Information);

                    if (openResult == MessageBoxResult.Yes)
                    {
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = result.FilePath,
                            UseShellExecute = true
                        });
                    }
                }
                else
                {
                    MessageBox.Show($"فشل في تصدير التوصيلات: {result.ErrorMessage}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير التوصيلات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                Mouse.OverrideCursor = null;
            }
        }

    }

    // Helper class for displaying rent receipts in the DataGrid
    public class RentReceiptDisplayInfo
    {
        public RentReceipt Receipt { get; set; } = new();
        public string ReceiptNumber { get; set; } = string.Empty;
        public string PaymentDateDisplay { get; set; } = string.Empty;
        public string AmountDisplay { get; set; } = string.Empty;
        public string PaymentPeriod { get; set; } = string.Empty;
        public string Notes { get; set; } = string.Empty;
        public string PrintStatusDisplay { get; set; } = string.Empty;
    }
}
