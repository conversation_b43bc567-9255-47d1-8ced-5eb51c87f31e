using System.Windows;
using System.Windows.Controls;
using SimpleRentalApp.Models;
using SimpleRentalApp.Services;

namespace SimpleRentalApp.Windows;

public partial class PropertyDialog : Window
{
    public Property? Property { get; private set; }
    private readonly Property? _existingProperty;
    private List<Customer> _customers = new();

    public PropertyDialog(Property? property = null)
    {
        InitializeComponent();
        _existingProperty = property;
        
        if (property != null)
        {
            Title = "تعديل بيانات العقار";
        }
        else
        {
            Title = "إضافة عقار جديد";
        }

        LoadData();

        // Add event handler for rent amount change
        MonthlyRentTextBox.TextChanged += MonthlyRentTextBox_TextChanged;
    }

    private async void LoadData()
    {
        try
        {
            // Load customers
            _customers = await DatabaseService.Instance.GetCustomersAsync();
            CustomerComboBox.ItemsSource = _customers;

            // Add empty option for no customer
            var emptyCustomer = new Customer { Id = 0, FirstName = "لا يوجد", LastName = "كاري" };
            _customers.Insert(0, emptyCustomer);
            CustomerComboBox.ItemsSource = null;
            CustomerComboBox.ItemsSource = _customers;

            if (_existingProperty != null)
            {
                LoadPropertyData(_existingProperty);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void LoadPropertyData(Property property)
    {
        NameTextBox.Text = property.Name;
        AddressTextBox.Text = property.Address;
        MonthlyRentTextBox.Text = property.MonthlyRent.ToString();
        RoomsTextBox.Text = property.Rooms?.ToString() ?? "";
        BathroomsTextBox.Text = property.Bathrooms?.ToString() ?? "";
        AreaTextBox.Text = property.Area?.ToString() ?? "";
        DescriptionTextBox.Text = property.Description;

        // Set type
        foreach (ComboBoxItem item in TypeComboBox.Items)
        {
            if (item.Tag.ToString() == property.Type.ToString())
            {
                TypeComboBox.SelectedItem = item;
                break;
            }
        }

        // Set status
        foreach (ComboBoxItem item in StatusComboBox.Items)
        {
            if (item.Tag.ToString() == property.Status.ToString())
            {
                StatusComboBox.SelectedItem = item;
                break;
            }
        }

        // Set cleaning tax payment option
        foreach (ComboBoxItem item in CleaningTaxPaymentComboBox.Items)
        {
            if (item.Tag.ToString() == property.CleaningTaxPaymentOption.ToString())
            {
                CleaningTaxPaymentComboBox.SelectedItem = item;
                break;
            }
        }

        // Set customer
        if (property.CustomerId.HasValue)
        {
            CustomerComboBox.SelectedValue = property.CustomerId.Value;
        }
        else
        {
            CustomerComboBox.SelectedValue = 0;
        }

        UpdateCleaningTaxInfo();
    }

    private async void SaveButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // Validation
            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم العقار", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                NameTextBox.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(AddressTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال عنوان العقار", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                AddressTextBox.Focus();
                return;
            }

            if (TypeComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار نوع العقار", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                TypeComboBox.Focus();
                return;
            }

            if (!decimal.TryParse(MonthlyRentTextBox.Text, out decimal monthlyRent) || monthlyRent <= 0)
            {
                MessageBox.Show("يرجى إدخال مبلغ إيجار صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                MonthlyRentTextBox.Focus();
                return;
            }

            // Create or update property
            var property = _existingProperty ?? new Property();
            property.Name = NameTextBox.Text.Trim();
            property.Address = AddressTextBox.Text.Trim();
            property.MonthlyRent = monthlyRent;
            property.Description = DescriptionTextBox.Text.Trim();

            // Set cleaning tax payment option
            var selectedPaymentOption = ((ComboBoxItem)CleaningTaxPaymentComboBox.SelectedItem).Tag.ToString();
            property.CleaningTaxPaymentOption = decimal.Parse(selectedPaymentOption!);

            // Set type
            var selectedType = ((ComboBoxItem)TypeComboBox.SelectedItem).Tag.ToString();
            property.Type = Enum.Parse<PropertyType>(selectedType!);

            // Set status
            var selectedStatus = ((ComboBoxItem)StatusComboBox.SelectedItem).Tag.ToString();
            property.Status = Enum.Parse<PropertyStatus>(selectedStatus!);

            // Set optional fields
            if (int.TryParse(RoomsTextBox.Text, out int rooms))
                property.Rooms = rooms;

            if (int.TryParse(BathroomsTextBox.Text, out int bathrooms))
                property.Bathrooms = bathrooms;

            if (decimal.TryParse(AreaTextBox.Text, out decimal area))
                property.Area = area;

            // Set customer
            if (CustomerComboBox.SelectedValue != null && (int)CustomerComboBox.SelectedValue > 0)
            {
                property.CustomerId = (int)CustomerComboBox.SelectedValue;
            }
            else
            {
                property.CustomerId = null;
            }

            if (_existingProperty == null)
            {
                property.CreatedDate = DateTime.Now;
            }

            // Save to database
            Property = await DatabaseService.Instance.SavePropertyAsync(property);

            MessageBox.Show("تم حفظ بيانات العقار بنجاح! ✅", "نجح الحفظ", 
                MessageBoxButton.OK, MessageBoxImage.Information);

            DialogResult = true;
            Close();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }



    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        DialogResult = false;
        Close();
    }

    private void MonthlyRentTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
    {
        UpdateCleaningTaxInfo();
    }

    private void CleaningTaxPaymentComboBox_SelectionChanged(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
    {
        UpdateCleaningTaxInfo();
    }

    private void UpdateCleaningTaxInfo()
    {
        if (CleaningTaxInfoTextBlock == null || MonthlyRentTextBox == null || CleaningTaxPaymentComboBox == null)
            return;

        if (decimal.TryParse(MonthlyRentTextBox.Text, out decimal monthlyRent) && monthlyRent > 0)
        {
            var annualCleaningTax = monthlyRent * 12 * 0.105m;

            if (CleaningTaxPaymentComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                var paymentOption = decimal.Parse(selectedItem.Tag.ToString()!);
                var cleaningTaxPerPayment = paymentOption switch
                {
                    1 => annualCleaningTax / 12,  // شهرياً
                    2 => annualCleaningTax / 2,   // نصف سنوي
                    3 => annualCleaningTax / 3,   // كل 4 أشهر
                    4 => annualCleaningTax / 4,   // ربع سنوي
                    12 => annualCleaningTax,      // سنوياً
                    _ => annualCleaningTax / 12
                };

                var paymentFrequency = paymentOption switch
                {
                    1 => "شهرياً (12 مرة)",
                    2 => "نصف سنوي (مرتين)",
                    3 => "كل 4 أشهر (3 مرات)",
                    4 => "ربع سنوي (4 مرات)",
                    12 => "سنوياً (مرة واحدة)",
                    _ => "شهرياً"
                };

                CleaningTaxInfoTextBlock.Text =
                    $"💰 الإيجار الشهري: {monthlyRent:N0} درهم\n" +
                    $"📊 الإيجار السنوي: {monthlyRent * 12:N0} درهم\n" +
                    $"🧹 ضريبة النظافة السنوية (10.5%): {annualCleaningTax:N0} درهم\n" +
                    $"📅 طريقة الدفع: {paymentFrequency}\n" +
                    $"💵 المبلغ لكل دفعة: {cleaningTaxPerPayment:N0} درهم";
            }
        }
        else
        {
            CleaningTaxInfoTextBlock.Text = "أدخل الإيجار الشهري لحساب ضريبة النظافة تلقائياً\n(10.5% من الإيجار السنوي)";
        }
    }
}
