using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;
using SimpleRentalApp.Services.CloudSync.Providers;
using SimpleRentalApp.Services.CloudSync.Security;
using SimpleRentalApp.Services.CloudSync.Performance;
using Newtonsoft.Json;
using Microsoft.EntityFrameworkCore;
using SimpleRentalApp.Data;
using SimpleRentalApp.Models;

namespace SimpleRentalApp.Services.CloudSync
{
    /// <summary>
    /// مدير المزامنة الرئيسي
    /// </summary>
    public class SyncManager : IDisposable
    {
        private readonly DatabaseService _databaseService;
        private ISyncService? _currentSyncService;
        private SyncConfiguration? _config;
        private System.Timers.Timer? _syncTimer;
        private readonly SemaphoreSlim _syncSemaphore = new(1, 1);
        private bool _isDisposed;
        private readonly EncryptionService _encryptionService;
        private readonly SyncOptimizer _syncOptimizer;
        private readonly PerformanceMonitor _performanceMonitor;

        public bool IsConnected => _currentSyncService?.IsConnected ?? false;
        public string? CurrentServiceName => _currentSyncService?.ServiceName;
        public DateTime? LastSyncTime => _currentSyncService?.LastSyncTime;
        public bool AutoSyncEnabled { get; private set; }

        // الأحداث
        public event EventHandler<ConnectionStatusChangedEventArgs>? ConnectionStatusChanged;
        public event EventHandler<SyncProgressEventArgs>? SyncProgress;
        public event EventHandler<SyncCompletedEventArgs>? SyncCompleted;
        public event EventHandler<SyncErrorEventArgs>? SyncError;
        public event EventHandler<ConflictDetectedEventArgs>? ConflictDetected;

        public SyncManager(DatabaseService databaseService)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            _encryptionService = new EncryptionService(EncryptionService.GenerateEncryptionKey());
            _syncOptimizer = new SyncOptimizer();
            _performanceMonitor = new PerformanceMonitor();

            // ربط أحداث محسن الأداء
            _syncOptimizer.BatchProcessed += OnBatchProcessed;
            _performanceMonitor.PerformanceAlert += OnPerformanceAlert;
        }

        /// <summary>
        /// تهيئة المزامنة
        /// </summary>
        public async Task<bool> InitializeAsync(SyncConfiguration config)
        {
            try
            {
                _config = config ?? throw new ArgumentNullException(nameof(config));

                // إنشاء خدمة المزامنة حسب النوع
                _currentSyncService = config.ServiceType.ToLower() switch
                {
                    "supabase" => new SupabaseSyncService(),
                    "firebase" => new FirebaseSyncService(),
                    "azuresql" => new AzureSqlSyncService(),
                    _ => throw new NotSupportedException($"نوع الخدمة غير مدعوم: {config.ServiceType}")
                };

                // ربط الأحداث
                _currentSyncService.ConnectionStatusChanged += OnConnectionStatusChanged;
                _currentSyncService.SyncProgress += OnSyncProgress;
                _currentSyncService.SyncCompleted += OnSyncCompleted;
                _currentSyncService.SyncError += OnSyncError;

                // تهيئة الخدمة
                var result = await _currentSyncService.InitializeAsync(config);

                if (result && config.AutoSync)
                {
                    StartAutoSync();
                }

                return result;
            }
            catch (Exception ex)
            {
                OnSyncError(new SyncErrorEventArgs
                {
                    Exception = ex,
                    ErrorMessage = $"فشل في تهيئة المزامنة: {ex.Message}"
                });
                return false;
            }
        }

        /// <summary>
        /// بدء المزامنة التلقائية
        /// </summary>
        public void StartAutoSync()
        {
            if (_config == null || _syncTimer != null) return;

            AutoSyncEnabled = true;
            _syncTimer = new System.Timers.Timer(_config.SyncIntervalMinutes * 60 * 1000); // تحويل إلى ميلي ثانية
            _syncTimer.Elapsed += async (s, e) => await PerformAutoSyncAsync();
            _syncTimer.AutoReset = true;
            _syncTimer.Start();
        }

        /// <summary>
        /// إيقاف المزامنة التلقائية
        /// </summary>
        public void StopAutoSync()
        {
            AutoSyncEnabled = false;
            _syncTimer?.Stop();
            _syncTimer?.Dispose();
            _syncTimer = null;
        }

        /// <summary>
        /// مزامنة يدوية
        /// </summary>
        public async Task<SyncResult> SyncNowAsync()
        {
            if (_currentSyncService == null)
            {
                return new SyncResult
                {
                    Success = false,
                    Message = "خدمة المزامنة غير مهيأة"
                };
            }

            await _syncSemaphore.WaitAsync();
            try
            {
                // مزامنة البيانات المحلية إلى السحابة
                var localChanges = await GetLocalChangesAsync();
                var uploadResult = await SyncLocalChangesToCloudAsync(localChanges);

                // مزامنة البيانات من السحابة
                var downloadResult = await _currentSyncService.SyncFromCloudAsync();

                // دمج النتائج
                var combinedResult = CombineSyncResults(uploadResult, downloadResult);

                // معالجة التعارضات إذا وجدت
                if (combinedResult.Conflicts.Any())
                {
                    var conflictResult = await HandleConflictsAsync(combinedResult.Conflicts);
                    combinedResult.Message += $" | تم حل {conflictResult.ResolvedConflicts.Count} تعارض";
                }

                // تطبيق التغييرات على قاعدة البيانات المحلية
                await ApplyRemoteChangesAsync(downloadResult.SyncedData);

                return combinedResult;
            }
            finally
            {
                _syncSemaphore.Release();
            }
        }

        /// <summary>
        /// مزامنة كيان واحد
        /// </summary>
        public async Task<bool> SyncEntityAsync<T>(T entity, SyncOperation operation) where T : class
        {
            if (_currentSyncService == null || entity == null) return false;

            using var operationTracker = _performanceMonitor.StartOperation("SyncEntity", typeof(T).Name);

            try
            {
                var syncData = CreateSyncData(entity, operation);

                // استخدام محسن الأداء للمزامنة
                var queued = await _syncOptimizer.EnqueueSyncDataAsync(syncData);
                if (!queued)
                {
                    // مزامنة مباشرة إذا فشل في الإضافة للطابور
                    var result = await _currentSyncService.SyncToCloudAsync(syncData);
                    return result.Success;
                }

                return true;
            }
            catch (Exception ex)
            {
                OnSyncError(new SyncErrorEventArgs
                {
                    Exception = ex,
                    ErrorMessage = $"فشل في مزامنة الكيان: {ex.Message}",
                    EntityType = typeof(T).Name
                });
                return false;
            }
        }

        /// <summary>
        /// مزامنة مجموعة كيانات
        /// </summary>
        public async Task<int> SyncEntitiesAsync<T>(IEnumerable<T> entities, SyncOperation operation) where T : class
        {
            if (_currentSyncService == null || entities == null) return 0;

            using var operationTracker = _performanceMonitor.StartOperation("SyncEntities", typeof(T).Name);

            try
            {
                var syncDataList = entities.Select(entity => CreateSyncData(entity, operation)).ToList();
                var queuedCount = await _syncOptimizer.EnqueueBatchAsync(syncDataList);

                _performanceMonitor.RecordMetric("BatchSize", syncDataList.Count, "items", "SyncEntities");
                _performanceMonitor.RecordMetric("QueuedItems", queuedCount, "items", "SyncEntities");

                return queuedCount;
            }
            catch (Exception ex)
            {
                OnSyncError(new SyncErrorEventArgs
                {
                    Exception = ex,
                    ErrorMessage = $"فشل في مزامنة الكيانات: {ex.Message}",
                    EntityType = typeof(T).Name
                });
                return 0;
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية
        /// </summary>
        public async Task<BackupResult> CreateBackupAsync()
        {
            if (_currentSyncService == null)
            {
                return new BackupResult
                {
                    Success = false,
                    Message = "خدمة المزامنة غير مهيأة"
                };
            }

            return await _currentSyncService.CreateFullBackupAsync();
        }

        /// <summary>
        /// استعادة من نسخة احتياطية
        /// </summary>
        public async Task<RestoreResult> RestoreFromBackupAsync(string backupId)
        {
            if (_currentSyncService == null)
            {
                return new RestoreResult
                {
                    Success = false,
                    Message = "خدمة المزامنة غير مهيأة"
                };
            }

            return await _currentSyncService.RestoreFromBackupAsync(backupId);
        }

        /// <summary>
        /// الحصول على قائمة النسخ الاحتياطية
        /// </summary>
        public async Task<List<BackupInfo>> GetBackupsAsync()
        {
            if (_currentSyncService == null) return new List<BackupInfo>();
            return await _currentSyncService.GetBackupsAsync();
        }

        /// <summary>
        /// اختبار الاتصال
        /// </summary>
        public async Task<bool> TestConnectionAsync()
        {
            if (_currentSyncService == null) return false;
            return await _currentSyncService.TestConnectionAsync();
        }

        /// <summary>
        /// قطع الاتصال
        /// </summary>
        public async Task DisconnectAsync()
        {
            StopAutoSync();

            if (_currentSyncService != null)
            {
                await _currentSyncService.DisconnectAsync();
                _currentSyncService = null;
            }
        }

        /// <summary>
        /// الحصول على إحصائيات الأداء
        /// </summary>
        public PerformanceStats GetPerformanceStats()
        {
            return _performanceMonitor.GetStats();
        }

        /// <summary>
        /// الحصول على تحليل الأداء
        /// </summary>
        public PerformanceAnalysis GetPerformanceAnalysis()
        {
            return _performanceMonitor.GetPerformanceAnalysis();
        }

        /// <summary>
        /// الحصول على إحصائيات محسن الأداء
        /// </summary>
        public SyncOptimizerStats GetOptimizerStats()
        {
            return _syncOptimizer.GetStats();
        }

        /// <summary>
        /// إعادة تعيين إحصائيات الأداء
        /// </summary>
        public void ResetPerformanceStats()
        {
            _performanceMonitor.Reset();
        }

        #region Private Methods

        /// <summary>
        /// تنفيذ المزامنة التلقائية
        /// </summary>
        private async Task PerformAutoSyncAsync()
        {
            try
            {
                if (!_syncSemaphore.Wait(100)) return; // تجنب التداخل

                await SyncNowAsync();
            }
            catch (Exception ex)
            {
                OnSyncError(new SyncErrorEventArgs
                {
                    Exception = ex,
                    ErrorMessage = $"خطأ في المزامنة التلقائية: {ex.Message}"
                });
            }
            finally
            {
                _syncSemaphore.Release();
            }
        }

        /// <summary>
        /// الحصول على التغييرات المحلية
        /// </summary>
        private async Task<List<SyncData>> GetLocalChangesAsync()
        {
            var changes = new List<SyncData>();

            try
            {
                using var context = _databaseService.CreateContext();

                // جلب التغييرات من جميع الجداول
                var lastSyncTime = _currentSyncService?.LastSyncTime ?? DateTime.MinValue;
                var customers = await context.Customers.Where(c => c.UpdatedDate > lastSyncTime).ToListAsync();
                var properties = await context.Properties.Where(p => p.UpdatedDate > lastSyncTime).ToListAsync();
                var contracts = await context.Contracts.Where(c => c.UpdatedDate > lastSyncTime).ToListAsync();
                var payments = await context.Payments.Where(p => p.UpdatedDate > lastSyncTime).ToListAsync();

                // تحويل إلى SyncData
                changes.AddRange(customers.Select(c => CreateSyncData(c, SyncOperation.Update)));
                changes.AddRange(properties.Select(p => CreateSyncData(p, SyncOperation.Update)));
                changes.AddRange(contracts.Select(c => CreateSyncData(c, SyncOperation.Update)));
                changes.AddRange(payments.Select(p => CreateSyncData(p, SyncOperation.Update)));
            }
            catch (Exception ex)
            {
                OnSyncError(new SyncErrorEventArgs
                {
                    Exception = ex,
                    ErrorMessage = $"فشل في جلب التغييرات المحلية: {ex.Message}"
                });
            }

            return changes;
        }

        /// <summary>
        /// مزامنة التغييرات المحلية إلى السحابة
        /// </summary>
        private async Task<SyncResult> SyncLocalChangesToCloudAsync(List<SyncData> localChanges)
        {
            var result = new SyncResult { TotalRecords = localChanges.Count };

            foreach (var change in localChanges)
            {
                try
                {
                    var syncResult = await _currentSyncService!.SyncToCloudAsync(change);
                    if (syncResult.Success)
                    {
                        result.SuccessfulRecords++;
                        result.SyncedData.Add(change);
                    }
                    else
                    {
                        result.FailedRecords++;
                    }
                }
                catch (Exception ex)
                {
                    result.FailedRecords++;
                    OnSyncError(new SyncErrorEventArgs
                    {
                        Exception = ex,
                        ErrorMessage = $"فشل في مزامنة {change.EntityType}: {ex.Message}",
                        EntityType = change.EntityType,
                        EntityId = change.EntityId
                    });
                }
            }

            result.Success = result.FailedRecords == 0;
            result.Message = $"تم رفع {result.SuccessfulRecords} من أصل {result.TotalRecords} عنصر";
            return result;
        }

        /// <summary>
        /// تطبيق التغييرات البعيدة على قاعدة البيانات المحلية
        /// </summary>
        private async Task ApplyRemoteChangesAsync(List<SyncData> remoteChanges)
        {
            try
            {
                using var context = _databaseService.CreateContext();

                foreach (var change in remoteChanges)
                {
                    try
                    {
                        await ApplySingleChangeAsync(context, change);
                    }
                    catch (Exception ex)
                    {
                        OnSyncError(new SyncErrorEventArgs
                        {
                            Exception = ex,
                            ErrorMessage = $"فشل في تطبيق التغيير {change.EntityType}: {ex.Message}",
                            EntityType = change.EntityType,
                            EntityId = change.EntityId
                        });
                    }
                }

                await context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                OnSyncError(new SyncErrorEventArgs
                {
                    Exception = ex,
                    ErrorMessage = $"فشل في تطبيق التغييرات البعيدة: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// تطبيق تغيير واحد
        /// </summary>
        private async Task ApplySingleChangeAsync(RentalDbContext context, SyncData change)
        {
            switch (change.EntityType.ToLower())
            {
                case "customer":
                    await ApplyCustomerChangeAsync(context, change);
                    break;
                case "property":
                    await ApplyPropertyChangeAsync(context, change);
                    break;
                case "contract":
                    await ApplyContractChangeAsync(context, change);
                    break;
                case "payment":
                    await ApplyPaymentChangeAsync(context, change);
                    break;
            }
        }

        /// <summary>
        /// تطبيق تغيير العميل
        /// </summary>
        private async Task ApplyCustomerChangeAsync(RentalDbContext context, SyncData change)
        {
            var customer = JsonConvert.DeserializeObject<Customer>(change.JsonData);
            if (customer == null) return;

            var existing = await context.Customers.FindAsync(customer.Id);

            switch (change.Operation)
            {
                case SyncOperation.Create:
                    if (existing == null)
                    {
                        context.Customers.Add(customer);
                    }
                    break;
                case SyncOperation.Update:
                    if (existing != null)
                    {
                        context.Entry(existing).CurrentValues.SetValues(customer);
                    }
                    else
                    {
                        context.Customers.Add(customer);
                    }
                    break;
                case SyncOperation.Delete:
                    if (existing != null)
                    {
                        context.Customers.Remove(existing);
                    }
                    break;
            }
        }

        /// <summary>
        /// تطبيق تغيير العقار
        /// </summary>
        private async Task ApplyPropertyChangeAsync(RentalDbContext context, SyncData change)
        {
            var property = JsonConvert.DeserializeObject<Property>(change.JsonData);
            if (property == null) return;

            var existing = await context.Properties.FindAsync(property.Id);

            switch (change.Operation)
            {
                case SyncOperation.Create:
                    if (existing == null)
                    {
                        context.Properties.Add(property);
                    }
                    break;
                case SyncOperation.Update:
                    if (existing != null)
                    {
                        context.Entry(existing).CurrentValues.SetValues(property);
                    }
                    else
                    {
                        context.Properties.Add(property);
                    }
                    break;
                case SyncOperation.Delete:
                    if (existing != null)
                    {
                        context.Properties.Remove(existing);
                    }
                    break;
            }
        }

        /// <summary>
        /// تطبيق تغيير العقد
        /// </summary>
        private async Task ApplyContractChangeAsync(RentalDbContext context, SyncData change)
        {
            var contract = JsonConvert.DeserializeObject<Contract>(change.JsonData);
            if (contract == null) return;

            var existing = await context.Contracts.FindAsync(contract.Id);

            switch (change.Operation)
            {
                case SyncOperation.Create:
                    if (existing == null)
                    {
                        context.Contracts.Add(contract);
                    }
                    break;
                case SyncOperation.Update:
                    if (existing != null)
                    {
                        context.Entry(existing).CurrentValues.SetValues(contract);
                    }
                    else
                    {
                        context.Contracts.Add(contract);
                    }
                    break;
                case SyncOperation.Delete:
                    if (existing != null)
                    {
                        context.Contracts.Remove(existing);
                    }
                    break;
            }
        }

        /// <summary>
        /// تطبيق تغيير الدفعة
        /// </summary>
        private async Task ApplyPaymentChangeAsync(RentalDbContext context, SyncData change)
        {
            var payment = JsonConvert.DeserializeObject<Payment>(change.JsonData);
            if (payment == null) return;

            var existing = await context.Payments.FindAsync(payment.Id);

            switch (change.Operation)
            {
                case SyncOperation.Create:
                    if (existing == null)
                    {
                        context.Payments.Add(payment);
                    }
                    break;
                case SyncOperation.Update:
                    if (existing != null)
                    {
                        context.Entry(existing).CurrentValues.SetValues(payment);
                    }
                    else
                    {
                        context.Payments.Add(payment);
                    }
                    break;
                case SyncOperation.Delete:
                    if (existing != null)
                    {
                        context.Payments.Remove(existing);
                    }
                    break;
            }
        }

        /// <summary>
        /// إنشاء بيانات المزامنة
        /// </summary>
        private SyncData CreateSyncData<T>(T entity, SyncOperation operation) where T : class
        {
            var entityType = typeof(T).Name;
            var entityId = GetEntityId(entity);
            var jsonData = JsonConvert.SerializeObject(entity);
            var hash = _encryptionService.CreateHash(jsonData);

            return new SyncData
            {
                EntityType = entityType,
                EntityId = entityId,
                JsonData = jsonData,
                Operation = operation,
                Timestamp = DateTime.UtcNow,
                UserId = Environment.UserName,
                DeviceId = EncryptionService.GenerateDeviceId(),
                Hash = hash,
                Metadata = new Dictionary<string, object>
                {
                    ["Version"] = "1.0",
                    ["Source"] = "Desktop"
                }
            };
        }

        /// <summary>
        /// الحصول على معرف الكيان
        /// </summary>
        private string GetEntityId<T>(T entity) where T : class
        {
            var idProperty = typeof(T).GetProperty("Id");
            return idProperty?.GetValue(entity)?.ToString() ?? Guid.NewGuid().ToString();
        }

        /// <summary>
        /// دمج نتائج المزامنة
        /// </summary>
        private SyncResult CombineSyncResults(SyncResult uploadResult, SyncResult downloadResult)
        {
            return new SyncResult
            {
                Success = uploadResult.Success && downloadResult.Success,
                Message = $"رفع: {uploadResult.Message} | تنزيل: {downloadResult.Message}",
                SyncedData = uploadResult.SyncedData.Concat(downloadResult.SyncedData).ToList(),
                Conflicts = uploadResult.Conflicts.Concat(downloadResult.Conflicts).ToList(),
                TotalRecords = uploadResult.TotalRecords + downloadResult.TotalRecords,
                SuccessfulRecords = uploadResult.SuccessfulRecords + downloadResult.SuccessfulRecords,
                FailedRecords = uploadResult.FailedRecords + downloadResult.FailedRecords,
                ConflictRecords = uploadResult.ConflictRecords + downloadResult.ConflictRecords,
                Duration = uploadResult.Duration + downloadResult.Duration
            };
        }

        /// <summary>
        /// معالجة التعارضات
        /// </summary>
        private async Task<ConflictResolutionResult> HandleConflictsAsync(List<SyncConflict> conflicts)
        {
            if (_currentSyncService == null)
            {
                return new ConflictResolutionResult
                {
                    Success = false,
                    Message = "خدمة المزامنة غير متاحة"
                };
            }

            // إشعار بوجود تعارضات
            OnConflictDetected(new ConflictDetectedEventArgs { Conflicts = conflicts });

            // حل التعارضات حسب الاستراتيجية المحددة
            return await _currentSyncService.ResolveConflictsAsync(conflicts);
        }

        #endregion

        #region Event Handlers

        private void OnConnectionStatusChanged(object? sender, ConnectionStatusChangedEventArgs e)
        {
            ConnectionStatusChanged?.Invoke(this, e);
        }

        private void OnSyncProgress(object? sender, SyncProgressEventArgs e)
        {
            SyncProgress?.Invoke(this, e);
        }

        private void OnSyncCompleted(object? sender, SyncCompletedEventArgs e)
        {
            SyncCompleted?.Invoke(this, e);
        }

        private void OnSyncError(object? sender, SyncErrorEventArgs e)
        {
            SyncError?.Invoke(this, e);
        }

        private void OnSyncError(SyncErrorEventArgs e)
        {
            SyncError?.Invoke(this, e);
        }

        private void OnConflictDetected(ConflictDetectedEventArgs e)
        {
            ConflictDetected?.Invoke(this, e);
        }

        /// <summary>
        /// معالج أحداث معالجة الدفعات
        /// </summary>
        private async void OnBatchProcessed(object? sender, BatchProcessedEventArgs e)
        {
            try
            {
                if (_currentSyncService == null) return;

                using var operationTracker = _performanceMonitor.StartOperation("ProcessBatch", "SyncOptimizer");

                // مزامنة الدفعة المحسنة
                foreach (var syncData in e.BatchData)
                {
                    try
                    {
                        await _currentSyncService.SyncToCloudAsync(syncData);
                        _performanceMonitor.IncrementCounter("SuccessfulSyncs", "BatchProcessing");
                    }
                    catch (Exception ex)
                    {
                        _performanceMonitor.IncrementCounter("FailedSyncs", "BatchProcessing");
                        OnSyncError(new SyncErrorEventArgs
                        {
                            Exception = ex,
                            ErrorMessage = $"فشل في مزامنة عنصر في الدفعة: {ex.Message}",
                            EntityType = syncData.EntityType,
                            EntityId = syncData.EntityId
                        });
                    }
                }

                _performanceMonitor.RecordMetric("BatchOptimizationRatio",
                    (double)e.OptimizedCount / e.OriginalCount, "ratio", "SyncOptimizer");
            }
            catch (Exception ex)
            {
                OnSyncError(new SyncErrorEventArgs
                {
                    Exception = ex,
                    ErrorMessage = $"فشل في معالجة الدفعة: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// معالج تنبيهات الأداء
        /// </summary>
        private void OnPerformanceAlert(object? sender, PerformanceAlertEventArgs e)
        {
            // يمكن إضافة منطق معالجة التنبيهات هنا
            // مثل تسجيل التنبيهات أو إرسال إشعارات
            System.Diagnostics.Debug.WriteLine($"تنبيه أداء: {e.Message}");
        }

        #endregion

        #region IDisposable

        public void Dispose()
        {
            if (_isDisposed) return;

            StopAutoSync();
            _syncSemaphore?.Dispose();
            _syncOptimizer?.Dispose();
            _performanceMonitor?.Dispose();
            _currentSyncService = null;
            _isDisposed = true;
        }

        #endregion
    }

    /// <summary>
    /// أحداث اكتشاف التعارضات
    /// </summary>
    public class ConflictDetectedEventArgs : EventArgs
    {
        public List<SyncConflict> Conflicts { get; set; } = new();
    }
}
