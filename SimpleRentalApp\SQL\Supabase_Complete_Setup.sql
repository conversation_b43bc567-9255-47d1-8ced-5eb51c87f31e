-- إعد<PERSON> كامل لقاعدة بيانات Supabase للمزامنة
-- تشغيل هذا الملف في SQL Editor في Supabase

-- 1. إنشاء جدول بيانات المزامنة
CREATE TABLE IF NOT EXISTS sync_data (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    entity_type VARCHAR(100) NOT NULL,
    entity_id VARCHAR(100) NOT NULL,
    json_data TEXT NOT NULL,
    operation VARCHAR(50) NOT NULL,
    timestamp TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    user_id VARCHAR(100),
    device_id VARCHAR(100),
    hash VARCHAR(500),
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- 2. <PERSON><PERSON><PERSON><PERSON><PERSON> جدول النسخ الاحتياطية
CREATE TABLE IF NOT EXISTS backups (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    size BIGINT DEFAULT 0,
    data TEXT,
    type VARCHAR(50) DEFAULT 'Full',
    version VARCHAR(20) DEFAULT '1.0',
    description TEXT
);

-- 3. إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_sync_data_entity_type ON sync_data(entity_type);
CREATE INDEX IF NOT EXISTS idx_sync_data_entity_id ON sync_data(entity_id);
CREATE INDEX IF NOT EXISTS idx_sync_data_timestamp ON sync_data(timestamp);
CREATE INDEX IF NOT EXISTS idx_sync_data_operation ON sync_data(operation);
CREATE INDEX IF NOT EXISTS idx_sync_data_user_id ON sync_data(user_id);

-- 4. تفعيل Row Level Security
ALTER TABLE sync_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE backups ENABLE ROW LEVEL SECURITY;

-- 5. إنشاء سياسات RLS للسماح بجميع العمليات
-- سياسة للقراءة
CREATE POLICY "Enable read access for all users" ON sync_data
    FOR SELECT USING (true);

-- سياسة للإدراج
CREATE POLICY "Enable insert access for all users" ON sync_data
    FOR INSERT WITH CHECK (true);

-- سياسة للتحديث
CREATE POLICY "Enable update access for all users" ON sync_data
    FOR UPDATE USING (true);

-- سياسة للحذف
CREATE POLICY "Enable delete access for all users" ON sync_data
    FOR DELETE USING (true);

-- سياسات للنسخ الاحتياطية
CREATE POLICY "Enable read access for backups" ON backups
    FOR SELECT USING (true);

CREATE POLICY "Enable insert access for backups" ON backups
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Enable update access for backups" ON backups
    FOR UPDATE USING (true);

CREATE POLICY "Enable delete access for backups" ON backups
    FOR DELETE USING (true);

-- 6. إنشاء دالة لإدراج بيانات المزامنة
CREATE OR REPLACE FUNCTION insert_sync_data(
    p_entity_type VARCHAR(100),
    p_entity_id VARCHAR(100),
    p_json_data TEXT,
    p_operation VARCHAR(50),
    p_user_id VARCHAR(100) DEFAULT NULL,
    p_device_id VARCHAR(100) DEFAULT NULL,
    p_hash VARCHAR(500) DEFAULT NULL,
    p_metadata JSONB DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    new_id UUID;
BEGIN
    INSERT INTO sync_data (entity_type, entity_id, json_data, operation, user_id, device_id, hash, metadata)
    VALUES (p_entity_type, p_entity_id, p_json_data, p_operation, p_user_id, p_device_id, p_hash, p_metadata)
    RETURNING id INTO new_id;
    
    RETURN new_id;
END;
$$ LANGUAGE plpgsql;

-- 7. إنشاء دالة لجلب البيانات المحدثة
CREATE OR REPLACE FUNCTION get_updated_data(
    p_last_sync_time TIMESTAMPTZ,
    p_entity_type VARCHAR(100) DEFAULT NULL
)
RETURNS TABLE(
    id UUID,
    entity_type VARCHAR(100),
    entity_id VARCHAR(100),
    json_data TEXT,
    operation VARCHAR(50),
    timestamp TIMESTAMPTZ,
    user_id VARCHAR(100),
    device_id VARCHAR(100),
    hash VARCHAR(500),
    metadata JSONB
) AS $$
BEGIN
    IF p_entity_type IS NULL THEN
        RETURN QUERY
        SELECT s.id, s.entity_type, s.entity_id, s.json_data, s.operation, 
               s.timestamp, s.user_id, s.device_id, s.hash, s.metadata
        FROM sync_data s
        WHERE s.timestamp > p_last_sync_time
        ORDER BY s.timestamp ASC;
    ELSE
        RETURN QUERY
        SELECT s.id, s.entity_type, s.entity_id, s.json_data, s.operation, 
               s.timestamp, s.user_id, s.device_id, s.hash, s.metadata
        FROM sync_data s
        WHERE s.timestamp > p_last_sync_time AND s.entity_type = p_entity_type
        ORDER BY s.timestamp ASC;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- 8. إنشاء دالة لحذف البيانات القديمة
CREATE OR REPLACE FUNCTION cleanup_old_sync_data(
    p_days_to_keep INTEGER DEFAULT 30
)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM sync_data 
    WHERE created_at < NOW() - INTERVAL '1 day' * p_days_to_keep;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 9. إنشاء trigger لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_sync_data_updated_at 
    BEFORE UPDATE ON sync_data
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 10. إدراج بيانات اختبار
INSERT INTO sync_data (entity_type, entity_id, json_data, operation, user_id, device_id) 
VALUES ('Test', 'test-1', '{"message": "اختبار المزامنة"}', 'Create', 'system', 'setup')
ON CONFLICT DO NOTHING;

-- 11. التحقق من الإعداد
SELECT 
    'sync_data' as table_name,
    COUNT(*) as record_count,
    MAX(created_at) as latest_record
FROM sync_data
UNION ALL
SELECT 
    'backups' as table_name,
    COUNT(*) as record_count,
    MAX(created_at) as latest_record
FROM backups;

-- رسالة تأكيد
SELECT 'تم إعداد قاعدة البيانات بنجاح! ✅' as message;
