# سكريبت تشغيل تطبيق تدبير الكراء مع المزامنة السحابية
# Rental Management App with Cloud Sync Runner Script

Write-Host "=== تطبيق تدبير الكراء مع المزامنة السحابية ===" -ForegroundColor Green
Write-Host "Rental Management App with Cloud Sync" -ForegroundColor Green

# التحقق من وجود .NET SDK
Write-Host "`n=== التحقق من .NET SDK ===" -ForegroundColor Yellow
try {
    $dotnetVersion = dotnet --version
    Write-Host "إصدار .NET SDK: $dotnetVersion" -ForegroundColor Cyan
    
    # التحقق من إصدار .NET المطلوب
    if ($dotnetVersion -lt "8.0") {
        Write-Host "تحذير: يتطلب التطبيق .NET 8.0 أو أحدث" -ForegroundColor Yellow
    }
} catch {
    Write-Host "خطأ: .NET SDK غير مثبت أو غير متاح في PATH" -ForegroundColor Red
    Write-Host "يرجى تثبيت .NET 8.0 SDK من: https://dotnet.microsoft.com/download" -ForegroundColor Yellow
    Read-Host "اضغط Enter للمتابعة أو Ctrl+C للخروج..."
}

# الانتقال إلى مجلد التطبيق
$appPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $appPath

Write-Host "مجلد التطبيق: $appPath" -ForegroundColor Cyan

# استعادة الحزم
Write-Host "`n=== استعادة حزم NuGet ===" -ForegroundColor Yellow
try {
    dotnet restore
    if ($LASTEXITCODE -ne 0) {
        Write-Host "فشل في استعادة الحزم" -ForegroundColor Red
        Write-Host "جاري المحاولة مرة أخرى..." -ForegroundColor Yellow
        dotnet restore --force
    }
} catch {
    Write-Host "خطأ في استعادة الحزم: $($_.Exception.Message)" -ForegroundColor Red
}

# بناء التطبيق
Write-Host "`n=== بناء التطبيق ===" -ForegroundColor Yellow
try {
    dotnet build --configuration Release
    if ($LASTEXITCODE -ne 0) {
        Write-Host "فشل في بناء التطبيق" -ForegroundColor Red
        Write-Host "جاري المحاولة مع Debug configuration..." -ForegroundColor Yellow
        dotnet build --configuration Debug
    }
} catch {
    Write-Host "خطأ في بناء التطبيق: $($_.Exception.Message)" -ForegroundColor Red
}

# التحقق من ملفات التطبيق
Write-Host "`n=== التحقق من ملفات التطبيق ===" -ForegroundColor Yellow
$mainFiles = @(
    "MainWindow.xaml",
    "App.xaml",
    "Services/CloudSync/SyncManager.cs",
    "Views/CloudSyncSettingsWindow.xaml",
    "Views/PerformanceMonitorWindow.xaml"
)

foreach ($file in $mainFiles) {
    if (Test-Path $file) {
        Write-Host "✓ $file" -ForegroundColor Green
    } else {
        Write-Host "✗ $file غير موجود" -ForegroundColor Red
    }
}

# تشغيل التطبيق
Write-Host "`n=== تشغيل التطبيق ===" -ForegroundColor Yellow
try {
    Write-Host "جاري تشغيل تطبيق تدبير الكراء..." -ForegroundColor Cyan
    Write-Host "الميزات المتاحة:" -ForegroundColor White
    Write-Host "  • إدارة العملاء والعقارات" -ForegroundColor White
    Write-Host "  • إدارة العقود والمدفوعات" -ForegroundColor White
    Write-Host "  • المزامنة السحابية (Supabase, Firebase, Azure SQL)" -ForegroundColor White
    Write-Host "  • التشفير والأمان المتقدم" -ForegroundColor White
    Write-Host "  • مراقب الأداء" -ForegroundColor White
    Write-Host "  • النسخ الاحتياطية" -ForegroundColor White
    Write-Host ""
    
    # محاولة تشغيل التطبيق
    if (Test-Path "bin/Release/net9.0-windows/SimpleRentalApp.exe") {
        Write-Host "تشغيل من Release build..." -ForegroundColor Green
        Start-Process "bin/Release/net9.0-windows/SimpleRentalApp.exe"
    } elseif (Test-Path "bin/Debug/net9.0-windows/SimpleRentalApp.exe") {
        Write-Host "تشغيل من Debug build..." -ForegroundColor Green
        Start-Process "bin/Debug/net9.0-windows/SimpleRentalApp.exe"
    } else {
        Write-Host "تشغيل باستخدام dotnet run..." -ForegroundColor Yellow
        dotnet run --configuration Release
    }
    
} catch {
    Write-Host "خطأ في تشغيل التطبيق: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "جاري المحاولة بطريقة بديلة..." -ForegroundColor Yellow
    
    try {
        dotnet run
    } catch {
        Write-Host "فشل في تشغيل التطبيق" -ForegroundColor Red
        Write-Host "يرجى التحقق من:" -ForegroundColor Yellow
        Write-Host "  1. تثبيت .NET 8.0 SDK أو أحدث" -ForegroundColor White
        Write-Host "  2. إعادة تشغيل PowerShell كمسؤول" -ForegroundColor White
        Write-Host "  3. التحقق من Windows Defender أو برامج مكافحة الفيروسات" -ForegroundColor White
    }
}

Write-Host "`n=== معلومات إضافية ===" -ForegroundColor Magenta
Write-Host "للوصول إلى المزامنة السحابية:" -ForegroundColor White
Write-Host "  1. افتح التطبيق" -ForegroundColor White
Write-Host "  2. اضغط على زر 'المزامنة السحابية' في الشريط العلوي" -ForegroundColor White
Write-Host "  3. اختر نوع الخدمة السحابية (Supabase/Firebase/Azure SQL)" -ForegroundColor White
Write-Host "  4. أدخل بيانات الاتصال" -ForegroundColor White
Write-Host "  5. اختبر الاتصال واحفظ الإعدادات" -ForegroundColor White
Write-Host ""
Write-Host "لمراقبة الأداء:" -ForegroundColor White
Write-Host "  • اضغط على 'مراقب الأداء' في نافذة إعدادات المزامنة" -ForegroundColor White
Write-Host ""
Write-Host "ملفات الإعداد متوفرة في:" -ForegroundColor White
Write-Host "  • SQL/CloudSync_Setup.sql (لـ Azure SQL)" -ForegroundColor White
Write-Host "  • SQL/Supabase_Setup.sql (لـ Supabase)" -ForegroundColor White
Write-Host "  • Firebase/ (لـ Firebase)" -ForegroundColor White

Write-Host "`n=== انتهى تشغيل السكريبت ===" -ForegroundColor Green
Read-Host "اضغط Enter للخروج..."
