using SimpleRentalApp.Services;
using System;
using System.Windows;
using System.Windows.Controls;

namespace SimpleRentalApp.Windows
{
    public partial class WhatsAppSettingsWindow : Window
    {
        private readonly WhatsAppSenderService _whatsAppSender;

        public WhatsAppSettingsWindow()
        {
            InitializeComponent();
            _whatsAppSender = new WhatsAppSenderService();
            LoadSettings();
        }

        private void LoadSettings()
        {
            try
            {
                // تحميل الإعدادات المحفوظة (يمكن تطويرها لاحقاً لحفظ الإعدادات في قاعدة البيانات)
                
                // إعدادات افتراضية
                WebRadioButton.IsChecked = true;
                CountryCodeComboBox.SelectedIndex = 0; // المغرب
                ShowConfirmationCheckBox.IsChecked = true;
                ShowPreviewCheckBox.IsChecked = true;
                AutoCloseCheckBox.IsChecked = false;
                DelaySlider.Value = 2;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveSettings()
        {
            try
            {
                // حفظ الإعدادات (يمكن تطويرها لاحقاً لحفظ الإعدادات في قاعدة البيانات)
                
                // هنا يمكن إضافة منطق حفظ الإعدادات
                // مثل حفظها في ملف تكوين أو قاعدة البيانات
                
                MessageBox.Show("تم حفظ الإعدادات بنجاح!", "تم الحفظ",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void TestSendBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                string testPhone = TestPhoneTextBox.Text.Trim();
                
                if (string.IsNullOrWhiteSpace(testPhone))
                {
                    MessageBox.Show("يرجى إدخال رقم هاتف للاختبار.", "رقم مطلوب",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    TestPhoneTextBox.Focus();
                    return;
                }

                if (!_whatsAppSender.IsValidPhoneNumber(testPhone))
                {
                    MessageBox.Show("رقم الهاتف غير صحيح. يرجى التحقق من الرقم.", "رقم غير صحيح",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    TestPhoneTextBox.Focus();
                    return;
                }

                if (sender is Button button)
                {
                    button.IsEnabled = false;
                    button.Content = "⏳ جاري الاختبار...";
                }

                string testMessage = "🧪 رسالة اختبار من تطبيق تدبير الكراء\n\n" +
                                   "هذه رسالة اختبار للتأكد من عمل إعدادات الواتساب بشكل صحيح.\n\n" +
                                   $"التاريخ: {DateTime.Now:dd/MM/yyyy HH:mm}\n\n" +
                                   "إذا وصلتك هذه الرسالة، فإن الإعدادات تعمل بشكل صحيح! ✅";

                bool useApp = AppRadioButton.IsChecked == true;
                bool showConfirmation = ShowConfirmationCheckBox.IsChecked == true;

                bool success = await _whatsAppSender.SendMessageWithOptionsAsync(
                    testPhone, 
                    testMessage, 
                    useApp, 
                    showConfirmation);

                if (success)
                {
                    MessageBox.Show("تم فتح الواتساب بنجاح!\n\nتحقق من وصول رسالة الاختبار.", 
                        "نجح الاختبار", 
                        MessageBoxButton.OK, 
                        MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("فشل في فتح الواتساب.\n\nتحقق من الإعدادات والمحاولة مرة أخرى.", 
                        "فشل الاختبار", 
                        MessageBoxButton.OK, 
                        MessageBoxImage.Error);
                }

                if (sender is Button btn)
                {
                    btn.IsEnabled = true;
                    btn.Content = "📱 اختبار الإرسال";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار الإرسال: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                
                if (sender is Button btn)
                {
                    btn.IsEnabled = true;
                    btn.Content = "📱 اختبار الإرسال";
                }
            }
        }

        private void SaveBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                SaveSettings();
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelBtn_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "هل تريد إغلاق النافذة بدون حفظ التغييرات؟",
                "تأكيد الإغلاق",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                DialogResult = false;
                Close();
            }
        }

        // خصائص للوصول إلى الإعدادات من النوافذ الأخرى
        public bool UseWhatsAppApp => AppRadioButton.IsChecked == true;
        public string CountryCode => ((ComboBoxItem)CountryCodeComboBox.SelectedItem)?.Tag?.ToString() ?? "212";
        public bool ShowConfirmation => ShowConfirmationCheckBox.IsChecked == true;
        public bool ShowPreview => ShowPreviewCheckBox.IsChecked == true;
        public bool AutoClose => AutoCloseCheckBox.IsChecked == true;
        public int DelayBetweenMessages => (int)DelaySlider.Value;
    }
}
