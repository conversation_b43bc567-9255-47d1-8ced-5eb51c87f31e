﻿#pragma checksum "..\..\..\..\Windows\AddRentReceiptDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "39C0EBB46221F54BEFAB519200BBD4130F9C0BA5"
//------------------------------------------------------------------------------
// <auto-generated>
//     Ce code a été généré par un outil.
//     Version du runtime :4.0.30319.42000
//
//     Les modifications apportées à ce fichier peuvent provoquer un comportement incorrect et seront perdues si
//     le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleRentalApp.Windows {
    
    
    /// <summary>
    /// AddRentReceiptDialog
    /// </summary>
    public partial class AddRentReceiptDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 98 "..\..\..\..\Windows\AddRentReceiptDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PropertyInfoText;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\Windows\AddRentReceiptDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ReceiptNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\..\Windows\AddRentReceiptDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ReceiptNumberHintText;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\..\Windows\AddRentReceiptDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button GenerateNewNumberBtn;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\Windows\AddRentReceiptDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker PaymentDatePicker;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\Windows\AddRentReceiptDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\..\Windows\AddRentReceiptDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker PeriodStartDatePicker;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\Windows\AddRentReceiptDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker PeriodEndDatePicker;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\Windows\AddRentReceiptDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\..\Windows\AddRentReceiptDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveBtn;
        
        #line default
        #line hidden
        
        
        #line 189 "..\..\..\..\Windows\AddRentReceiptDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelBtn;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleRentalApp;V1.0.0.0;component/windows/addrentreceiptdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\AddRentReceiptDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.PropertyInfoText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.ReceiptNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 119 "..\..\..\..\Windows\AddRentReceiptDialog.xaml"
            this.ReceiptNumberTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ReceiptNumberTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 122 "..\..\..\..\Windows\AddRentReceiptDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.GenerateReceiptNumber_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.ReceiptNumberHintText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.GenerateNewNumberBtn = ((System.Windows.Controls.Button)(target));
            
            #line 132 "..\..\..\..\Windows\AddRentReceiptDialog.xaml"
            this.GenerateNewNumberBtn.Click += new System.Windows.RoutedEventHandler(this.GenerateNewNumberBtn_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.PaymentDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 7:
            this.AmountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.PeriodStartDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 9:
            this.PeriodEndDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 10:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.SaveBtn = ((System.Windows.Controls.Button)(target));
            
            #line 188 "..\..\..\..\Windows\AddRentReceiptDialog.xaml"
            this.SaveBtn.Click += new System.Windows.RoutedEventHandler(this.SaveBtn_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.CancelBtn = ((System.Windows.Controls.Button)(target));
            
            #line 191 "..\..\..\..\Windows\AddRentReceiptDialog.xaml"
            this.CancelBtn.Click += new System.Windows.RoutedEventHandler(this.CancelBtn_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

