<Window x:Class="SimpleRentalApp.Windows.ReceiptPreviewWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="معاينة التوصيل" Height="700" Width="600"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">

    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="6"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Success Button Style -->
        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#4CAF50"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#45A049"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="White" Padding="20,15" 
                BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
            <StackPanel>
                <TextBlock Text="👁️ معاينة التوصيل" 
                           FontSize="20" FontWeight="Bold" 
                           Foreground="#1976D2" Margin="0,0,0,5"/>
                <TextBlock Name="ReceiptNumberText" Text="رقم التوصيل: R-001" 
                           FontSize="14" Foreground="#666"/>
            </StackPanel>
        </Border>

        <!-- Receipt Preview -->
        <Border Grid.Row="1" Background="White" Margin="20" 
                CornerRadius="8" BorderBrush="#E0E0E0" BorderThickness="1"
                Padding="30">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel Name="ReceiptContent">
                    
                    <!-- Receipt Header -->
                    <TextBlock Text="توصيل كراء" 
                               FontSize="24" FontWeight="Bold" 
                               HorizontalAlignment="Center" 
                               Foreground="#1976D2" Margin="0,0,0,20"/>

                    <!-- Receipt Details -->
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="رقم التوصيل:" FontWeight="Bold" Margin="0,5"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Name="PreviewReceiptNumber" Text="R-001" Margin="0,5"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="تاريخ الأداء:" FontWeight="Bold" Margin="0,5"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" Name="PreviewDate" Text="2024/01/01" Margin="0,5"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="اسم المكتري:" FontWeight="Bold" Margin="0,5"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" Name="PreviewCustomerName" Text="أحمد محمد" Margin="0,5"/>

                        <TextBlock Grid.Row="3" Grid.Column="0" Text="عنوان المحل:" FontWeight="Bold" Margin="0,5"/>
                        <TextBlock Grid.Row="3" Grid.Column="1" Name="PreviewPropertyAddress" Text="عنوان المحل" Margin="0,5"/>

                        <TextBlock Grid.Row="4" Grid.Column="0" Text="الفترة المؤداة:" FontWeight="Bold" Margin="0,5"/>
                        <TextBlock Grid.Row="4" Grid.Column="1" Name="PreviewPaymentPeriod" Text="من 01/01/2024 إلى 31/01/2024" Margin="0,5"/>

                        <TextBlock Grid.Row="5" Grid.Column="0" Text="المبلغ:" FontWeight="Bold" Margin="0,5"/>
                        <StackPanel Grid.Row="5" Grid.Column="1" Margin="0,5">
                            <TextBlock Name="PreviewAmount" Text="5000 درهم"
                                       FontWeight="Bold" Foreground="#4CAF50"/>
                            <TextBlock Name="PreviewAmountInWords" Text="خمسة آلاف درهم"
                                       FontStyle="Italic" Foreground="#666" FontSize="12"/>
                        </StackPanel>
                    </Grid>

                    <!-- Notes Section -->
                    <StackPanel Name="NotesSection" Margin="0,20,0,0" Visibility="Collapsed">
                        <TextBlock Text="ملاحظات:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <Border Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="1" Padding="10">
                            <TextBlock Name="PreviewNotes" TextWrapping="Wrap"/>
                        </Border>
                    </StackPanel>

                    <!-- Signature Area -->
                    <Border Margin="0,30,0,0" BorderBrush="#DDD" BorderThickness="1" Padding="15">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="توقيع الكاري:" FontWeight="Bold" Margin="0,0,0,30"/>
                                <Line X1="0" X2="150" Y1="0" Y2="0" Stroke="Black" StrokeThickness="1"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1">
                                <TextBlock Text="ختم المالك:" FontWeight="Bold" Margin="0,0,0,30"/>
                                <Rectangle Width="100" Height="60" Stroke="Black" StrokeThickness="1" 
                                          StrokeDashArray="5,5" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                </StackPanel>
            </ScrollViewer>
        </Border>

        <!-- Action Buttons -->
        <Border Grid.Row="2" Background="White" Padding="20,15" 
                BorderBrush="#E0E0E0" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Name="PrintBtn" Content="🖨️ طباعة" 
                        Style="{StaticResource SuccessButtonStyle}"
                        Click="PrintBtn_Click" Padding="20,10"/>
                <Button Name="CloseBtn" Content="❌ إغلاق" 
                        Style="{StaticResource ModernButtonStyle}"
                        Click="CloseBtn_Click" Padding="20,10"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
