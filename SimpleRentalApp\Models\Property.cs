using System.ComponentModel.DataAnnotations;

namespace SimpleRentalApp.Models;

public enum PropertyType
{
    Apartment = 1,
    Shop = 2,
    Office = 3,
    Villa = 4
}

public enum PropertyStatus
{
    Available = 1,
    Rented = 2,
    Maintenance = 3
}

public class Property
{
    public int Id { get; set; }
    
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [StringLength(200)]
    public string Address { get; set; } = string.Empty;
    
    public PropertyType Type { get; set; }
    
    public PropertyStatus Status { get; set; } = PropertyStatus.Available;
    
    [Required]
    public decimal MonthlyRent { get; set; }
    
    public decimal CleaningTaxPaymentOption { get; set; } = 1; // خيار دفع ضريبة النظافة (1=شهرياً، 2=نصف سنوي، 3=ثلث سنوي، 4=ربع سنوي، 12=سنوياً)
    
    public int? Rooms { get; set; }
    
    public int? Bathrooms { get; set; }
    
    public decimal? Area { get; set; }
    
    [StringLength(500)]
    public string Description { get; set; } = string.Empty;
    
    public DateTime CreatedDate { get; set; } = DateTime.Now;
    public DateTime UpdatedDate { get; set; } = DateTime.Now;

    // Foreign Keys
    public int? CustomerId { get; set; }
    
    // Navigation properties
    public virtual Customer? Customer { get; set; }
    public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();
    
    // Calculated properties
    public decimal AnnualCleaningTax => MonthlyRent * 12 * 0.105m; // 10.5% من الإيجار السنوي

    public decimal MonthlyCleaningTax => CleaningTaxPaymentOption switch
    {
        1 => AnnualCleaningTax / 12, // شهرياً
        2 => 0, // نصف سنوي - لا يدفع شهرياً
        3 => 0, // ثلث سنوي - لا يدفع شهرياً
        4 => 0, // ربع سنوي - لا يدفع شهرياً
        12 => 0, // سنوياً - لا يدفع شهرياً
        _ => AnnualCleaningTax / 12
    };

    public decimal CleaningTaxPerPayment => CleaningTaxPaymentOption switch
    {
        1 => AnnualCleaningTax / 12, // شهرياً
        2 => AnnualCleaningTax / 2,  // نصف سنوي
        3 => AnnualCleaningTax / 3,  // ثلث سنوي
        4 => AnnualCleaningTax / 4,  // ربع سنوي
        12 => AnnualCleaningTax,     // سنوياً
        _ => AnnualCleaningTax / 12
    };

    public decimal TotalMonthlyAmount => MonthlyRent + MonthlyCleaningTax;

    public string CleaningTaxPaymentOptionDisplay => CleaningTaxPaymentOption switch
    {
        1 => "شهرياً مع الإيجار",
        2 => "نصف سنوي (مرتين في السنة)",
        3 => "ثلث سنوي (3 مرات في السنة)",
        4 => "ربع سنوي (4 مرات في السنة)",
        12 => "سنوياً (مرة واحدة)",
        _ => "شهرياً مع الإيجار"
    };
    
    public string TypeDisplay => Type switch
    {
        PropertyType.Apartment => "شقة",
        PropertyType.Shop => "محل",
        PropertyType.Office => "مكتب",
        PropertyType.Villa => "فيلا",
        _ => "غير محدد"
    };
    
    public string StatusDisplay => Status switch
    {
        PropertyStatus.Available => "متاح",
        PropertyStatus.Rented => "مكتراة",
        PropertyStatus.Maintenance => "صيانة",
        _ => "غير محدد"
    };

    // Display property for ComboBox
    public string DisplayName => string.IsNullOrEmpty(Address) ? Name : $"{Name} - {Address}";
}
