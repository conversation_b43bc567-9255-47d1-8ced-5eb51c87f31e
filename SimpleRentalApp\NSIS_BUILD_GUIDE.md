# دليل بناء ملف التثبيت باستخدام NSIS
## NSIS Installer Build Guide

هذا الدليل يوضح كيفية بناء ملف تثبيت احترافي لنظام تدبير الكراء باستخدام NSIS.

---

## 📋 المتطلبات الأساسية

### 1. تثبيت NSIS
- **تحميل من**: https://nsis.sourceforge.io/Download
- **الإصدار المطلوب**: NSIS 3.x مع دعم Unicode
- **المسار الافتراضي**: `C:\Program Files\NSIS\` أو `C:\Program Files (x86)\NSIS\`

### 2. الملفات المطلوبة
- ✅ `RentalManagement_Installer.nsi` - ملف NSIS الرئيسي
- ✅ `dist_new\` - مجلد ملفات التطبيق المنشورة
- ✅ `app_icon.ico` - أيقونة التطبيق
- ✅ `build_nsis_installer.bat` - ملف البناء التلقائي

---

## 🚀 خطوات البناء

### الطريقة الأولى: استخدام ملف Batch التلقائي (الأسهل)
```bash
# تشغيل ملف البناء التلقائي
.\build_nsis_installer.bat
```

### الطريقة الثانية: استخدام NSIS يدوياً
```bash
# 1. فتح Command Prompt
# 2. الانتقال إلى مجلد المشروع
cd C:\Users\<USER>\Desktop\rental\SimpleRentalApp

# 3. تشغيل NSIS
"C:\Program Files\NSIS\makensis.exe" RentalManagement_Installer.nsi
```

### الطريقة الثالثة: استخدام واجهة NSIS الرسومية
```bash
# 1. فتح برنامج NSIS
# 2. سحب ملف RentalManagement_Installer.nsi إلى النافذة
# 3. الضغط على "Compile NSI"
```

---

## 📁 هيكل الملفات المطلوب

```
SimpleRentalApp/
├── RentalManagement_Installer.nsi    # ملف NSIS الرئيسي
├── build_nsis_installer.bat          # ملف البناء التلقائي
├── app_icon.ico                      # أيقونة التطبيق
├── dist_new/                         # ملفات التطبيق المنشورة
│   ├── SimpleRentalApp.exe
│   ├── *.dll
│   └── ...
└── NSIS_BUILD_GUIDE.md              # هذا الدليل
```

---

## ⚙️ ميزات ملف التثبيت

### الواجهة والتصميم:
- ✅ واجهة عربية بالكامل
- ✅ دعم Unicode للنصوص العربية
- ✅ أيقونة مخصصة للتطبيق
- ✅ تصميم Modern UI احترافي

### وظائف التثبيت:
- ✅ تثبيت تلقائي في Program Files
- ✅ إنشاء اختصارات عربية
- ✅ تسجيل البرنامج في النظام
- ✅ إنشاء مجلد البيانات

### الاختصارات المنشأة:
- 📁 قائمة ابدأ: "نظام تدبير الكراء"
- 🖥️ سطح المكتب: "نظام تدبير الكراء"
- 📖 دليل المستخدم
- 🗑️ أداة إلغاء التثبيت

### إلغاء التثبيت:
- ✅ إزالة كاملة للملفات
- ✅ حذف الاختصارات
- ✅ إزالة تسجيل النظام
- ✅ خيار حذف البيانات

---

## 🔧 تخصيص ملف NSIS

### تغيير معلومات التطبيق:
```nsis
!define APPNAME "اسم التطبيق الجديد"
!define COMPANYNAME "اسم المطور"
!define VERSIONMAJOR 1
!define VERSIONMINOR 0
!define VERSIONBUILD 0
```

### تغيير مسار التثبيت:
```nsis
InstallDir "$PROGRAMFILES64\اسم_المجلد_الجديد"
```

### إضافة ملفات إضافية:
```nsis
Section "الملفات الأساسية" SecMain
    ; ... الكود الموجود ...
    File "ملف_إضافي.txt"
SectionEnd
```

### تخصيص النصوص:
```nsis
!define MUI_WELCOMEPAGE_TITLE "عنوان مخصص"
!define MUI_WELCOMEPAGE_TEXT "نص ترحيبي مخصص"
```

---

## 🛠️ استكشاف الأخطاء

### خطأ: "NSIS not found"
**الحل**:
1. تثبيت NSIS من الموقع الرسمي
2. التأكد من المسار الصحيح
3. إعادة تشغيل Command Prompt

### خطأ: "Application files not found"
**الحل**:
1. تشغيل `simple_redistribute.ps1` أولاً
2. التأكد من وجود مجلد `dist_new`
3. التأكد من وجود `SimpleRentalApp.exe`

### خطأ: "Unicode characters not displaying"
**الحل**:
1. التأكد من استخدام `Unicode True`
2. حفظ ملف NSI بترميز UTF-8
3. استخدام إصدار NSIS يدعم Unicode

### خطأ: "Icon not found"
**الحل**:
1. التأكد من وجود `app_icon.ico`
2. استخدام مسار نسبي صحيح
3. تحويل PNG إلى ICO إذا لزم الأمر

---

## 📊 معلومات الملف المنشأ

### اسم الملف:
```
RentalManagement_Setup_v1.0.exe
```

### الحجم المتوقع:
- **مع التطبيق**: ~75-80 MB
- **مضغوط**: ~30-35 MB

### المعلومات المسجلة:
- اسم التطبيق
- الإصدار
- المطور
- مسار التثبيت
- حجم التثبيت
- روابط الدعم

---

## 🎯 نصائح للتوزيع

### قبل التوزيع:
1. **اختبار التثبيت** على أجهزة مختلفة
2. **اختبار إلغاء التثبيت** للتأكد من الإزالة الكاملة
3. **فحص الاختصارات** والتأكد من عملها
4. **اختبار الأيقونات** في أحجام مختلفة

### أثناء التوزيع:
1. **توفير دليل التثبيت** مع الملف
2. **ذكر متطلبات النظام** بوضوح
3. **توفير معلومات الدعم** الفني
4. **إرفاق لقطات شاشة** للواجهة

### بعد التوزيع:
1. **جمع التغذية الراجعة** من المستخدمين
2. **إصلاح المشاكل** المبلغ عنها
3. **تحديث الإصدار** عند الحاجة
4. **توفير التحديثات** بانتظام

---

## 📝 ملاحظات مهمة

### للمطورين:
- ملف NSIS يدعم العربية بالكامل
- يمكن تخصيص جميع النصوص
- الكود منظم ومعلق بوضوح
- سهل التعديل والتطوير

### للمستخدمين:
- التثبيت بسيط ومباشر
- واجهة عربية واضحة
- إزالة آمنة وكاملة
- دعم فني متوفر

### للموزعين:
- ملف واحد سهل التوزيع
- حجم معقول للتحميل
- متوافق مع جميع أنظمة Windows
- موقع رقمياً للأمان

---

## 🎉 النتيجة النهائية

بعد اتباع هذا الدليل، ستحصل على:

- ✅ ملف تثبيت احترافي (`RentalManagement_Setup_v1.0.exe`)
- ✅ واجهة تثبيت عربية كاملة
- ✅ تسجيل صحيح في النظام
- ✅ اختصارات عربية
- ✅ أداة إلغاء تثبيت

**ملف التثبيت جاهز للتوزيع الاحترافي!** 🚀

---

*تم إنشاء هذا الدليل تلقائياً بواسطة نظام البناء*
