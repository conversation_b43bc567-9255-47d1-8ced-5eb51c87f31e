﻿using System.Windows;

namespace RentalManagement
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();

            // Force window to show
            this.Show();
            this.Activate();
            this.Focus();
        }

        private void TestButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("التطبيق يعمل بنجاح! 🎉\n\nيمكنك الآن البدء في استخدام نظام إدارة العقارات",
                "نجح الاختبار", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ExitButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد إغلاق التطبيق؟", "تأكيد الخروج",
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                Application.Current.Shutdown();
            }
        }
    }
}