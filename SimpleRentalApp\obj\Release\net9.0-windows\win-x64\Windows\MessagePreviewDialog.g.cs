﻿#pragma checksum "..\..\..\..\..\Windows\MessagePreviewDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "ABBE3933F14105E6DAA5C02688FF96D9679589DC"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleRentalApp.Windows {
    
    
    /// <summary>
    /// MessagePreviewDialog
    /// </summary>
    public partial class MessagePreviewDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 80 "..\..\..\..\..\Windows\MessagePreviewDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SubtitleTextBlock;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\..\Windows\MessagePreviewDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CustomerNameTextBlock;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\..\Windows\MessagePreviewDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PhoneNumberTextBlock;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\..\Windows\MessagePreviewDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MessageContentTextBlock;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\..\..\Windows\MessagePreviewDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CharacterCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\..\Windows\MessagePreviewDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MessageLengthTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleRentalApp;V1.0.0.0;component/windows/messagepreviewdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\MessagePreviewDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.SubtitleTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.CustomerNameTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.PhoneNumberTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.MessageContentTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.CharacterCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.MessageLengthTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            
            #line 149 "..\..\..\..\..\Windows\MessagePreviewDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseBtn_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

