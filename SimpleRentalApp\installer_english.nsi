; Rental Management System - NSIS Installer Script
; Developer: <PERSON><PERSON><PERSON>
; English Version for Better Compatibility

; Enable Unicode support
Unicode true

!define APP_NAME "Rental Management System"
!define APP_NAME_AR "تدبير الكراء"
!define APP_VERSION "1.0.0"
!define APP_PUBLISHER "Hafid Abdo"
!define APP_URL ""
!define APP_DESCRIPTION "Property Rental Management Application"
!define APP_EXE "RentalManagement.exe"

; Include required libraries
!include "MUI2.nsh"
!include "FileFunc.nsh"
!include "LogicLib.nsh"

; General settings
Name "${APP_NAME}"
OutFile "RentalManagement_Setup_v${APP_VERSION}.exe"
InstallDir "$PROGRAMFILES64\${APP_NAME}"
InstallDirRegKey HKLM "Software\${APP_NAME}" "InstallDir"
RequestExecutionLevel admin

; Modern UI settings
!define MUI_ABORTWARNING
!define MUI_ICON "app_icon.ico"
!define MUI_UNICON "app_icon.ico"

; Custom welcome page text
!define MUI_WELCOMEPAGE_TITLE "Welcome to ${APP_NAME} Setup"
!define MUI_WELCOMEPAGE_TEXT "This wizard will guide you through the installation of ${APP_NAME}.$\r$\n$\r$\nIt is recommended that you close all other applications before continuing. This will make it possible to update relevant system files without having to reboot your computer.$\r$\n$\r$\nClick Next to continue."

; Custom finish page
!define MUI_FINISHPAGE_TITLE "Completing ${APP_NAME} Setup"
!define MUI_FINISHPAGE_TEXT "${APP_NAME} has been installed on your computer.$\r$\n$\r$\nClick Finish to close this wizard."
!define MUI_FINISHPAGE_RUN "$INSTDIR\${APP_EXE}"
!define MUI_FINISHPAGE_RUN_TEXT "Run ${APP_NAME}"

; Installer pages
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE_EN.txt"
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

; Uninstaller pages
!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

; Languages
!insertmacro MUI_LANGUAGE "English"

; Version information
VIProductVersion "${APP_VERSION}.0"
VIAddVersionKey /LANG=${LANG_ENGLISH} "ProductName" "${APP_NAME}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "Comments" "${APP_DESCRIPTION}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "CompanyName" "${APP_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "LegalCopyright" "© 2025 ${APP_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "FileDescription" "${APP_DESCRIPTION}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "FileVersion" "${APP_VERSION}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "ProductVersion" "${APP_VERSION}"

; Main installation section
Section "Main Application" SecMain
  SectionIn RO
  
  ; Set output path
  SetOutPath "$INSTDIR"
  
  ; Show progress messages
  DetailPrint "Copying application files..."
  
  ; Copy files
  File "dist\${APP_EXE}"
  File "README_EN.txt"
  File "dist\Installation_Guide.txt"
  
  ; Show progress
  DetailPrint "Creating shortcuts..."
  
  ; Create Start Menu shortcuts
  CreateDirectory "$SMPROGRAMS\${APP_NAME}"
  CreateShortCut "$SMPROGRAMS\${APP_NAME}\${APP_NAME}.lnk" "$INSTDIR\${APP_EXE}" "" "$INSTDIR\${APP_EXE}" 0 SW_SHOWNORMAL "" "Property Rental Management Application"
  CreateShortCut "$SMPROGRAMS\${APP_NAME}\Uninstall.lnk" "$INSTDIR\Uninstall.exe" "" "$INSTDIR\Uninstall.exe" 0 SW_SHOWNORMAL "" "Uninstall ${APP_NAME}"
  
  ; Create Desktop shortcut
  CreateShortCut "$DESKTOP\${APP_NAME}.lnk" "$INSTDIR\${APP_EXE}" "" "$INSTDIR\${APP_EXE}" 0 SW_SHOWNORMAL "" "Property Rental Management Application"
  
  ; Show progress
  DetailPrint "Registering application..."
  
  ; Write uninstall information to registry
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "DisplayName" "${APP_NAME}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "UninstallString" "$INSTDIR\Uninstall.exe"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "InstallLocation" "$INSTDIR"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "DisplayIcon" "$INSTDIR\${APP_EXE}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "Publisher" "${APP_PUBLISHER}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "DisplayVersion" "${APP_VERSION}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "Comments" "${APP_DESCRIPTION}"
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "NoModify" 1
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "NoRepair" 1
  
  ; Calculate installation size
  ${GetSize} "$INSTDIR" "/S=0K" $0 $1 $2
  IntFmt $0 "0x%08X" $0
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "EstimatedSize" "$0"
  
  ; Create uninstaller
  DetailPrint "Creating uninstaller..."
  WriteUninstaller "$INSTDIR\Uninstall.exe"
  
  ; Save installation directory
  WriteRegStr HKLM "Software\${APP_NAME}" "InstallDir" "$INSTDIR"
  
  ; Installation complete message
  DetailPrint "${APP_NAME} has been installed successfully!"
SectionEnd

; Section descriptions
LangString DESC_SecMain ${LANG_ENGLISH} "Essential application files"
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
  !insertmacro MUI_DESCRIPTION_TEXT ${SecMain} $(DESC_SecMain)
!insertmacro MUI_FUNCTION_DESCRIPTION_END

; Uninstaller section
Section "Uninstall"
  ; Progress messages
  DetailPrint "Removing application files..."
  
  ; Delete files
  Delete "$INSTDIR\${APP_EXE}"
  Delete "$INSTDIR\README_EN.txt"
  Delete "$INSTDIR\Installation_Guide.txt"
  Delete "$INSTDIR\Uninstall.exe"
  
  DetailPrint "Removing shortcuts..."
  
  ; Delete shortcuts
  Delete "$SMPROGRAMS\${APP_NAME}\${APP_NAME}.lnk"
  Delete "$SMPROGRAMS\${APP_NAME}\Uninstall.lnk"
  Delete "$DESKTOP\${APP_NAME}.lnk"
  
  DetailPrint "Removing directories..."
  
  ; Remove directories
  RMDir "$SMPROGRAMS\${APP_NAME}"
  RMDir "$INSTDIR"
  
  DetailPrint "Removing registry entries..."
  
  ; Delete registry keys
  DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}"
  DeleteRegKey HKLM "Software\${APP_NAME}"
  
  DetailPrint "${APP_NAME} has been uninstalled successfully!"
SectionEnd

; Function called at installer start
Function .onInit
  ; Welcome message
  MessageBox MB_YESNO "Welcome to ${APP_NAME} Setup!$\r$\n$\r$\nThis will install the Property Rental Management System on your computer.$\r$\n$\r$\nDo you want to continue?" IDYES +2
  Abort
FunctionEnd

; Function called on successful installation
Function .onInstSuccess
  ; Success message
  MessageBox MB_YESNO "${APP_NAME} has been installed successfully!$\r$\n$\r$\nWould you like to run the application now?" IDNO +2
  ExecShell "open" "$INSTDIR\${APP_EXE}"
FunctionEnd
