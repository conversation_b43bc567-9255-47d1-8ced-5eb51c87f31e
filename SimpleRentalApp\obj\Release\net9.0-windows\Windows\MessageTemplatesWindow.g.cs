﻿#pragma checksum "..\..\..\..\Windows\MessageTemplatesWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "F42C94FE6519B950A21EA0A9CA91D01E84256F75"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleRentalApp.Windows {
    
    
    /// <summary>
    /// MessageTemplatesWindow
    /// </summary>
    public partial class MessageTemplatesWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 27 "..\..\..\..\Windows\MessageTemplatesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddTemplateButton;
        
        #line default
        #line hidden
        
        
        #line 37 "..\..\..\..\Windows\MessageTemplatesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditTemplateButton;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\..\Windows\MessageTemplatesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeleteTemplateButton;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\Windows\MessageTemplatesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid TemplatesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\Windows\MessageTemplatesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TemplateNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\..\Windows\MessageTemplatesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TemplateTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 121 "..\..\..\..\Windows\MessageTemplatesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AvailableVariablesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\..\..\Windows\MessageTemplatesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TemplateContentTextBox;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\Windows\MessageTemplatesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IsActiveCheckBox;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\..\Windows\MessageTemplatesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IsDefaultCheckBox;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\..\Windows\MessageTemplatesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\Windows\MessageTemplatesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleRentalApp;V1.0.0.0;component/windows/messagetemplateswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\MessageTemplatesWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AddTemplateButton = ((System.Windows.Controls.Button)(target));
            
            #line 35 "..\..\..\..\Windows\MessageTemplatesWindow.xaml"
            this.AddTemplateButton.Click += new System.Windows.RoutedEventHandler(this.AddTemplateButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.EditTemplateButton = ((System.Windows.Controls.Button)(target));
            
            #line 45 "..\..\..\..\Windows\MessageTemplatesWindow.xaml"
            this.EditTemplateButton.Click += new System.Windows.RoutedEventHandler(this.EditTemplateButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.DeleteTemplateButton = ((System.Windows.Controls.Button)(target));
            
            #line 55 "..\..\..\..\Windows\MessageTemplatesWindow.xaml"
            this.DeleteTemplateButton.Click += new System.Windows.RoutedEventHandler(this.DeleteTemplateButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.TemplatesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 79 "..\..\..\..\Windows\MessageTemplatesWindow.xaml"
            this.TemplatesDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.TemplatesDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.TemplateNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.TemplateTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 7:
            this.AvailableVariablesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.TemplateContentTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.IsActiveCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 10:
            this.IsDefaultCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 11:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 165 "..\..\..\..\Windows\MessageTemplatesWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 176 "..\..\..\..\Windows\MessageTemplatesWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

