<Window x:Class="SimpleRentalApp.Windows.CleaningTaxReceiptSelectionDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="اختيار وصل ضريبة النظافة للطباعة" 
        Height="500" 
        Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">

    <Window.Resources>
        <Style TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="5"/>
        </Style>
        
        <Style TargetType="Button">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>
        
        <Style TargetType="DataGrid">
            <Setter Property="AutoGenerateColumns" Value="False"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="CanUserDeleteRows" Value="False"/>
            <Setter Property="IsReadOnly" Value="True"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
            <Setter Property="FontSize" Value="12"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" 
                Background="#9C27B0" 
                CornerRadius="8" 
                Padding="20,15" 
                Margin="0,0,0,20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="🖨️" FontSize="24" Margin="0,0,10,0" VerticalAlignment="Center"/>
                <TextBlock Text="اختيار وصل ضريبة النظافة للطباعة" 
                          FontSize="18" 
                          FontWeight="Bold" 
                          Foreground="White" 
                          VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Property Info -->
        <Border Grid.Row="1" 
                Background="#F8F9FA" 
                BorderBrush="#E0E0E0"
                BorderThickness="1"
                CornerRadius="8" 
                Padding="15" 
                Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" Grid.Column="0" Text="🏢 المحل:" FontWeight="Bold" Margin="0,0,10,0"/>
                <TextBlock Grid.Row="0" Grid.Column="1" Name="PropertyNameText" FontWeight="Bold" Foreground="#2E7D32"/>
                
                <TextBlock Grid.Row="1" Grid.Column="0" Text="📍 العنوان:" FontWeight="Bold" Margin="0,0,10,0"/>
                <TextBlock Grid.Row="1" Grid.Column="1" Name="PropertyAddressText" Foreground="#666" TextWrapping="Wrap"/>
            </Grid>
        </Border>

        <!-- Payments List -->
        <Border Grid.Row="2" 
                BorderBrush="#E0E0E0"
                BorderThickness="1"
                CornerRadius="8">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <Border Grid.Row="0" 
                        Background="#2E7D32" 
                        CornerRadius="8,8,0,0" 
                        Padding="15,10">
                    <TextBlock Text="📋 دفعات ضريبة النظافة المدفوعة" 
                              FontWeight="Bold" 
                              Foreground="White" 
                              FontSize="16"/>
                </Border>
                
                <DataGrid Grid.Row="1" 
                         Name="PaymentsDataGrid"
                         Margin="10"
                         SelectionChanged="PaymentsDataGrid_SelectionChanged">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="تاريخ الدفع" 
                                          Binding="{Binding PaymentDate, StringFormat=dd/MM/yyyy}" 
                                          Width="120"/>
                        <DataGridTextColumn Header="المبلغ" 
                                          Binding="{Binding CleaningTaxAmount, StringFormat=N0}" 
                                          Width="100"/>
                        <DataGridTextColumn Header="طريقة الدفع" 
                                          Binding="{Binding PaymentMethod}" 
                                          Width="120"/>
                        <DataGridTextColumn Header="ملاحظات" 
                                          Binding="{Binding Notes}" 
                                          Width="*"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>

        <!-- Buttons -->
        <StackPanel Grid.Row="3"
                   Orientation="Horizontal"
                   HorizontalAlignment="Center"
                   Margin="0,20,0,0">
            <Button Name="PreviewButton"
                   Content="👁️ معاينة قبل الطباعة"
                   Background="#2196F3"
                   Foreground="White"
                   IsEnabled="False"
                   Click="PreviewButton_Click"
                   MinWidth="150"
                   Margin="5"/>
            <Button Name="PrintButton"
                   Content="🖨️ طباعة مباشرة"
                   Background="#4CAF50"
                   Foreground="White"
                   IsEnabled="False"
                   Click="PrintButton_Click"
                   MinWidth="130"
                   Margin="5"/>
            <Button Name="CancelButton"
                   Content="❌ إلغاء"
                   Background="#F44336"
                   Foreground="White"
                   Click="CancelButton_Click"
                   MinWidth="100"
                   Margin="5"/>
        </StackPanel>
    </Grid>
</Window>
