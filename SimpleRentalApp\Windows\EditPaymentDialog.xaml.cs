using System;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using SimpleRentalApp.Models;
using SimpleRentalApp.Services;

namespace SimpleRentalApp.Windows
{
    public partial class EditPaymentDialog : Window
    {
        private readonly Payment _payment;
        private readonly Property _property;

        public bool IsDataChanged { get; private set; } = false;

        public EditPaymentDialog(Payment payment, Property property)
        {
            InitializeComponent();
            _payment = payment;
            _property = property;
            
            LoadPaymentData();
        }

        private void LoadPaymentData()
        {
            try
            {
                // Load payment data into form fields
                CleaningTaxAmountTextBox.Text = _payment.CleaningTaxAmount.ToString("F2");
                PaymentDatePicker.SelectedDate = _payment.PaymentDate;
                ReceiptNumberTextBox.Text = _payment.ReceiptNumber ?? "";
                NotesTextBox.Text = _payment.Notes ?? "";
                CreatedByTextBox.Text = "النظام"; // حقل ثابت

                // Set payment method
                var paymentMethod = _payment.PaymentMethod ?? "نقداً";
                var methodItem = PaymentMethodComboBox.Items.Cast<ComboBoxItem>()
                    .FirstOrDefault(item => item.Content.ToString() == paymentMethod);
                if (methodItem != null)
                {
                    PaymentMethodComboBox.SelectedItem = methodItem;
                }

                // Set status
                var statusItem = StatusComboBox.Items.Cast<ComboBoxItem>()
                    .FirstOrDefault(item => item.Tag.ToString() == _payment.Status.ToString());
                if (statusItem != null)
                {
                    StatusComboBox.SelectedItem = statusItem;
                }

                // Update window title
                Title = $"تعديل دفعة - {_property.Name}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الدفعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validate input
                if (!ValidateInput())
                    return;

                // Update payment object
                _payment.CleaningTaxAmount = decimal.Parse(CleaningTaxAmountTextBox.Text);
                _payment.PaymentDate = PaymentDatePicker.SelectedDate;
                _payment.ReceiptNumber = ReceiptNumberTextBox.Text?.Trim();
                _payment.PaymentMethod = ((ComboBoxItem)PaymentMethodComboBox.SelectedItem)?.Content.ToString();
                _payment.Notes = NotesTextBox.Text?.Trim();
                _payment.UpdatedDate = DateTime.Now;

                // Update status
                var selectedStatusItem = (ComboBoxItem)StatusComboBox.SelectedItem;
                if (selectedStatusItem?.Tag != null)
                {
                    if (Enum.TryParse<PaymentStatus>(selectedStatusItem.Tag.ToString(), out var status))
                    {
                        _payment.Status = status;
                    }
                }

                // Validate receipt number uniqueness if provided
                if (!string.IsNullOrEmpty(_payment.ReceiptNumber))
                {
                    if (await IsReceiptNumberDuplicate(_payment.ReceiptNumber, _payment.Id))
                    {
                        ShowValidationMessage("رقم التوصيل مستخدم مسبقاً. يرجى اختيار رقم آخر.");
                        return;
                    }
                }

                // Save to database
                await DatabaseService.Instance.UpdatePaymentAsync(_payment);

                IsDataChanged = true;
                MessageBox.Show("تم حفظ التعديلات بنجاح", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التعديلات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            // Clear previous validation message
            HideValidationMessage();

            // Validate cleaning tax amount
            if (!decimal.TryParse(CleaningTaxAmountTextBox.Text, out var amount) || amount <= 0)
            {
                ShowValidationMessage("يرجى إدخال مبلغ صحيح أكبر من صفر");
                CleaningTaxAmountTextBox.Focus();
                return false;
            }

            // Validate payment date
            if (!PaymentDatePicker.SelectedDate.HasValue)
            {
                ShowValidationMessage("يرجى اختيار تاريخ الدفع");
                PaymentDatePicker.Focus();
                return false;
            }

            // Validate payment date is not in future
            if (PaymentDatePicker.SelectedDate.Value.Date > DateTime.Now.Date)
            {
                ShowValidationMessage("لا يمكن أن يكون تاريخ الدفع في المستقبل");
                PaymentDatePicker.Focus();
                return false;
            }

            // Validate receipt number format if provided
            var receiptNumber = ReceiptNumberTextBox.Text?.Trim();
            if (!string.IsNullOrEmpty(receiptNumber))
            {
                if (receiptNumber.Length < 3 || receiptNumber.Length > 50)
                {
                    ShowValidationMessage("رقم التوصيل يجب أن يكون بين 3 و 50 حرف");
                    ReceiptNumberTextBox.Focus();
                    return false;
                }

                // Check for invalid characters
                if (receiptNumber.Any(c => !char.IsLetterOrDigit(c) && c != '-' && c != '_'))
                {
                    ShowValidationMessage("رقم التوصيل يجب أن يحتوي على أحرف وأرقام فقط (يمكن استخدام - و _)");
                    ReceiptNumberTextBox.Focus();
                    return false;
                }
            }

            return true;
        }

        private async Task<bool> IsReceiptNumberDuplicate(string receiptNumber, int excludePaymentId)
        {
            try
            {
                var allPayments = await DatabaseService.Instance.GetPaymentsAsync();
                return allPayments.Any(p => p.Id != excludePaymentId && 
                                          !string.IsNullOrEmpty(p.ReceiptNumber) &&
                                          p.ReceiptNumber.Equals(receiptNumber, StringComparison.OrdinalIgnoreCase));
            }
            catch
            {
                return false;
            }
        }

        private void ShowValidationMessage(string message)
        {
            ValidationMessageTextBlock.Text = message;
            ValidationMessageTextBlock.Visibility = Visibility.Visible;
        }

        private void HideValidationMessage()
        {
            ValidationMessageTextBlock.Visibility = Visibility.Collapsed;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        // Auto-format amount input
        private void CleaningTaxAmountTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (decimal.TryParse(CleaningTaxAmountTextBox.Text, out var amount))
            {
                CleaningTaxAmountTextBox.Text = amount.ToString("F2");
            }
        }

        // Auto-generate receipt number if empty
        private void ReceiptNumberTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(ReceiptNumberTextBox.Text) && PaymentDatePicker.SelectedDate.HasValue)
            {
                var suggestedNumber = GenerateSuggestedReceiptNumber();
                ReceiptNumberTextBox.Text = suggestedNumber;
            }
        }

        private string GenerateSuggestedReceiptNumber()
        {
            try
            {
                var date = PaymentDatePicker.SelectedDate ?? DateTime.Now;
                var propertyCode = _property.Name.Length >= 3 ? 
                    _property.Name.Substring(0, 3).ToUpper() : 
                    _property.Name.ToUpper();
                
                return $"CT{date.Year}-{propertyCode}-{_payment.Id:D3}";
            }
            catch
            {
                return $"CT{DateTime.Now.Year}-{_payment.Id:D6}";
            }
        }
    }
}
