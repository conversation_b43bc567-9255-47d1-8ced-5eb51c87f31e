# سكريبت إنشاء مثبت تطبيق تدبير الكراء
# PowerShell Installer Creator Script

param(
    [string]$OutputPath = "تدبير_الكراء_مثبت_v1.0.0.exe"
)

Write-Host "🏠 إنشاء مثبت تطبيق تدبير الكراء..." -ForegroundColor Green

# التحقق من وجود الملفات المطلوبة
$requiredFiles = @(
    "dist\RentalManagement.exe",
    "dist\README.txt",
    "dist\دليل_التثبيت_والاستخدام.txt"
)

foreach ($file in $requiredFiles) {
    if (-not (Test-Path $file)) {
        Write-Error "الملف المطلوب غير موجود: $file"
        exit 1
    }
}

Write-Host "✅ جميع الملفات المطلوبة موجودة" -ForegroundColor Green

# إنشاء مجلد مؤقت للمثبت
$tempDir = "temp_installer"
if (Test-Path $tempDir) {
    Remove-Item $tempDir -Recurse -Force
}
New-Item -ItemType Directory -Path $tempDir | Out-Null

# نسخ الملفات إلى المجلد المؤقت
Write-Host "📦 نسخ الملفات..." -ForegroundColor Yellow
Copy-Item "dist\*" -Destination $tempDir -Recurse

# إنشاء سكريبت التثبيت
$installScript = @"
@echo off
chcp 65001 > nul
echo.
echo 🏠 مرحباً بك في مثبت تطبيق تدبير الكراء
echo ==========================================
echo.
echo المطور: حفيظ عبدو
echo الإصدار: 1.0.0
echo.
pause

set "INSTALL_DIR=%ProgramFiles%\تدبير الكراء"
echo 📁 مجلد التثبيت: %INSTALL_DIR%
echo.

if not exist "%INSTALL_DIR%" (
    echo 📂 إنشاء مجلد التثبيت...
    mkdir "%INSTALL_DIR%"
)

echo 📋 نسخ الملفات...
copy /Y "RentalManagement.exe" "%INSTALL_DIR%\"
copy /Y "README.txt" "%INSTALL_DIR%\"
copy /Y "دليل_التثبيت_والاستخدام.txt" "%INSTALL_DIR%\"

echo.
echo 🔗 إنشاء الاختصارات...

rem إنشاء اختصار على سطح المكتب
powershell -Command "& {`$WshShell = New-Object -comObject WScript.Shell; `$Shortcut = `$WshShell.CreateShortcut('%USERPROFILE%\Desktop\تدبير الكراء.lnk'); `$Shortcut.TargetPath = '%INSTALL_DIR%\RentalManagement.exe'; `$Shortcut.Save()}"

rem إنشاء اختصار في قائمة البداية
if not exist "%ProgramData%\Microsoft\Windows\Start Menu\Programs\تدبير الكراء" (
    mkdir "%ProgramData%\Microsoft\Windows\Start Menu\Programs\تدبير الكراء"
)
powershell -Command "& {`$WshShell = New-Object -comObject WScript.Shell; `$Shortcut = `$WshShell.CreateShortcut('%ProgramData%\Microsoft\Windows\Start Menu\Programs\تدبير الكراء\تدبير الكراء.lnk'); `$Shortcut.TargetPath = '%INSTALL_DIR%\RentalManagement.exe'; `$Shortcut.Save()}"

echo.
echo ✅ تم تثبيت التطبيق بنجاح!
echo.
echo 🚀 يمكنك الآن تشغيل التطبيق من:
echo    - سطح المكتب
echo    - قائمة البداية
echo    - أو مباشرة من: %INSTALL_DIR%\RentalManagement.exe
echo.
echo 📖 لمزيد من المعلومات، راجع ملف دليل التثبيت والاستخدام
echo.
pause

rem تشغيل التطبيق بعد التثبيت
set /p "run=هل تريد تشغيل التطبيق الآن؟ (y/n): "
if /i "%run%"=="y" (
    start "" "%INSTALL_DIR%\RentalManagement.exe"
)

echo.
echo 🎉 شكراً لاستخدام تطبيق تدبير الكراء!
echo © 2025 حفيظ عبدو - جميع الحقوق محفوظة
echo.
pause
"@

# حفظ سكريبت التثبيت
$installScript | Out-File -FilePath "$tempDir\install.bat" -Encoding UTF8

# إنشاء سكريبت إلغاء التثبيت
$uninstallScript = @"
@echo off
chcp 65001 > nul
echo.
echo 🗑️ إلغاء تثبيت تطبيق تدبير الكراء
echo ================================
echo.

set "INSTALL_DIR=%ProgramFiles%\تدبير الكراء"

echo ⚠️ تحذير: سيتم حذف التطبيق وجميع الاختصارات
echo.
set /p "confirm=هل أنت متأكد من إلغاء التثبيت؟ (y/n): "
if /i not "%confirm%"=="y" (
    echo تم إلغاء العملية.
    pause
    exit /b
)

echo.
echo 🗂️ حذف الملفات...
if exist "%INSTALL_DIR%" (
    rmdir /s /q "%INSTALL_DIR%"
    echo ✅ تم حذف ملفات التطبيق
)

echo.
echo 🔗 حذف الاختصارات...
if exist "%USERPROFILE%\Desktop\تدبير الكراء.lnk" (
    del "%USERPROFILE%\Desktop\تدبير الكراء.lnk"
    echo ✅ تم حذف اختصار سطح المكتب
)

if exist "%ProgramData%\Microsoft\Windows\Start Menu\Programs\تدبير الكراء" (
    rmdir /s /q "%ProgramData%\Microsoft\Windows\Start Menu\Programs\تدبير الكراء"
    echo ✅ تم حذف اختصارات قائمة البداية
)

echo.
echo ✅ تم إلغاء تثبيت التطبيق بنجاح!
echo.
echo 💾 ملاحظة: ملفات قاعدة البيانات والنسخ الاحتياطية لم يتم حذفها
echo يمكنك حذفها يدوياً إذا كنت لا تحتاجها
echo.
pause
"@

# حفظ سكريبت إلغاء التثبيت
$uninstallScript | Out-File -FilePath "$tempDir\uninstall.bat" -Encoding UTF8

Write-Host "✅ تم إنشاء سكريبتات التثبيت" -ForegroundColor Green

# إنشاء ملف معلومات المثبت
$installerInfo = @"
🏠 مثبت تطبيق تدبير الكراء
==========================

📋 محتويات المثبت:
- RentalManagement.exe (التطبيق الرئيسي)
- README.txt (معلومات أساسية)
- دليل_التثبيت_والاستخدام.txt (دليل شامل)
- install.bat (سكريبت التثبيت)
- uninstall.bat (سكريبت إلغاء التثبيت)

🚀 طريقة التثبيت:
1. فك ضغط هذا الملف
2. تشغيل install.bat كمدير (Run as Administrator)
3. اتبع التعليمات على الشاشة

🗑️ إلغاء التثبيت:
- تشغيل uninstall.bat من مجلد التثبيت
- أو من قائمة البداية

⚠️ متطلبات:
- Windows 10 أو أحدث
- صلاحيات المدير للتثبيت

📞 الدعم:
- المطور: حفيظ عبدو
- البريد: <EMAIL>

© 2025 حفيظ عبدو - جميع الحقوق محفوظة
"@

$installerInfo | Out-File -FilePath "$tempDir\معلومات_المثبت.txt" -Encoding UTF8

Write-Host "📄 تم إنشاء ملف معلومات المثبت" -ForegroundColor Green

# إنشاء حزمة ZIP للمثبت
Write-Host "🗜️ إنشاء حزمة المثبت..." -ForegroundColor Yellow
$zipPath = "تدبير_الكراء_مثبت_كامل_v1.0.0.zip"
if (Test-Path $zipPath) {
    Remove-Item $zipPath -Force
}

Compress-Archive -Path "$tempDir\*" -DestinationPath $zipPath -Force

# تنظيف المجلد المؤقت
Remove-Item $tempDir -Recurse -Force

Write-Host "🎉 تم إنشاء المثبت بنجاح!" -ForegroundColor Green
Write-Host "📦 اسم الملف: $zipPath" -ForegroundColor Cyan
Write-Host "📁 الحجم: $((Get-Item $zipPath).Length / 1MB) MB" -ForegroundColor Cyan

Write-Host "`n✅ المثبت جاهز للتوزيع!" -ForegroundColor Green
Write-Host "🔗 يمكن للمستخدمين فك الضغط وتشغيل install.bat" -ForegroundColor Yellow
