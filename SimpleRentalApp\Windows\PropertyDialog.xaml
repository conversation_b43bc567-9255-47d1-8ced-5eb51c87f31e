<Window x:Class="SimpleRentalApp.Windows.PropertyDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة/تعديل محل"
        Height="650" Width="700"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        Background="White"
        ResizeMode="NoResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Title -->
        <TextBlock Grid.Row="0" 
                   Text="🏢 بيانات المحل"
                   FontSize="24" 
                   FontWeight="Bold"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,20"
                   Foreground="DarkBlue"/>

        <!-- Form -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Property Name -->
                <TextBlock Text="اسم المحل *" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox Name="NameTextBox" 
                         Padding="10"
                         FontSize="14"
                         Margin="0,0,0,15"
                         BorderBrush="Gray"
                         BorderThickness="1"/>

                <!-- Address -->
                <TextBlock Text="العنوان *" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox Name="AddressTextBox" 
                         Padding="10"
                         FontSize="14"
                         Height="60"
                         TextWrapping="Wrap"
                         AcceptsReturn="True"
                         Margin="0,0,0,15"
                         BorderBrush="Gray"
                         BorderThickness="1"/>

                <!-- Property Type -->
                <TextBlock Text="نوع المحل *" FontWeight="Bold" Margin="0,0,0,5"/>
                <ComboBox Name="TypeComboBox" 
                          Padding="10"
                          FontSize="14"
                          Margin="0,0,0,15"
                          BorderBrush="Gray"
                          BorderThickness="1">
                    <ComboBoxItem Content="شقة" Tag="Apartment"/>
                    <ComboBoxItem Content="محل تجاري" Tag="Shop"/>
                    <ComboBoxItem Content="مكتب" Tag="Office"/>
                    <ComboBoxItem Content="فيلا" Tag="Villa"/>
                </ComboBox>

                <!-- Status -->
                <TextBlock Text="حالة المحل" FontWeight="Bold" Margin="0,0,0,5"/>
                <ComboBox Name="StatusComboBox" 
                          Padding="10"
                          FontSize="14"
                          Margin="0,0,0,15"
                          BorderBrush="Gray"
                          BorderThickness="1">
                    <ComboBoxItem Content="متاح" Tag="Available" IsSelected="True"/>
                    <ComboBoxItem Content="مكتراة" Tag="Rented"/>
                    <ComboBoxItem Content="صيانة" Tag="Maintenance"/>
                </ComboBox>

                <!-- Monthly Rent -->
                <TextBlock Text="الإيجار الشهري (درهم) *" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox Name="MonthlyRentTextBox" 
                         Padding="10"
                         FontSize="14"
                         Margin="0,0,0,15"
                         BorderBrush="Gray"
                         BorderThickness="1"/>

                <!-- Cleaning Tax Payment Option -->
                <TextBlock Text="🧹 خيار دفع ضريبة النظافة (10.5% من الإيجار السنوي) *" FontWeight="Bold" Margin="0,0,0,5" Foreground="DarkBlue"/>
                <ComboBox Name="CleaningTaxPaymentComboBox"
                          Padding="10"
                          FontSize="14"
                          Margin="0,0,0,15"
                          BorderBrush="Gray"
                          BorderThickness="1"
                          SelectionChanged="CleaningTaxPaymentComboBox_SelectionChanged">
                    <ComboBoxItem Content="💰 شهرياً مع الإيجار (12 دفعة)" Tag="1" IsSelected="True"/>
                    <ComboBoxItem Content="📅 نصف سنوي (دفعتين في السنة)" Tag="2"/>
                    <ComboBoxItem Content="📆 كل 4 أشهر (3 دفعات في السنة)" Tag="3"/>
                    <ComboBoxItem Content="📋 ربع سنوي (4 دفعات في السنة)" Tag="4"/>
                    <ComboBoxItem Content="📊 سنوياً كاملة (دفعة واحدة)" Tag="12"/>
                </ComboBox>

                <!-- Cleaning Tax Info -->
                <Border Background="LightCyan"
                        BorderBrush="DarkCyan"
                        BorderThickness="2"
                        Padding="15"
                        Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="📊 معلومات ضريبة النظافة"
                                   FontWeight="Bold"
                                   FontSize="14"
                                   Foreground="DarkCyan"
                                   Margin="0,0,0,10"/>
                        <TextBlock Name="CleaningTaxInfoTextBlock"
                                   Text="ضريبة النظافة: سيتم حسابها تلقائياً (10.5% من الإيجار السنوي)"
                                   FontSize="12"
                                   Foreground="DarkBlue"
                                   TextWrapping="Wrap"/>
                    </StackPanel>
                </Border>

                <!-- Grid for Rooms, Bathrooms, Area -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Rooms -->
                    <StackPanel Grid.Column="0" Margin="0,0,5,0">
                        <TextBlock Text="عدد الغرف" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox Name="RoomsTextBox" 
                                 Padding="10"
                                 FontSize="14"
                                 BorderBrush="Gray"
                                 BorderThickness="1"/>
                    </StackPanel>

                    <!-- Bathrooms -->
                    <StackPanel Grid.Column="1" Margin="5,0">
                        <TextBlock Text="عدد الحمامات" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox Name="BathroomsTextBox" 
                                 Padding="10"
                                 FontSize="14"
                                 BorderBrush="Gray"
                                 BorderThickness="1"/>
                    </StackPanel>

                    <!-- Area -->
                    <StackPanel Grid.Column="2" Margin="5,0,0,0">
                        <TextBlock Text="المساحة (م²)" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox Name="AreaTextBox" 
                                 Padding="10"
                                 FontSize="14"
                                 BorderBrush="Gray"
                                 BorderThickness="1"/>
                    </StackPanel>
                </Grid>

                <!-- Description -->
                <TextBlock Text="الوصف" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox Name="DescriptionTextBox" 
                         Padding="10"
                         FontSize="14"
                         Height="80"
                         TextWrapping="Wrap"
                         AcceptsReturn="True"
                         VerticalScrollBarVisibility="Auto"
                         Margin="0,0,0,15"
                         BorderBrush="Gray"
                         BorderThickness="1"/>

                <!-- Customer (if rented) -->
                <TextBlock Text="الكاري (في حالة الكراء)" FontWeight="Bold" Margin="0,0,0,5"/>
                <ComboBox Name="CustomerComboBox" 
                          Padding="10"
                          FontSize="14"
                          Margin="0,0,0,15"
                          BorderBrush="Gray"
                          BorderThickness="1"
                          DisplayMemberPath="FullName"
                          SelectedValuePath="Id"/>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <StackPanel Grid.Row="2"
                    Orientation="Horizontal"
                    HorizontalAlignment="Center"
                    Margin="0,20,0,0">
            <Button Name="SaveButton"
                    Content="💾 حفظ"
                    Padding="20,10"
                    Margin="0,0,10,0"
                    Background="Green"
                    Foreground="White"
                    FontWeight="Bold"
                    Click="SaveButton_Click"/>

            <Button Name="CancelButton"
                    Content="❌ إلغاء"
                    Padding="20,10"
                    Margin="10,0,0,0"
                    Background="Gray"
                    Foreground="White"
                    Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
