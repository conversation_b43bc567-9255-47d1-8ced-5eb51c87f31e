﻿#pragma checksum "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "F55DC0E5746B87E8C2E6C95168FB10E236F63E70"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleRentalApp.Windows {
    
    
    /// <summary>
    /// WhatsAppNotificationsWindow
    /// </summary>
    public partial class WhatsAppNotificationsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 271 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button GenerateMonthlyRentButton;
        
        #line default
        #line hidden
        
        
        #line 278 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button GenerateCleaningTaxButton;
        
        #line default
        #line hidden
        
        
        #line 285 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button GenerateLateRemindersButton;
        
        #line default
        #line hidden
        
        
        #line 305 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DaysLateTextBox;
        
        #line default
        #line hidden
        
        
        #line 320 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SendAllButton;
        
        #line default
        #line hidden
        
        
        #line 327 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 334 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TemplatesButton;
        
        #line default
        #line hidden
        
        
        #line 378 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TypeFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 397 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 415 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 436 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid NotificationsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 599 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 616 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 633 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PendingCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 650 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SentCountTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleRentalApp;V1.0.0.0;component/windows/whatsappnotificationswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.GenerateMonthlyRentButton = ((System.Windows.Controls.Button)(target));
            
            #line 276 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
            this.GenerateMonthlyRentButton.Click += new System.Windows.RoutedEventHandler(this.GenerateMonthlyRentButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.GenerateCleaningTaxButton = ((System.Windows.Controls.Button)(target));
            
            #line 283 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
            this.GenerateCleaningTaxButton.Click += new System.Windows.RoutedEventHandler(this.GenerateCleaningTaxButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.GenerateLateRemindersButton = ((System.Windows.Controls.Button)(target));
            
            #line 290 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
            this.GenerateLateRemindersButton.Click += new System.Windows.RoutedEventHandler(this.GenerateLateRemindersButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.DaysLateTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.SendAllButton = ((System.Windows.Controls.Button)(target));
            
            #line 325 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
            this.SendAllButton.Click += new System.Windows.RoutedEventHandler(this.SendAllButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 332 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.TemplatesButton = ((System.Windows.Controls.Button)(target));
            
            #line 339 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
            this.TemplatesButton.Click += new System.Windows.RoutedEventHandler(this.TemplatesButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.TypeFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 382 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
            this.TypeFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.TypeFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.StatusFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 401 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
            this.StatusFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.StatusFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 420 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
            this.SearchTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.SearchTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 421 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
            this.SearchTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.SearchTextBox_LostFocus);
            
            #line default
            #line hidden
            
            #line 422 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.NotificationsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 18:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.CountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.PendingCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.SentCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 12:
            
            #line 527 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SendRentNotificationButton_Click);
            
            #line default
            #line hidden
            break;
            case 13:
            
            #line 535 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SendCleaningNotificationButton_Click);
            
            #line default
            #line hidden
            break;
            case 14:
            
            #line 543 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SendLateReminderButton_Click);
            
            #line default
            #line hidden
            break;
            case 15:
            
            #line 552 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SendNotificationButton_Click);
            
            #line default
            #line hidden
            break;
            case 16:
            
            #line 561 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PreviewNotificationButton_Click);
            
            #line default
            #line hidden
            break;
            case 17:
            
            #line 569 "..\..\..\..\..\Windows\WhatsAppNotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteNotificationButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

