; Rental Management System Installer 
[Setup] 
AppName=Rental Management System 
AppVersion=1.0.0 
AppPublisher=<PERSON><PERSON>d Abdo 
AppPublisherURL=https://github.com/hafidabdo 
AppSupportURL=https://github.com/hafidabdo 
AppUpdatesURL=https://github.com/hafidabdo 
DefaultDirName={autopf}\RentalManagement 
DefaultGroupName=Rental Management System 
AllowNoIcons=yes 
LicenseFile= 
OutputDir=. 
OutputBaseFilename=RentalManagementSetup 
SetupIconFile= 
Compression=lzma 
SolidCompression=yes 
WizardStyle=modern 
ArchitecturesAllowed=x64 
ArchitecturesInstallIn64BitMode=x64 
 
[Languages] 
Name: "english"; MessagesFile: "compiler:Default.isl" 
Name: "arabic"; MessagesFile: "compiler:Languages\Arabic.isl" 
 
[Tasks] 
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked 
 
[Files] 
Source: "app\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs 
Source: "docs\*"; DestDir: "{app}\docs"; Flags: ignoreversion recursesubdirs createallsubdirs 
 
[Icons] 
Name: "{group}\Rental Management System"; Filename: "{app}\SimpleRentalApp.exe" 
Name: "{group}\{cm:UninstallProgram,Rental Management System}"; Filename: "{uninstallexe}" 
Name: "{autodesktop}\Rental Management System"; Filename: "{app}\SimpleRentalApp.exe"; Tasks: desktopicon 
 
[Run] 
Filename: "{app}\SimpleRentalApp.exe"; Description: "{cm:LaunchProgram,Rental Management System}"; Flags: nowait postinstall skipifsilent 
