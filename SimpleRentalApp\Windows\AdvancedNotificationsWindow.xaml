<Window x:Class="SimpleRentalApp.Windows.AdvancedNotificationsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="🔔 نظام الإشعارات المتطور - تدبير الكراء"
        Height="800" Width="1400"
        WindowStartupLocation="CenterScreen"
        Background="#F5F5F5"
        FontFamily="Segoe UI">

    <Window.Resources>
        <!-- Local Color Resources (Fallback) -->
        <SolidColorBrush x:Key="LocalPrimaryColor" Color="#2E8BC0"/>
        <SolidColorBrush x:Key="LocalPrimaryDarkColor" Color="#1E5F8C"/>
        <SolidColorBrush x:Key="LocalSecondaryColor" Color="#3A506B"/>
        <SolidColorBrush x:Key="LocalAccentColor" Color="#5DADE2"/>
        <SolidColorBrush x:Key="LocalBackgroundColor" Color="#F5F5F5"/>
        <SolidColorBrush x:Key="LocalSurfaceColor" Color="White"/>
        <SolidColorBrush x:Key="LocalCardBackgroundColor" Color="White"/>
        <SolidColorBrush x:Key="LocalBorderColor" Color="#E0E0E0"/>
        <SolidColorBrush x:Key="LocalTextColor" Color="#333333"/>
        <SolidColorBrush x:Key="LocalTextPrimaryColor" Color="#2C3E50"/>
        <SolidColorBrush x:Key="LocalTextSecondaryColor" Color="#7F8C8D"/>
        <SolidColorBrush x:Key="LocalSuccessColor" Color="#28A745"/>
        <SolidColorBrush x:Key="LocalWarningColor" Color="#FFC107"/>
        <SolidColorBrush x:Key="LocalDangerColor" Color="#DC3545"/>
        <SolidColorBrush x:Key="LocalInfoColor" Color="#17A2B8"/>

        <!-- Backward Compatibility Aliases -->
        <SolidColorBrush x:Key="PrimaryColor" Color="#2E8BC0"/>
        <SolidColorBrush x:Key="PrimaryDarkColor" Color="#1E5F8C"/>
        <SolidColorBrush x:Key="SecondaryColor" Color="#3A506B"/>
        <SolidColorBrush x:Key="AccentColor" Color="#5DADE2"/>
        <SolidColorBrush x:Key="BackgroundColor" Color="#F5F5F5"/>
        <SolidColorBrush x:Key="SurfaceColor" Color="White"/>
        <SolidColorBrush x:Key="CardBackgroundColor" Color="White"/>
        <SolidColorBrush x:Key="BorderColor" Color="#E0E0E0"/>
        <SolidColorBrush x:Key="TextColor" Color="#333333"/>
        <SolidColorBrush x:Key="TextPrimaryColor" Color="#2C3E50"/>
        <SolidColorBrush x:Key="TextSecondaryColor" Color="#7F8C8D"/>
        <SolidColorBrush x:Key="SuccessColor" Color="#28A745"/>
        <SolidColorBrush x:Key="WarningColor" Color="#FFC107"/>
        <SolidColorBrush x:Key="DangerColor" Color="#DC3545"/>
        <SolidColorBrush x:Key="InfoColor" Color="#17A2B8"/>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryColor}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource PrimaryDarkColor}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Action Button Styles -->
        <Style x:Key="SendButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#4CAF50"/>
        </Style>

        <Style x:Key="RetryButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#FF9800"/>
        </Style>

        <Style x:Key="CancelButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#F44336"/>
        </Style>

        <Style x:Key="WarningButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#FF9800"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style x:Key="GenerateButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#9C27B0"/>
        </Style>

        <Style x:Key="AccentButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#17A2B8"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource SurfaceColor}" CornerRadius="15" Padding="25" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="3" BlurRadius="15"/>
            </Border.Effect>
            <StackPanel>
                <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                    <TextBlock Text="🔔" FontSize="32" VerticalAlignment="Center" Margin="0,0,15,0"/>
                    <StackPanel>
                        <TextBlock Text="نظام الإشعارات المتطور" 
                                   FontSize="24" 
                                   FontWeight="Bold" 
                                   Foreground="{StaticResource TextPrimaryColor}"/>
                        <TextBlock Name="SubtitleText" 
                                   Text="إدارة شاملة لإشعارات الإيجار وضريبة النظافة والتأخير" 
                                   FontSize="14" 
                                   Foreground="{StaticResource TextSecondaryColor}" 
                                   Margin="0,5,0,0"/>
                    </StackPanel>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Generation Controls -->
        <Border Grid.Row="1" Background="{StaticResource SurfaceColor}" CornerRadius="12" Padding="20" Margin="0,0,0,15">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="10"/>
            </Border.Effect>
            <StackPanel>
                <TextBlock Text="🚀 توليد الإشعارات" FontSize="16" FontWeight="Bold" Margin="0,0,0,15" 
                           Foreground="{StaticResource TextPrimaryColor}"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <Button Grid.Column="0" Content="📅 إيجار شهري" Style="{StaticResource GenerateButtonStyle}"
                            Margin="0,0,10,0" Click="GenerateMonthlyRentBtn_Click"
                            ToolTip="توليد إشعارات الإيجار الشهري للعقود النشطة"/>
                    
                    <Button Grid.Column="1" Content="🧹 ضريبة النظافة" Style="{StaticResource GenerateButtonStyle}"
                            Margin="0,0,10,0" Click="GenerateCleaningTaxBtn_Click"
                            ToolTip="توليد إشعارات ضريبة النظافة السنوية"/>
                    
                    <Button Grid.Column="2" Content="⚠️ إشعارات التأخير" Style="{StaticResource RetryButtonStyle}"
                            Margin="0,0,10,0" Click="GenerateOverdueBtn_Click"
                            ToolTip="توليد إشعارات التأخير للمدفوعات المستحقة"/>
                    
                    <Button Grid.Column="3" Content="💰 تحديث المبالغ" Style="{StaticResource WarningButtonStyle}"
                            Margin="0,0,10,0" Click="UpdateAmountsBtn_Click"
                            ToolTip="تحديث مبالغ الإشعارات لتعكس الإيجار الحالي"/>

                    <Button Grid.Column="4" Content="🔄 تحديث تلقائي" Style="{StaticResource ModernButtonStyle}"
                            Margin="0,0,10,0" Click="AutoUpdateBtn_Click"
                            ToolTip="تحديث تلقائي لجميع أنواع الإشعارات"/>

                    <Button Grid.Column="5" Content="🤖 توليد تلقائي" Style="{StaticResource GenerateButtonStyle}"
                            Margin="0,0,10,0" Click="AutoGenerateBtn_Click"
                            ToolTip="توليد الإشعارات تلقائياً حسب العقود والدفعات المستحقة"/>

                    <Button Grid.Column="5" Content="⚙️ إعدادات الواتساب" Style="{StaticResource AccentButtonStyle}"
                            Margin="0,0,10,0" Click="WhatsAppSettingsBtn_Click"
                            ToolTip="إعدادات إرسال الواتساب"/>

                    <Button Grid.Column="6" Content="📝 قوالب الرسائل" Style="{StaticResource AccentButtonStyle}"
                            Margin="0,0,10,0" Click="MessageTemplatesBtn_Click"
                            ToolTip="إدارة قوالب رسائل الواتساب"/>

                    <Button Grid.Column="7" Content="📤 إرسال جماعي" Style="{StaticResource AccentButtonStyle}"
                            Margin="0,0,10,0" Click="BulkSendBtn_Click"
                            ToolTip="إرسال جميع الإشعارات المحددة"/>

                    <Button Grid.Column="8" Content="🌙 الوضع الليلي" Style="{StaticResource SecondaryButtonStyle}"
                            Margin="0,0,10,0" Click="ThemeToggleBtn_Click"
                            ToolTip="تبديل بين الوضع الليلي والنهاري"/>

                    <Button Grid.Column="9" Content="📊 إحصائيات" Style="{StaticResource ModernButtonStyle}"
                            Click="StatisticsBtn_Click"
                            ToolTip="عرض إحصائيات الإشعارات"/>
                </Grid>
            </StackPanel>
        </Border>

        <!-- Filters -->
        <Border Grid.Row="2" Background="{StaticResource SurfaceColor}" CornerRadius="12" Padding="20" Margin="0,0,0,15">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="10"/>
            </Border.Effect>
            <StackPanel>
                <TextBlock Text="🔍 التصفية والبحث" FontSize="16" FontWeight="Bold" Margin="0,0,0,15" 
                           Foreground="{StaticResource TextPrimaryColor}"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="150"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="150"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="النوع:" VerticalAlignment="Center" Margin="0,0,10,0" FontWeight="Medium"/>
                    <ComboBox Grid.Column="1" Name="TypeFilterComboBox" Margin="0,0,15,0"
                              SelectionChanged="TypeFilterComboBox_SelectionChanged"/>

                    <TextBlock Grid.Column="2" Text="الحالة:" VerticalAlignment="Center" Margin="0,0,10,0" FontWeight="Medium"/>
                    <ComboBox Grid.Column="3" Name="StatusFilterComboBox" Margin="0,0,15,0"
                              SelectionChanged="StatusFilterComboBox_SelectionChanged"/>

                    <TextBlock Grid.Column="4" Text="الأولوية:" VerticalAlignment="Center" Margin="0,0,10,0" FontWeight="Medium"/>
                    <ComboBox Grid.Column="5" Name="PriorityFilterComboBox" Margin="0,0,15,0"
                              SelectionChanged="PriorityFilterComboBox_SelectionChanged"/>

                    <TextBlock Grid.Column="6" Text="البحث:" VerticalAlignment="Center" Margin="0,0,10,0" FontWeight="Medium"/>
                    <TextBox Grid.Column="7" Name="SearchTextBox" Margin="0,0,15,0"
                             TextChanged="SearchTextBox_TextChanged"
                             Text="البحث بالاسم أو المحل..."
                             Foreground="Gray"
                             GotFocus="SearchTextBox_GotFocus"
                             LostFocus="SearchTextBox_LostFocus"/>

                    <Button Grid.Column="9" Content="☑️ تحديد الكل" Style="{StaticResource AccentButtonStyle}"
                            Margin="0,0,10,0" Click="SelectAllBtn_Click"/>

                    <Button Grid.Column="10" Content="🗑️ مسح الفلاتر" Style="{StaticResource ModernButtonStyle}"
                            Click="ClearFiltersBtn_Click"/>
                </Grid>
            </StackPanel>
        </Border>

        <!-- Notifications DataGrid -->
        <Border Grid.Row="3" Background="{StaticResource SurfaceColor}" CornerRadius="12" Padding="20">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="10"/>
            </Border.Effect>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                    <TextBlock Text="📋 قائمة الإشعارات" FontSize="16" FontWeight="Bold" 
                               Foreground="{StaticResource TextPrimaryColor}" VerticalAlignment="Center"/>
                    <TextBlock Name="CountTextBlock" Text="(0 إشعار)" FontSize="14" 
                               Foreground="{StaticResource TextSecondaryColor}" 
                               VerticalAlignment="Center" Margin="10,0,0,0"/>
                </StackPanel>

                <DataGrid Grid.Row="1" Name="NotificationsDataGrid"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="None"
                          HeadersVisibility="Column"
                          SelectionMode="Single"
                          AlternatingRowBackground="#F8F9FA"
                          RowBackground="White"
                          FontSize="13">
                    
                    <DataGrid.Columns>
                        <!-- Selection CheckBox -->
                        <DataGridCheckBoxColumn Header="تحديد" Binding="{Binding IsSelected}" Width="60"/>

                        <!-- Property Name -->
                        <DataGridTextColumn Header="المحل" Binding="{Binding PropertyName}" Width="150"/>
                        
                        <!-- Customer Name -->
                        <DataGridTextColumn Header="المكتري" Binding="{Binding CustomerName}" Width="150"/>
                        
                        <!-- Type -->
                        <DataGridTextColumn Header="النوع" Binding="{Binding TypeDisplay}" Width="120"/>
                        
                        <!-- Status -->
                        <DataGridTemplateColumn Header="الحالة" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border CornerRadius="12" Padding="8,4" HorizontalAlignment="Center">
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Status}" Value="Pending">
                                                        <Setter Property="Background" Value="#FFF3CD"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="Sent">
                                                        <Setter Property="Background" Value="#D4EDDA"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="Failed">
                                                        <Setter Property="Background" Value="#F8D7DA"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="Cancelled">
                                                        <Setter Property="Background" Value="#E2E3E5"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        <TextBlock Text="{Binding StatusDisplay}" FontSize="11" FontWeight="Medium"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        
                        <!-- Priority -->
                        <DataGridTextColumn Header="الأولوية" Binding="{Binding PriorityDisplay}" Width="80"/>
                        
                        <!-- Amount -->
                        <DataGridTextColumn Header="المبلغ" Binding="{Binding AmountDisplay}" Width="100"/>
                        
                        <!-- Due Date -->
                        <DataGridTextColumn Header="تاريخ الاستحقاق" Binding="{Binding DueDateDisplay}" Width="120"/>
                        
                        <!-- Sent Date -->
                        <DataGridTextColumn Header="تاريخ الإرسال" Binding="{Binding SentDateDisplay}" Width="120"/>
                        
                        <!-- Actions -->
                        <DataGridTemplateColumn Header="الإجراءات" Width="280">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <Button Content="📤" ToolTip="إرسال" Background="#4CAF50" Foreground="White"
                                                BorderThickness="0" Padding="6" Margin="1" FontSize="12"
                                                Click="SendNotificationBtn_Click" Tag="{Binding}"/>

                                        <Button Content="📝" ToolTip="تعديل القالب" Background="#17A2B8" Foreground="White"
                                                BorderThickness="0" Padding="6" Margin="1" FontSize="12"
                                                Click="EditTemplateBtn_Click" Tag="{Binding}"/>

                                        <Button Content="👁️" ToolTip="معاينة الرسالة" Background="#6F42C1" Foreground="White"
                                                BorderThickness="0" Padding="6" Margin="1" FontSize="12"
                                                Click="PreviewMessageBtn_Click" Tag="{Binding}"/>

                                        <Button Content="🔄" ToolTip="إعادة المحاولة" Background="#FF9800" Foreground="White"
                                                BorderThickness="0" Padding="6" Margin="1" FontSize="12"
                                                Click="RetryNotificationBtn_Click" Tag="{Binding}"/>

                                        <Button Content="✏️" ToolTip="تعديل" Background="#2196F3" Foreground="White"
                                                BorderThickness="0" Padding="6" Margin="1" FontSize="12"
                                                Click="EditNotificationBtn_Click" Tag="{Binding}"/>

                                        <Button Content="❌" ToolTip="إلغاء" Background="#F44336" Foreground="White"
                                                BorderThickness="0" Padding="6" Margin="1" FontSize="12"
                                                Click="CancelNotificationBtn_Click" Tag="{Binding}"/>

                                        <Button Content="🗑️" ToolTip="حذف" Background="#9E9E9E" Foreground="White"
                                                BorderThickness="0" Padding="6" Margin="1" FontSize="12"
                                                Click="DeleteNotificationBtn_Click" Tag="{Binding}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>

        <!-- Status Bar -->
        <Border Grid.Row="4" Background="{StaticResource AccentColor}" CornerRadius="8" Padding="15,10" Margin="0,15,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Name="StatusTextBlock" 
                           Text="جاهز - اختر الإجراء المطلوب" 
                           Foreground="White" FontWeight="Medium"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="آخر تحديث:" Foreground="White" Margin="0,0,5,0"/>
                    <TextBlock Name="LastUpdateTextBlock" Text="-" Foreground="White" FontWeight="Medium"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
