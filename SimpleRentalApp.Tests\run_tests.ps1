# سكريبت تشغيل اختبارات المزامنة السحابية
# Cloud Sync Tests Runner Script

Write-Host "=== بدء تشغيل اختبارات المزامنة السحابية ===" -ForegroundColor Green
Write-Host "Starting Cloud Sync Tests..." -ForegroundColor Green

# التحقق من وجود .NET SDK
try {
    $dotnetVersion = dotnet --version
    Write-Host "إصدار .NET SDK: $dotnetVersion" -ForegroundColor Cyan
} catch {
    Write-Host "خطأ: .NET SDK غير مثبت أو غير متاح في PATH" -ForegroundColor Red
    exit 1
}

# الانتقال إلى مجلد الاختبارات
$testProjectPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $testProjectPath

Write-Host "مجلد الاختبارات: $testProjectPath" -ForegroundColor Cyan

# استعادة الحزم
Write-Host "`n=== استعادة حزم NuGet ===" -ForegroundColor Yellow
dotnet restore
if ($LASTEXITCODE -ne 0) {
    Write-Host "فشل في استعادة الحزم" -ForegroundColor Red
    exit 1
}

# بناء المشروع
Write-Host "`n=== بناء مشروع الاختبارات ===" -ForegroundColor Yellow
dotnet build --no-restore
if ($LASTEXITCODE -ne 0) {
    Write-Host "فشل في بناء المشروع" -ForegroundColor Red
    exit 1
}

# تشغيل جميع الاختبارات
Write-Host "`n=== تشغيل جميع الاختبارات ===" -ForegroundColor Yellow
dotnet test --no-build --verbosity normal --logger "console;verbosity=detailed"

if ($LASTEXITCODE -eq 0) {
    Write-Host "`n=== اكتملت جميع الاختبارات بنجاح! ===" -ForegroundColor Green
} else {
    Write-Host "`n=== فشلت بعض الاختبارات ===" -ForegroundColor Red
}

# تشغيل اختبارات محددة
Write-Host "`n=== تشغيل اختبارات التشفير ===" -ForegroundColor Yellow
dotnet test --no-build --filter "FullyQualifiedName~EncryptionServiceTests" --verbosity normal

Write-Host "`n=== تشغيل اختبارات مدير المزامنة ===" -ForegroundColor Yellow
dotnet test --no-build --filter "FullyQualifiedName~SyncManagerTests" --verbosity normal

Write-Host "`n=== تشغيل اختبارات معالجة التعارضات ===" -ForegroundColor Yellow
dotnet test --no-build --filter "FullyQualifiedName~ConflictResolutionTests" --verbosity normal

Write-Host "`n=== تشغيل اختبارات الأمان والأداء ===" -ForegroundColor Yellow
dotnet test --no-build --filter "FullyQualifiedName~SecurityAndPerformanceTests" --verbosity normal

Write-Host "`n=== تشغيل اختبارات التكامل ===" -ForegroundColor Yellow
dotnet test --no-build --filter "FullyQualifiedName~IntegrationTests" --verbosity normal

# إنشاء تقرير التغطية
Write-Host "`n=== إنشاء تقرير تغطية الكود ===" -ForegroundColor Yellow
dotnet test --no-build --collect:"XPlat Code Coverage" --results-directory:"./TestResults"

if (Test-Path "./TestResults") {
    Write-Host "تم إنشاء تقرير التغطية في مجلد TestResults" -ForegroundColor Green
}

# إحصائيات الاختبارات
Write-Host "`n=== إحصائيات الاختبارات ===" -ForegroundColor Cyan
$testFiles = Get-ChildItem -Path "CloudSync" -Filter "*.cs" -Recurse
$totalTestFiles = $testFiles.Count
$totalLines = ($testFiles | Get-Content | Measure-Object -Line).Lines

Write-Host "عدد ملفات الاختبارات: $totalTestFiles" -ForegroundColor White
Write-Host "إجمالي أسطر الكود: $totalLines" -ForegroundColor White

# عرض ملخص الاختبارات
Write-Host "`n=== ملخص اختبارات المزامنة السحابية ===" -ForegroundColor Magenta
Write-Host "✓ اختبارات التشفير والأمان" -ForegroundColor Green
Write-Host "✓ اختبارات مدير المزامنة" -ForegroundColor Green
Write-Host "✓ اختبارات معالجة التعارضات" -ForegroundColor Green
Write-Host "✓ اختبارات الأداء" -ForegroundColor Green
Write-Host "✓ اختبارات التكامل" -ForegroundColor Green

Write-Host "`n=== انتهى تشغيل الاختبارات ===" -ForegroundColor Green

# العودة إلى المجلد الأصلي
Set-Location ..

Read-Host "اضغط Enter للخروج..."
