@echo off
chcp 65001 > nul
echo ========================================
echo    إنشاء مثبت تطبيق تدبير الكراء
echo ========================================
echo.

echo التحقق من وجود NSIS...
where makensis >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo خطأ: NSIS غير مثبت على النظام!
    echo يرجى تحميل وتثبيت NSIS من: https://nsis.sourceforge.io/
    echo.
    pause
    exit /b 1
)

echo التحقق من وجود الملفات المطلوبة...
if not exist "dist\تدبير_الكراء.exe" (
    echo خطأ: ملف التطبيق غير موجود!
    echo يرجى تشغيل publish.bat أولاً لنشر التطبيق
    echo.
    pause
    exit /b 1
)

if not exist "installer.nsi" (
    echo خطأ: ملف سكريبت المثبت غير موجود!
    echo.
    pause
    exit /b 1
)

echo.
echo إنشاء المثبت...
makensis installer.nsi

if %ERRORLEVEL% neq 0 (
    echo خطأ في إنشاء المثبت!
    pause
    exit /b 1
)

echo.
echo ========================================
echo تم إنشاء المثبت بنجاح!
echo ========================================
echo.
echo ملف المثبت: تدبير_الكراء_مثبت_v1.0.0.exe
echo.
echo يمكنك الآن توزيع هذا الملف للمستخدمين
echo.
pause
