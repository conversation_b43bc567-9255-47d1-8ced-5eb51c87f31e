@echo off
chcp 65001 > nul
echo ========================================
echo    Creating Rental Management Installer
echo ========================================
echo.

echo [1/7] Cleaning previous builds...
if exist "publish_installer" rmdir /s /q "publish_installer"
if exist "installer_output" rmdir /s /q "installer_output"
dotnet clean --configuration Release > nul

echo [2/7] Building optimized release...
dotnet publish --configuration Release --runtime win-x64 --self-contained true -p:PublishSingleFile=false -p:PublishTrimmed=false --output publish_installer
if errorlevel 1 (
    echo Error publishing application
    pause
    exit /b 1
)

echo [3/7] Creating installer directory structure...
mkdir installer_output
mkdir installer_output\app
mkdir installer_output\data
mkdir installer_output\docs

echo [4/7] Copying application files...
xcopy publish_installer\*.* installer_output\app\ /E /H /C /I > nul
copy README.md installer_output\docs\ > nul 2>&1

echo [5/7] Creating installer script...
echo ; Rental Management System Installer > installer_output\installer.iss
echo [Setup] >> installer_output\installer.iss
echo AppName=Rental Management System >> installer_output\installer.iss
echo AppVersion=1.0.0 >> installer_output\installer.iss
echo AppPublisher=Hafid Abdo >> installer_output\installer.iss
echo AppPublisherURL=https://github.com/hafidabdo >> installer_output\installer.iss
echo AppSupportURL=https://github.com/hafidabdo >> installer_output\installer.iss
echo AppUpdatesURL=https://github.com/hafidabdo >> installer_output\installer.iss
echo DefaultDirName={autopf}\RentalManagement >> installer_output\installer.iss
echo DefaultGroupName=Rental Management System >> installer_output\installer.iss
echo AllowNoIcons=yes >> installer_output\installer.iss
echo LicenseFile= >> installer_output\installer.iss
echo OutputDir=. >> installer_output\installer.iss
echo OutputBaseFilename=RentalManagementSetup >> installer_output\installer.iss
echo SetupIconFile= >> installer_output\installer.iss
echo Compression=lzma >> installer_output\installer.iss
echo SolidCompression=yes >> installer_output\installer.iss
echo WizardStyle=modern >> installer_output\installer.iss
echo ArchitecturesAllowed=x64 >> installer_output\installer.iss
echo ArchitecturesInstallIn64BitMode=x64 >> installer_output\installer.iss
echo. >> installer_output\installer.iss
echo [Languages] >> installer_output\installer.iss
echo Name: "english"; MessagesFile: "compiler:Default.isl" >> installer_output\installer.iss
echo Name: "arabic"; MessagesFile: "compiler:Languages\Arabic.isl" >> installer_output\installer.iss
echo. >> installer_output\installer.iss
echo [Tasks] >> installer_output\installer.iss
echo Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked >> installer_output\installer.iss
echo. >> installer_output\installer.iss
echo [Files] >> installer_output\installer.iss
echo Source: "app\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs >> installer_output\installer.iss
echo Source: "docs\*"; DestDir: "{app}\docs"; Flags: ignoreversion recursesubdirs createallsubdirs >> installer_output\installer.iss
echo. >> installer_output\installer.iss
echo [Icons] >> installer_output\installer.iss
echo Name: "{group}\Rental Management System"; Filename: "{app}\SimpleRentalApp.exe" >> installer_output\installer.iss
echo Name: "{group}\{cm:UninstallProgram,Rental Management System}"; Filename: "{uninstallexe}" >> installer_output\installer.iss
echo Name: "{autodesktop}\Rental Management System"; Filename: "{app}\SimpleRentalApp.exe"; Tasks: desktopicon >> installer_output\installer.iss
echo. >> installer_output\installer.iss
echo [Run] >> installer_output\installer.iss
echo Filename: "{app}\SimpleRentalApp.exe"; Description: "{cm:LaunchProgram,Rental Management System}"; Flags: nowait postinstall skipifsilent >> installer_output\installer.iss

echo [6/7] Creating portable version...
mkdir installer_output\portable
xcopy installer_output\app\*.* installer_output\portable\ /E /H /C /I > nul
echo @echo off > installer_output\portable\run.bat
echo echo Starting Rental Management System... >> installer_output\portable\run.bat
echo start SimpleRentalApp.exe >> installer_output\portable\run.bat

echo [7/7] Creating documentation...
echo Rental Management System - Installation Guide > installer_output\INSTALL.txt
echo ================================================ >> installer_output\INSTALL.txt
echo. >> installer_output\INSTALL.txt
echo INSTALLATION OPTIONS: >> installer_output\INSTALL.txt
echo. >> installer_output\INSTALL.txt
echo 1. INSTALLER VERSION (Recommended): >> installer_output\INSTALL.txt
echo    - If you have Inno Setup installed, compile installer.iss >> installer_output\INSTALL.txt
echo    - This will create RentalManagementSetup.exe >> installer_output\INSTALL.txt
echo. >> installer_output\INSTALL.txt
echo 2. PORTABLE VERSION: >> installer_output\INSTALL.txt
echo    - Use the 'portable' folder >> installer_output\INSTALL.txt
echo    - Copy the entire folder to any location >> installer_output\INSTALL.txt
echo    - Run 'run.bat' or 'SimpleRentalApp.exe' directly >> installer_output\INSTALL.txt
echo. >> installer_output\INSTALL.txt
echo SYSTEM REQUIREMENTS: >> installer_output\INSTALL.txt
echo - Windows 10 or later >> installer_output\INSTALL.txt
echo - .NET 9.0 Runtime (included in installer) >> installer_output\INSTALL.txt
echo - 200 MB free disk space >> installer_output\INSTALL.txt
echo. >> installer_output\INSTALL.txt
echo DEFAULT LOGIN: >> installer_output\INSTALL.txt
echo Username: hafid >> installer_output\INSTALL.txt
echo Password: hafidos159357 >> installer_output\INSTALL.txt
echo. >> installer_output\INSTALL.txt
echo SUPPORT: >> installer_output\INSTALL.txt
echo Developer: Hafid Abdo >> installer_output\INSTALL.txt
echo Email: Contact developer for support >> installer_output\INSTALL.txt

echo.
echo ========================================
echo Installer package created successfully!
echo Location: installer_output folder
echo.
echo NEXT STEPS:
echo 1. For full installer: Install Inno Setup and compile installer.iss
echo 2. For portable version: Use the 'portable' folder
echo.
echo Application size: 
for %%A in (installer_output\app\SimpleRentalApp.exe) do echo %%~zA bytes
echo ========================================
echo.
pause
