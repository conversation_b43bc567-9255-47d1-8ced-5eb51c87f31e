# إعادة توزيع تطبيق تدبير الكراء مع الأيقونة الجديدة
# Redistribute Rental Management App with New Icon

param(
    [string]$OutputName = "RentalManagement_v1.0_NewIcon.zip"
)

Write-Host "========================================" -ForegroundColor Green
Write-Host "    Redistributing Rental Management App" -ForegroundColor Green
Write-Host "    إعادة توزيع تطبيق تدبير الكراء" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Step 1: Clean project
Write-Host "[1/8] Cleaning project..." -ForegroundColor Cyan
Write-Host "[1/8] تنظيف المشروع..." -ForegroundColor Cyan
$cleanResult = & dotnet clean --configuration Release
if ($LASTEXITCODE -ne 0) {
    Write-Host "Error cleaning project" -ForegroundColor Red
    Write-Host "خطأ في تنظيف المشروع" -ForegroundColor Red
    exit 1
}

# Step 2: Build project
Write-Host "[2/8] Building project..." -ForegroundColor Cyan
Write-Host "[2/8] بناء المشروع..." -ForegroundColor Cyan
$buildResult = & dotnet build --configuration Release
if ($LASTEXITCODE -ne 0) {
    Write-Host "Error building project" -ForegroundColor Red
    Write-Host "خطأ في بناء المشروع" -ForegroundColor Red
    exit 1
}

# Step 3: Create portable release
Write-Host "[3/8] Creating portable release..." -ForegroundColor Cyan
Write-Host "[3/8] إنشاء نشر محمول..." -ForegroundColor Cyan

# Remove existing dist folder
if (Test-Path "dist_portable") {
    Remove-Item "dist_portable" -Recurse -Force
}

$publishResult = & dotnet publish --configuration Release --runtime win-x64 --self-contained true --output dist_portable
if ($LASTEXITCODE -ne 0) {
    Write-Host "Error in portable publishing" -ForegroundColor Red
    Write-Host "خطأ في النشر المحمول" -ForegroundColor Red
    exit 1
}

# Step 4: Copy additional files
Write-Host "[4/8] Copying additional files..." -ForegroundColor Cyan
Write-Host "[4/8] نسخ الملفات الإضافية..." -ForegroundColor Cyan

if (Test-Path "app_icon.ico") {
    Copy-Item "app_icon.ico" -Destination "dist_portable\"
}
if (Test-Path "rental_icon.png") {
    Copy-Item "rental_icon.png" -Destination "dist_portable\"
}
if (Test-Path "README.md") {
    Copy-Item "README.md" -Destination "dist_portable\"
}

# Step 5: Create Arabic launcher
Write-Host "[5/8] Creating Arabic launcher..." -ForegroundColor Cyan
Write-Host "[5/8] إنشاء ملف تشغيل عربي..." -ForegroundColor Cyan

$launcherContent = @"
@echo off
chcp 65001 > nul
title Rental Management System
echo ========================================
echo       Rental Management System
echo          نظام تدبير الكراء
echo ========================================
echo.
echo Developer: Hafid Abdo
echo Version: 1.0.0 with New Icon
echo.
echo Starting system...
echo.
start SimpleRentalApp.exe
"@

$launcherContent | Out-File -FilePath "dist_portable\run_app.bat" -Encoding UTF8

# Step 6: Create user guide
Write-Host "[6/8] Creating user guide..." -ForegroundColor Cyan
Write-Host "[6/8] إنشاء دليل المستخدم..." -ForegroundColor Cyan

$userGuide = @"
Rental Management System - New Version with Enhanced Icon
=========================================================

Welcome to the Rental Management System developed by Hafid Abdo

What's New in This Version:
✓ New professional icon
✓ Performance improvements
✓ Bug fixes
✓ Enhanced distribution files

How to Run:
1. Run "run_app.bat" file
2. Or run "SimpleRentalApp.exe" directly

Login Credentials:
Username: hafid
Password: hafidos159357

System Requirements:
- Windows 10 or later
- .NET 9.0 Runtime (included)
- 4 GB RAM
- 200 MB free space

Features:
- Property and rental management
- Contract management
- Payment tracking
- Receipt generation
- Cleaning tax management
- WhatsApp notifications
- Reports and analytics
- Data backup and restore

For technical support, please contact the developer.

Note: The application interface is in Arabic and fully supports
Arabic text input and display.
"@

$userGuide | Out-File -FilePath "dist_portable\USER_GUIDE.txt" -Encoding UTF8

# Step 7: Create distribution archive
Write-Host "[7/8] Creating distribution archive..." -ForegroundColor Cyan
Write-Host "[7/8] إنشاء ملف مضغوط للتوزيع..." -ForegroundColor Cyan

if (Test-Path $OutputName) {
    Remove-Item $OutputName -Force
}

Add-Type -AssemblyName System.IO.Compression.FileSystem
[System.IO.Compression.ZipFile]::CreateFromDirectory("dist_portable", $OutputName)

# Step 8: Create release information
Write-Host "[8/8] Creating release information..." -ForegroundColor Cyan
Write-Host "[8/8] إنشاء معلومات الإصدار..." -ForegroundColor Cyan

$releaseInfo = @"
Rental Management System - Release Information
==============================================

Version: 1.0.0
Release Date: $(Get-Date -Format 'yyyy-MM-dd')
Developer: Hafid Abdo

What's New in This Version:
* New professional icon that reflects the application nature
* User interface improvements
* Build and deployment fixes
* Enhanced distribution files

Available Files:
* $OutputName (Portable version)
* dist_portable\ (Portable version folder)

System Requirements:
- Windows 10 or later
- .NET 9.0 Runtime (included)
- 4 GB RAM
- 200 MB free space

Installation:
1. Extract the ZIP file to any folder
2. Run run_app.bat or SimpleRentalApp.exe
3. Login with default credentials

Default Login:
Username: hafid
Password: hafidos159357

Application ready for distribution!
"@

$releaseInfo | Out-File -FilePath "RELEASE_INFO.txt" -Encoding UTF8

# Display results
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Application redistributed successfully!" -ForegroundColor Green
Write-Host "تم إعادة توزيع التطبيق بنجاح!" -ForegroundColor Green
Write-Host ""
Write-Host "Files created:" -ForegroundColor Cyan
Write-Host "الملفات المنشأة:" -ForegroundColor Cyan
Write-Host "- dist_portable\ (Portable version folder)" -ForegroundColor White
Write-Host "- $OutputName (Compressed file)" -ForegroundColor White
Write-Host "- RELEASE_INFO.txt (Release information)" -ForegroundColor White
Write-Host ""

if (Test-Path $OutputName) {
    $fileSize = (Get-Item $OutputName).Length
    $fileSizeMB = [math]::Round($fileSize / 1MB, 2)
    Write-Host "Compressed file size: $fileSizeMB MB" -ForegroundColor Yellow
    Write-Host "حجم الملف المضغوط: $fileSizeMB ميجابايت" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Application ready for distribution!" -ForegroundColor Green
Write-Host "التطبيق جاهز للتوزيع!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
