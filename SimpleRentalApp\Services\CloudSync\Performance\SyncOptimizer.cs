using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading.Channels;
using System.Threading;
using SimpleRentalApp.Services.CloudSync.Security;

namespace SimpleRentalApp.Services.CloudSync.Performance
{
    /// <summary>
    /// محسن أداء المزامنة
    /// </summary>
    public class SyncOptimizer : IDisposable
    {
        private readonly Channel<SyncData> _syncQueue;
        private readonly ChannelWriter<SyncData> _writer;
        private readonly ChannelReader<SyncData> _reader;
        private readonly SemaphoreSlim _batchSemaphore;
        private readonly Timer _batchTimer;
        private readonly List<SyncData> _currentBatch;
        private readonly object _batchLock = new();
        private readonly SyncOptimizerConfiguration _config;
        private bool _isDisposed;

        public event EventHandler<BatchProcessedEventArgs>? BatchProcessed;
        public event EventHandler<OptimizationStatsEventArgs>? OptimizationStats;

        public SyncOptimizer(SyncOptimizerConfiguration? config = null)
        {
            _config = config ?? new SyncOptimizerConfiguration();
            
            var options = new BoundedChannelOptions(_config.MaxQueueSize)
            {
                FullMode = BoundedChannelFullMode.Wait,
                SingleReader = true,
                SingleWriter = false
            };

            _syncQueue = Channel.CreateBounded<SyncData>(options);
            _writer = _syncQueue.Writer;
            _reader = _syncQueue.Reader;
            
            _batchSemaphore = new SemaphoreSlim(1, 1);
            _currentBatch = new List<SyncData>();
            
            _batchTimer = new Timer(ProcessBatchCallback, null, 
                TimeSpan.FromMilliseconds(_config.BatchIntervalMs), 
                TimeSpan.FromMilliseconds(_config.BatchIntervalMs));

            // بدء معالج الطوابير
            _ = Task.Run(ProcessQueueAsync);
        }

        /// <summary>
        /// إضافة بيانات للمزامنة
        /// </summary>
        public async Task<bool> EnqueueSyncDataAsync(SyncData syncData)
        {
            if (_isDisposed) return false;

            try
            {
                // تحسين البيانات قبل الإضافة
                var optimizedData = OptimizeSyncData(syncData);
                
                await _writer.WriteAsync(optimizedData);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// إضافة مجموعة بيانات للمزامنة
        /// </summary>
        public async Task<int> EnqueueBatchAsync(IEnumerable<SyncData> syncDataList)
        {
            if (_isDisposed) return 0;

            var successCount = 0;
            var optimizedBatch = OptimizeBatch(syncDataList);

            foreach (var syncData in optimizedBatch)
            {
                if (await EnqueueSyncDataAsync(syncData))
                {
                    successCount++;
                }
            }

            return successCount;
        }

        /// <summary>
        /// معالجة الطابور
        /// </summary>
        private async Task ProcessQueueAsync()
        {
            try
            {
                await foreach (var syncData in _reader.ReadAllAsync())
                {
                    if (_isDisposed) break;

                    await _batchSemaphore.WaitAsync();
                    try
                    {
                        lock (_batchLock)
                        {
                            _currentBatch.Add(syncData);
                        }

                        // معالجة الدفعة إذا وصلت للحد الأقصى
                        if (_currentBatch.Count >= _config.BatchSize)
                        {
                            await ProcessCurrentBatchAsync();
                        }
                    }
                    finally
                    {
                        _batchSemaphore.Release();
                    }
                }
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                OnOptimizationStats(new OptimizationStatsEventArgs
                {
                    ErrorMessage = $"خطأ في معالجة الطابور: {ex.Message}",
                    Timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// معالجة الدفعة الحالية
        /// </summary>
        private async Task ProcessCurrentBatchAsync()
        {
            List<SyncData> batchToProcess;
            
            lock (_batchLock)
            {
                if (_currentBatch.Count == 0) return;
                
                batchToProcess = new List<SyncData>(_currentBatch);
                _currentBatch.Clear();
            }

            var startTime = DateTime.UtcNow;
            
            try
            {
                // تحسين الدفعة
                var optimizedBatch = OptimizeBatch(batchToProcess);
                
                // إشعار بمعالجة الدفعة
                OnBatchProcessed(new BatchProcessedEventArgs
                {
                    OriginalCount = batchToProcess.Count,
                    OptimizedCount = optimizedBatch.Count(),
                    ProcessedAt = DateTime.UtcNow,
                    BatchData = optimizedBatch.ToList()
                });

                // إحصائيات الأداء
                var duration = DateTime.UtcNow - startTime;
                OnOptimizationStats(new OptimizationStatsEventArgs
                {
                    ProcessedItems = batchToProcess.Count,
                    OptimizedItems = optimizedBatch.Count(),
                    ProcessingTime = duration,
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                OnOptimizationStats(new OptimizationStatsEventArgs
                {
                    ErrorMessage = $"خطأ في معالجة الدفعة: {ex.Message}",
                    ProcessedItems = batchToProcess.Count,
                    Timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// تحسين بيانات المزامنة الواحدة
        /// </summary>
        private SyncData OptimizeSyncData(SyncData syncData)
        {
            // ضغط البيانات إذا كانت كبيرة
            if (syncData.JsonData.Length > _config.CompressionThreshold)
            {
                syncData.JsonData = CompressData(syncData.JsonData);
                syncData.Metadata["compressed"] = true;
            }

            // تحسين الـ metadata
            if (syncData.Metadata.Count > _config.MaxMetadataItems)
            {
                var essentialMetadata = syncData.Metadata
                    .Take(_config.MaxMetadataItems)
                    .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                syncData.Metadata = essentialMetadata;
            }

            return syncData;
        }

        /// <summary>
        /// تحسين مجموعة البيانات
        /// </summary>
        private IEnumerable<SyncData> OptimizeBatch(IEnumerable<SyncData> syncDataList)
        {
            var dataList = syncDataList.ToList();
            
            // إزالة التكرارات
            var uniqueData = RemoveDuplicates(dataList);
            
            // دمج العمليات المتتالية
            var mergedData = MergeSequentialOperations(uniqueData);
            
            // ترتيب حسب الأولوية
            var prioritizedData = PrioritizeOperations(mergedData);
            
            return prioritizedData;
        }

        /// <summary>
        /// إزالة البيانات المكررة
        /// </summary>
        private List<SyncData> RemoveDuplicates(List<SyncData> dataList)
        {
            var uniqueData = new Dictionary<string, SyncData>();
            
            foreach (var data in dataList.OrderBy(d => d.Timestamp))
            {
                var key = $"{data.EntityType}_{data.EntityId}";
                uniqueData[key] = data; // الأحدث يحل محل الأقدم
            }
            
            return uniqueData.Values.ToList();
        }

        /// <summary>
        /// دمج العمليات المتتالية
        /// </summary>
        private List<SyncData> MergeSequentialOperations(List<SyncData> dataList)
        {
            var mergedData = new List<SyncData>();
            var groupedData = dataList.GroupBy(d => $"{d.EntityType}_{d.EntityId}");
            
            foreach (var group in groupedData)
            {
                var operations = group.OrderBy(d => d.Timestamp).ToList();
                
                if (operations.Count == 1)
                {
                    mergedData.Add(operations[0]);
                    continue;
                }

                // دمج العمليات المتتالية
                var finalOperation = operations.Last();
                
                // إذا كانت العملية الأولى Create والأخيرة Delete، يمكن تجاهل كليهما
                if (operations.First().Operation == SyncOperation.Create && 
                    finalOperation.Operation == SyncOperation.Delete)
                {
                    continue; // تجاهل العنصر تماماً
                }
                
                mergedData.Add(finalOperation);
            }
            
            return mergedData;
        }

        /// <summary>
        /// ترتيب العمليات حسب الأولوية
        /// </summary>
        private List<SyncData> PrioritizeOperations(List<SyncData> dataList)
        {
            return dataList.OrderBy(d => GetOperationPriority(d.Operation))
                          .ThenBy(d => d.Timestamp)
                          .ToList();
        }

        /// <summary>
        /// الحصول على أولوية العملية
        /// </summary>
        private int GetOperationPriority(SyncOperation operation)
        {
            return operation switch
            {
                SyncOperation.Delete => 1, // حذف أولاً
                SyncOperation.Create => 2, // إنشاء ثانياً
                SyncOperation.Update => 3, // تحديث أخيراً
                _ => 4
            };
        }

        /// <summary>
        /// ضغط البيانات
        /// </summary>
        private string CompressData(string data)
        {
            try
            {
                var bytes = System.Text.Encoding.UTF8.GetBytes(data);
                using var output = new System.IO.MemoryStream();
                using (var gzip = new System.IO.Compression.GZipStream(output, System.IO.Compression.CompressionMode.Compress))
                {
                    gzip.Write(bytes, 0, bytes.Length);
                }
                return Convert.ToBase64String(output.ToArray());
            }
            catch
            {
                return data; // إرجاع البيانات الأصلية في حالة فشل الضغط
            }
        }

        /// <summary>
        /// فك ضغط البيانات
        /// </summary>
        public static string DecompressData(string compressedData)
        {
            try
            {
                var bytes = Convert.FromBase64String(compressedData);
                using var input = new System.IO.MemoryStream(bytes);
                using var gzip = new System.IO.Compression.GZipStream(input, System.IO.Compression.CompressionMode.Decompress);
                using var output = new System.IO.MemoryStream();
                gzip.CopyTo(output);
                return System.Text.Encoding.UTF8.GetString(output.ToArray());
            }
            catch
            {
                return compressedData; // إرجاع البيانات كما هي في حالة فشل فك الضغط
            }
        }

        /// <summary>
        /// معالجة الدفعة بالمؤقت
        /// </summary>
        private async void ProcessBatchCallback(object? state)
        {
            if (_isDisposed) return;

            await _batchSemaphore.WaitAsync();
            try
            {
                await ProcessCurrentBatchAsync();
            }
            finally
            {
                _batchSemaphore.Release();
            }
        }

        /// <summary>
        /// إحصائيات الأداء
        /// </summary>
        public SyncOptimizerStats GetStats()
        {
            return new SyncOptimizerStats
            {
                QueueSize = _syncQueue.Reader.CanCount ? _syncQueue.Reader.Count : -1,
                CurrentBatchSize = _currentBatch.Count,
                IsProcessing = _batchSemaphore.CurrentCount == 0,
                Configuration = _config
            };
        }

        #region Events

        protected virtual void OnBatchProcessed(BatchProcessedEventArgs e)
        {
            BatchProcessed?.Invoke(this, e);
        }

        protected virtual void OnOptimizationStats(OptimizationStatsEventArgs e)
        {
            OptimizationStats?.Invoke(this, e);
        }

        #endregion

        #region IDisposable

        public void Dispose()
        {
            if (_isDisposed) return;

            _isDisposed = true;
            
            _writer.Complete();
            _batchTimer?.Dispose();
            _batchSemaphore?.Dispose();
            
            // معالجة آخر دفعة
            if (_currentBatch.Count > 0)
            {
                _ = Task.Run(ProcessCurrentBatchAsync);
            }
        }

        #endregion
    }

    /// <summary>
    /// إعدادات محسن الأداء
    /// </summary>
    public class SyncOptimizerConfiguration
    {
        public int BatchSize { get; set; } = 50;
        public int BatchIntervalMs { get; set; } = 5000; // 5 ثوان
        public int MaxQueueSize { get; set; } = 1000;
        public int CompressionThreshold { get; set; } = 1024; // 1KB
        public int MaxMetadataItems { get; set; } = 10;
        public bool EnableCompression { get; set; } = true;
        public bool EnableDeduplication { get; set; } = true;
        public bool EnableOperationMerging { get; set; } = true;
    }

    /// <summary>
    /// إحصائيات محسن الأداء
    /// </summary>
    public class SyncOptimizerStats
    {
        public int QueueSize { get; set; }
        public int CurrentBatchSize { get; set; }
        public bool IsProcessing { get; set; }
        public SyncOptimizerConfiguration Configuration { get; set; } = new();
    }

    /// <summary>
    /// أحداث معالجة الدفعة
    /// </summary>
    public class BatchProcessedEventArgs : EventArgs
    {
        public int OriginalCount { get; set; }
        public int OptimizedCount { get; set; }
        public DateTime ProcessedAt { get; set; }
        public List<SyncData> BatchData { get; set; } = new();
    }

    /// <summary>
    /// أحداث إحصائيات التحسين
    /// </summary>
    public class OptimizationStatsEventArgs : EventArgs
    {
        public int ProcessedItems { get; set; }
        public int OptimizedItems { get; set; }
        public TimeSpan ProcessingTime { get; set; }
        public string? ErrorMessage { get; set; }
        public DateTime Timestamp { get; set; }
    }
}
