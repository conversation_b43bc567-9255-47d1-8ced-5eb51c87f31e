using Microsoft.EntityFrameworkCore;
using SimpleRentalApp.Data;
using SimpleRentalApp.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SimpleRentalApp.Services
{
    /// <summary>
    /// خدمة إدارة إشعارات الواتساب المبسطة
    /// </summary>
    public class WhatsAppNotificationService
    {
        private readonly RentalDbContext _context;

        public WhatsAppNotificationService(RentalDbContext context)
        {
            _context = context;
        }

        public WhatsAppNotificationService()
        {
            _context = new RentalDbContext();
        }

        /// <summary>
        /// إنشاء إشعار جديد
        /// </summary>
        public async Task<WhatsAppNotification> CreateNotificationAsync(
            int customerId, 
            int propertyId, 
            WhatsAppNotificationType type, 
            string phoneNumber, 
            decimal amount, 
            DateTime? dueDate = null,
            int? month = null,
            int? year = null)
        {
            try
            {
                var notification = new WhatsAppNotification
                {
                    CustomerId = customerId,
                    PropertyId = propertyId,
                    Type = type,
                    PhoneNumber = phoneNumber,
                    Amount = amount,
                    DueDate = dueDate,
                    Month = month,
                    Year = year,
                    Status = WhatsAppNotificationStatus.Pending,
                    CreatedDate = DateTime.Now,
                    IsAutoGenerated = false
                };

                _context.WhatsAppNotifications.Add(notification);
                await _context.SaveChangesAsync();

                return notification;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء الإشعار: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على جميع الإشعارات
        /// </summary>
        public async Task<List<WhatsAppNotification>> GetAllNotificationsAsync()
        {
            try
            {
                return await _context.WhatsAppNotifications
                    .Include(n => n.Customer)
                    .Include(n => n.Property)
                    .Include(n => n.Contract)
                    .OrderByDescending(n => n.CreatedDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب الإشعارات: {ex.Message}");
                return new List<WhatsAppNotification>();
            }
        }

        /// <summary>
        /// الحصول على الإشعارات المعلقة
        /// </summary>
        public async Task<List<WhatsAppNotification>> GetPendingNotificationsAsync()
        {
            try
            {
                return await _context.WhatsAppNotifications
                    .Include(n => n.Customer)
                    .Include(n => n.Property)
                    .Include(n => n.Contract)
                    .Where(n => n.Status == WhatsAppNotificationStatus.Pending)
                    .OrderBy(n => n.DueDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب الإشعارات المعلقة: {ex.Message}");
                return new List<WhatsAppNotification>();
            }
        }

        /// <summary>
        /// تحديث حالة الإشعار
        /// </summary>
        public async Task<bool> UpdateNotificationStatusAsync(int notificationId, WhatsAppNotificationStatus status, string? errorMessage = null)
        {
            try
            {
                var notification = await _context.WhatsAppNotifications.FindAsync(notificationId);
                if (notification == null)
                    return false;

                notification.Status = status;
                notification.ErrorMessage = errorMessage;

                if (status == WhatsAppNotificationStatus.Sent)
                {
                    notification.SentDate = DateTime.Now;
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث حالة الإشعار: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حذف إشعار
        /// </summary>
        public async Task<bool> DeleteNotificationAsync(int notificationId)
        {
            try
            {
                var notification = await _context.WhatsAppNotifications.FindAsync(notificationId);
                if (notification == null)
                    return false;

                _context.WhatsAppNotifications.Remove(notification);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حذف الإشعار: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إنشاء إشعارات الإيجار الشهري للعقود النشطة
        /// </summary>
        public async Task<List<WhatsAppNotification>> GenerateMonthlyRentNotificationsAsync()
        {
            try
            {
                var notifications = new List<WhatsAppNotification>();
                var currentDate = DateTime.Now;
                var currentMonth = currentDate.Month;
                var currentYear = currentDate.Year;

                // جلب العقود النشطة
                var activeContracts = await _context.Contracts
                    .Include(c => c.Customer)
                    .Include(c => c.Property)
                    .Where(c => c.Status == ContractStatus.Active)
                    .ToListAsync();

                foreach (var contract in activeContracts)
                {
                    // التحقق من عدم وجود إشعار مماثل لنفس الشهر
                    var existingNotification = await _context.WhatsAppNotifications
                        .AnyAsync(n => n.CustomerId == contract.CustomerId &&
                                      n.PropertyId == contract.PropertyId &&
                                      n.Type == WhatsAppNotificationType.MonthlyRent &&
                                      n.Month == currentMonth &&
                                      n.Year == currentYear);

                    if (!existingNotification)
                    {
                        var dueDate = new DateTime(currentYear, currentMonth, contract.PaymentDay ?? 1);
                        
                        var notification = new WhatsAppNotification
                        {
                            CustomerId = contract.CustomerId,
                            PropertyId = contract.PropertyId,
                            ContractId = contract.Id,
                            Type = WhatsAppNotificationType.MonthlyRent,
                            PhoneNumber = contract.Customer.Phone,
                            Amount = contract.Property.MonthlyRent,
                            DueDate = dueDate,
                            Month = currentMonth,
                            Year = currentYear,
                            Status = WhatsAppNotificationStatus.Pending,
                            CreatedDate = DateTime.Now,
                            IsAutoGenerated = true
                        };

                        _context.WhatsAppNotifications.Add(notification);
                        notifications.Add(notification);
                    }
                }

                if (notifications.Any())
                {
                    await _context.SaveChangesAsync();
                }

                return notifications;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في توليد إشعارات الإيجار الشهري: {ex.Message}");
                return new List<WhatsAppNotification>();
            }
        }

        /// <summary>
        /// إنشاء إشعارات ضريبة النظافة
        /// </summary>
        public async Task<List<WhatsAppNotification>> GenerateCleaningTaxNotificationsAsync()
        {
            try
            {
                var notifications = new List<WhatsAppNotification>();
                var currentYear = DateTime.Now.Year;
                var cleaningTaxDeadline = new DateTime(currentYear, 5, 31); // 31 مايو

                // جلب العقارات المؤجرة
                var rentedProperties = await _context.Properties
                    .Include(p => p.Customer)
                    .Where(p => p.Status == PropertyStatus.Rented && p.CustomerId.HasValue)
                    .ToListAsync();

                foreach (var property in rentedProperties)
                {
                    // التحقق من عدم وجود إشعار مماثل لنفس السنة
                    var existingNotification = await _context.WhatsAppNotifications
                        .AnyAsync(n => n.PropertyId == property.Id &&
                                      n.Type == WhatsAppNotificationType.CleaningTax &&
                                      n.Year == currentYear);

                    if (!existingNotification)
                    {
                        // حساب ضريبة النظافة (10.5% من الإيجار السنوي)
                        var annualRent = property.MonthlyRent * 12;
                        var cleaningTaxAmount = annualRent * 0.105m;

                        var notification = new WhatsAppNotification
                        {
                            CustomerId = property.CustomerId.Value,
                            PropertyId = property.Id,
                            Type = WhatsAppNotificationType.CleaningTax,
                            PhoneNumber = property.Customer.Phone,
                            Amount = cleaningTaxAmount,
                            DueDate = cleaningTaxDeadline,
                            Year = currentYear,
                            Status = WhatsAppNotificationStatus.Pending,
                            CreatedDate = DateTime.Now,
                            IsAutoGenerated = true
                        };

                        _context.WhatsAppNotifications.Add(notification);
                        notifications.Add(notification);
                    }
                }

                if (notifications.Any())
                {
                    await _context.SaveChangesAsync();
                }

                return notifications;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في توليد إشعارات ضريبة النظافة: {ex.Message}");
                return new List<WhatsAppNotification>();
            }
        }

        /// <summary>
        /// تنظيف الإشعارات القديمة
        /// </summary>
        public async Task<int> CleanupOldNotificationsAsync(int daysOld = 90)
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-daysOld);
                
                var oldNotifications = await _context.WhatsAppNotifications
                    .Where(n => n.CreatedDate < cutoffDate)
                    .ToListAsync();

                if (oldNotifications.Any())
                {
                    _context.WhatsAppNotifications.RemoveRange(oldNotifications);
                    await _context.SaveChangesAsync();
                }

                return oldNotifications.Count;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تنظيف الإشعارات القديمة: {ex.Message}");
                return 0;
            }
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
