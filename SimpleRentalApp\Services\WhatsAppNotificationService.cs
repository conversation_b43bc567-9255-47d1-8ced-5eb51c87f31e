using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using Microsoft.EntityFrameworkCore;
using SimpleRentalApp.Data;
using SimpleRentalApp.Models;

namespace SimpleRentalApp.Services
{
    /// <summary>
    /// خدمة إشعارات الواتساب المحسنة
    /// </summary>
    public class WhatsAppNotificationService
    {
        private readonly RentalDbContext _context;
        
        public WhatsAppNotificationService()
        {
            _context = new RentalDbContext();
        }
        
        /// <summary>
        /// إنشاء إشعارات الكراء الشهرية لجميع المكترين
        /// </summary>
        public async Task<List<WhatsAppNotification>> CreateMonthlyRentNotificationsAsync()
        {
            var notifications = new List<WhatsAppNotification>();
            var currentMonth = DateTime.Now.ToString("MMMM yyyy", new CultureInfo("ar-SA"));
            var currentYear = DateTime.Now.Year;
            var currentMonthNumber = DateTime.Now.Month;
            
            // جلب العقود النشطة
            var activeContracts = await _context.Contracts
                .Include(c => c.Customer)
                .Include(c => c.Property)
                .Where(c => c.IsActive && c.Customer.Phone != null && c.Customer.Phone != "")
                .ToListAsync();
            
            foreach (var contract in activeContracts)
            {
                // التحقق من عدم وجود إشعار مكرر لنفس الشهر
                var uniqueKey = $"RENT_{contract.CustomerId}_{contract.PropertyId}_{currentYear}_{currentMonthNumber}";
                var existingNotification = await _context.WhatsAppNotifications
                    .FirstOrDefaultAsync(n => n.UniqueKey == uniqueKey);
                
                if (existingNotification == null)
                {
                    var paymentDay = 1; // افتراضي
                    var dueDate = new DateTime(currentYear, currentMonthNumber, Math.Min(paymentDay, DateTime.DaysInMonth(currentYear, currentMonthNumber)));
                    if (dueDate < DateTime.Now)
                        dueDate = dueDate.AddMonths(1);
                    
                    var notification = new WhatsAppNotification
                    {
                        CustomerId = contract.CustomerId,
                        PropertyId = contract.PropertyId,
                        Type = NotificationType.MonthlyRent,
                        PhoneNumber = contract.Customer.Phone,
                        Amount = contract.Property.MonthlyRent,
                        DueDate = dueDate,
                        Month = currentMonth,
                        Year = currentYear,
                        ScheduledDate = DateTime.Now,
                        Status = NotificationStatus.Pending,
                        IsAutoGenerated = true,
                        UniqueKey = uniqueKey
                    };
                    
                    // توليد الرسالة
                    notification.Message = await GenerateMessageAsync(notification);
                    
                    _context.WhatsAppNotifications.Add(notification);
                    notifications.Add(notification);
                }
            }
            
            await _context.SaveChangesAsync();
            return notifications;
        }
        
        /// <summary>
        /// إنشاء إشعارات ضريبة النظافة المتأخرة
        /// </summary>
        public async Task<List<WhatsAppNotification>> CreateOverdueCleaningTaxNotificationsAsync()
        {
            var notifications = new List<WhatsAppNotification>();
            var currentYear = DateTime.Now.Year;
            var cleaningTaxDeadline = new DateTime(currentYear, 5, 31); // 31 مايو
            
            if (DateTime.Now <= cleaningTaxDeadline)
                return notifications; // لم يحن موعد التأخير بعد
            
            // جلب المحلات التي لم تدفع ضريبة النظافة
            var properties = await _context.Properties
                .Include(p => p.Customer)
                .Where(p => p.CustomerId.HasValue && p.Customer.Phone != null && p.Customer.Phone != "")
                .ToListAsync();
            
            foreach (var property in properties)
            {
                // التحقق من وجود دفعة ضريبة النظافة للسنة الحالية
                var hasPayment = await _context.Payments
                    .AnyAsync(p => p.PropertyId == property.Id &&
                                  p.PaymentDate.HasValue &&
                                  p.PaymentDate.Value.Year == currentYear &&
                                  p.CleaningTaxAmount > 0);
                
                if (!hasPayment)
                {
                    var uniqueKey = $"CLEANING_TAX_{property.CustomerId}_{property.Id}_{currentYear}";
                    var existingNotification = await _context.WhatsAppNotifications
                        .FirstOrDefaultAsync(n => n.UniqueKey == uniqueKey);
                    
                    if (existingNotification == null)
                    {
                        var cleaningTaxAmount = property.MonthlyRent * 12 * 0.105m; // 10.5%
                        
                        var notification = new WhatsAppNotification
                        {
                            CustomerId = property.CustomerId.Value,
                            PropertyId = property.Id,
                            Type = NotificationType.CleaningTax,
                            PhoneNumber = property.Customer.Phone,
                            Amount = cleaningTaxAmount,
                            DueDate = cleaningTaxDeadline,
                            Year = currentYear,
                            ScheduledDate = DateTime.Now,
                            Status = NotificationStatus.Pending,
                            IsAutoGenerated = true,
                            UniqueKey = uniqueKey
                        };
                        
                        // توليد الرسالة
                        notification.Message = await GenerateMessageAsync(notification);
                        
                        _context.WhatsAppNotifications.Add(notification);
                        notifications.Add(notification);
                    }
                }
            }
            
            await _context.SaveChangesAsync();
            return notifications;
        }
        
        /// <summary>
        /// إنشاء إشعارات متأخرة للكراء الشهري
        /// </summary>
        public async Task<List<WhatsAppNotification>> CreateLateRentNotificationsAsync()
        {
            var notifications = new List<WhatsAppNotification>();
            var settings = await GetNotificationSettingsAsync();

            if (!settings.LateRemindersEnabled || !settings.RentNotificationsEnabled)
                return notifications;

            var currentDate = DateTime.Now;

            // جلب العقود النشطة
            var activeContracts = await _context.Contracts
                .Include(c => c.Customer)
                .Include(c => c.Property)
                .Where(c => c.IsActive && c.Customer.Phone != null && c.Customer.Phone != "")
                .ToListAsync();

            foreach (var contract in activeContracts)
            {
                // حساب تاريخ الاستحقاق للشهر الحالي
                var paymentDay = 1; // افتراضي
                var currentMonth = currentDate.Month;
                var currentYear = currentDate.Year;

                var dueDate = new DateTime(currentYear, currentMonth, Math.Min(paymentDay, DateTime.DaysInMonth(currentYear, currentMonth)));

                // إذا كان تاريخ الاستحقاق في المستقبل، تحقق من الشهر السابق
                if (dueDate > currentDate)
                {
                    dueDate = dueDate.AddMonths(-1);
                }

                // التحقق من التأخير
                var daysPastDue = (currentDate - dueDate).Days;

                if (daysPastDue >= settings.RentLateDays)
                {
                    // التحقق من عدم وجود دفعة للشهر المستحق
                    var hasPayment = await _context.Payments
                        .AnyAsync(p => p.CustomerId == contract.CustomerId &&
                                      p.PropertyId == contract.PropertyId &&
                                      p.PaymentDate.HasValue &&
                                      p.PaymentDate.Value.Month == dueDate.Month &&
                                      p.PaymentDate.Value.Year == dueDate.Year &&
                                      p.RentAmount > 0);

                    if (!hasPayment)
                    {
                        // التحقق من عدم تجاوز الحد الأقصى للإشعارات المتأخرة
                        var lateRemindersThisMonth = await _context.WhatsAppNotifications
                            .CountAsync(n => n.CustomerId == contract.CustomerId &&
                                           n.PropertyId == contract.PropertyId &&
                                           n.Type == NotificationType.PaymentReminder &&
                                           n.CreatedDate.Month == currentDate.Month &&
                                           n.CreatedDate.Year == currentDate.Year);

                        if (lateRemindersThisMonth < settings.MaxLateRemindersPerMonth)
                        {
                            // التحقق من آخر إشعار متأخر
                            var lastLateReminder = await _context.WhatsAppNotifications
                                .Where(n => n.CustomerId == contract.CustomerId &&
                                          n.PropertyId == contract.PropertyId &&
                                          n.Type == NotificationType.PaymentReminder)
                                .OrderByDescending(n => n.CreatedDate)
                                .FirstOrDefaultAsync();

                            bool canSendReminder = true;
                            if (lastLateReminder != null)
                            {
                                var daysSinceLastReminder = (currentDate - lastLateReminder.CreatedDate).Days;
                                canSendReminder = daysSinceLastReminder >= settings.DaysBetweenLateReminders;
                            }

                            if (canSendReminder)
                            {
                                var uniqueKey = $"LATE_RENT_{contract.CustomerId}_{contract.PropertyId}_{dueDate:yyyyMM}_{DateTime.Now.Ticks}";

                                var notification = new WhatsAppNotification
                                {
                                    CustomerId = contract.CustomerId,
                                    PropertyId = contract.PropertyId,
                                    Type = NotificationType.PaymentReminder,
                                    PhoneNumber = contract.Customer.Phone,
                                    Amount = contract.Property.MonthlyRent,
                                    DueDate = dueDate,
                                    Month = dueDate.ToString("MMMM yyyy", new CultureInfo("ar-SA")),
                                    Year = dueDate.Year,
                                    ScheduledDate = DateTime.Now,
                                    Status = NotificationStatus.Pending,
                                    IsAutoGenerated = true,
                                    UniqueKey = uniqueKey
                                };

                                // توليد الرسالة
                                notification.Message = await GenerateMessageAsync(notification);

                                _context.WhatsAppNotifications.Add(notification);
                                notifications.Add(notification);
                            }
                        }
                    }
                }
            }

            await _context.SaveChangesAsync();
            return notifications;
        }

        /// <summary>
        /// إنشاء إشعارات متأخرة لضريبة النظافة
        /// </summary>
        public async Task<List<WhatsAppNotification>> CreateLateCleaningTaxNotificationsAsync()
        {
            var notifications = new List<WhatsAppNotification>();
            var settings = await GetNotificationSettingsAsync();

            if (!settings.LateRemindersEnabled || !settings.CleaningTaxNotificationsEnabled)
                return notifications;

            var currentDate = DateTime.Now;
            var currentYear = currentDate.Year;
            var cleaningTaxDeadline = new DateTime(currentYear, 5, 31); // 31 مايو

            // التحقق من التأخير
            var daysPastDue = (currentDate - cleaningTaxDeadline).Days;

            if (daysPastDue >= settings.CleaningTaxLateDays)
            {
                // جلب المحلات التي لم تدفع ضريبة النظافة
                var properties = await _context.Properties
                    .Include(p => p.Customer)
                    .Where(p => p.CustomerId.HasValue && p.Customer.Phone != null && p.Customer.Phone != "")
                    .ToListAsync();

                foreach (var property in properties)
                {
                    // التحقق من وجود دفعة ضريبة النظافة للسنة الحالية
                    var hasPayment = await _context.Payments
                        .AnyAsync(p => p.PropertyId == property.Id &&
                                      p.PaymentDate.HasValue &&
                                      p.PaymentDate.Value.Year == currentYear &&
                                      p.CleaningTaxAmount > 0);

                    if (!hasPayment)
                    {
                        // التحقق من عدم تجاوز الحد الأقصى للإشعارات المتأخرة
                        var lateRemindersThisMonth = await _context.WhatsAppNotifications
                            .CountAsync(n => n.CustomerId == property.CustomerId &&
                                           n.PropertyId == property.Id &&
                                           n.Type == NotificationType.PaymentReminder &&
                                           n.CreatedDate.Month == currentDate.Month &&
                                           n.CreatedDate.Year == currentDate.Year);

                        if (lateRemindersThisMonth < settings.MaxLateRemindersPerMonth)
                        {
                            var uniqueKey = $"LATE_CLEANING_{property.CustomerId}_{property.Id}_{currentYear}_{DateTime.Now.Ticks}";

                            var cleaningTaxAmount = property.MonthlyRent * 12 * 0.105m; // 10.5%

                            var notification = new WhatsAppNotification
                            {
                                CustomerId = property.CustomerId.Value,
                                PropertyId = property.Id,
                                Type = NotificationType.PaymentReminder,
                                PhoneNumber = property.Customer.Phone,
                                Amount = cleaningTaxAmount,
                                DueDate = cleaningTaxDeadline,
                                Year = currentYear,
                                ScheduledDate = DateTime.Now,
                                Status = NotificationStatus.Pending,
                                IsAutoGenerated = true,
                                UniqueKey = uniqueKey
                            };

                            // توليد الرسالة
                            notification.Message = await GenerateMessageAsync(notification);

                            _context.WhatsAppNotifications.Add(notification);
                            notifications.Add(notification);
                        }
                    }
                }
            }

            await _context.SaveChangesAsync();
            return notifications;
        }

        /// <summary>
        /// جلب إعدادات الإشعارات
        /// </summary>
        private async Task<NotificationSettings> GetNotificationSettingsAsync()
        {
            var settings = await _context.NotificationSettings.FirstOrDefaultAsync();

            if (settings == null)
            {
                // إنشاء إعدادات افتراضية
                settings = new NotificationSettings();
                _context.NotificationSettings.Add(settings);
                await _context.SaveChangesAsync();
            }

            return settings;
        }

        /// <summary>
        /// توليد رسالة الإشعار حسب القالب
        /// </summary>
        public async Task<string> GenerateMessageAsync(WhatsAppNotification notification)
        {
            // جلب القالب المناسب
            var template = await GetTemplateForTypeAsync(notification.Type);
            
            // جلب بيانات المكتري والمحل
            var customer = await _context.Customers.FindAsync(notification.CustomerId);
            var property = await _context.Properties.FindAsync(notification.PropertyId);
            
            if (customer == null || property == null)
                return "خطأ في جلب البيانات";
            
            // جلب يوم الأداء من العقد
            var contract = await _context.Contracts
                .FirstOrDefaultAsync(c => c.CustomerId == notification.CustomerId &&
                                         c.PropertyId == notification.PropertyId &&
                                         c.IsActive);

            var paymentDay = contract?.PaymentDay ?? contract?.StartDate.Day ?? notification.DueDate.Day;

            // استبدال المتغيرات
            var message = template.Template
                .Replace(TemplateVariables.CustomerName, customer.FullName)
                .Replace(TemplateVariables.PropertyName, property.Name)
                .Replace(TemplateVariables.Amount, $"{notification.Amount:N0}")
                .Replace(TemplateVariables.DueDate, notification.DueDate.ToString("dd/MM/yyyy"))
                .Replace(TemplateVariables.PaymentDay, paymentDay.ToString())
                .Replace(TemplateVariables.Month, notification.Month)
                .Replace(TemplateVariables.Year, notification.Year.ToString())
                .Replace(TemplateVariables.CurrentDate, DateTime.Now.ToString("dd/MM/yyyy"));
            
            return message;
        }
        
        /// <summary>
        /// جلب القالب المناسب لنوع الإشعار
        /// </summary>
        private async Task<MessageTemplate> GetTemplateForTypeAsync(NotificationType type)
        {
            var template = await _context.MessageTemplates
                .FirstOrDefaultAsync(t => t.Type == type && t.IsActive && t.IsDefault);
            
            if (template == null)
            {
                // إنشاء قالب افتراضي إذا لم يوجد
                template = CreateDefaultTemplate(type);
                _context.MessageTemplates.Add(template);
                await _context.SaveChangesAsync();
            }
            
            return template;
        }
        
        /// <summary>
        /// إنشاء قالب افتراضي
        /// </summary>
        private MessageTemplate CreateDefaultTemplate(NotificationType type)
        {
            return type switch
            {
                NotificationType.MonthlyRent => new MessageTemplate
                {
                    Type = NotificationType.MonthlyRent,
                    Name = "قالب الكراء الشهري الافتراضي",
                    Template = $"🏠 السلام عليكم {TemplateVariables.CustomerName}\n\n" +
                              $"تذكير بواجب كراء محل {TemplateVariables.PropertyName} لشهر {TemplateVariables.Month}.\n\n" +
                              $"💰 المبلغ: {TemplateVariables.Amount} درهم\n" +
                              $"📅 المرجو الأداء قبل يوم {TemplateVariables.PaymentDay} من كل شهر\n" +
                              $"📅 تاريخ الاستحقاق: {TemplateVariables.DueDate}\n\n" +
                              $"شكراً لتعاونكم 🙏",
                    IsActive = true,
                    IsDefault = true,
                    AvailableVariables = string.Join(", ", TemplateVariables.GetVariablesForType(NotificationType.MonthlyRent))
                },
                
                NotificationType.CleaningTax => new MessageTemplate
                {
                    Type = NotificationType.CleaningTax,
                    Name = "قالب ضريبة النظافة الافتراضي",
                    Template = $"🧹 تذكير بدفع ضريبة النظافة المتأخرة\n\n" +
                              $"عزيزي/عزيزتي {TemplateVariables.CustomerName}\n\n" +
                              $"نلفت انتباهكم إلى أن ضريبة النظافة للمحل: {TemplateVariables.PropertyName}\n" +
                              $"💰 المبلغ: {TemplateVariables.Amount} درهم\n" +
                              $"📅 لسنة: {TemplateVariables.Year}\n" +
                              $"📅 كان مستحق في: {TemplateVariables.DueDate}\n\n" +
                              $"⚠️ الدفعة متأخرة، يرجى سدادها في أقرب وقت ممكن.\n" +
                              $"شكراً لتعاونكم 🙏",
                    IsActive = true,
                    IsDefault = true,
                    AvailableVariables = string.Join(", ", TemplateVariables.GetVariablesForType(NotificationType.CleaningTax))
                },

                NotificationType.PaymentReminder => new MessageTemplate
                {
                    Type = NotificationType.PaymentReminder,
                    Name = "قالب الإشعار المتأخر الافتراضي",
                    Template = $"⏰ تذكير عاجل - دفعة متأخرة\n\n" +
                              $"عزيزي/عزيزتي {TemplateVariables.CustomerName}\n\n" +
                              $"نلفت انتباهكم إلى وجود دفعة متأخرة للمحل: {TemplateVariables.PropertyName}\n" +
                              $"💰 المبلغ المستحق: {TemplateVariables.Amount} درهم\n" +
                              $"📅 كان مستحق في يوم {TemplateVariables.PaymentDay} من الشهر\n" +
                              $"📅 تاريخ الاستحقاق: {TemplateVariables.DueDate}\n" +
                              $"📅 اليوم: {TemplateVariables.CurrentDate}\n\n" +
                              $"🚨 الدفعة متأخرة، يرجى سدادها فوراً لتجنب أي إجراءات إضافية.\n\n" +
                              $"شكراً لتفهمكم وسرعة استجابتكم 🙏",
                    IsActive = true,
                    IsDefault = true,
                    AvailableVariables = string.Join(", ", TemplateVariables.GetVariablesForType(NotificationType.PaymentReminder))
                },
                
                _ => new MessageTemplate
                {
                    Type = type,
                    Name = "قالب افتراضي",
                    Template = $"تذكير من إدارة الكراء\n\n" +
                              $"عزيزي/عزيزتي {TemplateVariables.CustomerName}\n\n" +
                              $"المحل: {TemplateVariables.PropertyName}\n" +
                              $"المبلغ: {TemplateVariables.Amount} درهم\n" +
                              $"التاريخ: {TemplateVariables.DueDate}\n\n" +
                              $"شكراً لتعاونكم 🙏",
                    IsActive = true,
                    IsDefault = true,
                    AvailableVariables = string.Join(", ", TemplateVariables.GetAllVariables())
                }
            };
        }
        
        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
