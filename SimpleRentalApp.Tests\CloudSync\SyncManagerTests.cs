using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;
using Moq;
using SimpleRentalApp.Services.CloudSync;
using SimpleRentalApp.Data;
using SimpleRentalApp.Models;

namespace SimpleRentalApp.Tests.CloudSync
{
    /// <summary>
    /// اختبارات مدير المزامنة
    /// </summary>
    public class SyncManagerTests : IDisposable
    {
        private readonly Mock<DatabaseService> _mockDatabaseService;
        private readonly Mock<ISyncService> _mockSyncService;
        private readonly SyncManager _syncManager;
        private readonly SyncConfiguration _testConfig;

        public SyncManagerTests()
        {
            _mockDatabaseService = new Mock<DatabaseService>();
            _mockSyncService = new Mock<ISyncService>();
            _syncManager = new SyncManager(_mockDatabaseService.Object);
            
            _testConfig = new SyncConfiguration
            {
                ServiceType = "test",
                AutoSync = true,
                SyncIntervalMinutes = 30,
                EnableEncryption = true,
                ConflictResolution = ConflictResolutionStrategy.ServerWins,
                EnableRealTimeSync = false,
                DatabaseUrl = "https://test.example.com",
                ApiKey = "test-api-key",
                EncryptionKey = "test-encryption-key-32-characters"
            };
        }

        [Fact]
        public async Task InitializeAsync_WithValidConfig_ShouldReturnTrue()
        {
            // Arrange
            _mockSyncService.Setup(s => s.InitializeAsync(It.IsAny<SyncConfiguration>()))
                .ReturnsAsync(true);

            // Act
            var result = await _syncManager.InitializeAsync(_testConfig);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task InitializeAsync_WithInvalidConfig_ShouldReturnFalse()
        {
            // Arrange
            _mockSyncService.Setup(s => s.InitializeAsync(It.IsAny<SyncConfiguration>()))
                .ReturnsAsync(false);

            // Act
            var result = await _syncManager.InitializeAsync(_testConfig);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task InitializeAsync_WithNullConfig_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            await Assert.ThrowsAsync<ArgumentNullException>(() => _syncManager.InitializeAsync(null));
        }

        [Fact]
        public async Task SyncEntityAsync_WithValidEntity_ShouldReturnTrue()
        {
            // Arrange
            var customer = new Customer { Id = 1, Name = "Test Customer" };
            
            _mockSyncService.Setup(s => s.SyncToCloudAsync(It.IsAny<SyncData>()))
                .ReturnsAsync(new SyncResult { Success = true });

            // Act
            var result = await _syncManager.SyncEntityAsync(customer, SyncOperation.Create);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task SyncEntityAsync_WithNullEntity_ShouldReturnFalse()
        {
            // Act
            var result = await _syncManager.SyncEntityAsync<Customer>(null, SyncOperation.Create);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task SyncNowAsync_WhenNotInitialized_ShouldReturnFailureResult()
        {
            // Act
            var result = await _syncManager.SyncNowAsync();

            // Assert
            Assert.False(result.Success);
            Assert.Equal("خدمة المزامنة غير مهيأة", result.Message);
        }

        [Fact]
        public async Task CreateBackupAsync_WhenNotInitialized_ShouldReturnFailureResult()
        {
            // Act
            var result = await _syncManager.CreateBackupAsync();

            // Assert
            Assert.False(result.Success);
            Assert.Equal("خدمة المزامنة غير مهيأة", result.Message);
        }

        [Fact]
        public async Task RestoreFromBackupAsync_WhenNotInitialized_ShouldReturnFailureResult()
        {
            // Arrange
            var backupId = "test-backup-id";

            // Act
            var result = await _syncManager.RestoreFromBackupAsync(backupId);

            // Assert
            Assert.False(result.Success);
            Assert.Equal("خدمة المزامنة غير مهيأة", result.Message);
        }

        [Fact]
        public async Task TestConnectionAsync_WhenNotInitialized_ShouldReturnFalse()
        {
            // Act
            var result = await _syncManager.TestConnectionAsync();

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task GetBackupsAsync_WhenNotInitialized_ShouldReturnEmptyList()
        {
            // Act
            var result = await _syncManager.GetBackupsAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public void StartAutoSync_ShouldSetAutoSyncEnabled()
        {
            // Act
            _syncManager.StartAutoSync();

            // Assert
            Assert.True(_syncManager.AutoSyncEnabled);
        }

        [Fact]
        public void StopAutoSync_ShouldSetAutoSyncDisabled()
        {
            // Arrange
            _syncManager.StartAutoSync();

            // Act
            _syncManager.StopAutoSync();

            // Assert
            Assert.False(_syncManager.AutoSyncEnabled);
        }

        [Fact]
        public async Task DisconnectAsync_ShouldStopAutoSync()
        {
            // Arrange
            _syncManager.StartAutoSync();

            // Act
            await _syncManager.DisconnectAsync();

            // Assert
            Assert.False(_syncManager.AutoSyncEnabled);
        }

        [Fact]
        public void IsConnected_WhenNotInitialized_ShouldReturnFalse()
        {
            // Act & Assert
            Assert.False(_syncManager.IsConnected);
        }

        [Fact]
        public void CurrentServiceName_WhenNotInitialized_ShouldReturnNull()
        {
            // Act & Assert
            Assert.Null(_syncManager.CurrentServiceName);
        }

        [Fact]
        public void LastSyncTime_WhenNotInitialized_ShouldReturnNull()
        {
            // Act & Assert
            Assert.Null(_syncManager.LastSyncTime);
        }

        [Theory]
        [InlineData("")]
        [InlineData("invalid")]
        [InlineData("unknown_service")]
        public async Task InitializeAsync_WithUnsupportedServiceType_ShouldThrowNotSupportedException(string serviceType)
        {
            // Arrange
            var config = new SyncConfiguration { ServiceType = serviceType };

            // Act & Assert
            await Assert.ThrowsAsync<NotSupportedException>(() => _syncManager.InitializeAsync(config));
        }

        [Fact]
        public async Task SyncNowAsync_WithConflicts_ShouldHandleConflictsCorrectly()
        {
            // Arrange
            var conflicts = new List<SyncConflict>
            {
                new SyncConflict
                {
                    EntityType = "Customer",
                    EntityId = "1",
                    LocalData = new SyncData { EntityType = "Customer", EntityId = "1" },
                    RemoteData = new SyncData { EntityType = "Customer", EntityId = "1" },
                    ConflictType = ConflictType.DataConflict
                }
            };

            var syncResult = new SyncResult
            {
                Success = true,
                Conflicts = conflicts
            };

            _mockSyncService.Setup(s => s.SyncFromCloudAsync(It.IsAny<DateTime?>()))
                .ReturnsAsync(syncResult);

            _mockSyncService.Setup(s => s.ResolveConflictsAsync(It.IsAny<List<SyncConflict>>()))
                .ReturnsAsync(new ConflictResolutionResult
                {
                    Success = true,
                    ResolvedConflicts = conflicts
                });

            // Act
            var result = await _syncManager.SyncNowAsync();

            // Assert
            Assert.True(result.Success);
            Assert.Contains("تم حل 1 تعارض", result.Message);
        }

        [Fact]
        public void Dispose_ShouldStopAutoSyncAndCleanup()
        {
            // Arrange
            _syncManager.StartAutoSync();

            // Act
            _syncManager.Dispose();

            // Assert
            Assert.False(_syncManager.AutoSyncEnabled);
        }

        [Fact]
        public void Dispose_CalledMultipleTimes_ShouldNotThrow()
        {
            // Act & Assert
            _syncManager.Dispose();
            _syncManager.Dispose(); // يجب ألا يرمي استثناء
        }

        public void Dispose()
        {
            _syncManager?.Dispose();
        }
    }

    /// <summary>
    /// اختبارات أحداث مدير المزامنة
    /// </summary>
    public class SyncManagerEventTests : IDisposable
    {
        private readonly Mock<DatabaseService> _mockDatabaseService;
        private readonly SyncManager _syncManager;
        private bool _connectionStatusChanged;
        private bool _syncProgressReported;
        private bool _syncCompleted;
        private bool _syncErrorOccurred;
        private bool _conflictDetected;

        public SyncManagerEventTests()
        {
            _mockDatabaseService = new Mock<DatabaseService>();
            _syncManager = new SyncManager(_mockDatabaseService.Object);

            // ربط الأحداث
            _syncManager.ConnectionStatusChanged += (s, e) => _connectionStatusChanged = true;
            _syncManager.SyncProgress += (s, e) => _syncProgressReported = true;
            _syncManager.SyncCompleted += (s, e) => _syncCompleted = true;
            _syncManager.SyncError += (s, e) => _syncErrorOccurred = true;
            _syncManager.ConflictDetected += (s, e) => _conflictDetected = true;
        }

        [Fact]
        public void Events_ShouldBeRaisedCorrectly()
        {
            // هذا اختبار أساسي للتأكد من أن الأحداث مربوطة بشكل صحيح
            // في الاختبارات الفعلية، ستحتاج لمحاكاة السيناريوهات التي تؤدي لإطلاق الأحداث

            Assert.False(_connectionStatusChanged);
            Assert.False(_syncProgressReported);
            Assert.False(_syncCompleted);
            Assert.False(_syncErrorOccurred);
            Assert.False(_conflictDetected);
        }

        public void Dispose()
        {
            _syncManager?.Dispose();
        }
    }
}
