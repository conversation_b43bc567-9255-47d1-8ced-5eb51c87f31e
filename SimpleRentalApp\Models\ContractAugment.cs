using System.ComponentModel.DataAnnotations;

namespace SimpleRentalApp.Models;

public class ContractAugment
{
    public int Id { get; set; }
    
    [Required]
    public int ContractId { get; set; }
    public Contract Contract { get; set; } = null!;
    
    [Required]
    [StringLength(100)]
    public string AugmentType { get; set; } = string.Empty;

    [Required]
    public decimal Percentage { get; set; }

    [Required]
    public decimal BaseRent { get; set; }

    [Required]
    public decimal CalculatedAmount { get; set; }

    [Required]
    public DateTime ApplicationDate { get; set; }

    [StringLength(500)]
    public string Notes { get; set; } = string.Empty;
    
    public DateTime CreatedDate { get; set; } = DateTime.Now;
    
    public bool IsActive { get; set; } = true;
    
    // Computed properties
    public string CalculatedAmountDisplay => $"{CalculatedAmount:N0} درهم";

    public string BaseRentDisplay => $"{BaseRent:N0} درهم";

    public string PercentageDisplay => $"{Percentage:F1}%";

    public string ApplicationDateDisplay => ApplicationDate.ToString("dd/MM/yyyy");

    public string AugmentTypeDisplay => AugmentType switch
    {
        "AdditionalService" => "خدمة إضافية",
        "EndOfContractIncrease" => "زيادة نهاية العقد",
        "MaintenanceFee" => "رسوم صيانة",
        "InsuranceFee" => "رسوم تأمين",
        "UtilityFee" => "رسوم مرافق",
        "PenaltyFee" => "غرامة",
        "Other" => "أخرى",
        _ => AugmentType
    };
    
    public string StatusDisplay => IsActive ? "نشط" : "ملغي";
    
    public string StatusColor => IsActive ? "#4CAF50" : "#F44336";
    
    public string AugmentIcon => AugmentType switch
    {
        "AdditionalService" => "🔧",
        "EndOfContractIncrease" => "📈",
        "MaintenanceFee" => "🔨",
        "InsuranceFee" => "🛡️",
        "UtilityFee" => "⚡",
        "PenaltyFee" => "⚠️",
        "Other" => "📋",
        _ => "📋"
    };

    public string FullDescription
    {
        get
        {
            var description = $"{AugmentIcon} {AugmentTypeDisplay}: {CalculatedAmountDisplay} ({PercentageDisplay})";
            if (!string.IsNullOrEmpty(Notes))
                description += $" - {Notes}";
            return description;
        }
    }
}

public static class AugmentTypes
{
    public static readonly Dictionary<string, string> Types = new()
    {
        { "AdditionalService", "🔧 خدمة إضافية" },
        { "EndOfContractIncrease", "📈 زيادة نهاية العقد" },
        { "MaintenanceFee", "🔨 رسوم صيانة" },
        { "InsuranceFee", "🛡️ رسوم تأمين" },
        { "UtilityFee", "⚡ رسوم مرافق" },
        { "PenaltyFee", "⚠️ غرامة" },
        { "Other", "📋 أخرى" }
    };

    public static List<KeyValuePair<string, string>> GetTypesList()
    {
        return Types.ToList();
    }
}
