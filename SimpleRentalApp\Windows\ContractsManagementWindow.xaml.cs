using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Media;
using Microsoft.EntityFrameworkCore;
using SimpleRentalApp.Data;
using SimpleRentalApp.Models;

namespace SimpleRentalApp.Windows
{
    public partial class ContractsManagementWindow : Window
    {
        private readonly RentalDbContext _context;
        private List<ContractDisplayInfo> _allContracts;
        private CollectionViewSource _contractsViewSource;

        public ContractsManagementWindow()
        {
            InitializeComponent();
            _context = new RentalDbContext();
            _allContracts = new List<ContractDisplayInfo>();
            _contractsViewSource = new CollectionViewSource();
            
            Loaded += ContractsManagementWindow_Loaded;
        }

        private async void ContractsManagementWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadContractsAsync();
        }

        private async Task LoadContractsAsync()
        {
            try
            {
                var contracts = await _context.Contracts
                    .Include(c => c.Property)
                    .Include(c => c.Customer)
                    .Include(c => c.RentIncreases)
                    .Include(c => c.Attachments)
                    .ToListAsync();

                // إصلاح العقود التي لها InitialRentAmount = 0
                await FixContractsWithZeroRent(contracts);

                _allContracts = contracts.Select(c => new ContractDisplayInfo
                {
                    Contract = c,
                    Property = c.Property,
                    Customer = c.Customer
                }).ToList();

                UpdateStatistics();
                ApplyFiltersAndSort();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل العقود: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateStatistics()
        {
            var totalContracts = _allContracts.Count;
            var activeContracts = _allContracts.Count(c => c.Contract.Status == ContractStatus.Active);
            var expiringSoon = _allContracts.Count(c => c.Contract.Status == ContractStatus.ExpiringSoon);
            var expired = _allContracts.Count(c => c.Contract.Status == ContractStatus.Expired);
            var averageDuration = _allContracts.Any() ? 
                _allContracts.Average(c => (c.Contract.EndDate - c.Contract.StartDate).TotalDays / 30) : 0;

            TotalContractsText.Text = totalContracts.ToString();
            ActiveContractsText.Text = activeContracts.ToString();
            ExpiringSoonText.Text = expiringSoon.ToString();
            ExpiredContractsText.Text = expired.ToString();
            AverageDurationText.Text = $"{averageDuration:F0} شهر";
        }

        private void ApplyFiltersAndSort()
        {
            if (_allContracts == null)
                return;

            var filtered = _allContracts.AsEnumerable();

            // Apply search filter
            var searchText = SearchTextBox?.Text?.Trim().ToLower();
            if (!string.IsNullOrEmpty(searchText))
            {
                filtered = filtered.Where(c =>
                    c.PropertyName.ToLower().Contains(searchText) ||
                    c.CustomerName.ToLower().Contains(searchText) ||
                    c.Contract.Id.ToString().Contains(searchText));
            }

            // Apply status filter
            var statusFilter = StatusFilterComboBox?.SelectedIndex ?? 0;
            switch (statusFilter)
            {
                case 1: // Active
                    filtered = filtered.Where(c => c.Contract.Status == ContractStatus.Active);
                    break;
                case 2: // Expiring Soon
                    filtered = filtered.Where(c => c.Contract.Status == ContractStatus.ExpiringSoon);
                    break;
                case 3: // Expired
                    filtered = filtered.Where(c => c.Contract.Status == ContractStatus.Expired);
                    break;
            }

            // Apply sorting
            var sortOption = SortComboBox?.SelectedIndex ?? 0;
            switch (sortOption)
            {
                case 0: // Start Date
                    filtered = filtered.OrderByDescending(c => c.Contract.StartDate);
                    break;
                case 1: // End Date
                    filtered = filtered.OrderBy(c => c.Contract.EndDate);
                    break;
                case 2: // Current rent
                    filtered = filtered.OrderByDescending(c => c.Contract.CurrentRentAmount);
                    break;
                case 3: // Property name
                    filtered = filtered.OrderBy(c => c.PropertyName);
                    break;
                case 4: // Customer name
                    filtered = filtered.OrderBy(c => c.CustomerName);
                    break;
            }

            var filteredList = filtered.ToList();
            if (ContractsDataGrid != null)
                ContractsDataGrid.ItemsSource = filteredList;
            if (ResultsCountText != null)
                ResultsCountText.Text = $"{filteredList.Count} عقد معروض";
        }

        // Event Handlers
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_allContracts != null && _allContracts.Any())
                ApplyFiltersAndSort();
        }

        private void StatusFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_allContracts != null && _allContracts.Any())
                ApplyFiltersAndSort();
        }

        private void SortComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_allContracts != null && _allContracts.Any())
                ApplyFiltersAndSort();
        }



        private void AddContractButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var dialog = new ContractCreationDialog();
                if (dialog.ShowDialog() == true)
                {
                    _ = LoadContractsAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة إضافة العقد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void EditContractButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is int contractId)
                {
                    var contract = _allContracts.FirstOrDefault(c => c.Contract.Id == contractId);
                    if (contract != null)
                    {
                        var dialog = new ContractDetailsDialog(contract.Contract);
                        if (dialog.ShowDialog() == true)
                        {
                            _ = LoadContractsAsync();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة تعديل العقد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddRentIncreaseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is int contractId)
                {
                    var contract = _allContracts.FirstOrDefault(c => c.Contract.Id == contractId);
                    if (contract != null)
                    {
                        var dialog = new RentIncreaseDialog(contract.Contract);
                        if (dialog.ShowDialog() == true)
                        {
                            _ = LoadContractsAsync();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة زيادة الإيجار: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ManageAttachmentsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is int contractId)
                {
                    var contract = _allContracts.FirstOrDefault(c => c.Contract.Id == contractId);
                    if (contract != null)
                    {
                        var dialog = new AttachmentsDialog(contract.Contract);
                        dialog.ShowDialog();
                        _ = LoadContractsAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة إدارة المرفقات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void DeleteContractButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is int contractId)
                {
                    var contract = _allContracts.FirstOrDefault(c => c.Contract.Id == contractId);
                    if (contract != null)
                    {
                        var result = MessageBox.Show(
                            $"هل أنت متأكد من حذف عقد {contract.PropertyName} مع {contract.CustomerName}؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                            "تأكيد الحذف",
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Warning);

                        if (result == MessageBoxResult.Yes)
                        {
                            _context.Contracts.Remove(contract.Contract);
                            await _context.SaveChangesAsync();
                            await LoadContractsAsync();
                            
                            MessageBox.Show("تم حذف العقد بنجاح", "تم الحذف",
                                MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف العقد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void FixContractsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "هل تريد إصلاح العقود التي تظهر بإيجار 0 درهم؟\n\n" +
                    "سيتم تعيين الإيجار الأولي من قيمة الإيجار الشهري للعقار.\n\n" +
                    "هذا الإجراء آمن ولن يؤثر على البيانات الموجودة.",
                    "تأكيد إصلاح العقود",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    if (sender is Button button)
                    {
                        button.IsEnabled = false;
                        button.Content = "⏳ جاري الإصلاح...";
                    }

                    // البحث عن العقود التي تحتاج إصلاح
                    var contractsToFix = await _context.Contracts
                        .Include(c => c.Property)
                        .Where(c => c.InitialRentAmount == 0 && c.Property.MonthlyRent > 0)
                        .ToListAsync();

                    if (contractsToFix.Any())
                    {
                        int fixedCount = 0;
                        foreach (var contract in contractsToFix)
                        {
                            contract.InitialRentAmount = contract.Property.MonthlyRent;
                            fixedCount++;
                        }

                        await _context.SaveChangesAsync();
                        await LoadContractsAsync();

                        MessageBox.Show(
                            $"تم إصلاح {fixedCount} عقد بنجاح!\n\n" +
                            "الآن ستظهر قيم الإيجار الصحيحة في جميع العقود.",
                            "تم الإصلاح بنجاح",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show(
                            "لا توجد عقود تحتاج إلى إصلاح.\n\nجميع العقود تحتوي على قيم إيجار صحيحة.",
                            "لا توجد عقود للإصلاح",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information);
                    }

                    if (sender is Button btn)
                    {
                        btn.IsEnabled = true;
                        btn.Content = "🔧 إصلاح العقود";
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إصلاح العقود: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);

                if (sender is Button btn)
                {
                    btn.IsEnabled = true;
                    btn.Content = "🔧 إصلاح العقود";
                }
            }
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("تصدير التقرير - قيد التطوير", "معلومات",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        /// <summary>
        /// إصلاح العقود التي لها InitialRentAmount = 0 بتعيين قيمة من العقار
        /// </summary>
        private async Task FixContractsWithZeroRent(List<Contract> contracts)
        {
            try
            {
                bool hasChanges = false;

                foreach (var contract in contracts.Where(c => c.InitialRentAmount == 0 && c.Property != null))
                {
                    if (contract.Property.MonthlyRent > 0)
                    {
                        contract.InitialRentAmount = contract.Property.MonthlyRent;
                        hasChanges = true;

                        System.Diagnostics.Debug.WriteLine(
                            $"تم إصلاح العقد {contract.Id}: تعيين الإيجار الأولي إلى {contract.InitialRentAmount} درهم");
                    }
                }

                if (hasChanges)
                {
                    await _context.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine("تم حفظ التغييرات لإصلاح العقود");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إصلاح العقود: {ex.Message}");
                // لا نعرض رسالة خطأ للمستخدم لأن هذا إصلاح تلقائي
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            base.OnClosed(e);
        }
    }

    // Enhanced ContractDisplayInfo class
    public class ContractDisplayInfo
    {
        public Contract Contract { get; set; }
        public Property Property { get; set; }
        public Customer Customer { get; set; }

        // Basic info
        public int Id => Contract.Id;
        public string PropertyName => Property?.Name ?? "غير محدد";
        public string CustomerName => Customer?.FullName ?? "غير محدد";

        // Status information
        public string StatusDisplay => Contract.Status switch
        {
            ContractStatus.Active => "نشط",
            ContractStatus.ExpiringSoon => "قريب الانتهاء",
            ContractStatus.Expired => "منتهي",
            _ => "غير محدد"
        };

        public Brush StatusColor => Contract.Status switch
        {
            ContractStatus.Active => new SolidColorBrush(Color.FromRgb(76, 175, 80)),
            ContractStatus.ExpiringSoon => new SolidColorBrush(Color.FromRgb(255, 152, 0)),
            ContractStatus.Expired => new SolidColorBrush(Color.FromRgb(244, 67, 54)),
            _ => new SolidColorBrush(Color.FromRgb(158, 158, 158))
        };

        // Date displays
        public string StartDateDisplay => Contract.StartDate.ToString("dd/MM/yyyy");
        public string EndDateDisplay => Contract.EndDate.ToString("dd/MM/yyyy");

        // Financial info
        public string CurrentRentDisplay => $"{Contract.CurrentRentAmount:N0} درهم";

        // Duration
        public string DurationDisplay => $"{(Contract.EndDate - Contract.StartDate).TotalDays / 30:F0} شهر";

        // Days remaining
        public string DaysRemainingDisplay
        {
            get
            {
                var daysRemaining = (Contract.EndDate - DateTime.Now).TotalDays;
                if (daysRemaining < 0)
                    return $"انتهى منذ {Math.Abs(daysRemaining):F0} يوم";
                else if (daysRemaining == 0)
                    return "ينتهي اليوم";
                else
                    return $"{daysRemaining:F0} يوم متبقي";
            }
        }

        public Brush DaysRemainingColor
        {
            get
            {
                var daysRemaining = (Contract.EndDate - DateTime.Now).TotalDays;
                if (daysRemaining < 0)
                    return new SolidColorBrush(Color.FromRgb(244, 67, 54)); // Red
                else if (daysRemaining <= 30)
                    return new SolidColorBrush(Color.FromRgb(255, 152, 0)); // Orange
                else if (daysRemaining <= 90)
                    return new SolidColorBrush(Color.FromRgb(255, 193, 7)); // Yellow
                else
                    return new SolidColorBrush(Color.FromRgb(76, 175, 80)); // Green
            }
        }

        // Attachments
        public string AttachmentsCountDisplay => $"{Contract.Attachments?.Count ?? 0} ملف";
    }
}
