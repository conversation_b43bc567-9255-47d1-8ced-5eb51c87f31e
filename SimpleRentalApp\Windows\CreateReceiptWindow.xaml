<Window x:Class="SimpleRentalApp.Windows.CreateReceiptWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إنشاء توصيل كراء"
        Height="750" Width="700"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        Background="#F8F9FA"
        WindowStyle="None"
        AllowsTransparency="True"
        ResizeMode="NoResize">
    
    <Border Background="White" CornerRadius="16" Margin="20">
        <Border.Effect>
            <DropShadowEffect Color="#424242" 
                            Direction="270" 
                            ShadowDepth="12" 
                            BlurRadius="24" 
                            Opacity="0.3"/>
        </Border.Effect>
        
        <Grid Margin="30">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Modern Header -->
            <Border Grid.Row="0" 
                    Background="#2196F3" 
                    CornerRadius="12" 
                    Padding="25,20"
                    Margin="0,0,0,25">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <TextBlock Text="🧾" FontSize="28" Margin="0,0,15,0"/>
                        <TextBlock Text="إنشاء توصيل كراء"
                                   FontSize="22"
                                   FontWeight="Bold"
                                   Foreground="White"/>
                    </StackPanel>
                    <TextBlock Name="PropertyInfoTextBlock"
                               Text="المحل: --"
                               FontSize="16"
                               Foreground="#E3F2FD"
                               HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>

            <!-- Form Content -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    
                    <!-- Receipt Number -->
                    <Border Background="#E8F5E8" 
                            BorderBrush="#4CAF50" 
                            BorderThickness="2" 
                            CornerRadius="12" 
                            Padding="20"
                            Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="🔢 رقم التوصيل" 
                                       FontWeight="Bold" 
                                       FontSize="16"
                                       Foreground="#2E7D32"
                                       Margin="0,0,0,15"/>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBox Name="ReceiptNumberTextBox" 
                                         Grid.Column="0"
                                         Height="45"
                                         FontSize="16"
                                         FontWeight="Bold"
                                         Padding="15,10"
                                         BorderBrush="#4CAF50"
                                         BorderThickness="2"
                                         Background="White"
                                         Foreground="#2E7D32"
                                         HorizontalContentAlignment="Center"/>
                                <Button Name="GenerateNumberButton"
                                        Grid.Column="1"
                                        Content="🔄 توليد"
                                        Width="80"
                                        Height="45"
                                        Margin="10,0,0,0"
                                        Background="#4CAF50"
                                        Foreground="White"
                                        BorderThickness="0"
                                        FontWeight="Bold"
                                        Click="GenerateNumberButton_Click"/>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- Amount -->
                    <Border Background="#FFF3E0" 
                            BorderBrush="#FF9800" 
                            BorderThickness="2" 
                            CornerRadius="12" 
                            Padding="20"
                            Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="💰 المبلغ المؤدى (درهم) *" 
                                       FontWeight="Bold" 
                                       FontSize="16"
                                       Foreground="#E65100"
                                       Margin="0,0,0,15"/>
                            <TextBox Name="AmountTextBox" 
                                     Height="50"
                                     FontSize="18"
                                     FontWeight="Bold"
                                     Padding="15,10"
                                     BorderBrush="#FF9800"
                                     BorderThickness="2"
                                     Background="White"
                                     Foreground="#E65100"
                                     HorizontalContentAlignment="Center"
                                     TextChanged="AmountTextBox_TextChanged"/>
                            <TextBlock Name="AmountInWordsTextBlock"
                                       Text="المبلغ بالحروف: --"
                                       FontSize="12"
                                       Foreground="#FF9800"
                                       FontStyle="Italic"
                                       Margin="0,10,0,0"
                                       TextWrapping="Wrap"/>
                        </StackPanel>
                    </Border>

                    <!-- Tenant Name -->
                    <Border Background="#E8F5E8"
                            BorderBrush="#4CAF50"
                            BorderThickness="1"
                            CornerRadius="12"
                            Padding="20"
                            Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="👤 اسم المكتري *"
                                       FontWeight="Bold"
                                       FontSize="16"
                                       Foreground="#2E7D32"
                                       Margin="0,0,0,15"/>
                            <TextBox Name="TenantNameTextBox"
                                     Height="45"
                                     FontSize="14"
                                     Padding="15,10"
                                     BorderBrush="#4CAF50"
                                     BorderThickness="1"
                                     Background="White"/>
                        </StackPanel>
                    </Border>

                    <!-- Property Address -->
                    <Border Background="#F3E5F5"
                            BorderBrush="#9C27B0"
                            BorderThickness="1"
                            CornerRadius="12"
                            Padding="20"
                            Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="🏢 عنوان المحل *"
                                       FontWeight="Bold"
                                       FontSize="16"
                                       Foreground="#7B1FA2"
                                       Margin="0,0,0,15"/>
                            <TextBox Name="PropertyAddressTextBox"
                                     Height="60"
                                     FontSize="14"
                                     Padding="15,10"
                                     BorderBrush="#9C27B0"
                                     BorderThickness="1"
                                     Background="White"
                                     TextWrapping="Wrap"
                                     AcceptsReturn="True"/>
                        </StackPanel>
                    </Border>

                    <!-- Payment Period -->
                    <Border Background="#E3F2FD"
                            BorderBrush="#2196F3"
                            BorderThickness="2"
                            CornerRadius="12"
                            Padding="20"
                            Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="📅 مدة الأداء *"
                                       FontWeight="Bold"
                                       FontSize="16"
                                       Foreground="#1976D2"
                                       Margin="0,0,0,15"/>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="من:" FontWeight="Bold" Margin="0,0,0,5"/>
                                    <DatePicker Name="PeriodStartDatePicker"
                                                Height="40"
                                                FontSize="14"
                                                BorderBrush="#2196F3"
                                                BorderThickness="1"/>
                                </StackPanel>

                                <TextBlock Grid.Column="1"
                                           Text="إلى"
                                           FontSize="16"
                                           FontWeight="Bold"
                                           VerticalAlignment="Center"
                                           Margin="15,20,15,0"/>

                                <StackPanel Grid.Column="2">
                                    <TextBlock Text="إلى:" FontWeight="Bold" Margin="0,0,0,5"/>
                                    <DatePicker Name="PeriodEndDatePicker"
                                                Height="40"
                                                FontSize="14"
                                                BorderBrush="#2196F3"
                                                BorderThickness="1"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- Payment Date -->
                    <Border Background="#F3E5F5" 
                            BorderBrush="#9C27B0" 
                            BorderThickness="2" 
                            CornerRadius="12" 
                            Padding="20"
                            Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="📆 تاريخ الأداء *" 
                                       FontWeight="Bold" 
                                       FontSize="16"
                                       Foreground="#7B1FA2"
                                       Margin="0,0,0,15"/>
                            <DatePicker Name="PaymentDatePicker" 
                                        Height="45"
                                        FontSize="14"
                                        BorderBrush="#9C27B0"
                                        BorderThickness="2"/>
                        </StackPanel>
                    </Border>

                    <!-- Payment Method -->
                    <Border Background="#F0F8FF" 
                            BorderBrush="#4169E1" 
                            BorderThickness="1" 
                            CornerRadius="12" 
                            Padding="20"
                            Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="💳 طريقة الدفع" 
                                       FontWeight="Bold" 
                                       FontSize="16"
                                       Foreground="#4169E1"
                                       Margin="0,0,0,15"/>
                            <ComboBox Name="PaymentMethodComboBox" 
                                      Height="45"
                                      FontSize="14"
                                      Padding="15,10"
                                      BorderBrush="#4169E1"
                                      BorderThickness="1"
                                      Background="White">
                                <ComboBoxItem Content="نقداً" IsSelected="True"/>
                                <ComboBoxItem Content="تحويل بنكي"/>
                                <ComboBoxItem Content="شيك"/>
                                <ComboBoxItem Content="أخرى"/>
                            </ComboBox>
                        </StackPanel>
                    </Border>

                    <!-- Legal Notices Option -->
                    <Border Background="#FFF3E0"
                            BorderBrush="#FF9800"
                            BorderThickness="1"
                            CornerRadius="12"
                            Padding="20"
                            Margin="0,0,0,20">
                        <StackPanel>
                            <CheckBox Name="ShowLegalNoticesCheckBox"
                                      Content="⚖️ إظهار التعليمات القانونية في التوصيل"
                                      FontWeight="Bold"
                                      FontSize="14"
                                      Foreground="#E65100"
                                      IsChecked="True"/>
                        </StackPanel>
                    </Border>

                    <!-- Notes -->
                    <Border Background="#F8F9FA"
                            BorderBrush="#DEE2E6"
                            BorderThickness="1"
                            CornerRadius="12"
                            Padding="20"
                            Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="📝 ملاحظات إضافية"
                                       FontWeight="Bold"
                                       FontSize="16"
                                       Foreground="#495057"
                                       Margin="0,0,0,15"/>
                            <TextBox Name="NotesTextBox"
                                     Height="80"
                                     FontSize="14"
                                     Padding="15,10"
                                     BorderBrush="#CED4DA"
                                     BorderThickness="1"
                                     Background="White"
                                     TextWrapping="Wrap"
                                     AcceptsReturn="True"
                                     VerticalScrollBarVisibility="Auto"/>
                        </StackPanel>
                    </Border>

                </StackPanel>
            </ScrollViewer>

            <!-- Action Buttons -->
            <Border Grid.Row="2" 
                    Background="#F8F9FA" 
                    CornerRadius="12" 
                    Padding="20"
                    Margin="0,25,0,0">
                <StackPanel Orientation="Horizontal" 
                            HorizontalAlignment="Center">
                    <Button Name="PreviewAndPrintButton"
                            Content="🖨️ معاينة وطباعة"
                            Width="160"
                            Height="50"
                            FontSize="16"
                            FontWeight="Bold"
                            Background="#2196F3"
                            Foreground="White"
                            BorderThickness="0"
                            Margin="0,0,15,0"
                            Click="PreviewAndPrintButton_Click"/>

                    <Button Name="SaveAsPdfButton"
                            Content="📄 حفظ PDF"
                            Width="130"
                            Height="50"
                            FontSize="16"
                            Background="#FF9800"
                            Foreground="White"
                            BorderThickness="0"
                            Margin="0,0,15,0"
                            Click="SaveAsPdfButton_Click"/>

                    <Button Name="CreateOnlyButton"
                            Content="💾 إنشاء فقط"
                            Width="120"
                            Height="50"
                            FontSize="16"
                            Background="#4CAF50"
                            Foreground="White"
                            BorderThickness="0"
                            Margin="0,0,15,0"
                            Click="CreateOnlyButton_Click"/>

                    <Button Name="CancelButton"
                            Content="❌ إلغاء"
                            Width="100"
                            Height="50"
                            FontSize="16"
                            Background="#6C757D"
                            Foreground="White"
                            BorderThickness="0"
                            Click="CancelButton_Click"/>
                </StackPanel>
            </Border>
        </Grid>
    </Border>
</Window>
