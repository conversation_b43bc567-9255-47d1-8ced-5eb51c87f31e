using System.ComponentModel.DataAnnotations;

namespace SimpleRentalApp.Models;

public class RentReceipt
{
    public int Id { get; set; }
    
    [Required]
    [StringLength(50)]
    public string ReceiptNumber { get; set; } = string.Empty;
    
    [Required]
    public int ContractId { get; set; }
    public Contract Contract { get; set; } = null!;

    public int? PaymentId { get; set; }
    public Payment? Payment { get; set; }
    
    [Required]
    public decimal Amount { get; set; }
    
    [Required]
    public DateTime PaymentDate { get; set; }
    
    public DateTime? PeriodStartDate { get; set; }

    public DateTime? PeriodEndDate { get; set; }

    [StringLength(100)]
    public string PaymentPeriod { get; set; } = string.Empty; // Fallback for compatibility
    
    [StringLength(50)]
    public string PaymentMethod { get; set; } = "نقداً";
    
    [StringLength(500)]
    public string Notes { get; set; } = string.Empty;
    
    public DateTime CreatedDate { get; set; } = DateTime.Now;
    
    [StringLength(100)]
    public string CreatedBy { get; set; } = "النظام";
    
    public bool IsPrinted { get; set; } = false;
    
    public DateTime? PrintedDate { get; set; }

    public bool ShowLegalNotices { get; set; } = true;
    
    // Computed properties
    public string AmountInWords => ConvertToArabicWords(Amount);
    
    public string PaymentDateDisplay => PaymentDate.ToString("dd/MM/yyyy");

    public string CreatedDateDisplay => CreatedDate.ToString("dd/MM/yyyy HH:mm");

    public string PeriodStartDateDisplay => PeriodStartDate?.ToString("dd/MM/yyyy") ?? "";

    public string PeriodEndDateDisplay => PeriodEndDate?.ToString("dd/MM/yyyy") ?? "";

    public string PaymentPeriodDisplay
    {
        get
        {
            if (PeriodStartDate.HasValue && PeriodEndDate.HasValue)
                return $"من {PeriodStartDateDisplay} إلى {PeriodEndDateDisplay}";
            else
                return PaymentPeriod; // Fallback to old field
        }
    }
    
    public string AmountDisplay => $"{Amount:N0} درهم";
    
    public string StatusDisplay => IsPrinted ? "مطبوع" : "غير مطبوع";
    
    public string StatusColor => IsPrinted ? "#4CAF50" : "#FF9800";
    
    // Convert amount to Arabic words
    private string ConvertToArabicWords(decimal amount)
    {
        if (amount == 0) return "صفر درهم";
        
        var integerPart = (long)Math.Floor(amount);
        var decimalPart = (int)Math.Round((amount - integerPart) * 100);
        
        string result = ConvertIntegerToArabicWords(integerPart);
        
        if (integerPart == 1)
            result += " درهم واحد";
        else if (integerPart == 2)
            result += " درهمان";
        else if (integerPart >= 3 && integerPart <= 10)
            result += " دراهم";
        else
            result += " درهم";
        
        if (decimalPart > 0)
        {
            result += " و " + ConvertIntegerToArabicWords(decimalPart);
            if (decimalPart == 1)
                result += " سنتيم واحد";
            else if (decimalPart == 2)
                result += " سنتيمان";
            else if (decimalPart >= 3 && decimalPart <= 10)
                result += " سنتيمات";
            else
                result += " سنتيم";
        }
        
        return result + "";
    }
    
    private string ConvertIntegerToArabicWords(long number)
    {
        if (number == 0) return "صفر";
        
        string[] ones = { "", "واحد", "اثنان", "ثلاثة", "أربعة", "خمسة", "ستة", "سبعة", "ثمانية", "تسعة" };
        string[] tens = { "", "", "عشرون", "ثلاثون", "أربعون", "خمسون", "ستون", "سبعون", "ثمانون", "تسعون" };
        string[] teens = { "عشرة", "أحد عشر", "اثنا عشر", "ثلاثة عشر", "أربعة عشر", "خمسة عشر", "ستة عشر", "سبعة عشر", "ثمانية عشر", "تسعة عشر" };
        string[] hundreds = { "", "مائة", "مائتان", "ثلاثمائة", "أربعمائة", "خمسمائة", "ستمائة", "سبعمائة", "ثمانمائة", "تسعمائة" };
        
        if (number < 10)
            return ones[number];
        else if (number < 20)
            return teens[number - 10];
        else if (number < 100)
        {
            var tensDigit = number / 10;
            var onesDigit = number % 10;
            return tens[tensDigit] + (onesDigit > 0 ? " و " + ones[onesDigit] : "");
        }
        else if (number < 1000)
        {
            var hundredsDigit = number / 100;
            var remainder = number % 100;
            return hundreds[hundredsDigit] + (remainder > 0 ? " و " + ConvertIntegerToArabicWords(remainder) : "");
        }
        else if (number < 1000000)
        {
            var thousands = number / 1000;
            var remainder = number % 1000;
            string result = ConvertIntegerToArabicWords(thousands);
            
            if (thousands == 1)
                result = "ألف";
            else if (thousands == 2)
                result = "ألفان";
            else if (thousands >= 3 && thousands <= 10)
                result += " آلاف";
            else
                result += " ألف";
            
            return result + (remainder > 0 ? " و " + ConvertIntegerToArabicWords(remainder) : "");
        }
        else
        {
            var millions = number / 1000000;
            var remainder = number % 1000000;
            string result = ConvertIntegerToArabicWords(millions);
            
            if (millions == 1)
                result = "مليون";
            else if (millions == 2)
                result = "مليونان";
            else if (millions >= 3 && millions <= 10)
                result += " ملايين";
            else
                result += " مليون";
            
            return result + (remainder > 0 ? " و " + ConvertIntegerToArabicWords(remainder) : "");
        }
    }
}

public static class ReceiptNumberGenerator
{
    public static string GenerateReceiptNumber()
    {
        var year = DateTime.Now.Year;
        var timestamp = DateTime.Now.ToString("MMddHHmmss");
        return $"R{year}-{timestamp}";
    }

    public static string GenerateSequentialReceiptNumber(int lastSequence)
    {
        var year = DateTime.Now.Year;
        var sequence = (lastSequence + 1).ToString("D4");
        return $"R{year}-{sequence}";
    }

    public static string GeneratePropertyReceiptNumber(int propertyId, int lastSequence)
    {
        var year = DateTime.Now.Year;
        var sequence = (lastSequence + 1).ToString("D3");
        return $"R{year}-{propertyId:D3}-{sequence}";
    }
}

public static class LegalNotices
{
    public const string ArabicLegalText = @"* تحت جميع التحفظات القانونية *

تنبيهات:

أولا: إن لم يبيّن لرب الملك بطريق توصيل من القابض بأنه أدى جميع ما عليه من الأداءات الشخصية والخاصة بالمنقول للعام الجاري.

ثانيا: إن لم يدفع أو يقبض طلب الإفراغ كتابة في الآجال المقررة.

ثالثا: إن لم يجعل جميع الإصلاحات التي تلزم المكتري طبق العادة في ذلك أو طبق حالة المحل إن وجدت، ولا يسوغ للمكتري أن يوالي كُلاً أو بعضًا كراء المحل للغير من غير إذن من رب الملك.";
}
