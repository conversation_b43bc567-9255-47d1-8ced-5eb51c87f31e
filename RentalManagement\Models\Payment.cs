using System.ComponentModel.DataAnnotations;

namespace RentalManagement.Models
{
    public enum PaymentStatus
    {
        Unpaid = 1,     // غير مدفوع
        Paid = 2,       // مدفوع
        Overdue = 3     // متأخر
    }

    public class Payment
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "تاريخ الاستحقاق مطلوب")]
        public DateTime DueDate { get; set; }

        public DateTime? PaymentDate { get; set; }

        [Required(ErrorMessage = "المبلغ مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "المبلغ يجب أن يكون أكبر من صفر")]
        public decimal Amount { get; set; }

        public PaymentStatus Status { get; set; } = PaymentStatus.Unpaid;

        [StringLength(500, ErrorMessage = "الملاحظات لا يجب أن تتجاوز 500 حرف")]
        public string Notes { get; set; } = string.Empty;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Foreign keys
        [Required(ErrorMessage = "العميل مطلوب")]
        public int CustomerId { get; set; }

        [Required(ErrorMessage = "العقار مطلوب")]
        public int PropertyId { get; set; }

        // Navigation properties
        public virtual Customer Customer { get; set; } = null!;
        public virtual Property Property { get; set; } = null!;

        // Computed property to check if payment is overdue
        public bool IsOverdue => Status == PaymentStatus.Unpaid && DueDate < DateTime.Now;
    }
}
