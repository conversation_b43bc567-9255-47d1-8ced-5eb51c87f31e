using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SimpleRentalApp.Models
{
    /// <summary>
    /// نموذج إشعار واتساب حقيقي - محدث للنظام الجديد
    /// </summary>
    public class WhatsAppNotification
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// نوع الإشعار
        /// </summary>
        [Required]
        public WhatsAppNotificationType Type { get; set; }

        /// <summary>
        /// معرف المكتري
        /// </summary>
        [Required]
        public int CustomerId { get; set; }

        /// <summary>
        /// معرف المحل
        /// </summary>
        [Required]
        public int PropertyId { get; set; }

        /// <summary>
        /// معرف العقد (اختياري)
        /// </summary>
        public int? ContractId { get; set; }

        /// <summary>
        /// رقم الهاتف المرسل إليه
        /// </summary>
        [Required]
        [StringLength(20)]
        public string PhoneNumber { get; set; } = string.Empty;

        /// <summary>
        /// نص الرسالة
        /// </summary>
        [Required]
        public string MessageContent { get; set; } = string.Empty;

        /// <summary>
        /// المبلغ المرتبط بالإشعار
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        /// <summary>
        /// تاريخ الاستحقاق
        /// </summary>
        public DateTime? DueDate { get; set; }

        /// <summary>
        /// حالة الإشعار
        /// </summary>
        [Required]
        public WhatsAppNotificationStatus Status { get; set; } = WhatsAppNotificationStatus.Pending;

        /// <summary>
        /// تاريخ إنشاء الإشعار
        /// </summary>
        [Required]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ الإرسال الفعلي
        /// </summary>
        public DateTime? SentDate { get; set; }

        /// <summary>
        /// تاريخ آخر محاولة إرسال
        /// </summary>
        public DateTime? LastAttemptDate { get; set; }

        /// <summary>
        /// عدد محاولات الإرسال
        /// </summary>
        public int AttemptCount { get; set; } = 0;

        /// <summary>
        /// رسالة الخطأ في حالة فشل الإرسال
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// معرف الرسالة من واتساب (للتتبع)
        /// </summary>
        public string? WhatsAppMessageId { get; set; }

        /// <summary>
        /// معرف القالب المستخدم
        /// </summary>
        public int? TemplateId { get; set; }

        /// <summary>
        /// الشهر المرتبط بالإشعار (للإيجار الشهري)
        /// </summary>
        public int? Month { get; set; }

        /// <summary>
        /// السنة المرتبطة بالإشعار
        /// </summary>
        public int? Year { get; set; }

        /// <summary>
        /// أولوية الإشعار
        /// </summary>
        public WhatsAppNotificationPriority Priority { get; set; } = WhatsAppNotificationPriority.Normal;

        /// <summary>
        /// هل الإشعار مجدول للإرسال لاحقاً
        /// </summary>
        public bool IsScheduled { get; set; } = false;

        /// <summary>
        /// تاريخ الإرسال المجدول
        /// </summary>
        public DateTime? ScheduledDate { get; set; }

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// مفتاح فريد لمنع التكرار
        /// </summary>
        public string UniqueKey { get; set; } = string.Empty;

        /// <summary>
        /// هل تم إنشاؤه تلقائياً
        /// </summary>
        public bool IsAutoGenerated { get; set; } = false;

        // Navigation Properties
        [ForeignKey("CustomerId")]
        public virtual Customer? Customer { get; set; }

        [ForeignKey("PropertyId")]
        public virtual Property? Property { get; set; }

        [ForeignKey("ContractId")]
        public virtual Contract? Contract { get; set; }

        [ForeignKey("TemplateId")]
        public virtual MessageTemplate? Template { get; set; }

        // خصائص محسوبة للعرض
        public string CustomerName => Customer?.FullName ?? "غير محدد";
        public string PropertyName => Property?.Name ?? "غير محدد";

        public string DueDateDisplay => DueDate.ToString("dd/MM/yyyy");
        public string CreatedDateDisplay => CreatedDate.ToString("dd/MM/yyyy HH:mm");
        public string SentDateDisplay => SentDate?.ToString("dd/MM/yyyy HH:mm") ?? "-";

        public string AmountDisplay => Amount > 0 ? $"{Amount:F0} درهم" : "-";

        public int DaysRemaining => (DueDate.Date - DateTime.Today).Days;
        public string DaysRemainingDisplay
        {
            get
            {
                var days = DaysRemaining;
                if (days > 0)
                    return $"باقي {days} يوم";
                else if (days == 0)
                    return "اليوم";
                else
                    return $"متأخر {Math.Abs(days)} يوم";
            }
        }

        public string DaysRemainingColor
        {
            get
            {
                var days = DaysRemaining;
                if (days > 7) return "#52C41A"; // أخضر
                else if (days > 3) return "#FAAD14"; // أصفر
                else if (days >= 0) return "#FF7A45"; // برتقالي
                else return "#FF4D4F"; // أحمر
            }
        }

        public string TypeDisplay
        {
            get
            {
                return Type switch
                {
                    NotificationType.MonthlyRent => "🏠 كراء شهري",
                    NotificationType.CleaningTax => "🧹 ضريبة النظافة",
                    NotificationType.ContractExpiry => "📋 انتهاء العقد",
                    NotificationType.RentOverdue => "⚠️ دفعة متأخرة",
                    NotificationType.PaymentReminder => "📌 تذكير متأخر",
                    _ => "غير محدد"
                };
            }
        }

        public string StatusDisplay
        {
            get
            {
                return Status switch
                {
                    NotificationStatus.Pending => "⏳ معلق",
                    NotificationStatus.Sent => "✅ تم الإرسال",
                    NotificationStatus.Failed => "❌ فشل",
                    NotificationStatus.Cancelled => "🚫 ملغي",
                    _ => "غير محدد"
                };
            }
        }

        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    WhatsAppNotificationStatus.Pending => "#FAAD14",
                    WhatsAppNotificationStatus.Sent => "#52C41A",
                    WhatsAppNotificationStatus.Delivered => "#52C41A",
                    WhatsAppNotificationStatus.Read => "#1890FF",
                    WhatsAppNotificationStatus.Failed => "#FF4D4F",
                    WhatsAppNotificationStatus.Cancelled => "#8C8C8C",
                    WhatsAppNotificationStatus.Scheduled => "#722ED1",
                    WhatsAppNotificationStatus.Expired => "#FF7A45",
                    _ => "#6C757D"
                };
            }
        }
    }

    /// <summary>
    /// أنواع إشعارات الواتساب
    /// </summary>
    public enum WhatsAppNotificationType
    {
        /// <summary>
        /// إشعار الإيجار الشهري
        /// </summary>
        MonthlyRent = 1,

        /// <summary>
        /// إشعار ضريبة النظافة
        /// </summary>
        CleaningTax = 2,

        /// <summary>
        /// إشعار تأخير الإيجار
        /// </summary>
        RentOverdue = 3,

        /// <summary>
        /// إشعار تأخير ضريبة النظافة
        /// </summary>
        CleaningTaxOverdue = 4,

        /// <summary>
        /// تذكير عام
        /// </summary>
        GeneralReminder = 5,

        /// <summary>
        /// إشعار انتهاء العقد
        /// </summary>
        ContractExpiry = 6,

        /// <summary>
        /// إشعار زيادة الإيجار
        /// </summary>
        RentIncrease = 7
    }

    /// <summary>
    /// حالات إشعار الواتساب
    /// </summary>
    public enum WhatsAppNotificationStatus
    {
        /// <summary>
        /// بانتظار الإرسال
        /// </summary>
        Pending = 1,

        /// <summary>
        /// تم الإرسال بنجاح
        /// </summary>
        Sent = 2,

        /// <summary>
        /// تم التسليم
        /// </summary>
        Delivered = 3,

        /// <summary>
        /// تم القراءة
        /// </summary>
        Read = 4,

        /// <summary>
        /// فشل الإرسال
        /// </summary>
        Failed = 5,

        /// <summary>
        /// ملغي
        /// </summary>
        Cancelled = 6,

        /// <summary>
        /// مجدول
        /// </summary>
        Scheduled = 7,

        /// <summary>
        /// انتهت صلاحيته
        /// </summary>
        Expired = 8
    }

    /// <summary>
    /// أولوية الإشعار
    /// </summary>
    public enum WhatsAppNotificationPriority
    {
        /// <summary>
        /// منخفضة
        /// </summary>
        Low = 1,

        /// <summary>
        /// عادية
        /// </summary>
        Normal = 2,

        /// <summary>
        /// عالية
        /// </summary>
        High = 3,

        /// <summary>
        /// عاجلة
        /// </summary>
        Urgent = 4
    }
}
