using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace SimpleRentalApp.Models
{
    /// <summary>
    /// نموذج إشعار الواتساب
    /// </summary>
    public class WhatsAppNotification
    {
        [Key]
        public int Id { get; set; }

        public int CustomerId { get; set; }
        public virtual Customer Customer { get; set; } = null!;

        public int PropertyId { get; set; }
        public virtual Property Property { get; set; } = null!;

        public NotificationType Type { get; set; }

        public string Message { get; set; } = string.Empty;

        public string PhoneNumber { get; set; } = string.Empty;

        public DateTime ScheduledDate { get; set; }

        public DateTime? SentDate { get; set; }

        public NotificationStatus Status { get; set; }

        public string? ErrorMessage { get; set; }

        public decimal Amount { get; set; }

        public DateTime DueDate { get; set; }

        public string Month { get; set; } = string.Empty; // الشهر للكراء الشهري

        public int Year { get; set; } // السنة

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime UpdatedDate { get; set; } = DateTime.Now;

        // منع الإرسال المكرر
        public bool IsAutoGenerated { get; set; } = false;

        public string UniqueKey { get; set; } = string.Empty; // مفتاح فريد لمنع التكرار

        // خصائص محسوبة للعرض
        public string CustomerName => Customer?.FullName ?? "غير محدد";
        public string PropertyName => Property?.Name ?? "غير محدد";

        public string DueDateDisplay => DueDate.ToString("dd/MM/yyyy");
        public string CreatedDateDisplay => CreatedDate.ToString("dd/MM/yyyy HH:mm");
        public string SentDateDisplay => SentDate?.ToString("dd/MM/yyyy HH:mm") ?? "-";

        public string AmountDisplay => Amount > 0 ? $"{Amount:F0} درهم" : "-";

        public int DaysRemaining => (DueDate.Date - DateTime.Today).Days;
        public string DaysRemainingDisplay
        {
            get
            {
                var days = DaysRemaining;
                if (days > 0)
                    return $"باقي {days} يوم";
                else if (days == 0)
                    return "اليوم";
                else
                    return $"متأخر {Math.Abs(days)} يوم";
            }
        }

        public string DaysRemainingColor
        {
            get
            {
                var days = DaysRemaining;
                if (days > 7) return "#52C41A"; // أخضر
                else if (days > 3) return "#FAAD14"; // أصفر
                else if (days >= 0) return "#FF7A45"; // برتقالي
                else return "#FF4D4F"; // أحمر
            }
        }

        public string TypeDisplay
        {
            get
            {
                return Type switch
                {
                    NotificationType.MonthlyRent => "🏠 كراء شهري",
                    NotificationType.CleaningTax => "🧹 ضريبة النظافة",
                    NotificationType.ContractExpiry => "📋 انتهاء العقد",
                    NotificationType.OverduePayment => "⚠️ دفعة متأخرة",
                    NotificationType.LateReminder => "📌 تذكير متأخر",
                    _ => "غير محدد"
                };
            }
        }

        public string StatusDisplay
        {
            get
            {
                return Status switch
                {
                    NotificationStatus.Pending => "⏳ معلق",
                    NotificationStatus.Sent => "✅ تم الإرسال",
                    NotificationStatus.Failed => "❌ فشل",
                    NotificationStatus.Cancelled => "🚫 ملغي",
                    _ => "غير محدد"
                };
            }
        }

        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    NotificationStatus.Pending => "#FAAD14",
                    NotificationStatus.Sent => "#52C41A",
                    NotificationStatus.Failed => "#FF4D4F",
                    NotificationStatus.Cancelled => "#8C8C8C",
                    _ => "#6C757D"
                };
            }
        }
    }

    /// <summary>
    /// أنواع الإشعارات
    /// </summary>
    public enum NotificationType
    {
        MonthlyRent = 1,        // الكراء الشهري
        CleaningTax = 2,        // ضريبة النظافة
        ContractExpiry = 3,     // انتهاء العقد
        OverduePayment = 4,     // دفعة متأخرة
        LateReminder = 5        // إشعار متأخر
    }

    /// <summary>
    /// حالات الإشعار
    /// </summary>
    public enum NotificationStatus
    {
        Pending = 1,    // معلق
        Sent = 2,       // تم الإرسال
        Failed = 3,     // فشل
        Cancelled = 4   // ملغي
    }

}
