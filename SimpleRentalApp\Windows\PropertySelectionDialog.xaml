<Window x:Class="SimpleRentalApp.Windows.PropertySelectionDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="اختيار المحل" 
        Height="500" 
        Width="600"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        Background="#F5F5F5"
        ResizeMode="NoResize">

    <Window.Resources>
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="CancelButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#757575"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#616161"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="SelectButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#4CAF50"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#388E3C"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="ModernListBox" TargetType="ListBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="12"/>
        </Style>

        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Height" Value="35"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" 
                Background="#4CAF50" 
                CornerRadius="8,8,0,0" 
                Padding="20,15"
                Margin="0,0,0,20">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="🏢" FontSize="24" Margin="0,0,10,0"/>
                <TextBlock Name="TitleText"
                          Text="اختيار المحل" 
                          FontSize="18" 
                          FontWeight="Bold" 
                          Foreground="White"/>
            </StackPanel>
        </Border>

        <!-- Search -->
        <Border Grid.Row="1" 
                Background="White" 
                BorderBrush="#E0E0E0" 
                BorderThickness="1" 
                CornerRadius="6" 
                Padding="15"
                Margin="0,0,0,15">
            <StackPanel>
                <TextBlock Text="البحث في المحلات:" 
                          FontWeight="Bold" 
                          Margin="0,0,0,10"/>
                <TextBox Name="SearchTextBox"
                        Style="{StaticResource ModernTextBox}"
                        Text=""
                        TextChanged="SearchTextBox_TextChanged">
                    <TextBox.Tag>
                        <TextBlock Text="ابحث باسم المحل أو المكتري..." Foreground="Gray" IsHitTestVisible="False"/>
                    </TextBox.Tag>
                </TextBox>
            </StackPanel>
        </Border>

        <!-- Properties List -->
        <Border Grid.Row="2" 
                Background="White" 
                BorderBrush="#E0E0E0" 
                BorderThickness="1" 
                CornerRadius="6" 
                Padding="10">
            <ListBox Name="PropertiesListBox" 
                    Style="{StaticResource ModernListBox}"
                    SelectionMode="Single"
                    MouseDoubleClick="PropertiesListBox_MouseDoubleClick">
                <ListBox.ItemTemplate>
                    <DataTemplate>
                        <Border Background="Transparent" 
                               Padding="10" 
                               Margin="2"
                               CornerRadius="4">
                            <Border.Style>
                                <Style TargetType="Border">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=ListBoxItem}, Path=IsSelected}" Value="True">
                                            <Setter Property="Background" Value="#E3F2FD"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=ListBoxItem}, Path=IsMouseOver}" Value="True">
                                            <Setter Property="Background" Value="#F5F5F5"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Border.Style>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="{Binding Name}" 
                                              FontWeight="Bold" 
                                              FontSize="14" 
                                              Margin="0,0,0,5"/>
                                    <TextBlock Text="{Binding CustomerDisplay}" 
                                              FontSize="12" 
                                              Foreground="#666" 
                                              Margin="0,0,0,3"/>
                                    <TextBlock Text="{Binding AddressDisplay}" 
                                              FontSize="11" 
                                              Foreground="#888"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Column="1" 
                                           VerticalAlignment="Center">
                                    <TextBlock Text="{Binding MonthlyRentDisplay}" 
                                              FontWeight="Bold" 
                                              FontSize="12" 
                                              Foreground="#4CAF50" 
                                              TextAlignment="Center"/>
                                    <TextBlock Text="إيجار شهري" 
                                              FontSize="10" 
                                              Foreground="#666" 
                                              TextAlignment="Center"/>
                                </StackPanel>
                            </Grid>
                        </Border>
                    </DataTemplate>
                </ListBox.ItemTemplate>
            </ListBox>
        </Border>

        <!-- Buttons -->
        <StackPanel Grid.Row="3" 
                   Orientation="Horizontal" 
                   HorizontalAlignment="Center" 
                   Margin="0,20,0,0">
            <Button Name="SelectButton" 
                   Content="✅ اختيار المحل" 
                   Style="{StaticResource SelectButton}" 
                   Click="SelectButton_Click"
                   MinWidth="120"
                   IsEnabled="False"/>
            <Button Name="CancelButton" 
                   Content="❌ إلغاء" 
                   Style="{StaticResource CancelButton}" 
                   Click="CancelButton_Click"
                   MinWidth="120"/>
        </StackPanel>
    </Grid>
</Window>
