<Window x:Class="SimpleRentalApp.Windows.CustomerDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة/تعديل مكتري"
        Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        Background="White"
        ResizeMode="NoResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Title -->
        <TextBlock Grid.Row="0" 
                   Text="👤 بيانات المكتري"
                   FontSize="24" 
                   FontWeight="Bold"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,20"
                   Foreground="DarkBlue"/>

        <!-- Form -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- First Name -->
                <TextBlock Text="الاسم الأول *" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox Name="FirstNameTextBox" 
                         Padding="10"
                         FontSize="14"
                         Margin="0,0,0,15"
                         BorderBrush="Gray"
                         BorderThickness="1"/>

                <!-- Last Name -->
                <TextBlock Text="اسم العائلة *" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox Name="LastNameTextBox" 
                         Padding="10"
                         FontSize="14"
                         Margin="0,0,0,15"
                         BorderBrush="Gray"
                         BorderThickness="1"/>

                <!-- Phone -->
                <TextBlock Text="رقم الهاتف *" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox Name="PhoneTextBox" 
                         Padding="10"
                         FontSize="14"
                         Margin="0,0,0,15"
                         BorderBrush="Gray"
                         BorderThickness="1"/>

                <!-- Email -->
                <TextBlock Text="البريد الإلكتروني" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox Name="EmailTextBox" 
                         Padding="10"
                         FontSize="14"
                         Margin="0,0,0,15"
                         BorderBrush="Gray"
                         BorderThickness="1"/>

                <!-- National ID -->
                <TextBlock Text="رقم الهوية الوطنية" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox Name="NationalIdTextBox" 
                         Padding="10"
                         FontSize="14"
                         Margin="0,0,0,15"
                         BorderBrush="Gray"
                         BorderThickness="1"/>

                <!-- Address -->
                <TextBlock Text="العنوان" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox Name="AddressTextBox" 
                         Padding="10"
                         FontSize="14"
                         Height="80"
                         TextWrapping="Wrap"
                         AcceptsReturn="True"
                         VerticalScrollBarVisibility="Auto"
                         Margin="0,0,0,15"
                         BorderBrush="Gray"
                         BorderThickness="1"/>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <StackPanel Grid.Row="2"
                    Orientation="Horizontal"
                    HorizontalAlignment="Center"
                    Margin="0,20,0,0">
            <Button Name="SaveButton"
                    Content="💾 حفظ"
                    Padding="20,10"
                    Margin="0,0,10,0"
                    Background="Green"
                    Foreground="White"
                    FontWeight="Bold"
                    Click="SaveButton_Click"/>

            <Button Name="CancelButton"
                    Content="❌ إلغاء"
                    Padding="20,10"
                    Margin="10,0,0,0"
                    Background="Gray"
                    Foreground="White"
                    Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
