<Window x:Class="SimpleRentalApp.Windows.ContractCreationDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة/تعديل عقد" 
        Height="700" Width="600"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        Background="#F8F9FA"
        WindowStyle="None"
        AllowsTransparency="True"
        ResizeMode="NoResize">

    <Window.Resources>
        <!-- Modern ComboBox Style -->
        <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="35"/>
        </Style>

        <!-- ComboBox Item Style -->
        <Style x:Key="ModernComboBoxItemStyle" TargetType="ComboBoxItem">
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Style.Triggers>
                <Trigger Property="IsHighlighted" Value="True">
                    <Setter Property="Background" Value="#E3F2FD"/>
                    <Setter Property="Foreground" Value="Black"/>
                </Trigger>
                <Trigger Property="IsSelected" Value="True">
                    <Setter Property="Background" Value="#BBDEFB"/>
                    <Setter Property="Foreground" Value="Black"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Border Background="White" CornerRadius="12" Margin="20">
        <Border.Effect>
            <DropShadowEffect Color="#424242" 
                            Direction="270" 
                            ShadowDepth="12" 
                            BlurRadius="24" 
                            Opacity="0.3"/>
        </Border.Effect>
        
        <Grid Margin="30">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Modern Header -->
            <Border Grid.Row="0" 
                    Background="#1976D2" 
                    CornerRadius="8" 
                    Padding="20,15"
                    Margin="0,0,0,25">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <TextBlock Text="📋" FontSize="24" Margin="0,0,10,0"/>
                    <TextBlock Name="HeaderTextBlock"
                               Text="إضافة عقد جديد" 
                               FontSize="18" 
                               FontWeight="Bold"
                               Foreground="White"/>
                </StackPanel>
            </Border>

            <!-- Form Content -->
            <ScrollViewer Grid.Row="1"
                         VerticalScrollBarVisibility="Auto"
                         PanningMode="VerticalOnly"
                         CanContentScroll="True">
                <StackPanel>
                    
                    <!-- Customer Selection -->
                    <Border Background="#F8F9FA" CornerRadius="8" Padding="20" Margin="0,0,0,15">
                        <StackPanel>
                            <TextBlock Text="🏠 معلومات المكتري والمحل" 
                                       FontSize="16" 
                                       FontWeight="Bold" 
                                       Foreground="#1976D2" 
                                       Margin="0,0,0,15"/>
                            
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                    <TextBlock Text="المكتري:" FontWeight="Bold" Margin="0,0,0,5"/>
                                    <ComboBox Name="CustomerComboBox"
                                              Style="{StaticResource ModernComboBoxStyle}"
                                              ItemContainerStyle="{StaticResource ModernComboBoxItemStyle}"
                                              DisplayMemberPath="FullName"
                                              SelectedValuePath="Id"
                                              SelectionChanged="CustomerComboBox_SelectionChanged"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                    <TextBlock Text="المحل:" FontWeight="Bold" Margin="0,0,0,5"/>
                                    <ComboBox Name="PropertyComboBox"
                                              Style="{StaticResource ModernComboBoxStyle}"
                                              ItemContainerStyle="{StaticResource ModernComboBoxItemStyle}"
                                              DisplayMemberPath="DisplayName"
                                              SelectedValuePath="Id"
                                              SelectionChanged="PropertyComboBox_SelectionChanged"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- Contract Dates -->
                    <Border Background="#F8F9FA" CornerRadius="8" Padding="20" Margin="0,0,0,15">
                        <StackPanel>
                            <TextBlock Text="📅 تواريخ العقد" 
                                       FontSize="16" 
                                       FontWeight="Bold" 
                                       Foreground="#1976D2" 
                                       Margin="0,0,0,15"/>
                            
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                    <TextBlock Text="تاريخ البداية:" FontWeight="Bold" Margin="0,0,0,5"/>
                                    <DatePicker Name="StartDatePicker"
                                                Height="35"
                                                SelectedDateChanged="DatePicker_SelectedDateChanged"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                    <TextBlock Text="تاريخ النهاية:" FontWeight="Bold" Margin="0,0,0,5"/>
                                    <DatePicker Name="EndDatePicker"
                                                Height="35"
                                                SelectedDateChanged="DatePicker_SelectedDateChanged"/>
                                </StackPanel>
                            </Grid>
                            
                            <!-- Duration Display -->
                            <Border Name="DurationBorder"
                                    Background="#E3F2FD"
                                    CornerRadius="6"
                                    Padding="15"
                                    Margin="0,15,0,0"
                                    Visibility="Collapsed">
                                <TextBlock Name="DurationTextBlock"
                                           FontSize="12"
                                           Foreground="#1976D2"
                                           TextWrapping="Wrap"/>
                            </Border>
                        </StackPanel>
                    </Border>

                    <!-- Financial Information -->
                    <Border Background="#F8F9FA" CornerRadius="8" Padding="20" Margin="0,0,0,15">
                        <StackPanel>
                            <TextBlock Text="💰 المعلومات المالية" 
                                       FontSize="16" 
                                       FontWeight="Bold" 
                                       Foreground="#1976D2" 
                                       Margin="0,0,0,15"/>
                            
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                    <TextBlock Text="الإيجار الأولي (درهم):" FontWeight="Bold" Margin="0,0,0,5"/>
                                    <TextBox Name="InitialRentAmountTextBox"
                                             Height="35"
                                             VerticalContentAlignment="Center"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                    <TextBlock Text="طريقة الدفع:" FontWeight="Bold" Margin="0,0,0,5"/>
                                    <ComboBox Name="PaymentMethodComboBox"
                                              Style="{StaticResource ModernComboBoxStyle}"
                                              ItemContainerStyle="{StaticResource ModernComboBoxItemStyle}"
                                              Height="35">
                                        <ComboBoxItem Content="شهرياً" IsSelected="True"/>
                                        <ComboBoxItem Content="ربع سنوي"/>
                                        <ComboBoxItem Content="نصف سنوي"/>
                                        <ComboBoxItem Content="سنوياً"/>
                                    </ComboBox>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- Notes -->
                    <Border Background="#F8F9FA" CornerRadius="8" Padding="20" Margin="0,0,0,15">
                        <StackPanel>
                            <TextBlock Text="📝 ملاحظات" 
                                       FontSize="16" 
                                       FontWeight="Bold" 
                                       Foreground="#1976D2" 
                                       Margin="0,0,0,15"/>
                            
                            <TextBox Name="NotesTextBox"
                                     Height="80"
                                     TextWrapping="Wrap"
                                     AcceptsReturn="True"
                                     VerticalScrollBarVisibility="Auto"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </ScrollViewer>

            <!-- Action Buttons -->
            <StackPanel Grid.Row="2" 
                        Orientation="Horizontal" 
                        HorizontalAlignment="Center" 
                        Margin="0,20,0,0">
                <Button Name="SaveButton"
                        Content="💾 حفظ"
                        Background="#4CAF50"
                        Foreground="White"
                        Padding="20,10"
                        Margin="10,0"
                        BorderThickness="0"
                        FontWeight="Bold"
                        Click="SaveButton_Click"/>
                
                <Button Name="CancelButton"
                        Content="❌ إلغاء"
                        Background="#F44336"
                        Foreground="White"
                        Padding="20,10"
                        Margin="10,0"
                        BorderThickness="0"
                        FontWeight="Bold"
                        Click="CancelButton_Click"/>
            </StackPanel>
        </Grid>
    </Border>
</Window>
