﻿#pragma checksum "..\..\..\..\Windows\ContractDetailsDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "01CCE978A3A7FB1C02944B520B7B7CDA3A90986E"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleRentalApp.Windows {
    
    
    /// <summary>
    /// ContractDetailsDialog
    /// </summary>
    public partial class ContractDetailsDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 44 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ContractNumberTextBlock;
        
        #line default
        #line hidden
        
        
        #line 49 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border StatusBorder;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CustomerNameTextBlock;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CustomerPhoneTextBlock;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CustomerEmailTextBlock;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PropertyNameTextBlock;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PropertyAddressTextBlock;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PropertyTypeTextBlock;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StartDateTextBlock;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock EndDateTextBlock;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DurationTextBlock;
        
        #line default
        #line hidden
        
        
        #line 199 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InitialRentTextBlock;
        
        #line default
        #line hidden
        
        
        #line 212 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentRentTextBlock;
        
        #line default
        #line hidden
        
        
        #line 225 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalIncreaseTextBlock;
        
        #line default
        #line hidden
        
        
        #line 253 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock IncreaseCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 257 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock IncreasePercentageTextBlock;
        
        #line default
        #line hidden
        
        
        #line 268 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RemainingTimeTextBlock;
        
        #line default
        #line hidden
        
        
        #line 277 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border RentIncreasesSection;
        
        #line default
        #line hidden
        
        
        #line 294 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer RentIncreasesScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 299 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel RentIncreasesPanel;
        
        #line default
        #line hidden
        
        
        #line 307 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border AugmentsSection;
        
        #line default
        #line hidden
        
        
        #line 324 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer AugmentsScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 329 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel AugmentsPanel;
        
        #line default
        #line hidden
        
        
        #line 349 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NotesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 368 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditButton;
        
        #line default
        #line hidden
        
        
        #line 380 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AttachmentsButton;
        
        #line default
        #line hidden
        
        
        #line 392 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleRentalApp;V1.0.0.0;component/windows/contractdetailsdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ContractNumberTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.StatusBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 3:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.CustomerNameTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.CustomerPhoneTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.CustomerEmailTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.PropertyNameTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.PropertyAddressTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.PropertyTypeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.StartDateTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.EndDateTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.DurationTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.InitialRentTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.CurrentRentTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.TotalIncreaseTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.IncreaseCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.IncreasePercentageTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.RemainingTimeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.RentIncreasesSection = ((System.Windows.Controls.Border)(target));
            return;
            case 20:
            this.RentIncreasesScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 21:
            this.RentIncreasesPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 22:
            this.AugmentsSection = ((System.Windows.Controls.Border)(target));
            return;
            case 23:
            this.AugmentsScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 24:
            this.AugmentsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 25:
            this.NotesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.EditButton = ((System.Windows.Controls.Button)(target));
            
            #line 378 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
            this.EditButton.Click += new System.Windows.RoutedEventHandler(this.EditButton_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.AttachmentsButton = ((System.Windows.Controls.Button)(target));
            
            #line 390 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
            this.AttachmentsButton.Click += new System.Windows.RoutedEventHandler(this.AttachmentsButton_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 400 "..\..\..\..\Windows\ContractDetailsDialog.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

