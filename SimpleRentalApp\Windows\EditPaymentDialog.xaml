<Window x:Class="SimpleRentalApp.Windows.EditPaymentDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تعديل الدفعة" 
        Height="500" 
        Width="600"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        Background="#F8F9FA"
        ResizeMode="NoResize">

    <Window.Resources>
        <!-- Modern Card Style -->
        <Style x:Key="ModernCard" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="25"/>
            <Setter Property="Margin" Value="15"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="4" BlurRadius="12" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern Button Style -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="8"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Success Button Style -->
        <Style x:Key="SuccessButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#4CAF50"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#388E3C"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Cancel Button Style -->
        <Style x:Key="CancelButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#757575"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#616161"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Modern TextBox Style -->
        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Padding" Value="12,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                         Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#2196F3"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Label Style -->
        <Style x:Key="ModernLabel" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="#424242"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>

        <!-- Modern ComboBox Style -->
        <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Padding" Value="12,10"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>

        <!-- ComboBox Item Style -->
        <Style x:Key="ModernComboBoxItemStyle" TargetType="ComboBoxItem">
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Padding" Value="12,10"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>
    </Window.Resources>

    <Grid>
        <!-- Header -->
        <Border Height="80" VerticalAlignment="Top">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                    <GradientStop Color="#2196F3" Offset="0"/>
                    <GradientStop Color="#1976D2" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <StackPanel Orientation="Horizontal" 
                       HorizontalAlignment="Center" 
                       VerticalAlignment="Center">
                <TextBlock Text="✏️" FontSize="24" Margin="0,0,15,0"/>
                <TextBlock Text="تعديل دفعة ضريبة النظافة" 
                          FontSize="20" 
                          FontWeight="Bold" 
                          Foreground="White"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <Border Style="{StaticResource ModernCard}" 
                Margin="20,100,20,20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Scrollable Form Fields -->
                <ScrollViewer Grid.Row="0"
                              VerticalScrollBarVisibility="Auto"
                              HorizontalScrollBarVisibility="Disabled"
                              PanningMode="VerticalOnly">
                    <StackPanel>
                        <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Left Column -->
                        <StackPanel Grid.Column="0" Margin="0,0,15,0">
                            <TextBlock Text="💰 مبلغ ضريبة النظافة:" Style="{StaticResource ModernLabel}"/>
                            <TextBox Name="CleaningTaxAmountTextBox" 
                                    Style="{StaticResource ModernTextBox}"/>

                            <TextBlock Text="📅 تاريخ الدفع:" Style="{StaticResource ModernLabel}"/>
                            <DatePicker Name="PaymentDatePicker" 
                                       Padding="12,10"
                                       FontSize="14"/>

                            <TextBlock Text="💳 طريقة الدفع:" Style="{StaticResource ModernLabel}"/>
                            <ComboBox Name="PaymentMethodComboBox"
                                     Style="{StaticResource ModernComboBoxStyle}"
                                     ItemContainerStyle="{StaticResource ModernComboBoxItemStyle}">
                                <ComboBoxItem Content="نقداً" IsSelected="True"/>
                                <ComboBoxItem Content="شيك"/>
                                <ComboBoxItem Content="تحويل بنكي"/>
                                <ComboBoxItem Content="بطاقة ائتمان"/>
                            </ComboBox>
                        </StackPanel>

                        <!-- Right Column -->
                        <StackPanel Grid.Column="1" Margin="15,0,0,0">
                            <TextBlock Text="🧾 رقم التوصيل:" Style="{StaticResource ModernLabel}"/>
                            <TextBox Name="ReceiptNumberTextBox" 
                                    Style="{StaticResource ModernTextBox}"
                                    ToolTip="أدخل رقم التوصيل الخاص بهذه الدفعة"/>

                            <TextBlock Text="📊 حالة الدفع:" Style="{StaticResource ModernLabel}"/>
                            <ComboBox Name="StatusComboBox"
                                     Style="{StaticResource ModernComboBoxStyle}"
                                     ItemContainerStyle="{StaticResource ModernComboBoxItemStyle}">
                                <ComboBoxItem Content="مدفوع" Tag="Paid" IsSelected="True"/>
                                <ComboBoxItem Content="معلق" Tag="Pending"/>
                                <ComboBoxItem Content="متأخر" Tag="Overdue"/>
                                <ComboBoxItem Content="ملغي" Tag="Cancelled"/>
                            </ComboBox>

                            <TextBlock Text="👤 المستخدم:" Style="{StaticResource ModernLabel}"/>
                            <TextBox Name="CreatedByTextBox" 
                                    Style="{StaticResource ModernTextBox}"
                                    IsReadOnly="True"
                                    Background="#F5F5F5"/>
                        </StackPanel>
                    </Grid>

                    <!-- Notes Section -->
                    <TextBlock Text="📝 الملاحظات:" Style="{StaticResource ModernLabel}" Margin="0,20,0,5"/>
                    <TextBox Name="NotesTextBox" 
                            Style="{StaticResource ModernTextBox}"
                            Height="80"
                            TextWrapping="Wrap"
                            AcceptsReturn="True"
                            VerticalScrollBarVisibility="Auto"/>

                        <!-- Validation Message -->
                        <TextBlock Name="ValidationMessageTextBlock"
                                  Foreground="Red"
                                  FontSize="12"
                                  Margin="0,10,0,0"
                                  Visibility="Collapsed"/>
                    </StackPanel>
                </ScrollViewer>

                <!-- Action Buttons -->
                <StackPanel Grid.Row="1" 
                           Orientation="Horizontal" 
                           HorizontalAlignment="Center" 
                           Margin="0,30,0,0">
                    <Button Name="SaveButton" 
                           Style="{StaticResource SuccessButton}"
                           Content="💾 حفظ التعديلات"
                           Click="SaveButton_Click"/>
                    <Button Name="CancelButton" 
                           Style="{StaticResource CancelButton}"
                           Content="❌ إلغاء"
                           Click="CancelButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
