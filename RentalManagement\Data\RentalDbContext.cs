using Microsoft.EntityFrameworkCore;
using RentalManagement.Models;
using System.IO;

namespace RentalManagement.Data
{
    public class RentalDbContext : DbContext
    {
        public DbSet<Customer> Customers { get; set; }
        public DbSet<Property> Properties { get; set; }
        public DbSet<Payment> Payments { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            var dbPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), 
                                     "RentalManagement", "rental.db");
            
            // Create directory if it doesn't exist
            var directory = Path.GetDirectoryName(dbPath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory!);
            }

            optionsBuilder.UseSqlite($"Data Source={dbPath}");
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure Customer entity
            modelBuilder.Entity<Customer>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FullName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.NationalId).IsRequired().HasMaxLength(20);
                entity.Property(e => e.PhoneNumber).IsRequired().HasMaxLength(15);
                entity.Property(e => e.Address).HasMaxLength(200);
                entity.HasIndex(e => e.NationalId).IsUnique();
            });

            // Configure Property entity
            modelBuilder.Entity<Property>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Location).IsRequired().HasMaxLength(200);
                entity.Property(e => e.RentAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Description).HasMaxLength(500);
                
                entity.HasOne(e => e.Customer)
                      .WithMany(c => c.RentedProperties)
                      .HasForeignKey(e => e.CustomerId)
                      .OnDelete(DeleteBehavior.SetNull);
            });

            // Configure Payment entity
            modelBuilder.Entity<Payment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Amount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Notes).HasMaxLength(500);

                entity.HasOne(e => e.Customer)
                      .WithMany(c => c.Payments)
                      .HasForeignKey(e => e.CustomerId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Property)
                      .WithMany(p => p.Payments)
                      .HasForeignKey(e => e.PropertyId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // Seed initial data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Seed sample customers
            modelBuilder.Entity<Customer>().HasData(
                new Customer
                {
                    Id = 1,
                    FullName = "أحمد محمد علي",
                    NationalId = "1234567890",
                    PhoneNumber = "0123456789",
                    Address = "شارع الملك فهد، الرياض",
                    CreatedDate = DateTime.Now.AddDays(-30)
                },
                new Customer
                {
                    Id = 2,
                    FullName = "فاطمة عبدالله",
                    NationalId = "0987654321",
                    PhoneNumber = "0987654321",
                    Address = "حي النخيل، جدة",
                    CreatedDate = DateTime.Now.AddDays(-20)
                }
            );

            // Seed sample properties
            modelBuilder.Entity<Property>().HasData(
                new Property
                {
                    Id = 1,
                    Type = PropertyType.Apartment,
                    Location = "شقة في حي الملز، الرياض",
                    RentAmount = 2500m,
                    Status = PropertyStatus.Rented,
                    Description = "شقة من غرفتين وصالة",
                    CustomerId = 1,
                    CreatedDate = DateTime.Now.AddDays(-25)
                },
                new Property
                {
                    Id = 2,
                    Type = PropertyType.Shop,
                    Location = "محل تجاري في شارع التحلية، جدة",
                    RentAmount = 5000m,
                    Status = PropertyStatus.Available,
                    Description = "محل تجاري بمساحة 100 متر مربع",
                    CreatedDate = DateTime.Now.AddDays(-15)
                }
            );
        }
    }
}
