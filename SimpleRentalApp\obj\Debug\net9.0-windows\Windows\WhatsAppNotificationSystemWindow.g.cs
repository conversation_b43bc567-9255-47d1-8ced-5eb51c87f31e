﻿#pragma checksum "..\..\..\..\Windows\WhatsAppNotificationSystemWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "B57075346D000AF249EAD1C5E246469A02025FA6"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleRentalApp.Windows {
    
    
    /// <summary>
    /// WhatsAppNotificationSystemWindow
    /// </summary>
    public partial class WhatsAppNotificationSystemWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 210 "..\..\..\..\Windows\WhatsAppNotificationSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TypeFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\..\Windows\WhatsAppNotificationSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\..\Windows\WhatsAppNotificationSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PriorityFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 221 "..\..\..\..\Windows\WhatsAppNotificationSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 257 "..\..\..\..\Windows\WhatsAppNotificationSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid NotificationsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 332 "..\..\..\..\Windows\WhatsAppNotificationSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 336 "..\..\..\..\Windows\WhatsAppNotificationSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatisticsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 340 "..\..\..\..\Windows\WhatsAppNotificationSystemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastUpdateTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleRentalApp;V1.0.0.0;component/windows/whatsappnotificationsystemwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\WhatsAppNotificationSystemWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 133 "..\..\..\..\Windows\WhatsAppNotificationSystemWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshBtn_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 137 "..\..\..\..\Windows\WhatsAppNotificationSystemWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SettingsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 164 "..\..\..\..\Windows\WhatsAppNotificationSystemWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.GenerateMonthlyRentBtn_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 168 "..\..\..\..\Windows\WhatsAppNotificationSystemWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.GenerateCleaningTaxBtn_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 172 "..\..\..\..\Windows\WhatsAppNotificationSystemWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.GenerateOverdueBtn_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 176 "..\..\..\..\Windows\WhatsAppNotificationSystemWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.MessageTemplatesBtn_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 180 "..\..\..\..\Windows\WhatsAppNotificationSystemWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BulkSendBtn_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.TypeFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 211 "..\..\..\..\Windows\WhatsAppNotificationSystemWindow.xaml"
            this.TypeFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.TypeFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.StatusFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 215 "..\..\..\..\Windows\WhatsAppNotificationSystemWindow.xaml"
            this.StatusFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.StatusFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.PriorityFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 219 "..\..\..\..\Windows\WhatsAppNotificationSystemWindow.xaml"
            this.PriorityFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PriorityFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 223 "..\..\..\..\Windows\WhatsAppNotificationSystemWindow.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 12:
            this.NotificationsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 17:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.StatisticsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.LastUpdateTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 13:
            
            #line 297 "..\..\..\..\Windows\WhatsAppNotificationSystemWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PreviewBtn_Click);
            
            #line default
            #line hidden
            break;
            case 14:
            
            #line 302 "..\..\..\..\Windows\WhatsAppNotificationSystemWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SendBtn_Click);
            
            #line default
            #line hidden
            break;
            case 15:
            
            #line 307 "..\..\..\..\Windows\WhatsAppNotificationSystemWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditBtn_Click);
            
            #line default
            #line hidden
            break;
            case 16:
            
            #line 312 "..\..\..\..\Windows\WhatsAppNotificationSystemWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteBtn_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

