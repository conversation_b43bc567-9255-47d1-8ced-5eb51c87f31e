# Rental Management System - Simple Installer Creator
# This script creates a self-extracting installer using PowerShell

param(
    [string]$OutputPath = "RentalManagementInstaller.exe"
)

Write-Host "Creating Rental Management System Installer..." -ForegroundColor Green

# Check if 7-Zip is available
$sevenZipPath = ""
$possiblePaths = @(
    "${env:ProgramFiles}\7-Zip\7z.exe",
    "${env:ProgramFiles(x86)}\7-Zip\7z.exe",
    "7z.exe"
)

foreach ($path in $possiblePaths) {
    if (Test-Path $path) {
        $sevenZipPath = $path
        break
    }
}

if (-not $sevenZipPath) {
    Write-Host "7-Zip not found. Creating ZIP archive instead..." -ForegroundColor Yellow
    
    # Create ZIP archive
    $zipPath = "RentalManagementPortable.zip"
    if (Test-Path $zipPath) { Remove-Item $zipPath }
    
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    [System.IO.Compression.ZipFile]::CreateFromDirectory("portable", $zipPath)
    
    Write-Host "Created portable ZIP: $zipPath" -ForegroundColor Green
    Write-Host "Extract and run SimpleRentalApp.exe to use the application." -ForegroundColor Cyan
} else {
    Write-Host "Found 7-Zip at: $sevenZipPath" -ForegroundColor Cyan
    
    # Create self-extracting archive
    $sfxModule = "${env:ProgramFiles}\7-Zip\7zS.sfx"
    if (-not (Test-Path $sfxModule)) {
        $sfxModule = "${env:ProgramFiles(x86)}\7-Zip\7zS.sfx"
    }
    
    if (Test-Path $sfxModule) {
        # Create 7z archive first
        $tempArchive = "temp_rental.7z"
        & $sevenZipPath a -t7z $tempArchive "portable\*" -mx9
        
        # Create config for SFX
        $configContent = @"
;!@Install@!UTF-8!
Title="Rental Management System Installer"
BeginPrompt="Do you want to install Rental Management System?"
RunProgram="SimpleRentalApp.exe"
Directory="RentalManagement"
;!@InstallEnd@!
"@
        
        $configFile = "config.txt"
        $configContent | Out-File -FilePath $configFile -Encoding UTF8
        
        # Combine SFX module, config, and archive
        cmd /c copy /b "$sfxModule" + "$configFile" + "$tempArchive" "$OutputPath"
        
        # Cleanup
        Remove-Item $tempArchive -ErrorAction SilentlyContinue
        Remove-Item $configFile -ErrorAction SilentlyContinue
        
        Write-Host "Created self-extracting installer: $OutputPath" -ForegroundColor Green
    } else {
        Write-Host "SFX module not found. Creating regular 7z archive..." -ForegroundColor Yellow
        & $sevenZipPath a -t7z "RentalManagement.7z" "portable\*" -mx9
        Write-Host "Created 7z archive: RentalManagement.7z" -ForegroundColor Green
    }
}

# Create installation instructions
$instructions = @"
Rental Management System - Installation Instructions
==================================================

INSTALLATION OPTIONS:

1. PORTABLE VERSION (Recommended for most users):
   - Extract the archive to any folder
   - Run SimpleRentalApp.exe directly
   - No installation required

2. FULL INSTALLATION:
   - If you have Inno Setup: Compile installer.iss
   - If you have NSIS: Compile installer_nsis.nsi
   - This will create a proper Windows installer

SYSTEM REQUIREMENTS:
- Windows 10 or later (64-bit)
- .NET 9.0 Runtime (included)
- 100 MB free disk space

DEFAULT LOGIN CREDENTIALS:
Username: hafid
Password: hafidos159357

FEATURES:
- Property Management
- Rent Collection Tracking
- Contract Management
- Cleaning Tax Management
- Receipt Generation (PDF)
- WhatsApp Notifications
- Data Export/Import
- Reports and Analytics

SUPPORT:
Developer: Hafid Abdo
For technical support, please contact the developer.

FIRST RUN:
1. Extract/Install the application
2. Run SimpleRentalApp.exe
3. Login with the default credentials
4. Start adding your properties and contracts

DATA LOCATION:
Application data is stored in:
%APPDATA%\TadbirAlKira\

BACKUP:
Regular backups are recommended. Use the Export feature
in the application to backup your data.
"@

$instructions | Out-File -FilePath "INSTALLATION_GUIDE.txt" -Encoding UTF8

Write-Host "`nInstallation package created successfully!" -ForegroundColor Green
Write-Host "Files created:" -ForegroundColor Cyan
Write-Host "- Portable version in 'portable' folder" -ForegroundColor White
Write-Host "- INSTALLATION_GUIDE.txt" -ForegroundColor White

if (Test-Path $OutputPath) {
    Write-Host "- $OutputPath (Self-extracting installer)" -ForegroundColor White
}

Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. Test the portable version by running portable\SimpleRentalApp.exe" -ForegroundColor White
Write-Host "2. Distribute the installer or portable folder to users" -ForegroundColor White
Write-Host "3. Provide users with the installation guide" -ForegroundColor White
