using RentalManagement.Helpers;
using RentalManagement.Models;
using System.ComponentModel.DataAnnotations;
using System.Windows.Input;

namespace RentalManagement.ViewModels
{
    public class CustomerDialogViewModel : BaseViewModel
    {
        private Customer _customer;
        private string _fullName = string.Empty;
        private string _nationalId = string.Empty;
        private string _phoneNumber = string.Empty;
        private string _address = string.Empty;

        public CustomerDialogViewModel() : this(new Customer())
        {
            Title = "إضافة زبون جديد";
        }

        public CustomerDialogViewModel(Customer customer)
        {
            _customer = customer ?? new Customer();
            Title = customer.Id == 0 ? "إضافة زبون جديد" : "تعديل بيانات الزبون";
            
            // Initialize properties
            FullName = _customer.FullName;
            NationalId = _customer.NationalId;
            PhoneNumber = _customer.PhoneNumber;
            Address = _customer.Address;

            // Initialize commands
            SaveCommand = new RelayCommand(Save, CanSave);
            CancelCommand = new RelayCommand(Cancel);
        }

        public Customer Customer => _customer;

        [Required(ErrorMessage = "الاسم الكامل مطلوب")]
        [StringLength(100, ErrorMessage = "الاسم لا يجب أن يتجاوز 100 حرف")]
        public string FullName
        {
            get => _fullName;
            set
            {
                if (SetProperty(ref _fullName, value))
                {
                    ValidateProperty(value);
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        [Required(ErrorMessage = "رقم البطاقة الوطنية مطلوب")]
        [StringLength(20, ErrorMessage = "رقم البطاقة لا يجب أن يتجاوز 20 حرف")]
        public string NationalId
        {
            get => _nationalId;
            set
            {
                if (SetProperty(ref _nationalId, value))
                {
                    ValidateProperty(value);
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        [Required(ErrorMessage = "رقم الهاتف مطلوب")]
        [Phone(ErrorMessage = "رقم الهاتف غير صحيح")]
        [StringLength(15, ErrorMessage = "رقم الهاتف لا يجب أن يتجاوز 15 رقم")]
        public string PhoneNumber
        {
            get => _phoneNumber;
            set
            {
                if (SetProperty(ref _phoneNumber, value))
                {
                    ValidateProperty(value);
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        [StringLength(200, ErrorMessage = "العنوان لا يجب أن يتجاوز 200 حرف")]
        public string Address
        {
            get => _address;
            set
            {
                if (SetProperty(ref _address, value))
                {
                    ValidateProperty(value);
                    CommandManager.InvalidateRequerySuggested();
                }
            }
        }

        // Commands
        public ICommand SaveCommand { get; }
        public ICommand CancelCommand { get; }

        // Events
        public event EventHandler<bool>? DialogClosed;

        private bool CanSave()
        {
            return !string.IsNullOrWhiteSpace(FullName) &&
                   !string.IsNullOrWhiteSpace(NationalId) &&
                   !string.IsNullOrWhiteSpace(PhoneNumber) &&
                   IsValid();
        }

        private void Save()
        {
            if (!CanSave()) return;

            try
            {
                ClearError();

                // Update customer properties
                _customer.FullName = FullName.Trim();
                _customer.NationalId = NationalId.Trim();
                _customer.PhoneNumber = PhoneNumber.Trim();
                _customer.Address = Address.Trim();

                if (_customer.Id == 0)
                {
                    _customer.CreatedDate = DateTime.Now;
                }

                DialogClosed?.Invoke(this, true);
            }
            catch (Exception ex)
            {
                SetError($"خطأ في حفظ البيانات: {ex.Message}");
            }
        }

        private void Cancel()
        {
            DialogClosed?.Invoke(this, false);
        }

        private void ValidateProperty(object? value, [System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            if (propertyName == null) return;

            var context = new ValidationContext(this) { MemberName = propertyName };
            var results = new List<ValidationResult>();
            
            Validator.TryValidateProperty(value, context, results);
            
            if (results.Any())
            {
                SetError(results.First().ErrorMessage ?? "خطأ في التحقق من صحة البيانات");
            }
            else
            {
                ClearError();
            }
        }

        private bool IsValid()
        {
            var context = new ValidationContext(this);
            var results = new List<ValidationResult>();
            return Validator.TryValidateObject(this, context, results, true);
        }
    }
}
