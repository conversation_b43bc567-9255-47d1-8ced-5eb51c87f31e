; نظام تدبير الكراء - ملف التثبيت
; المطور: حفيظ عبدو
; الإصدار: 1.0.0
; دعم اللغة العربية

Unicode True

!define APPNAME "نظام تدبير الكراء"
!define APPNAME_EN "RentalManagement"
!define COMPANYNAME "حفيظ عبدو"
!define DESCRIPTION "نظام إدارة العقارات والكراء"
!define VERSIONMAJOR 1
!define VERSIONMINOR 0
!define VERSIONBUILD 0
!define HELPURL "<EMAIL>"
!define UPDATEURL ""
!define ABOUTURL ""
!define INSTALLSIZE 100000

RequestExecutionLevel admin
InstallDir "$PROGRAMFILES64\${APPNAME_EN}"
Name "${APPNAME}"
outFile "تثبيت_نظام_تدبير_الكراء.exe"

!include LogicLib.nsh
!include MUI2.nsh
!include FileFunc.nsh

; Modern UI Configuration
!define MUI_ABORTWARNING
!define MUI_WELCOMEPAGE_TITLE "مرحباً بك في معالج تثبيت ${APPNAME}"
!define MUI_WELCOMEPAGE_TEXT "سيقوم هذا المعالج بإرشادك خلال تثبيت ${APPNAME}.$\r$\n$\r$\nيُنصح بإغلاق جميع التطبيقات الأخرى قبل المتابعة.$\r$\n$\r$\nانقر التالي للمتابعة."

!define MUI_LICENSEPAGE_TEXT_TOP "يرجى قراءة اتفاقية الترخيص التالية. يجب قبول شروط هذه الاتفاقية لمتابعة التثبيت."
!define MUI_LICENSEPAGE_TEXT_BOTTOM "إذا كنت توافق على شروط الاتفاقية، انقر أوافق للمتابعة. يجب قبول الاتفاقية لتثبيت ${APPNAME}."
!define MUI_LICENSEPAGE_BUTTON "&أوافق"

!define MUI_DIRECTORYPAGE_TEXT_TOP "سيتم تثبيت ${APPNAME} في المجلد التالي.$\r$\n$\r$\nلتثبيت البرنامج في مجلد مختلف، انقر استعراض واختر مجلد آخر. انقر التالي للمتابعة."

!define MUI_INSTFILESPAGE_FINISHHEADER_TEXT "اكتمل التثبيت"
!define MUI_INSTFILESPAGE_FINISHHEADER_SUBTEXT "تم تثبيت ${APPNAME} بنجاح."

!define MUI_FINISHPAGE_TITLE "اكتمل تثبيت ${APPNAME}"
!define MUI_FINISHPAGE_TEXT "تم تثبيت ${APPNAME} بنجاح على جهاز الكمبيوتر.$\r$\n$\r$\nانقر إنهاء لإغلاق هذا المعالج."
!define MUI_FINISHPAGE_RUN "$INSTDIR\SimpleRentalApp.exe"
!define MUI_FINISHPAGE_RUN_TEXT "تشغيل ${APPNAME}"

; Pages
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE_Arabic.txt"
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

; Uninstaller pages
!define MUI_UNCONFIRMPAGE_TEXT_TOP "سيتم إلغاء تثبيت ${APPNAME} من جهاز الكمبيوتر. انقر إلغاء التثبيت للمتابعة."
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES

; Languages
!insertmacro MUI_LANGUAGE "Arabic"

; Custom strings for Arabic
LangString DESC_SecMain ${LANG_ARABIC} "الملفات الأساسية للبرنامج"
LangString DESC_SecDesktop ${LANG_ARABIC} "إنشاء اختصار على سطح المكتب"

!macro VerifyUserIsAdmin
UserInfo::GetAccountType
pop $0
${If} $0 != "admin"
    messageBox mb_iconstop "يتطلب هذا البرنامج صلاحيات المدير للتثبيت!"
    setErrorLevel 740
    quit
${EndIf}
!macroend

function .onInit
    setShellVarContext all
    !insertmacro VerifyUserIsAdmin
functionEnd

Section "الملفات الأساسية" SecMain
    SectionIn RO
    setOutPath $INSTDIR
    
    ; Copy application files
    File /r "app\*.*"
    
    ; Copy documentation
    CreateDirectory "$INSTDIR\docs"
    File /oname=docs\README_Arabic.txt "README_Arabic.txt"
    
    ; Create uninstaller
    WriteUninstaller "$INSTDIR\uninstall.exe"
    
    ; Start Menu
    CreateDirectory "$SMPROGRAMS\${APPNAME}"
    CreateShortCut "$SMPROGRAMS\${APPNAME}\${APPNAME}.lnk" "$INSTDIR\SimpleRentalApp.exe" "" "$INSTDIR\SimpleRentalApp.exe" 0
    CreateShortCut "$SMPROGRAMS\${APPNAME}\إلغاء التثبيت.lnk" "$INSTDIR\uninstall.exe" "" "$INSTDIR\uninstall.exe" 0
    CreateShortCut "$SMPROGRAMS\${APPNAME}\دليل المستخدم.lnk" "$INSTDIR\docs\README_Arabic.txt"
    
    ; Registry information for add/remove programs
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_EN}" "DisplayName" "${APPNAME}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_EN}" "UninstallString" "$\"$INSTDIR\uninstall.exe$\""
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_EN}" "QuietUninstallString" "$\"$INSTDIR\uninstall.exe$\" /S"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_EN}" "InstallLocation" "$\"$INSTDIR$\""
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_EN}" "DisplayIcon" "$\"$INSTDIR\SimpleRentalApp.exe$\""
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_EN}" "Publisher" "${COMPANYNAME}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_EN}" "HelpLink" "${HELPURL}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_EN}" "URLUpdateInfo" "${UPDATEURL}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_EN}" "URLInfoAbout" "${ABOUTURL}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_EN}" "DisplayVersion" "${VERSIONMAJOR}.${VERSIONMINOR}.${VERSIONBUILD}"
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_EN}" "VersionMajor" ${VERSIONMAJOR}
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_EN}" "VersionMinor" ${VERSIONMINOR}
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_EN}" "NoModify" 1
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_EN}" "NoRepair" 1
    
    ; Calculate and set estimated size
    ${GetSize} "$INSTDIR" "/S=0K" $0 $1 $2
    IntFmt $0 "0x%08X" $0
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_EN}" "EstimatedSize" "$0"
    
    ; Create data directory in AppData
    CreateDirectory "$APPDATA\TadbirAlKira"
    
SectionEnd

Section "اختصار سطح المكتب" SecDesktop
    CreateShortCut "$DESKTOP\${APPNAME}.lnk" "$INSTDIR\SimpleRentalApp.exe" "" "$INSTDIR\SimpleRentalApp.exe" 0
SectionEnd

; Section descriptions
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
    !insertmacro MUI_DESCRIPTION_TEXT ${SecMain} $(DESC_SecMain)
    !insertmacro MUI_DESCRIPTION_TEXT ${SecDesktop} $(DESC_SecDesktop)
!insertmacro MUI_FUNCTION_DESCRIPTION_END

Section "Uninstall"
    ; Remove Start Menu launcher
    Delete "$SMPROGRAMS\${APPNAME}\${APPNAME}.lnk"
    Delete "$SMPROGRAMS\${APPNAME}\إلغاء التثبيت.lnk"
    Delete "$SMPROGRAMS\${APPNAME}\دليل المستخدم.lnk"
    RmDir "$SMPROGRAMS\${APPNAME}"
    
    ; Remove desktop shortcut
    Delete "$DESKTOP\${APPNAME}.lnk"
    
    ; Remove files
    RmDir /r "$INSTDIR"
    
    ; Remove uninstaller information from the registry
    DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME_EN}"
    
    ; Ask user if they want to remove data
    MessageBox MB_YESNO "هل تريد حذف بيانات التطبيق؟ سيؤدي هذا إلى حذف جميع بيانات الكراء الخاصة بك." IDNO skip_data_removal
    RmDir /r "$APPDATA\TadbirAlKira"
    skip_data_removal:
    
SectionEnd
