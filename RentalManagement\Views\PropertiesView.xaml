<UserControl x:Class="RentalManagement.Views.PropertiesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:models="clr-namespace:RentalManagement.Models"
             xmlns:helpers="clr-namespace:RentalManagement.Helpers"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <helpers:PropertyTypeConverter x:Key="PropertyTypeConverter"/>
        <helpers:PropertyStatusConverter x:Key="PropertyStatusConverter"/>
    </UserControl.Resources>
    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Page Title -->
        <TextBlock Grid.Row="0"
                   Text="إدارة العقارات"
                   Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                   Margin="0,0,0,24"/>

        <!-- Search and Filters -->
        <Grid Grid.Row="1" Margin="0,0,0,16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Search and Actions -->
            <Grid Grid.Row="0" Margin="0,0,0,8">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Search Box -->
                <TextBox Grid.Column="0"
                         Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                         materialDesign:HintAssist.Hint="البحث في المحلات..."
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         Margin="0,0,16,0">
                    <TextBox.InputBindings>
                        <KeyBinding Key="Enter" Command="{Binding SearchCommand}"/>
                    </TextBox.InputBindings>
                </TextBox>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="بحث"
                            Command="{Binding SearchCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Margin="0,0,8,0"/>
                    <Button Content="إضافة عقار"
                            Command="{Binding AddPropertyCommand}"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Margin="0,0,8,0"/>
                    <Button Content="تحديث"
                            Command="{Binding RefreshCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"/>
                </StackPanel>
            </Grid>

            <!-- Filters -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Type Filter -->
                <ComboBox Grid.Column="0"
                          SelectedValue="{Binding TypeFilter}"
                          materialDesign:HintAssist.Hint="نوع المحل"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          Margin="0,0,8,0">
                    <ComboBoxItem Content="الكل"/>
                    <ComboBoxItem Content="شقة">
                        <ComboBoxItem.Tag>
                            <models:PropertyType>Apartment</models:PropertyType>
                        </ComboBoxItem.Tag>
                    </ComboBoxItem>
                    <ComboBoxItem Content="محل">
                        <ComboBoxItem.Tag>
                            <models:PropertyType>Shop</models:PropertyType>
                        </ComboBoxItem.Tag>
                    </ComboBoxItem>
                </ComboBox>

                <!-- Status Filter -->
                <ComboBox Grid.Column="1"
                          SelectedValue="{Binding StatusFilter}"
                          materialDesign:HintAssist.Hint="حالة المحل"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          Margin="8,0">
                    <ComboBoxItem Content="الكل"/>
                    <ComboBoxItem Content="متاح">
                        <ComboBoxItem.Tag>
                            <models:PropertyStatus>Available</models:PropertyStatus>
                        </ComboBoxItem.Tag>
                    </ComboBoxItem>
                    <ComboBoxItem Content="مكتراة">
                        <ComboBoxItem.Tag>
                            <models:PropertyStatus>Rented</models:PropertyStatus>
                        </ComboBoxItem.Tag>
                    </ComboBoxItem>
                </ComboBox>

                <!-- Clear Filters -->
                <Button Grid.Column="2"
                        Content="مسح الفلاتر"
                        Command="{Binding ClearFiltersCommand}"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Margin="8,0,0,0"/>
            </Grid>
        </Grid>

        <!-- Properties List -->
        <materialDesign:Card Grid.Row="2"
                           materialDesign:ElevationAssist.Elevation="Dp2">
            <DataGrid ItemsSource="{Binding Properties}"
                      SelectedItem="{Binding SelectedProperty}"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      IsReadOnly="True"
                      materialDesign:DataGridAssist.CellPadding="13 8 8 8"
                      materialDesign:DataGridAssist.ColumnHeaderPadding="8">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="النوع" Width="80">
                        <DataGridTextColumn.Binding>
                            <Binding Path="Type" Converter="{StaticResource PropertyTypeConverter}"/>
                        </DataGridTextColumn.Binding>
                    </DataGridTextColumn>

                    <DataGridTextColumn Header="الموقع"
                                      Binding="{Binding Location}"
                                      Width="*"/>

                    <DataGridTextColumn Header="مبلغ الكراء"
                                      Binding="{Binding RentAmount, StringFormat='{}{0:C}'}"
                                      Width="120"/>

                    <DataGridTextColumn Header="الحالة" Width="80">
                        <DataGridTextColumn.Binding>
                            <Binding Path="Status" Converter="{StaticResource PropertyStatusConverter}"/>
                        </DataGridTextColumn.Binding>
                    </DataGridTextColumn>

                    <DataGridTextColumn Header="الزبون"
                                      Binding="{Binding Customer.FullName}"
                                      Width="150"/>

                    <DataGridTextColumn Header="تاريخ الإضافة"
                                      Binding="{Binding CreatedDate, StringFormat='{}{0:dd/MM/yyyy}'}"
                                      Width="120"/>

                    <DataGridTemplateColumn Header="الإجراءات" Width="200">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Button ToolTip="تعديل"
                                            Command="{Binding DataContext.EditPropertyCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                            CommandParameter="{Binding}"
                                            Style="{StaticResource MaterialDesignIconButton}"
                                            Margin="0,0,4,0">
                                        <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"/>
                                    </Button>
                                    <Button ToolTip="حذف"
                                            Command="{Binding DataContext.DeletePropertyCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                            CommandParameter="{Binding}"
                                            Style="{StaticResource MaterialDesignIconButton}"
                                            Foreground="{StaticResource ErrorBrush}"
                                            Margin="0,0,4,0">
                                        <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                    </Button>
                                    <Button ToolTip="عرض التفاصيل"
                                            Command="{Binding DataContext.ViewPropertyDetailsCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                            CommandParameter="{Binding}"
                                            Style="{StaticResource MaterialDesignIconButton}">
                                        <materialDesign:PackIcon Kind="Eye" Width="16" Height="16"/>
                                    </Button>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>
    </Grid>
</UserControl>
