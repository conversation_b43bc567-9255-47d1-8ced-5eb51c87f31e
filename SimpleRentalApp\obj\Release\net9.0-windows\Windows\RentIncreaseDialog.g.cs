﻿#pragma checksum "..\..\..\..\Windows\RentIncreaseDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "55095F1DDB62F7D8705A0035994238D6E2529787"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleRentalApp.Windows {
    
    
    /// <summary>
    /// RentIncreaseDialog
    /// </summary>
    public partial class RentIncreaseDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 31 "..\..\..\..\Windows\RentIncreaseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ContractInfoText;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\Windows\RentIncreaseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentRentText;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\Windows\RentIncreaseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox IncreaseTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\Windows\RentIncreaseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox IncreaseValueTextBox;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\..\..\Windows\RentIncreaseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock IncreaseUnitText;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\Windows\RentIncreaseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker EffectiveDatePicker;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\Windows\RentIncreaseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NewRentText;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\Windows\RentIncreaseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NewCleaningTaxText;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\Windows\RentIncreaseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\..\Windows\RentIncreaseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 176 "..\..\..\..\Windows\RentIncreaseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleRentalApp;V1.0.0.0;component/windows/rentincreasedialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\RentIncreaseDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ContractInfoText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.CurrentRentText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.IncreaseTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 88 "..\..\..\..\Windows\RentIncreaseDialog.xaml"
            this.IncreaseTypeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.IncreaseTypeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.IncreaseValueTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 104 "..\..\..\..\Windows\RentIncreaseDialog.xaml"
            this.IncreaseValueTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.IncreaseValueTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.IncreaseUnitText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.EffectiveDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 7:
            this.NewRentText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.NewCleaningTaxText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 174 "..\..\..\..\Windows\RentIncreaseDialog.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 185 "..\..\..\..\Windows\RentIncreaseDialog.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

