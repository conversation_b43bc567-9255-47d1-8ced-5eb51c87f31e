using System;
using System.Diagnostics;
using System.Web;
using System.Windows;

namespace SimpleRentalApp.Windows
{
    public partial class NotificationPreviewDialog : Window
    {
        private readonly string _message;
        private readonly string _phoneNumber;

        public NotificationPreviewDialog(string message, string phoneNumber)
        {
            InitializeComponent();
            _message = message;
            _phoneNumber = phoneNumber;
            
            LoadData();
        }

        private void LoadData()
        {
            PhoneNumberTextBox.Text = _phoneNumber;
            MessageTextBox.Text = _message;
        }

        private void SendButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تنظيف رقم الهاتف
                var cleanPhoneNumber = CleanPhoneNumber(_phoneNumber);
                if (string.IsNullOrEmpty(cleanPhoneNumber))
                {
                    MessageBox.Show("رقم الهاتف غير صحيح", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // إنشاء رابط الواتساب
                var encodedMessage = HttpUtility.UrlEncode(_message);
                var whatsappUrl = $"https://wa.me/{cleanPhoneNumber.Replace("+", "")}?text={encodedMessage}";

                // فتح الواتساب
                Process.Start(new ProcessStartInfo
                {
                    FileName = whatsappUrl,
                    UseShellExecute = true
                });

                MessageBox.Show("تم فتح الواتساب! يرجى إرسال الرسالة يدوياً.", "تم بنجاح",
                    MessageBoxButton.OK, MessageBoxImage.Information);

                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الواتساب: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string CleanPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber)) return "";
            
            // إزالة المسافات والرموز
            phoneNumber = phoneNumber.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");
            
            // إضافة رمز الدولة إذا لم يكن موجود
            if (phoneNumber.StartsWith("0"))
                phoneNumber = "+212" + phoneNumber.Substring(1);
            else if (!phoneNumber.StartsWith("+"))
                phoneNumber = "+212" + phoneNumber;
                
            return phoneNumber;
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
