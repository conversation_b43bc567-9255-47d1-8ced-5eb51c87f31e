﻿Rental Management System - Installation Instructions
==================================================

INSTALLATION OPTIONS:

1. PORTABLE VERSION (Recommended for most users):
   - Extract the archive to any folder
   - Run SimpleRentalApp.exe directly
   - No installation required

2. FULL INSTALLATION:
   - If you have Inno Setup: Compile installer.iss
   - If you have NSIS: Compile installer_nsis.nsi
   - This will create a proper Windows installer

SYSTEM REQUIREMENTS:
- Windows 10 or later (64-bit)
- .NET 9.0 Runtime (included)
- 100 MB free disk space

DEFAULT LOGIN CREDENTIALS:
Username: hafid
Password: ask dev (<EMAIL>)

FEATURES:
- Property Management
- Rent Collection Tracking
- Contract Management
- Cleaning Tax Management
- Receipt Generation (PDF)
- WhatsApp Notifications
- Data Export/Import
- Reports and Analytics

SUPPORT:
Developer: <PERSON><PERSON><PERSON>
For technical support, please contact the developer.

FIRST RUN:
1. Extract/Install the application
2. Run SimpleRentalApp.exe
3. Login with the default credentials
4. Start adding your properties and contracts

DATA LOCATION:
Application data is stored in:
%APPDATA%\TadbirAlKira\

BACKUP:
Regular backups are recommended. Use the Export feature
in the application to backup your data.
