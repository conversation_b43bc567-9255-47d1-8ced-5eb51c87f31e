using System;
using System.Windows;

namespace SimpleRentalApp.Windows
{
    public partial class EditDateDialog : Window
    {
        public DateTime SelectedDate { get; private set; }

        public EditDateDialog(DateTime currentDate)
        {
            InitializeComponent();
            
            // Set current date
            CurrentDateTextBlock.Text = currentDate.ToString("dd/MM/yyyy");
            NewDatePicker.SelectedDate = currentDate;
            SelectedDate = currentDate;
            
            // Focus on date picker
            NewDatePicker.Focus();
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (NewDatePicker.SelectedDate.HasValue)
            {
                SelectedDate = NewDatePicker.SelectedDate.Value;
                DialogResult = true;
                Close();
            }
            else
            {
                MessageBox.Show("يرجى اختيار تاريخ صحيح", "تاريخ غير صحيح", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                NewDatePicker.Focus();
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
