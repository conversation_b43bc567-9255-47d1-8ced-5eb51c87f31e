using System.ComponentModel.DataAnnotations;

namespace RentalManagement.Models
{
    public enum PropertyType
    {
        Apartment = 1,  // شقة
        Shop = 2        // محل
    }

    public enum PropertyStatus
    {
        Available = 1,  // فارغة
        Rented = 2      // مكتراة
    }

    public class Property
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "نوع العقار مطلوب")]
        public PropertyType Type { get; set; }

        [Required(ErrorMessage = "الموقع مطلوب")]
        [StringLength(200, ErrorMessage = "الموقع لا يجب أن يتجاوز 200 حرف")]
        public string Location { get; set; } = string.Empty;

        [Required(ErrorMessage = "مبلغ الكراء مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "مبلغ الكراء يجب أن يكون أكبر من صفر")]
        public decimal RentAmount { get; set; }

        public PropertyStatus Status { get; set; } = PropertyStatus.Available;

        [StringLength(500, ErrorMessage = "الوصف لا يجب أن يتجاوز 500 حرف")]
        public string Description { get; set; } = string.Empty;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Foreign key
        public int? CustomerId { get; set; }

        // Navigation properties
        public virtual Customer? Customer { get; set; }
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();
    }
}
