using System.ComponentModel.DataAnnotations;

namespace SimpleRentalApp.Models;

public enum UserRole
{
    Admin = 1,
    Manager = 2,
    Employee = 3
}

public class User
{
    public int Id { get; set; }
    
    [Required]
    [StringLength(50)]
    public string Username { get; set; } = string.Empty;
    
    [Required]
    [StringLength(100)]
    public string FullName { get; set; } = string.Empty;
    
    [Required]
    [StringLength(255)]
    public string PasswordHash { get; set; } = string.Empty;
    
    [StringLength(100)]
    public string Email { get; set; } = string.Empty;
    
    public UserRole Role { get; set; } = UserRole.Employee;
    
    public bool IsActive { get; set; } = true;
    
    public DateTime CreatedDate { get; set; } = DateTime.Now;
    
    public DateTime? LastLoginDate { get; set; }
    
    public string RoleDisplay => Role switch
    {
        UserRole.Admin => "مدير النظام",
        UserRole.Manager => "مدير",
        UserRole.Employee => "موظف",
        _ => "غير محدد"
    };
}
