<Window x:Class="RentalManagement.Views.PaymentDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:models="clr-namespace:RentalManagement.Models"
        mc:Ignorable="d"
        Title="{Binding Title}" 
        Height="650" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Title -->
        <TextBlock Grid.Row="0"
                   Text="{Binding Title}"
                   Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,24"/>

        <!-- Form Fields -->
        <StackPanel Grid.Row="1">
            <!-- Customer -->
            <ComboBox ItemsSource="{Binding Customers}"
                      SelectedItem="{Binding SelectedCustomer}"
                      DisplayMemberPath="FullName"
                      materialDesign:HintAssist.Hint="الزبون *"
                      Style="{StaticResource MaterialDesignOutlinedComboBox}"
                      Margin="0,0,0,16"/>

            <!-- Property -->
            <ComboBox ItemsSource="{Binding Properties}"
                      SelectedItem="{Binding SelectedProperty}"
                      DisplayMemberPath="Location"
                      materialDesign:HintAssist.Hint="العقار *"
                      Style="{StaticResource MaterialDesignOutlinedComboBox}"
                      Margin="0,0,0,16"/>

            <!-- Amount -->
            <TextBox Text="{Binding Amount, UpdateSourceTrigger=PropertyChanged}"
                     materialDesign:HintAssist.Hint="المبلغ *"
                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                     materialDesign:ValidationAssist.UsePopup="True"
                     Margin="0,0,0,16"/>

            <!-- Due Date -->
            <DatePicker SelectedDate="{Binding DueDate}"
                        materialDesign:HintAssist.Hint="تاريخ الاستحقاق *"
                        Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                        Margin="0,0,0,16"/>

            <!-- Status -->
            <ComboBox SelectedValue="{Binding Status}"
                      materialDesign:HintAssist.Hint="حالة الأداء"
                      Style="{StaticResource MaterialDesignOutlinedComboBox}"
                      Margin="0,0,0,16">
                <ComboBoxItem Content="غير مدفوع">
                    <ComboBoxItem.Tag>
                        <models:PaymentStatus>Unpaid</models:PaymentStatus>
                    </ComboBoxItem.Tag>
                </ComboBoxItem>
                <ComboBoxItem Content="مدفوع">
                    <ComboBoxItem.Tag>
                        <models:PaymentStatus>Paid</models:PaymentStatus>
                    </ComboBoxItem.Tag>
                </ComboBoxItem>
                <ComboBoxItem Content="متأخر">
                    <ComboBoxItem.Tag>
                        <models:PaymentStatus>Overdue</models:PaymentStatus>
                    </ComboBoxItem.Tag>
                </ComboBoxItem>
            </ComboBox>

            <!-- Payment Date (only if paid) -->
            <DatePicker SelectedDate="{Binding PaymentDate}"
                        materialDesign:HintAssist.Hint="تاريخ الدفع"
                        Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                        Margin="0,0,0,16"/>

            <!-- Notes -->
            <TextBox Text="{Binding Notes, UpdateSourceTrigger=PropertyChanged}"
                     materialDesign:HintAssist.Hint="الملاحظات"
                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                     AcceptsReturn="True"
                     TextWrapping="Wrap"
                     MinLines="3"
                     MaxLines="5"
                     VerticalScrollBarVisibility="Auto"/>
        </StackPanel>

        <!-- Error Message -->
        <TextBlock Grid.Row="2"
                   Text="{Binding ErrorMessage}"
                   Foreground="{StaticResource ErrorBrush}"
                   TextWrapping="Wrap"
                   Margin="0,16,0,0"
                   Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"/>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="3"
                    Orientation="Horizontal"
                    HorizontalAlignment="Left"
                    Margin="0,24,0,0">
            <Button Content="حفظ"
                    Command="{Binding SaveCommand}"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    IsDefault="True"
                    Margin="0,0,16,0"
                    Width="100"/>
            
            <Button Content="إلغاء"
                    Command="{Binding CancelCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    IsCancel="True"
                    Width="100"/>
        </StackPanel>
    </Grid>
</Window>
