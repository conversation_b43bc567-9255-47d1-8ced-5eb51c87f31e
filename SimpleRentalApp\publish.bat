@echo off
chcp 65001 > nul
echo ========================================
echo    Rental Management - Publishing App
echo ========================================
echo.

echo [1/5] Cleaning project...
dotnet clean --configuration Release
if errorlevel 1 (
    echo Error cleaning project
    pause
    exit /b 1
)

echo [2/5] Building project...
dotnet build --configuration Release
if errorlevel 1 (
    echo Error building project
    pause
    exit /b 1
)

echo [3/5] Publishing application...
dotnet publish --configuration Release --runtime win-x64 --self-contained true -p:PublishSingleFile=true --output publish
if errorlevel 1 (
    echo Error publishing application
    pause
    exit /b 1
)

echo [4/5] Copying additional files...
copy README.md publish\ > nul 2>&1

echo [5/5] Creating launcher...
echo @echo off > publish\run_app.bat
echo chcp 65001 ^> nul >> publish\run_app.bat
echo echo Starting Rental Management App... >> publish\run_app.bat
echo start SimpleRentalApp.exe >> publish\run_app.bat

echo.
echo ========================================
echo Application published successfully in publish folder
echo Executable file size:
for %%A in (publish\SimpleRentalApp.exe) do echo %%~zA bytes
echo ========================================
echo.

echo Creating system info file...
echo Rental Management System - Property and Rent Management > publish\system_info.txt
echo ================================================ >> publish\system_info.txt
echo. >> publish\system_info.txt
echo Developer: Hafid Abdo >> publish\system_info.txt
echo Version: 1.0.0 >> publish\system_info.txt
echo Release Date: %date% >> publish\system_info.txt
echo. >> publish\system_info.txt
echo Default Login Credentials: >> publish\system_info.txt
echo Username: hafid >> publish\system_info.txt
echo Password: hafidos159357 >> publish\system_info.txt
echo. >> publish\system_info.txt
echo For technical support please contact the developer >> publish\system_info.txt

echo System info file created successfully
echo.
pause
