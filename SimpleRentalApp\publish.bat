@echo off
chcp 65001 > nul
echo ========================================
echo       نشر تطبيق تدبير الكراء
echo ========================================
echo.

echo تنظيف المجلدات السابقة...
if exist "bin\Release" rmdir /s /q "bin\Release"
if exist "publish" rmdir /s /q "publish"
if exist "dist" rmdir /s /q "dist"

echo.
echo بناء التطبيق في وضع Release...
dotnet build --configuration Release

if %ERRORLEVEL% neq 0 (
    echo خطأ في بناء التطبيق!
    pause
    exit /b 1
)

echo.
echo نشر التطبيق...
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output "publish"

if %ERRORLEVEL% neq 0 (
    echo خطأ في نشر التطبيق!
    pause
    exit /b 1
)

echo.
echo إنشاء مجلد التوزيع...
if not exist "dist" mkdir "dist"

echo.
echo نسخ الملفات...
copy "publish\SimpleRentalApp.exe" "dist\تدبير_الكراء.exe"

echo.
echo إنشاء ملف README...
echo تطبيق تدبير الكراء > "dist\README.txt"
echo ==================== >> "dist\README.txt"
echo. >> "dist\README.txt"
echo هذا التطبيق لإدارة الكراء والعقارات >> "dist\README.txt"
echo. >> "dist\README.txt"
echo طريقة التشغيل: >> "dist\README.txt"
echo 1. انقر نقراً مزدوجاً على ملف "تدبير_الكراء.exe" >> "dist\README.txt"
echo 2. سيتم إنشاء قاعدة البيانات تلقائياً عند أول تشغيل >> "dist\README.txt"
echo. >> "dist\README.txt"
echo المطور: حفيظ عبدو >> "dist\README.txt"
echo السنة: 2024 >> "dist\README.txt"

echo.
echo ========================================
echo تم نشر التطبيق بنجاح!
echo ========================================
echo.
echo الملفات متوفرة في مجلد: dist
echo ملف التطبيق: تدبير_الكراء.exe
echo.
echo يمكنك الآن توزيع مجلد "dist" أو ضغطه في ملف ZIP
echo.
pause
