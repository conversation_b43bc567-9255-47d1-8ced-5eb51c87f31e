<?xml version="1.0" encoding="utf-8"?>
<Window x:Class="SimpleRentalApp.Windows.MessageTemplatesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="📝 إدارة قوالب الرسائل"
        Height="700" Width="900"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        FlowDirection="RightToLeft">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
            <TextBlock Text="📝" FontSize="24" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <TextBlock Text="إدارة قوالب الرسائل" FontSize="18" FontWeight="Bold" Foreground="#2C3E50"/>
        </StackPanel>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,15">
            <Button Name="AddTemplateButton"
                    Content="➕ إضافة قالب جديد"
                    Padding="12,8"
                    Margin="5,0"
                    Background="#27AE60"
                    Foreground="White"
                    BorderThickness="0"
                    FontWeight="Bold"
                    Click="AddTemplateButton_Click"/>
            
            <Button Name="EditTemplateButton"
                    Content="✏️ تعديل القالب"
                    Padding="12,8"
                    Margin="5,0"
                    Background="#3498DB"
                    Foreground="White"
                    BorderThickness="0"
                    FontWeight="Bold"
                    Click="EditTemplateButton_Click"/>
            
            <Button Name="DeleteTemplateButton"
                    Content="🗑️ حذف القالب"
                    Padding="12,8"
                    Margin="5,0"
                    Background="#E74C3C"
                    Foreground="White"
                    BorderThickness="0"
                    FontWeight="Bold"
                    Click="DeleteTemplateButton_Click"/>
            

        </StackPanel>

        <!-- Templates Grid -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Templates List -->
            <Border Grid.Column="0" BorderBrush="#BDC3C7" BorderThickness="1" Background="White" Margin="0,0,10,0">
                <DataGrid Name="TemplatesDataGrid"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          AlternatingRowBackground="#F8F9FA"
                          SelectionMode="Single"
                          FontSize="12"
                          SelectionChanged="TemplatesDataGrid_SelectionChanged">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="اسم القالب" Binding="{Binding Name}" Width="150"/>
                        <DataGridTextColumn Header="النوع" Binding="{Binding TypeDisplay}" Width="100"/>
                        <DataGridCheckBoxColumn Header="افتراضي" Binding="{Binding IsDefault}" Width="50"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Border>

            <!-- Template Details -->
            <Border Grid.Column="1" BorderBrush="#BDC3C7" BorderThickness="1" Background="White">
                <Grid Margin="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Template Name -->
                    <StackPanel Grid.Row="0" Margin="0,0,0,10">
                        <TextBlock Text="اسم القالب:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox Name="TemplateNameTextBox" Padding="8" FontSize="14"/>
                    </StackPanel>

                    <!-- Template Type -->
                    <StackPanel Grid.Row="1" Margin="0,0,0,10">
                        <TextBlock Text="نوع الإشعار:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox Name="TemplateTypeComboBox" Padding="8" FontSize="14">
                            <ComboBoxItem Content="الكراء الشهري" Tag="MonthlyRent"/>
                            <ComboBoxItem Content="ضريبة النظافة" Tag="CleaningTax"/>
                            <ComboBoxItem Content="انتهاء العقد" Tag="ContractExpiry"/>
                            <ComboBoxItem Content="دفعة متأخرة" Tag="OverduePayment"/>
                            <ComboBoxItem Content="إشعار متأخر" Tag="LateReminder"/>
                        </ComboBox>
                    </StackPanel>

                    <!-- Available Variables -->
                    <StackPanel Grid.Row="2" Margin="0,0,0,10">
                        <TextBlock Text="المتغيرات المتاحة:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBlock Name="AvailableVariablesTextBlock" 
                                   Text="[اسم المكتري], [اسم المحل], [المبلغ], [تاريخ الأداء]"
                                   FontSize="12"
                                   Foreground="#7F8C8D"
                                   TextWrapping="Wrap"/>
                    </StackPanel>

                    <!-- Template Content -->
                    <StackPanel Grid.Row="3" Margin="0,0,0,10">
                        <TextBlock Text="نص القالب:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <Border BorderBrush="#BDC3C7" BorderThickness="1">
                            <ScrollViewer VerticalScrollBarVisibility="Auto">
                                <TextBox Name="TemplateContentTextBox"
                                         TextWrapping="Wrap"
                                         AcceptsReturn="True"
                                         Background="Transparent"
                                         BorderThickness="0"
                                         Padding="10"
                                         FontSize="14"
                                         MinHeight="200"/>
                            </ScrollViewer>
                        </Border>
                    </StackPanel>

                    <!-- Options -->
                    <StackPanel Grid.Row="4" Orientation="Horizontal">
                        <CheckBox Name="IsActiveCheckBox" Content="نشط" Margin="0,0,15,0"/>
                        <CheckBox Name="IsDefaultCheckBox" Content="افتراضي" Margin="0,0,15,0"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,15,0,0">
            <Button Name="SaveButton"
                    Content="💾 حفظ التغييرات"
                    Padding="15,10"
                    Margin="10,0"
                    Background="#27AE60"
                    Foreground="White"
                    BorderThickness="0"
                    FontSize="14"
                    FontWeight="Bold"
                    Click="SaveButton_Click"/>
            
            <Button Name="CancelButton"
                    Content="❌ إلغاء"
                    Padding="15,10"
                    Margin="10,0"
                    Background="#95A5A6"
                    Foreground="White"
                    BorderThickness="0"
                    FontSize="14"
                    FontWeight="Bold"
                    Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
