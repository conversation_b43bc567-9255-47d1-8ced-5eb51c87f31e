using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;
using SimpleRentalApp.Data;
using SimpleRentalApp.Models;

namespace SimpleRentalApp.Windows
{
    public partial class MessageTemplatesWindow : Window
    {
        private readonly RentalDbContext _context;
        private List<MessageTemplateViewModel> _templates = new();
        private MessageTemplateViewModel? _selectedTemplate;

        public MessageTemplatesWindow()
        {
            InitializeComponent();
            _context = new RentalDbContext();
            LoadTemplates();
        }

        private async void LoadTemplates()
        {
            try
            {
                var templates = await _context.MessageTemplates
                    .OrderBy(t => t.Type)
                    .ThenBy(t => t.Name)
                    .ToListAsync();

                _templates = templates.Select(t => new MessageTemplateViewModel
                {
                    Id = t.Id,
                    Name = t.Name,
                    Type = t.Type,
                    TypeDisplay = GetTypeDisplay(t.Type),
                    Template = t.Template,
                    IsActive = t.IsActive,
                    IsDefault = t.IsDefault,
                    AvailableVariables = t.AvailableVariables
                }).ToList();

                TemplatesDataGrid.ItemsSource = _templates;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل القوالب: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string GetTypeDisplay(NotificationType type)
        {
            return type switch
            {
                NotificationType.MonthlyRent => "الكراء الشهري",
                NotificationType.CleaningTax => "ضريبة النظافة",
                NotificationType.ContractExpiry => "انتهاء العقد",
                NotificationType.RentOverdue => "دفعة متأخرة",
                NotificationType.PaymentReminder => "إشعار متأخر",
                _ => "غير محدد"
            };
        }

        private void AddTemplateButton_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
            _selectedTemplate = null;
        }

        private void EditTemplateButton_Click(object sender, RoutedEventArgs e)
        {
            if (TemplatesDataGrid.SelectedItem is MessageTemplateViewModel template)
            {
                LoadTemplateToForm(template);
            }
            else
            {
                MessageBox.Show("يرجى اختيار قالب للتعديل", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async void DeleteTemplateButton_Click(object sender, RoutedEventArgs e)
        {
            if (TemplatesDataGrid.SelectedItem is MessageTemplateViewModel template)
            {
                var result = MessageBox.Show($"هل أنت متأكد من حذف القالب '{template.Name}'؟", "تأكيد الحذف",
                    MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        var dbTemplate = await _context.MessageTemplates.FindAsync(template.Id);
                        if (dbTemplate != null)
                        {
                            _context.MessageTemplates.Remove(dbTemplate);
                            await _context.SaveChangesAsync();
                            LoadTemplates();
                            ClearForm();
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف القالب: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار قالب للحذف", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadTemplates();
        }

        private void TemplatesDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (TemplatesDataGrid.SelectedItem is MessageTemplateViewModel template)
            {
                LoadTemplateToForm(template);
            }
        }

        private void LoadTemplateToForm(MessageTemplateViewModel template)
        {
            _selectedTemplate = template;
            TemplateNameTextBox.Text = template.Name;
            TemplateContentTextBox.Text = template.Template;
            IsActiveCheckBox.IsChecked = template.IsActive;
            IsDefaultCheckBox.IsChecked = template.IsDefault;

            // تحديد نوع القالب
            foreach (ComboBoxItem item in TemplateTypeComboBox.Items)
            {
                if (item.Tag?.ToString() == template.Type.ToString())
                {
                    TemplateTypeComboBox.SelectedItem = item;
                    break;
                }
            }

            UpdateAvailableVariables();
        }

        private void ClearForm()
        {
            TemplateNameTextBox.Text = "";
            TemplateContentTextBox.Text = "";
            IsActiveCheckBox.IsChecked = true;
            IsDefaultCheckBox.IsChecked = false;
            TemplateTypeComboBox.SelectedIndex = 0;
            UpdateAvailableVariables();
        }

        private void UpdateAvailableVariables()
        {
            if (TemplateTypeComboBox.SelectedItem is ComboBoxItem item && item.Tag != null)
            {
                var typeString = item.Tag.ToString();
                if (Enum.TryParse<NotificationType>(typeString, out var type))
                {
                    var variables = TemplateVariables.GetVariablesForType(type);
                    AvailableVariablesTextBlock.Text = string.Join(", ", variables);
                }
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(TemplateNameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم القالب", "خطأ في البيانات",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(TemplateContentTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال نص القالب", "خطأ في البيانات",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (TemplateTypeComboBox.SelectedItem is not ComboBoxItem typeItem || typeItem.Tag == null)
                {
                    MessageBox.Show("يرجى اختيار نوع القالب", "خطأ في البيانات",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var type = Enum.Parse<NotificationType>(typeItem.Tag.ToString()!);

                MessageTemplate template;
                if (_selectedTemplate != null)
                {
                    // تعديل قالب موجود
                    template = await _context.MessageTemplates.FindAsync(_selectedTemplate.Id);
                    if (template == null)
                    {
                        MessageBox.Show("القالب غير موجود", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }
                }
                else
                {
                    // إنشاء قالب جديد
                    template = new MessageTemplate();
                    _context.MessageTemplates.Add(template);
                }

                template.Name = TemplateNameTextBox.Text.Trim();
                template.Type = type;
                template.Template = TemplateContentTextBox.Text.Trim();
                template.IsActive = IsActiveCheckBox.IsChecked == true;
                template.IsDefault = IsDefaultCheckBox.IsChecked == true;
                template.UpdatedDate = DateTime.Now;
                template.AvailableVariables = string.Join(", ", TemplateVariables.GetVariablesForType(type));

                await _context.SaveChangesAsync();

                MessageBox.Show("تم حفظ القالب بنجاح", "نجح الحفظ",
                    MessageBoxButton.OK, MessageBoxImage.Information);

                LoadTemplates();
                ClearForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ القالب: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            base.OnClosed(e);
        }
    }

    public class MessageTemplateViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public NotificationType Type { get; set; }
        public string TypeDisplay { get; set; } = "";
        public string Template { get; set; } = "";
        public bool IsActive { get; set; }
        public bool IsDefault { get; set; }
        public string AvailableVariables { get; set; } = "";
    }
}
