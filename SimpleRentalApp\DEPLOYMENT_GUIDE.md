# نظام تدبير الكراء - دليل النشر والتثبيت

## 📦 ملفات التوزيع المتاحة

تم إنشاء عدة خيارات للتثبيت والتوزيع:

### 1. النسخة المحمولة (Portable) - الأسهل ✅
- **الملف**: `RentalManagementPortable.zip`
- **الحجم**: ~50 MB مضغوط
- **المتطلبات**: لا يحتاج تثبيت
- **الاستخدام**: 
  1. فك الضغط في أي مجلد
  2. تشغيل `SimpleRentalApp.exe`

### 2. ملف التثبيت الاحترافي (Installer)
- **الملف**: `installer_output/installer.iss` (Inno Setup)
- **الملف**: `installer_output/installer_nsis.nsi` (NSIS)
- **يتطلب**: برنامج Inno Setup أو NSIS لإنشاء ملف .exe

### 3. النسخة الكاملة للمطورين
- **المجلد**: `installer_output/`
- **يحتوي على**: جميع الملفات والأدوات

## 🚀 طرق التثبيت

### الطريقة الأولى: النسخة المحمولة (موصى بها)

```bash
1. تحميل ملف RentalManagementPortable.zip
2. فك الضغط في مجلد (مثل: C:\RentalManagement)
3. تشغيل SimpleRentalApp.exe
4. تسجيل الدخول بالبيانات الافتراضية
```

### الطريقة الثانية: إنشاء ملف تثبيت احترافي

#### باستخدام Inno Setup:
```bash
1. تحميل وتثبيت Inno Setup من: https://jrsoftware.org/isinfo.php
2. فتح ملف installer_output/installer.iss
3. الضغط على Build > Compile
4. سيتم إنشاء RentalManagementSetup.exe
```

#### باستخدام NSIS:
```bash
1. تحميل وتثبيت NSIS من: https://nsis.sourceforge.io/
2. فتح ملف installer_output/installer_nsis.nsi
3. الضغط على Compile NSI
4. سيتم إنشاء RentalManagementSetup.exe
```

## 🔧 متطلبات النظام

- **نظام التشغيل**: Windows 10 أو أحدث (64-bit)
- **الذاكرة**: 4 GB RAM (الحد الأدنى)
- **مساحة القرص**: 200 MB مساحة فارغة
- **الشبكة**: اختيارية (للإشعارات WhatsApp)

## 🔐 بيانات تسجيل الدخول الافتراضية

```
اسم المستخدم: hafid
كلمة المرور: hafidos159357
```

## 📁 مواقع الملفات

### بيانات التطبيق:
```
%APPDATA%\TadbirAlKira\
```

### قاعدة البيانات:
```
%APPDATA%\TadbirAlKira\rental_management.db
```

### النسخ الاحتياطية:
```
%USERPROFILE%\Documents\TadbirAlKira_Backups\
```

## 🎯 الميزات الرئيسية

- ✅ إدارة العقارات والمحلات
- ✅ تتبع عقود الإيجار
- ✅ إدارة المدفوعات والإيصالات
- ✅ ضريبة النظافة
- ✅ إنشاء تقارير PDF
- ✅ إشعارات WhatsApp
- ✅ استيراد وتصدير البيانات
- ✅ نظام النسخ الاحتياطي

## 🔄 التحديث

### للنسخة المحمولة:
1. نسخ احتياطي لمجلد البيانات
2. استبدال الملفات بالإصدار الجديد
3. الاحتفاظ بملف قاعدة البيانات

### للنسخة المثبتة:
1. تشغيل ملف التثبيت الجديد
2. سيتم التحديث تلقائياً مع الاحتفاظ بالبيانات

## 🛠️ استكشاف الأخطاء

### مشكلة: التطبيق لا يبدأ
**الحل**: 
- التأكد من وجود .NET 9.0 Runtime
- تشغيل كمسؤول
- فحص مكافح الفيروسات

### مشكلة: فقدان البيانات
**الحل**:
- استعادة من النسخة الاحتياطية
- فحص مجلد %APPDATA%\TadbirAlKira\

### مشكلة: خطأ في قاعدة البيانات
**الحل**:
- استخدام ميزة إصلاح قاعدة البيانات في التطبيق
- استعادة من نسخة احتياطية

## 📞 الدعم الفني

**المطور**: حفيظ عبدو  
**الإصدار**: 1.0.0  
**تاريخ الإصدار**: 2025

### للحصول على الدعم:
1. فحص دليل المستخدم
2. البحث في الأسئلة الشائعة
3. التواصل مع المطور

## 📋 قائمة التحقق للنشر

- [x] بناء التطبيق بنجاح
- [x] إنشاء النسخة المحمولة
- [x] إنشاء ملفات التثبيت
- [x] اختبار التطبيق
- [x] إنشاء الوثائق
- [x] تحضير ملفات التوزيع

## 🎉 ملاحظات النشر

هذا الإصدار الأول من نظام تدبير الكراء يتضمن جميع الميزات الأساسية لإدارة العقارات والإيجارات. التطبيق جاهز للاستخدام الإنتاجي.

---

**تم إنشاء هذا الدليل تلقائياً بواسطة نظام البناء**
