# إنشاء أيقونة عربية لتطبيق تدبير الكراء
# Create Arabic icon for Rental Management Application

Add-Type -AssemblyName System.Drawing
Add-Type -AssemblyName System.Windows.Forms

Write-Host "إنشاء أيقونة عربية للتطبيق..." -ForegroundColor Green
Write-Host "Creating Arabic application icon..." -ForegroundColor Green

# Create a 256x256 bitmap
$size = 256
$bitmap = New-Object System.Drawing.Bitmap($size, $size)
$graphics = [System.Drawing.Graphics]::FromImage($bitmap)

# Set high quality rendering
$graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
$graphics.TextRenderingHint = [System.Drawing.Text.TextRenderingHint]::AntiAlias

# Create modern gradient background (professional blue)
$brush = New-Object System.Drawing.Drawing2D.LinearGradientBrush(
    (New-Object System.Drawing.Point(0, 0)),
    (New-Object System.Drawing.Point($size, $size)),
    [System.Drawing.Color]::FromArgb(52, 73, 94),   # Dark blue-gray
    [System.Drawing.Color]::FromArgb(44, 62, 80)    # Darker blue-gray
)

# Fill background with gradient
$graphics.FillRectangle($brush, 0, 0, $size, $size)

# Create modern building silhouette
$whiteBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
$lightBlueBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(74, 144, 226))

# Draw main building
$building1 = New-Object System.Drawing.Rectangle(60, 100, 40, 100)
$building2 = New-Object System.Drawing.Rectangle(110, 80, 50, 120)
$building3 = New-Object System.Drawing.Rectangle(170, 90, 35, 110)

$graphics.FillRectangle($whiteBrush, $building1)
$graphics.FillRectangle($lightBlueBrush, $building2)
$graphics.FillRectangle($whiteBrush, $building3)

# Add windows to buildings
$windowBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(52, 73, 94))

# Building 1 windows
for ($i = 0; $i -lt 3; $i++) {
    for ($j = 0; $j -lt 2; $j++) {
        $window = New-Object System.Drawing.Rectangle((70 + $j * 15), (120 + $i * 20), 8, 12)
        $graphics.FillRectangle($windowBrush, $window)
    }
}

# Building 2 windows
for ($i = 0; $i -lt 4; $i++) {
    for ($j = 0; $j -lt 3; $j++) {
        $window = New-Object System.Drawing.Rectangle((120 + $j * 12), (100 + $i * 20), 8, 12)
        $graphics.FillRectangle($windowBrush, $window)
    }
}

# Building 3 windows
for ($i = 0; $i -lt 3; $i++) {
    for ($j = 0; $j -lt 2; $j++) {
        $window = New-Object System.Drawing.Rectangle((178 + $j * 12), (110 + $i * 20), 6, 10)
        $graphics.FillRectangle($windowBrush, $window)
    }
}

# Add Arabic text "كراء" (Rent)
try {
    $arabicFont = New-Object System.Drawing.Font("Arial", 32, [System.Drawing.FontStyle]::Bold)
    $textBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
    $arabicText = "كراء"
    
    # Measure text size
    $textSize = $graphics.MeasureString($arabicText, $arabicFont)
    $textX = ($size - $textSize.Width) / 2
    $textY = 210
    
    # Add text shadow
    $shadowBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(100, 0, 0, 0))
    $graphics.DrawString($arabicText, $arabicFont, $shadowBrush, $textX + 2, $textY + 2)
    
    # Draw main text
    $graphics.DrawString($arabicText, $arabicFont, $textBrush, $textX, $textY)
    
    $arabicFont.Dispose()
    $textBrush.Dispose()
    $shadowBrush.Dispose()
}
catch {
    # Fallback to English if Arabic font fails
    $fallbackFont = New-Object System.Drawing.Font("Arial", 24, [System.Drawing.FontStyle]::Bold)
    $textBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
    $fallbackText = "RENT"
    
    $textSize = $graphics.MeasureString($fallbackText, $fallbackFont)
    $textX = ($size - $textSize.Width) / 2
    $textY = 210
    
    $graphics.DrawString($fallbackText, $fallbackFont, $textBrush, $textX, $textY)
    
    $fallbackFont.Dispose()
    $textBrush.Dispose()
}

# Add decorative elements
$accentBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(230, 126, 34))
$accentRect = New-Object System.Drawing.Rectangle(50, 50, 156, 8)
$graphics.FillRectangle($accentBrush, $accentRect)

# Clean up graphics
$graphics.Dispose()

# Save as PNG
$pngPath = "rental_icon.png"
$bitmap.Save($pngPath, [System.Drawing.Imaging.ImageFormat]::Png)
Write-Host "تم إنشاء ملف PNG: $pngPath" -ForegroundColor Cyan
Write-Host "PNG icon created: $pngPath" -ForegroundColor Cyan

# Convert to ICO format
try {
    $icoPath = "rental_icon.ico"
    
    # Create a simple ICO file
    $fileStream = [System.IO.File]::Create($icoPath)
    $bitmap.Save($fileStream, [System.Drawing.Imaging.ImageFormat]::Icon)
    $fileStream.Close()
    
    Write-Host "تم إنشاء ملف ICO: $icoPath" -ForegroundColor Cyan
    Write-Host "ICO icon created: $icoPath" -ForegroundColor Cyan
}
catch {
    Write-Host "لم يتم إنشاء ملف ICO. يمكنك تحويل PNG إلى ICO باستخدام أدوات أونلاين." -ForegroundColor Yellow
    Write-Host "Could not create ICO file. You can convert PNG to ICO using online tools." -ForegroundColor Yellow
}

# Clean up
$bitmap.Dispose()
$brush.Dispose()
$whiteBrush.Dispose()
$lightBlueBrush.Dispose()
$windowBrush.Dispose()
$accentBrush.Dispose()

Write-Host ""
Write-Host "تم إنشاء الأيقونة بنجاح!" -ForegroundColor Green
Write-Host "Icon creation completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "الملفات المنشأة:" -ForegroundColor Cyan
Write-Host "Files created:" -ForegroundColor Cyan
Write-Host "- rental_icon.png (تنسيق PNG)" -ForegroundColor White
Write-Host "- rental_icon.png (PNG format)" -ForegroundColor White

if (Test-Path "rental_icon.ico") {
    Write-Host "- rental_icon.ico (تنسيق ICO)" -ForegroundColor White
    Write-Host "- rental_icon.ico (ICO format)" -ForegroundColor White
}

Write-Host ""
Write-Host "الخطوات التالية:" -ForegroundColor Yellow
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. استبدال app_icon.ico بـ rental_icon.ico" -ForegroundColor White
Write-Host "1. Replace app_icon.ico with rental_icon.ico" -ForegroundColor White
Write-Host "2. إعادة بناء المشروع" -ForegroundColor White
Write-Host "2. Rebuild the project" -ForegroundColor White
