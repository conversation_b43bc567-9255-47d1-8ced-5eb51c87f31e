using Microsoft.EntityFrameworkCore;
using SimpleRentalApp.Data;
using SimpleRentalApp.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SimpleRentalApp.Services
{
    /// <summary>
    /// خدمة إنشاء وإدارة القوالب الافتراضية للرسائل
    /// </summary>
    public class DefaultTemplateService
    {
        private readonly RentalDbContext _context;

        public DefaultTemplateService(RentalDbContext context)
        {
            _context = context;
        }

        public DefaultTemplateService()
        {
            _context = new RentalDbContext();
        }

        /// <summary>
        /// إنشاء القوالب الافتراضية إذا لم تكن موجودة
        /// </summary>
        public async Task<bool> CreateDefaultTemplatesAsync()
        {
            try
            {
                var templatesCreated = 0;

                // قالب الإيجار الشهري
                if (!await _context.MessageTemplates.AnyAsync(t => t.Type == NotificationType.MonthlyRent))
                {
                    var monthlyRentTemplate = new MessageTemplate
                    {
                        Name = "قالب الإيجار الشهري الافتراضي",
                        Type = NotificationType.MonthlyRent,
                        Template = "السلام عليكم {اسم_المكتري}،\n\n" +
                                  "نذكركم بوجوب أداء كراء محل {اسم_المحل} لشهر {شهر_الدفع}.\n\n" +
                                  "📍 المحل: {اسم_المحل}\n" +
                                  "📍 العنوان: {عنوان_المحل}\n" +
                                  "💰 المبلغ المستحق: {المبلغ} درهم\n" +
                                  "📅 آخر أجل للدفع: {تاريخ_الاستحقاق}\n\n" +
                                  "شكراً لتعاونكم معنا.\n" +
                                  "فريق الإدارة",
                        IsActive = true,
                        CreatedDate = DateTime.Now,
                        UpdatedDate = DateTime.Now
                    };
                    
                    _context.MessageTemplates.Add(monthlyRentTemplate);
                    templatesCreated++;
                }

                // قالب ضريبة النظافة
                if (!await _context.MessageTemplates.AnyAsync(t => t.Type == NotificationType.CleaningTax))
                {
                    var cleaningTaxTemplate = new MessageTemplate
                    {
                        Name = "قالب ضريبة النظافة الافتراضي",
                        Type = NotificationType.CleaningTax,
                        Template = "مرحباً {اسم_المكتري}،\n\n" +
                                  "المرجو تسديد ضريبة النظافة عن المحل {اسم_المحل} لسنة {سنة_الضريبة}.\n\n" +
                                  "📍 المحل: {اسم_المحل}\n" +
                                  "📍 العنوان: {عنوان_المحل}\n" +
                                  "💰 المبلغ المستحق: {المبلغ} درهم\n" +
                                  "📅 آخر أجل للدفع: {موعد_انتهاء_الضريبة}\n\n" +
                                  "ملاحظة: ضريبة النظافة إجبارية ويجب تسديدها قبل الموعد المحدد.\n\n" +
                                  "مع تحياتنا،\n" +
                                  "فريق الإدارة",
                        IsActive = true,
                        CreatedDate = DateTime.Now,
                        UpdatedDate = DateTime.Now
                    };
                    
                    _context.MessageTemplates.Add(cleaningTaxTemplate);
                    templatesCreated++;
                }

                // قالب تأخير الإيجار
                if (!await _context.MessageTemplates.AnyAsync(t => t.Type == NotificationType.RentOverdue))
                {
                    var rentOverdueTemplate = new MessageTemplate
                    {
                        Name = "قالب تأخير الإيجار الافتراضي",
                        Type = NotificationType.RentOverdue,
                        Template = "⚠️ تنبيه مهم ⚠️\n\n" +
                                  "السيد/ة {اسم_المكتري}،\n\n" +
                                  "نلفت انتباهكم إلى وجود دفعة متأخرة تخص المحل {اسم_المحل}.\n\n" +
                                  "📍 المحل: {اسم_المحل}\n" +
                                  "📍 العنوان: {عنوان_المحل}\n" +
                                  "💰 المبلغ المتأخر: {المبلغ} درهم\n" +
                                  "⏰ عدد أيام التأخير: {ايام_التاخير} يوم\n\n" +
                                  "المرجو الأداء في أقرب وقت ممكن لتفادي أي إجراءات إضافية.\n\n" +
                                  "للاستفسار يرجى الاتصال بنا.\n" +
                                  "شكراً لتفهمكم.",
                        IsActive = true,
                        CreatedDate = DateTime.Now,
                        UpdatedDate = DateTime.Now
                    };
                    
                    _context.MessageTemplates.Add(rentOverdueTemplate);
                    templatesCreated++;
                }

                // قالب تأخير ضريبة النظافة
                if (!await _context.MessageTemplates.AnyAsync(t => t.Type == NotificationType.CleaningTaxOverdue))
                {
                    var cleaningTaxOverdueTemplate = new MessageTemplate
                    {
                        Name = "قالب تأخير ضريبة النظافة الافتراضي",
                        Type = NotificationType.CleaningTaxOverdue,
                        Template = "🚨 تنبيه عاجل - ضريبة النظافة 🚨\n\n" +
                                  "السيد/ة {اسم_المكتري}،\n\n" +
                                  "ضريبة النظافة للمحل {اسم_المحل} لسنة {سنة_الضريبة} متأخرة عن موعد الاستحقاق.\n\n" +
                                  "📍 المحل: {اسم_المحل}\n" +
                                  "📍 العنوان: {عنوان_المحل}\n" +
                                  "💰 المبلغ المتأخر: {المبلغ} درهم\n" +
                                  "⏰ عدد أيام التأخير: {ايام_التاخير} يوم\n\n" +
                                  "تذكير: ضريبة النظافة إجبارية وقد تترتب عليها غرامات في حالة التأخير.\n" +
                                  "المرجو التسديد فوراً.\n\n" +
                                  "مع تحياتنا،\n" +
                                  "فريق الإدارة",
                        IsActive = true,
                        CreatedDate = DateTime.Now,
                        UpdatedDate = DateTime.Now
                    };
                    
                    _context.MessageTemplates.Add(cleaningTaxOverdueTemplate);
                    templatesCreated++;
                }

                if (templatesCreated > 0)
                {
                    await _context.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"تم إنشاء {templatesCreated} قالب افتراضي جديد");
                }

                return templatesCreated > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء القوالب الافتراضية: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود القوالب الأساسية وإنشاؤها إذا لزم الأمر
        /// </summary>
        public async Task<bool> EnsureDefaultTemplatesExistAsync()
        {
            try
            {
                var requiredTypes = new[]
                {
                    NotificationType.MonthlyRent,
                    NotificationType.CleaningTax,
                    NotificationType.RentOverdue,
                    NotificationType.CleaningTaxOverdue
                };

                var existingTypes = await _context.MessageTemplates
                    .Where(t => t.IsActive)
                    .Select(t => t.Type)
                    .Distinct()
                    .ToListAsync();

                var missingTypes = requiredTypes.Except(existingTypes).ToList();

                if (missingTypes.Any())
                {
                    System.Diagnostics.Debug.WriteLine($"أنواع القوالب المفقودة: {string.Join(", ", missingTypes)}");
                    return await CreateDefaultTemplatesAsync();
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التحقق من القوالب: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على إحصائيات القوالب
        /// </summary>
        public async Task<TemplateStatistics> GetTemplateStatisticsAsync()
        {
            try
            {
                var stats = new TemplateStatistics();

                stats.TotalTemplates = await _context.MessageTemplates.CountAsync();
                stats.ActiveTemplates = await _context.MessageTemplates.CountAsync(t => t.IsActive);
                stats.InactiveTemplates = stats.TotalTemplates - stats.ActiveTemplates;

                stats.MonthlyRentTemplates = await _context.MessageTemplates
                    .CountAsync(t => t.Type == NotificationType.MonthlyRent && t.IsActive);
                stats.CleaningTaxTemplates = await _context.MessageTemplates
                    .CountAsync(t => t.Type == NotificationType.CleaningTax && t.IsActive);
                stats.RentOverdueTemplates = await _context.MessageTemplates
                    .CountAsync(t => t.Type == NotificationType.RentOverdue && t.IsActive);
                stats.CleaningTaxOverdueTemplates = await _context.MessageTemplates
                    .CountAsync(t => t.Type == NotificationType.CleaningTaxOverdue && t.IsActive);

                return stats;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الحصول على إحصائيات القوالب: {ex.Message}");
                return new TemplateStatistics();
            }
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }

    /// <summary>
    /// إحصائيات القوالب
    /// </summary>
    public class TemplateStatistics
    {
        public int TotalTemplates { get; set; }
        public int ActiveTemplates { get; set; }
        public int InactiveTemplates { get; set; }
        public int MonthlyRentTemplates { get; set; }
        public int CleaningTaxTemplates { get; set; }
        public int RentOverdueTemplates { get; set; }
        public int CleaningTaxOverdueTemplates { get; set; }
    }
}
