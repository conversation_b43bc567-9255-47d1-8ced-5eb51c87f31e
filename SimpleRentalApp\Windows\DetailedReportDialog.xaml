<Window x:Class="SimpleRentalApp.Windows.DetailedReportDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="التقرير المفصل" 
        Height="800" 
        Width="1000"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        Background="#F8F9FA"
        ResizeMode="CanResize">

    <Window.Resources>
        <!-- Modern Card Style -->
        <Style x:Key="ModernCard" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="4" BlurRadius="12" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern Button Style -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,10"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="8"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Success Button Style -->
        <Style x:Key="SuccessButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#4CAF50"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#388E3C"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Warning Button Style -->
        <Style x:Key="WarningButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#FF9800"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#F57C00"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Info Label Style -->
        <Style x:Key="InfoLabel" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="#424242"/>
            <Setter Property="Margin" Value="0,5,0,2"/>
        </Style>

        <Style x:Key="InfoValue" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#616161"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>

        <Style x:Key="SectionTitle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#1976D2"/>
            <Setter Property="Margin" Value="0,20,0,15"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0"
                CornerRadius="12,12,0,0"
                Padding="25,20"
                Margin="0,0,0,20">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#2196F3" Offset="0"/>
                    <GradientStop Color="#1976D2" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock Name="ReportTitleText" 
                              Text="التقرير المفصل لضريبة النظافة" 
                              FontSize="24" 
                              FontWeight="Bold" 
                              Foreground="White"/>
                    <TextBlock Name="ReportSubtitleText" 
                              Text="تقرير شامل عن حالة دفعات ضريبة النظافة" 
                              FontSize="14" 
                              Foreground="#E3F2FD"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Name="PrintReportButton" 
                           Style="{StaticResource SuccessButton}"
                           Content="🖨️ طباعة التقرير"
                           Click="PrintReportButton_Click"/>
                    <Button Name="ExportReportButton" 
                           Style="{StaticResource WarningButton}"
                           Content="📤 تصدير PDF"
                           Click="ExportReportButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Report Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Name="ReportContentPanel">
                
                <!-- Property Summary -->
                <Border Style="{StaticResource ModernCard}">
                    <StackPanel>
                        <TextBlock Text="📋 ملخص المحل" Style="{StaticResource SectionTitle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="اسم المحل:" Style="{StaticResource InfoLabel}"/>
                                <TextBlock Name="PropertyNameText" Style="{StaticResource InfoValue}"/>
                                
                                <TextBlock Text="العنوان:" Style="{StaticResource InfoLabel}"/>
                                <TextBlock Name="PropertyAddressText" Style="{StaticResource InfoValue}"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="الإيجار الشهري:" Style="{StaticResource InfoLabel}"/>
                                <TextBlock Name="MonthlyRentText" Style="{StaticResource InfoValue}"/>
                                
                                <TextBlock Text="اسم المكتري:" Style="{StaticResource InfoLabel}"/>
                                <TextBlock Name="CustomerNameText" Style="{StaticResource InfoValue}"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2">
                                <TextBlock Text="تاريخ التقرير:" Style="{StaticResource InfoLabel}"/>
                                <TextBlock Name="ReportDateText" Style="{StaticResource InfoValue}"/>
                                
                                <TextBlock Text="السنة المختارة:" Style="{StaticResource InfoLabel}"/>
                                <TextBlock Name="SelectedYearText" Style="{StaticResource InfoValue}"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Financial Summary -->
                <Border Style="{StaticResource ModernCard}">
                    <StackPanel>
                        <TextBlock Text="💰 الملخص المالي" Style="{StaticResource SectionTitle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <Border Grid.Column="0" Background="#E3F2FD" CornerRadius="8" Padding="15" Margin="5">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="💵" FontSize="24" HorizontalAlignment="Center"/>
                                    <TextBlock Text="الضريبة السنوية" FontWeight="Bold" HorizontalAlignment="Center"/>
                                    <TextBlock Name="AnnualTaxText" FontSize="18" FontWeight="Bold" 
                                              Foreground="#1976D2" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>
                            
                            <Border Grid.Column="1" Background="#E8F5E8" CornerRadius="8" Padding="15" Margin="5">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="✅" FontSize="24" HorizontalAlignment="Center"/>
                                    <TextBlock Text="المبلغ المدفوع" FontWeight="Bold" HorizontalAlignment="Center"/>
                                    <TextBlock Name="PaidAmountText" FontSize="18" FontWeight="Bold" 
                                              Foreground="#388E3C" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>
                            
                            <Border Grid.Column="2" Background="#FFEBEE" CornerRadius="8" Padding="15" Margin="5">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="⏳" FontSize="24" HorizontalAlignment="Center"/>
                                    <TextBlock Text="المبلغ المتبقي" FontWeight="Bold" HorizontalAlignment="Center"/>
                                    <TextBlock Name="RemainingAmountText" FontSize="18" FontWeight="Bold" 
                                              Foreground="#D32F2F" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>
                            
                            <Border Grid.Column="3" Background="#FFF3E0" CornerRadius="8" Padding="15" Margin="5">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="📊" FontSize="24" HorizontalAlignment="Center"/>
                                    <TextBlock Text="نسبة الإنجاز" FontWeight="Bold" HorizontalAlignment="Center"/>
                                    <TextBlock Name="CompletionPercentageText" FontSize="18" FontWeight="Bold" 
                                              Foreground="#F57C00" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Payment Statistics -->
                <Border Style="{StaticResource ModernCard}">
                    <StackPanel>
                        <TextBlock Text="📈 إحصائيات الدفعات" Style="{StaticResource SectionTitle}"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="عدد الدفعات:" Style="{StaticResource InfoLabel}"/>
                                <TextBlock Name="PaymentCountText" Style="{StaticResource InfoValue}"/>
                                
                                <TextBlock Text="متوسط الدفعة:" Style="{StaticResource InfoLabel}"/>
                                <TextBlock Name="AveragePaymentText" Style="{StaticResource InfoValue}"/>
                                
                                <TextBlock Text="أكبر دفعة:" Style="{StaticResource InfoLabel}"/>
                                <TextBlock Name="LargestPaymentText" Style="{StaticResource InfoValue}"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="أصغر دفعة:" Style="{StaticResource InfoLabel}"/>
                                <TextBlock Name="SmallestPaymentText" Style="{StaticResource InfoValue}"/>
                                
                                <TextBlock Text="أول دفعة:" Style="{StaticResource InfoLabel}"/>
                                <TextBlock Name="FirstPaymentText" Style="{StaticResource InfoValue}"/>
                                
                                <TextBlock Text="آخر دفعة:" Style="{StaticResource InfoLabel}"/>
                                <TextBlock Name="LastPaymentText" Style="{StaticResource InfoValue}"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Payment Timeline -->
                <Border Style="{StaticResource ModernCard}">
                    <StackPanel>
                        <TextBlock Text="📅 الجدول الزمني للدفعات" Style="{StaticResource SectionTitle}"/>
                        <DataGrid Name="PaymentTimelineDataGrid" 
                                 AutoGenerateColumns="False"
                                 IsReadOnly="True"
                                 GridLinesVisibility="Horizontal"
                                 HeadersVisibility="Column"
                                 Background="White"
                                 RowBackground="White"
                                 AlternatingRowBackground="#FAFAFA">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="التاريخ" Binding="{Binding PaymentDateDisplay}" Width="120"/>
                                <DataGridTextColumn Header="المبلغ" Binding="{Binding CleaningTaxAmountDisplay}" Width="120"/>
                                <DataGridTextColumn Header="رقم التوصيل" Binding="{Binding ReceiptNumber}" Width="130"/>
                                <DataGridTextColumn Header="المتبقي قبل" Binding="{Binding RemainingBeforeDisplay}" Width="120"/>
                                <DataGridTextColumn Header="المتبقي بعد" Binding="{Binding RemainingAfterDisplay}" Width="120"/>
                                <DataGridTextColumn Header="طريقة الدفع" Binding="{Binding PaymentMethod}" Width="120"/>
                                <DataGridTextColumn Header="الملاحظات" Binding="{Binding Notes}" Width="*"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </Border>

                <!-- Recommendations -->
                <Border Style="{StaticResource ModernCard}">
                    <StackPanel>
                        <TextBlock Text="💡 التوصيات والملاحظات" Style="{StaticResource SectionTitle}"/>
                        <TextBlock Name="RecommendationsText" 
                                  TextWrapping="Wrap" 
                                  FontSize="14" 
                                  LineHeight="22"/>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="2" 
                   Orientation="Horizontal" 
                   HorizontalAlignment="Center" 
                   Margin="0,20,0,0">
            <Button Name="CloseButton" 
                   Style="{StaticResource ModernButton}"
                   Content="❌ إغلاق"
                   Background="#757575"
                   Click="CloseButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
