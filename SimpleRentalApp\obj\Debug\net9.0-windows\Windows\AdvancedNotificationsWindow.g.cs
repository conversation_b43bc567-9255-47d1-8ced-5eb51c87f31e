﻿#pragma checksum "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "BFF3EE103653191E0A044BAD2D123033B9AEE5DF"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleRentalApp.Windows {
    
    
    /// <summary>
    /// AdvancedNotificationsWindow
    /// </summary>
    public partial class AdvancedNotificationsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 120 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SubtitleText;
        
        #line default
        #line hidden
        
        
        #line 229 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TypeFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 233 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 237 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PriorityFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 241 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 271 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 276 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid NotificationsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 391 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 397 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastUpdateTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleRentalApp;V1.0.0.0;component/windows/advancednotificationswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.SubtitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            
            #line 154 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.GenerateMonthlyRentBtn_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 158 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.GenerateCleaningTaxBtn_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 162 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.GenerateOverdueBtn_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 166 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.UpdateAmountsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 170 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AutoUpdateBtn_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 174 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AutoGenerateBtn_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 178 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.WhatsAppSettingsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 182 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.MessageTemplatesBtn_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 186 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BulkSendBtn_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 190 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CreateDefaultTemplatesBtn_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 194 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ThemeToggleBtn_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 198 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.StatisticsBtn_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.TypeFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 230 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
            this.TypeFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.TypeFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 15:
            this.StatusFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 234 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
            this.StatusFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.StatusFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 16:
            this.PriorityFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 238 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
            this.PriorityFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PriorityFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 17:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 242 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            
            #line 245 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
            this.SearchTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.SearchTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 246 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
            this.SearchTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.SearchTextBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 249 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectAllBtn_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            
            #line 252 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearFiltersBtn_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.CountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.NotificationsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 29:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 30:
            this.LastUpdateTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 22:
            
            #line 349 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SendNotificationBtn_Click);
            
            #line default
            #line hidden
            break;
            case 23:
            
            #line 353 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditTemplateBtn_Click);
            
            #line default
            #line hidden
            break;
            case 24:
            
            #line 357 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PreviewMessageBtn_Click);
            
            #line default
            #line hidden
            break;
            case 25:
            
            #line 361 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RetryNotificationBtn_Click);
            
            #line default
            #line hidden
            break;
            case 26:
            
            #line 365 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditNotificationBtn_Click);
            
            #line default
            #line hidden
            break;
            case 27:
            
            #line 369 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CancelNotificationBtn_Click);
            
            #line default
            #line hidden
            break;
            case 28:
            
            #line 373 "..\..\..\..\Windows\AdvancedNotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteNotificationBtn_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

