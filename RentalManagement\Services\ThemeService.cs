using MaterialDesignThemes.Wpf;
using System.Windows;
using System.Windows.Media;

namespace RentalManagement.Services
{
    public enum AppTheme
    {
        Light,
        Dark
    }

    public class ThemeService
    {
        private static ThemeService? _instance;
        private static readonly object _lock = new object();
        private AppTheme _currentTheme = AppTheme.Light;

        public static ThemeService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                            _instance = new ThemeService();
                    }
                }
                return _instance;
            }
        }

        private ThemeService()
        {
            LoadThemeFromSettings();
        }

        public AppTheme CurrentTheme
        {
            get => _currentTheme;
            private set
            {
                if (_currentTheme != value)
                {
                    _currentTheme = value;
                    ThemeChanged?.Invoke(this, value);
                }
            }
        }

        public event EventHandler<AppTheme>? ThemeChanged;

        public void SetTheme(AppTheme theme)
        {
            CurrentTheme = theme;
            ApplyTheme(theme);
            SaveThemeToSettings(theme);
        }

        public void ToggleTheme()
        {
            var newTheme = CurrentTheme == AppTheme.Light ? AppTheme.Dark : AppTheme.Light;
            SetTheme(newTheme);
        }

        private void ApplyTheme(AppTheme theme)
        {
            try
            {
                var paletteHelper = new PaletteHelper();
                var currentTheme = paletteHelper.GetTheme();

                // Set base theme
                currentTheme.SetBaseTheme(theme == AppTheme.Dark ? BaseTheme.Dark : BaseTheme.Light);
                
                paletteHelper.SetTheme(currentTheme);

                // Update custom resources
                UpdateCustomResources(theme);
            }
            catch (Exception ex)
            {
                // Log error but don't crash the application
                System.Diagnostics.Debug.WriteLine($"Error applying theme: {ex.Message}");
            }
        }

        private void UpdateCustomResources(AppTheme theme)
        {
            var app = Application.Current;
            if (app?.Resources == null) return;

            // Find the themes resource dictionary
            var themesDict = app.Resources.MergedDictionaries
                .FirstOrDefault(d => d.Source?.OriginalString?.Contains("Themes.xaml") == true);

            if (themesDict == null) return;

            // Get the appropriate theme dictionary
            var themeKey = theme == AppTheme.Dark ? "DarkTheme" : "LightTheme";
            if (themesDict[themeKey] is ResourceDictionary selectedTheme)
            {
                // Update the main resource keys
                foreach (var key in selectedTheme.Keys)
                {
                    if (key is string stringKey && selectedTheme[key] is SolidColorBrush brush)
                    {
                        app.Resources[stringKey] = brush;
                    }
                }
            }
        }

        private void LoadThemeFromSettings()
        {
            try
            {
                var settings = Properties.Settings.Default;
                if (Enum.TryParse<AppTheme>(settings.Theme, out var theme))
                {
                    SetTheme(theme);
                }
                else
                {
                    SetTheme(AppTheme.Light);
                }
            }
            catch
            {
                SetTheme(AppTheme.Light);
            }
        }

        private void SaveThemeToSettings(AppTheme theme)
        {
            try
            {
                var settings = Properties.Settings.Default;
                settings.Theme = theme.ToString();
                settings.Save();
            }
            catch
            {
                // Ignore save errors
            }
        }

        public void InitializeTheme()
        {
            ApplyTheme(CurrentTheme);
        }
    }
}
