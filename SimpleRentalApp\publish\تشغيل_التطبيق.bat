@echo off
chcp 65001 >nul
title تطبيق إدارة الإيجارات - Simple Rental App

echo.
echo ========================================
echo    🏠 تطبيق إدارة الإيجارات
echo    Simple Rental App
echo ========================================
echo.
echo جاري تشغيل التطبيق...
echo.

REM التحقق من وجود الملف التنفيذي
if not exist "SimpleRentalApp.exe" (
    echo ❌ خطأ: لم يتم العثور على SimpleRentalApp.exe
    echo تأكد من وجود الملف في نفس المجلد
    pause
    exit /b 1
)

REM تشغيل التطبيق
start "" "SimpleRentalApp.exe"

REM انتظار قصير للتأكد من بدء التطبيق
timeout /t 2 /nobreak >nul

echo ✅ تم تشغيل التطبيق بنجاح!
echo.
echo 💡 نصائح:
echo • للمزامنة السحابية: اذهب إلى إعدادات المزامنة
echo • لاستكشاف الأخطاء: استخدم أداة التشخيص
echo • للمساعدة: راجع ملف README.md
echo.
echo اضغط أي مفتاح للإغلاق...
pause >nul
