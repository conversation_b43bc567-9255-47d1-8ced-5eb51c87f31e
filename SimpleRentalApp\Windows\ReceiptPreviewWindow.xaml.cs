using SimpleRentalApp.Models;
using SimpleRentalApp.Services;
using System;
using System.Windows;
using System.Windows.Input;

namespace SimpleRentalApp.Windows
{
    public partial class ReceiptPreviewWindow : Window
    {
        private readonly RentReceipt _receipt;
        private readonly Property _property;
        private readonly Customer? _customer;

        public ReceiptPreviewWindow(RentReceipt receipt, Property property, Customer? customer)
        {
            InitializeComponent();
            _receipt = receipt;
            _property = property;
            _customer = customer;
            
            LoadReceiptData();
        }

        private void LoadReceiptData()
        {
            try
            {
                // Set header
                ReceiptNumberText.Text = $"رقم التوصيل: {_receipt.ReceiptNumber}";

                // Set preview data
                PreviewReceiptNumber.Text = _receipt.ReceiptNumber;
                PreviewDate.Text = _receipt.PaymentDate.ToString("dd/MM/yyyy");
                PreviewCustomerName.Text = _customer?.FullName ?? "غير محدد";
                PreviewPropertyAddress.Text = _property.Address ?? _property.Name;
                PreviewPaymentPeriod.Text = _receipt.PaymentPeriod;
                PreviewAmount.Text = $"{_receipt.Amount:N0} درهم";
                PreviewAmountInWords.Text = ConvertNumberToArabicWords(_receipt.Amount) + " درهم";

                // Show notes if available
                if (!string.IsNullOrWhiteSpace(_receipt.Notes))
                {
                    NotesSection.Visibility = Visibility.Visible;
                    PreviewNotes.Text = _receipt.Notes;
                }
                else
                {
                    NotesSection.Visibility = Visibility.Collapsed;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات التوصيل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void PrintBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Mouse.OverrideCursor = System.Windows.Input.Cursors.Wait;

                var printService = ModernPrintService.Instance;
                var success = printService.PrintReceiptWithPreview(_receipt, _property, _customer);

                if (success)
                {
                    // Mark as printed
                    _receipt.IsPrinted = true;
                    await DatabaseService.Instance.UpdateRentReceiptAsync(_receipt);
                    
                    MessageBox.Show("تم طباعة التوصيل بنجاح! ✅", "نجحت الطباعة",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التوصيل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                Mouse.OverrideCursor = null;
            }
        }

        private void CloseBtn_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private string ConvertNumberToArabicWords(decimal number)
        {
            var integerPart = (int)number;

            return integerPart switch
            {
                0 => "صفر",
                1 => "واحد",
                2 => "اثنان",
                3 => "ثلاثة",
                4 => "أربعة",
                5 => "خمسة",
                6 => "ستة",
                7 => "سبعة",
                8 => "ثمانية",
                9 => "تسعة",
                10 => "عشرة",
                11 => "أحد عشر",
                12 => "اثنا عشر",
                13 => "ثلاثة عشر",
                14 => "أربعة عشر",
                15 => "خمسة عشر",
                16 => "ستة عشر",
                17 => "سبعة عشر",
                18 => "ثمانية عشر",
                19 => "تسعة عشر",
                20 => "عشرون",
                30 => "ثلاثون",
                40 => "أربعون",
                50 => "خمسون",
                60 => "ستون",
                70 => "سبعون",
                80 => "ثمانون",
                90 => "تسعون",
                100 => "مائة",
                200 => "مائتان",
                300 => "ثلاثمائة",
                400 => "أربعمائة",
                500 => "خمسمائة",
                600 => "ستمائة",
                700 => "سبعمائة",
                800 => "ثمانمائة",
                900 => "تسعمائة",
                1000 => "ألف",
                2000 => "ألفان",
                3000 => "ثلاثة آلاف",
                4000 => "أربعة آلاف",
                5000 => "خمسة آلاف",
                6000 => "ستة آلاف",
                7000 => "سبعة آلاف",
                8000 => "ثمانية آلاف",
                9000 => "تسعة آلاف",
                10000 => "عشرة آلاف",
                _ when integerPart < 100 => ConvertTens(integerPart),
                _ when integerPart < 1000 => ConvertHundreds(integerPart),
                _ when integerPart < 10000 => ConvertThousands(integerPart),
                _ => integerPart.ToString("N0")
            };
        }

        private string ConvertTens(int number)
        {
            if (number < 20) return ConvertNumberToArabicWords(number);

            var tens = (number / 10) * 10;
            var ones = number % 10;

            if (ones == 0) return ConvertNumberToArabicWords(tens);
            return $"{ConvertNumberToArabicWords(ones)} و{ConvertNumberToArabicWords(tens)}";
        }

        private string ConvertHundreds(int number)
        {
            var hundreds = (number / 100) * 100;
            var remainder = number % 100;

            if (remainder == 0) return ConvertNumberToArabicWords(hundreds);
            return $"{ConvertNumberToArabicWords(hundreds)} و{ConvertNumberToArabicWords(remainder)}";
        }

        private string ConvertThousands(int number)
        {
            var thousands = (number / 1000) * 1000;
            var remainder = number % 1000;

            if (remainder == 0) return ConvertNumberToArabicWords(thousands);
            return $"{ConvertNumberToArabicWords(thousands)} و{ConvertNumberToArabicWords(remainder)}";
        }
    }
}
