🏠 تطبيق إدارة الإيجارات - Simple Rental App
===============================================

📊 معلومات النسخة:
- الإصدار: 1.0.0
- تاريخ البناء: 2025-01-11
- نوع البناء: Release (Self-Contained)
- المنصة: Windows x64
- .NET Version: 9.0

🔧 المتطلبات:
- نظام التشغيل: Windows 10/11 (x64)
- الذاكرة: 4 GB RAM (الحد الأدنى)
- مساحة القرص: 500 MB
- اتصال إنترنت: للمزامنة السحابية فقط

📦 المكونات المضمنة:
✅ .NET 9.0 Runtime
✅ Entity Framework Core
✅ SQLite Database Engine
✅ Supabase Client Libraries
✅ PDF Generation (QuestPDF)
✅ Arabic Font Support (Lato)
✅ WPF UI Framework

🗂️ ملفات قاعدة البيانات:
- قاعدة البيانات المحلية: %AppData%\SimpleRentalApp\rental.db
- إعدادات المزامنة: %AppData%\SimpleRentalApp\sync_config.json
- ملفات السجل: %AppData%\SimpleRentalApp\logs\
- النسخ الاحتياطية: %AppData%\SimpleRentalApp\backups\

🌐 إعدادات Supabase:
- Project URL: https://npqfduvprqrkfmogxfzu.supabase.co
- API Key: يجب إدخاله في إعدادات المزامنة
- الجداول المطلوبة: sync_data, backups
- سياسات RLS: مفعلة مع سماح كامل

🔒 الأمان:
- تشفير البيانات الحساسة
- حماية API Keys
- سياسات Row Level Security
- نسخ احتياطية آمنة

📈 الأداء:
- مزامنة تلقائية كل 30 دقيقة
- ضغط البيانات المنقولة
- تحسين استعلامات قاعدة البيانات
- ذاكرة تخزين مؤقت ذكية

🖨️ الطباعة:
- دعم طباعة A6
- تنسيق RTL للعربية
- حفظ PDF
- طباعة مباشرة

🔧 أدوات التشخيص:
- اختبار الاتصال
- فحص الجداول
- تحليل الأخطاء
- مراقب الأداء

📞 الدعم:
- أداة التشخيص المدمجة
- ملفات السجل التفصيلية
- رسائل خطأ واضحة
- دليل استكشاف الأخطاء

🎯 الاستخدام الأمثل:
1. قم بإعداد Supabase أولاً
2. اختبر المزامنة باستخدام أداة التشخيص
3. قم بعمل نسخة احتياطية دورية
4. راقب الأداء بانتظام

⚠️ تحذيرات مهمة:
- لا تشارك API Key مع أحد
- قم بعمل نسخة احتياطية قبل التحديثات
- تأكد من صحة بيانات Supabase
- استخدم anon key وليس service_role key

📝 ملاحظات إضافية:
- التطبيق يعمل بدون إنترنت (وضع محلي)
- المزامنة السحابية اختيارية
- يمكن تصدير البيانات كـ JSON/CSV
- دعم كامل للغة العربية

===============================================
تم تطوير التطبيق بواسطة Augment Agent 🤖
تاريخ الإنشاء: 2025-01-11
