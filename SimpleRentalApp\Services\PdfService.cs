using SimpleRentalApp.Models;
using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Win32;
using System.Windows;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;

namespace SimpleRentalApp.Services
{
    public class PdfService
    {
        private static PdfService? _instance;
        public static PdfService Instance => _instance ??= new PdfService();

        private PdfService() { }

        public async Task<bool> ExportReceiptToPdfAsync(RentReceipt receipt)
        {
            try
            {
                // Configure QuestPDF license (Community license is free)
                QuestPDF.Settings.License = LicenseType.Community;

                // Show save dialog
                var saveDialog = new SaveFileDialog
                {
                    Title = "حفظ توصيل الكراء كـ PDF",
                    Filter = "PDF Files (*.pdf)|*.pdf",
                    FileName = $"توصيل_كراء_{receipt.ReceiptNumber}_{DateTime.Now:yyyyMMdd}.pdf",
                    DefaultExt = "pdf"
                };

                if (saveDialog.ShowDialog() != true)
                    return false;

                await CreatePdfReceiptAsync(receipt, saveDialog.FileName);

                MessageBox.Show($"تم حفظ التوصيل بنجاح في:\n{saveDialog.FileName}", "نجح الحفظ",
                    MessageBoxButton.OK, MessageBoxImage.Information);

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ PDF:\n{ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        private async Task CreatePdfReceiptAsync(RentReceipt receipt, string filePath)
        {
            await Task.Run(() =>
            {
                // Create PDF document using QuestPDF
                Document.Create(container =>
                {
                    container.Page(page =>
                    {
                        // A6 Portrait page size (105mm x 148mm) - optimized for vertical layout
                        page.Size(105, 148, Unit.Millimetre);
                        page.Margin(5, Unit.Millimetre); // Small margins for A6 portrait

                        // Set default RTL direction for Arabic text
                        page.DefaultTextStyle(x => x.FontFamily("Tahoma").DirectionFromRightToLeft());

                        // Professional content layout
                        page.Content().Element(container => ComposeModernReceipt(container, receipt));
                    });
                }).GeneratePdf(filePath);
            });
        }

        private void ComposeModernReceipt(IContainer container, RentReceipt receipt)
        {
            container.Column(column =>
            {
                // 1. Professional Header with RTL support
                column.Item().Background("#1976D2").Padding(8).Column(headerCol =>
                {
                    headerCol.Item().Text("توصيل كراء المحل")
                        .FontFamily("Tahoma")
                        .FontSize(16)
                        .FontColor(Colors.White)
                        .Bold()
                        .AlignCenter()
                        .DirectionFromRightToLeft();

                    headerCol.Item().PaddingTop(3).Text($"رقم التوصيل: {receipt.ReceiptNumber}")
                        .FontFamily("Tahoma")
                        .FontSize(10)
                        .FontColor("#E3F2FD")
                        .Bold()
                        .AlignCenter()
                        .DirectionFromRightToLeft();
                });

                // 2. Date and Receipt Info
                column.Item().Background("#E3F2FD").Padding(6).Row(row =>
                {
                    row.RelativeItem().Text($"تاريخ الأداء: {receipt.PaymentDate:dd/MM/yyyy}")
                        .FontFamily("Tahoma")
                        .FontSize(10)
                        .FontColor("#1976D2")
                        .Bold()
                        .AlignCenter()
                        .DirectionFromRightToLeft();
                });

                column.Item().PaddingVertical(4);

                // 3. Tenant and Property Information Section
                column.Item().Border(1).BorderColor("#1976D2").Padding(6).Column(infoCol =>
                {
                    infoCol.Item().Text("المكتري والمحل")
                        .FontFamily("Tahoma")
                        .FontSize(13)
                        .FontColor("#1976D2")
                        .Bold()
                        .AlignCenter()
                        .DirectionFromRightToLeft();

                    // Tenant Information
                    infoCol.Item().PaddingTop(4).Text($"الاسم: {receipt.Payment?.Customer?.FullName ?? "غير محدد"}")
                        .FontFamily("Tahoma")
                        .FontSize(12)
                        .FontColor("#424242")
                        .Bold()
                        .AlignLeft()
                        .DirectionFromRightToLeft();

                    // Property Address
                    if (!string.IsNullOrEmpty(receipt.Payment?.Property?.Address))
                    {
                        infoCol.Item().PaddingTop(2).Text($"العنوان: {receipt.Payment.Property.Address}")
                            .FontFamily("Tahoma")
                            .FontSize(12)
                            .FontColor("#424242")
                            .Bold()
                            .AlignLeft()
                            .DirectionFromRightToLeft();
                    }

                    // Monthly Rent
                    if (receipt.Payment?.Property != null)
                    {
                        infoCol.Item().PaddingTop(2).Text($"الإيجار الشهري: {receipt.Payment.Property.MonthlyRent:F2} درهم")
                            .FontFamily("Tahoma")
                            .FontSize(12)
                            .FontColor("#424242")
                            .Bold()
                            .AlignLeft()
                            .DirectionFromRightToLeft();
                    }
                });

                column.Item().PaddingVertical(3);

                // 4. Payment Details Section
                column.Item().Border(1).BorderColor("#1976D2").Padding(6).Column(paymentCol =>
                {
                    paymentCol.Item().Text("تفاصيل الأداء")
                        .FontFamily("Tahoma")
                        .FontSize(13)
                        .FontColor("#1976D2")
                        .Bold()
                        .AlignCenter()
                        .DirectionFromRightToLeft();

                    // Payment Date
                    paymentCol.Item().PaddingTop(4).Text($"تاريخ الأداء: {receipt.PaymentDate:dd/MM/yyyy}")
                        .FontFamily("Tahoma")
                        .FontSize(12)
                        .FontColor("#424242")
                        .Bold()
                        .AlignLeft()
                        .DirectionFromRightToLeft();

                    // Rental Period
                    paymentCol.Item().PaddingTop(2).Text($"فترة الكراء: من {receipt.PeriodStartDate?.ToString("dd/MM/yyyy") ?? "غير محدد"} إلى {receipt.PeriodEndDate?.ToString("dd/MM/yyyy") ?? "غير محدد"}")
                        .FontFamily("Tahoma")
                        .FontSize(12)
                        .FontColor("#424242")
                        .Bold()
                        .AlignLeft()
                        .DirectionFromRightToLeft();

                    // Notes if any
                    if (!string.IsNullOrEmpty(receipt.Notes))
                    {
                        paymentCol.Item().PaddingTop(2).Text($"ملاحظات: {receipt.Notes}")
                            .FontFamily("Tahoma")
                            .FontSize(12)
                            .FontColor("#424242")
                            .AlignLeft()
                            .DirectionFromRightToLeft();
                    }
                });

                column.Item().PaddingVertical(4);

                // 5. Amount Highlight Box - Professional Design with RTL
                column.Item().Background("#FFF3E0").Border(2).BorderColor("#FF9800").Padding(8).Column(amountCol =>
                {
                    amountCol.Item().Text("المبلغ المدفوع")
                        .FontFamily("Tahoma")
                        .FontSize(12)
                        .FontColor("#E65100")
                        .Bold()
                        .AlignCenter()
                        .DirectionFromRightToLeft();

                    amountCol.Item().PaddingTop(4).Text($"{receipt.Amount:F2} درهم")
                        .FontFamily("Tahoma")
                        .FontSize(16)
                        .FontColor("#E65100")
                        .Bold()
                        .AlignCenter()
                        .DirectionFromRightToLeft();

                    amountCol.Item().PaddingTop(2).Text($"({receipt.AmountInWords})")
                        .FontFamily("Tahoma")
                        .FontSize(9)
                        .FontColor("#BF360C")
                        .Bold()
                        .AlignCenter()
                        .DirectionFromRightToLeft();
                });

                // 5. Notes Section (if any)
                if (!string.IsNullOrWhiteSpace(receipt.Notes))
                {
                    column.Item().Background("#F5F5F5").Border(1).BorderColor("#BDBDBD").Padding(6).Column(notesCol =>
                    {
                        notesCol.Item().Text("ملاحظات")
                            .FontFamily("Tahoma")
                            .FontSize(10)
                            .FontColor("#424242")
                            .Bold()
                            .AlignRight()
                            .DirectionFromRightToLeft();

                        notesCol.Item().PaddingTop(2).Text(receipt.Notes)
                            .FontFamily("Tahoma")
                            .FontSize(9)
                            .FontColor("#666666")
                            .AlignRight()
                            .DirectionFromRightToLeft();
                    });

                    column.Item().PaddingVertical(2);
                }

                // 6. Legal Notices (if enabled) - Compact and Professional with RTL
                if (receipt.ShowLegalNotices)
                {
                    column.Item().Background("#ECEFF1").Border(1).BorderColor("#90A4AE").Padding(10).Column(legalCol =>
                    {
                        legalCol.Item().Text("التحفظات القانونية")
                            .FontFamily("Tahoma")
                            .FontSize(10)
                            .FontColor("#37474F")
                            .Bold()
                            .AlignCenter()
                            .DirectionFromRightToLeft();

                        legalCol.Item().PaddingTop(6).Text(GetLegalNoticesForPdf())
                            .FontFamily("Tahoma")
                            .FontSize(9)
                            .FontColor("#424242")
                            .AlignLeft()
                            .DirectionFromRightToLeft();
                    });
                }

                // 7. System name footer only
                column.Item().PaddingTop(8).Text("نظام تدبير الكراء")
                    .FontFamily("Tahoma")
                    .FontSize(8)
                    .FontColor("#666666")
                    .AlignCenter()
                    .DirectionFromRightToLeft();

                // 6. Professional Footer with RTL
                column.Item().PaddingTop(3);
                column.Item().Height(1).Background("#BDC3C7"); // Separator line

                column.Item().PaddingTop(2).Background("#ECF0F1").Padding(3).Row(footerRow =>
                {
                    footerRow.RelativeItem().Text($"تاريخ الإصدار: {receipt.CreatedDateDisplay}")
                        .FontFamily("Tahoma")
                        .FontSize(6)
                        .FontColor("#7F8C8D")
                        .AlignLeft()
                        .DirectionFromRightToLeft();

                    footerRow.RelativeItem().Text("نظام تدبير الكراء")
                        .FontFamily("Tahoma")
                        .FontSize(6)
                        .FontColor("#7F8C8D")
                        .AlignRight()
                        .DirectionFromRightToLeft();
                });
            });
        }

        private string GetTenantInfoForPdf(RentReceipt receipt)
        {
            var info = "";

            if (receipt.Payment?.Customer != null)
            {
                info += $"• الاسم الكامل: {receipt.Payment.Customer.FullName}\n\n";
            }

            if (receipt.Payment?.Property != null)
            {
                info += $"• اسم المحل: {receipt.Payment.Property.Name}\n\n";
                info += $"• عنوان المحل: {receipt.Payment.Property.Address}";
            }

            return info;
        }

        private string GetPaymentDetailsForPdf(RentReceipt receipt)
        {
            var details = $"• تاريخ الأداء: {receipt.PaymentDateDisplay}\n\n";
            details += $"• فترة الكراء: {receipt.PaymentPeriodDisplay}";

            if (!string.IsNullOrWhiteSpace(receipt.Notes))
            {
                details += $"\n\n• ملاحظات: {receipt.Notes}";
            }

            return details;
        }

        private string GetLegalNoticesForPdf()
        {
            return "• أداء جميع الأداءات الشخصية والخاصة بالمنقول للعام الجاري\n" +
                   "• دفع الكراء في الوقت المحدد له\n" +
                   "• القيام بجميع الإصلاحات التي تلزم المكتري طبق العادة\n" +
                   "• المحافظة على حالة المحل وعدم إحداث أضرار به\n" +
                   "• احترام الآجال المقررة لطلب الإفراغ كتابة\n" +
                   "هذا التوصيل صحيح ومعتبر قانونياً ويُعتبر إثباتاً لأداء الكراء المذكور.";
        }











    }
}
