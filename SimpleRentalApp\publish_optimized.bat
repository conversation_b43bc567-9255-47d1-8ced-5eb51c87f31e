@echo off
chcp 65001 > nul
echo ========================================
echo    Optimized Rental Management Publishing
echo ========================================
echo.

echo [1/6] Cleaning project...
dotnet clean --configuration Release
if errorlevel 1 (
    echo Error cleaning project
    pause
    exit /b 1
)

echo [2/6] Building project...
dotnet build --configuration Release
if errorlevel 1 (
    echo Error building project
    pause
    exit /b 1
)

echo [3/6] Publishing optimized application...
dotnet publish --configuration Release --runtime win-x64 --self-contained true -p:PublishSingleFile=true -p:PublishTrimmed=true -p:TrimMode=partial --output publish_optimized
if errorlevel 1 (
    echo Error publishing application
    pause
    exit /b 1
)

echo [4/6] Copying essential files...
copy README.md publish_optimized\ > nul 2>&1

echo [5/6] Creating launcher and info files...
echo @echo off > publish_optimized\run_app.bat
echo chcp 65001 ^> nul >> publish_optimized\run_app.bat
echo echo Starting Rental Management App... >> publish_optimized\run_app.bat
echo start SimpleRentalApp.exe >> publish_optimized\run_app.bat

echo Rental Management System - Property and Rent Management > publish_optimized\system_info.txt
echo ================================================ >> publish_optimized\system_info.txt
echo. >> publish_optimized\system_info.txt
echo Developer: Hafid Abdo >> publish_optimized\system_info.txt
echo Version: 1.0.0 >> publish_optimized\system_info.txt
echo Release Date: %date% >> publish_optimized\system_info.txt
echo. >> publish_optimized\system_info.txt
echo Default Login Credentials: >> publish_optimized\system_info.txt
echo Username: hafid >> publish_optimized\system_info.txt
echo Password: hafidos159357 >> publish_optimized\system_info.txt
echo. >> publish_optimized\system_info.txt
echo For technical support please contact the developer >> publish_optimized\system_info.txt

echo [6/6] Cleaning up unnecessary files...
del /q publish_optimized\*.pdb > nul 2>&1
del /q publish_optimized\*.xml > nul 2>&1
rmdir /s /q publish_optimized\cs > nul 2>&1
rmdir /s /q publish_optimized\de > nul 2>&1
rmdir /s /q publish_optimized\es > nul 2>&1
rmdir /s /q publish_optimized\fr > nul 2>&1
rmdir /s /q publish_optimized\it > nul 2>&1
rmdir /s /q publish_optimized\ja > nul 2>&1
rmdir /s /q publish_optimized\ko > nul 2>&1
rmdir /s /q publish_optimized\pl > nul 2>&1
rmdir /s /q publish_optimized\pt-BR > nul 2>&1
rmdir /s /q publish_optimized\ru > nul 2>&1
rmdir /s /q publish_optimized\tr > nul 2>&1
rmdir /s /q publish_optimized\zh-Hans > nul 2>&1
rmdir /s /q publish_optimized\zh-Hant > nul 2>&1

echo.
echo ========================================
echo Optimized application published successfully
echo Location: publish_optimized folder
echo Executable file size:
for %%A in (publish_optimized\SimpleRentalApp.exe) do echo %%~zA bytes
echo ========================================
echo.
pause
