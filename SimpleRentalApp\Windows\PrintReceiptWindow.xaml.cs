using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Xps;
using System.Windows.Xps.Packaging;
using Microsoft.Win32;
using SimpleRentalApp.Models;
using SimpleRentalApp.Services;

namespace SimpleRentalApp.Windows;

public partial class PrintReceiptWindow : Window
{
    private readonly RentReceipt _receipt;
    private readonly bool _pdfMode;

    public PrintReceiptWindow(RentReceipt receipt, bool pdfMode = false)
    {
        InitializeComponent();
        _receipt = receipt;
        _pdfMode = pdfMode;
        LoadReceiptData();

        if (_pdfMode)
        {
            // Hide print button and show only PDF save
            PrintButton.Visibility = Visibility.Collapsed;
            SaveAsPdfButton.Content = "💾 حفظ PDF";
        }
    }

    private void LoadReceiptData()
    {
        try
        {
            // Receipt header info
            ReceiptNumberText.Text = _receipt.ReceiptNumber;
            IssueDateText.Text = _receipt.CreatedDateDisplay;
            
            // Tenant and property info
            if (_receipt.Payment != null)
            {
                TenantNameText.Text = _receipt.Payment.Customer.FullName;
                PropertyAddressText.Text = _receipt.Payment.Property.Address;
                ContractNumberText.Text = _receipt.Payment.Id.ToString();
            }
            else if (_receipt.Contract != null)
            {
                TenantNameText.Text = _receipt.Contract.Customer.FullName;
                PropertyAddressText.Text = _receipt.Contract.Property.Address;
                ContractNumberText.Text = _receipt.Contract.Id.ToString();
            }
            
            // Payment details
            PaymentPeriodText.Text = _receipt.PaymentPeriodDisplay;
            PaymentDateText.Text = _receipt.PaymentDateDisplay;
            PaymentMethodText.Text = _receipt.PaymentMethod;
            AmountText.Text = _receipt.AmountDisplay;
            AmountInWordsText.Text = _receipt.AmountInWords;
            
            // Notes (show only if not empty)
            if (!string.IsNullOrWhiteSpace(_receipt.Notes))
            {
                NotesSection.Visibility = Visibility.Visible;
                NotesText.Text = _receipt.Notes;
            }
            else
            {
                NotesSection.Visibility = Visibility.Collapsed;
            }
            
            StatusText.Text = $"إيصال رقم {_receipt.ReceiptNumber} - جاهز للطباعة";
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل بيانات الإيصال: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void PreviewPrintButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            StatusText.Text = "جاري فتح المعاينة...";

            // Use the new modern print service with preview
            var success = Services.ModernPrintService.Instance.PrintReceiptWithPreview(_receipt);

            if (success)
            {
                // Mark as printed in database
                await DatabaseService.Instance.MarkReceiptAsPrintedAsync(_receipt.Id);

                StatusText.Text = "تم الطباعة بنجاح ✅";
            }
            else
            {
                StatusText.Text = "جاهز للطباعة";
            }
        }
        catch (Exception ex)
        {
            StatusText.Text = "فشل في المعاينة ❌";
            MessageBox.Show($"خطأ في المعاينة: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void PrintButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            StatusText.Text = "جاري الطباعة...";

            // Use the new modern print service
            var success = Services.ModernPrintService.Instance.PrintReceipt(_receipt);

            if (success)
            {
                // Mark as printed in database
                await DatabaseService.Instance.MarkReceiptAsPrintedAsync(_receipt.Id);

                StatusText.Text = "تم الطباعة بنجاح ✅";

                MessageBox.Show("تم طباعة التوصيل بنجاح! ✅", "نجحت الطباعة",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                StatusText.Text = "تم إلغاء الطباعة";
            }
        }
        catch (Exception ex)
        {
            StatusText.Text = "فشل في الطباعة ❌";
            MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void SaveAsPdfButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            StatusText.Text = "جاري إنشاء PDF...";

            // Use the new PDF service
            var success = await Services.PdfService.Instance.ExportReceiptToPdfAsync(_receipt);

            if (success)
            {
                StatusText.Text = "تم حفظ PDF بنجاح ✅";

                // Mark as printed in database
                await DatabaseService.Instance.MarkReceiptAsPrintedAsync(_receipt.Id);
            }
            else
            {
                StatusText.Text = "تم إلغاء العملية";
            }
        }
        catch (Exception ex)
        {
            StatusText.Text = "فشل في الحفظ ❌";
            MessageBox.Show($"خطأ في حفظ PDF: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void SaveAsXps(string fileName)
    {
        try
        {
            using (var xpsDocument = new XpsDocument(fileName, FileAccess.Write))
            {
                var writer = XpsDocument.CreateXpsDocumentWriter(xpsDocument);
                
                // Create a copy of the receipt content for saving
                var printContent = CreatePrintableContent();
                
                writer.Write(printContent);
            }
        }
        catch (Exception ex)
        {
            throw new Exception($"فشل في إنشاء ملف XPS: {ex.Message}");
        }
    }

    private Visual CreatePrintableContent(bool isA6Size = false)
    {
        // A6 dimensions: 105mm × 148mm = 297 × 420 pixels at 72 DPI
        double width = isA6Size ? 297 : 600;
        double height = isA6Size ? 420 : 800;

        // Clone the receipt content for printing
        var printBorder = new Border
        {
            Background = Brushes.White,
            Padding = new Thickness(isA6Size ? 15 : 40),
            Width = width,
            Child = CloneReceiptContent(isA6Size)
        };

        // Measure and arrange
        printBorder.Measure(new Size(width, height));
        printBorder.Arrange(new Rect(0, 0, width, height));

        return printBorder;
    }

    private FrameworkElement CloneReceiptContent(bool isA6Size = false)
    {
        // Create a simplified version of the receipt content for printing
        var content = new StackPanel();

        // Adjust font sizes for A6
        double headerFontSize = isA6Size ? 16 : 28;
        double titleFontSize = isA6Size ? 10 : 16;
        double normalFontSize = isA6Size ? 8 : 14;
        double largeFontSize = isA6Size ? 10 : 18;
        
        // Header
        var header = new Border
        {
            Background = new SolidColorBrush(Color.FromRgb(33, 150, 243)),
            Padding = new Thickness(isA6Size ? 10 : 20),
            Margin = new Thickness(0, 0, 0, isA6Size ? 15 : 30)
        };

        var headerStack = new StackPanel { HorizontalAlignment = HorizontalAlignment.Center };
        headerStack.Children.Add(new TextBlock
        {
            Text = "🏢 توصيل كراء",
            FontSize = headerFontSize,
            FontWeight = FontWeights.Bold,
            Foreground = Brushes.White,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, isA6Size ? 5 : 10)
        });

        // Receipt number in header
        headerStack.Children.Add(new TextBlock
        {
            Text = $"رقم: {_receipt.ReceiptNumber}",
            FontSize = titleFontSize,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush(Color.FromRgb(227, 242, 253)),
            HorizontalAlignment = HorizontalAlignment.Center
        });
        
        header.Child = headerStack;
        content.Children.Add(header);
        
        // Receipt number and date
        var detailsGrid = new Grid { Margin = new Thickness(0, 0, 0, isA6Size ? 15 : 30) };
        detailsGrid.ColumnDefinitions.Add(new ColumnDefinition());
        detailsGrid.ColumnDefinitions.Add(new ColumnDefinition());
        
        var leftStack = new StackPanel();
        leftStack.Children.Add(new TextBlock 
        { 
            Text = "رقم الإيصال:", 
            FontSize = 14, 
            FontWeight = FontWeights.Bold, 
            Foreground = new SolidColorBrush(Color.FromRgb(102, 102, 102)) 
        });
        leftStack.Children.Add(new TextBlock 
        { 
            Text = _receipt.ReceiptNumber, 
            FontSize = 18, 
            FontWeight = FontWeights.Bold, 
            Foreground = new SolidColorBrush(Color.FromRgb(33, 150, 243)) 
        });
        Grid.SetColumn(leftStack, 0);
        detailsGrid.Children.Add(leftStack);
        
        var rightStack = new StackPanel { HorizontalAlignment = HorizontalAlignment.Left };
        rightStack.Children.Add(new TextBlock 
        { 
            Text = "تاريخ الإصدار:", 
            FontSize = 14, 
            FontWeight = FontWeights.Bold, 
            Foreground = new SolidColorBrush(Color.FromRgb(102, 102, 102)) 
        });
        rightStack.Children.Add(new TextBlock 
        { 
            Text = _receipt.CreatedDateDisplay, 
            FontSize = 16, 
            FontWeight = FontWeights.Bold 
        });
        Grid.SetColumn(rightStack, 1);
        detailsGrid.Children.Add(rightStack);
        
        content.Children.Add(detailsGrid);
        
        // Tenant info section
        content.Children.Add(CreateTenantInfoSection(isA6Size));

        // Payment details section
        content.Children.Add(CreatePaymentDetailsSection(isA6Size));
        
        // Notes if available
        if (!string.IsNullOrWhiteSpace(_receipt.Notes))
        {
            content.Children.Add(CreateNotesSection(isA6Size));
        }

        // Legal notices if enabled
        if (_receipt.ShowLegalNotices)
        {
            content.Children.Add(CreateLegalNoticesSection(isA6Size));
        }

        // Footer
        content.Children.Add(CreateFooterSection(isA6Size));

        return content;
    }

    private FrameworkElement CreateTenantInfoSection(bool isA6Size = false)
    {
        var border = new Border
        {
            Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
            BorderBrush = new SolidColorBrush(Color.FromRgb(224, 224, 224)),
            BorderThickness = new Thickness(1),
            Padding = new Thickness(isA6Size ? 10 : 20),
            Margin = new Thickness(0, 0, 0, isA6Size ? 15 : 30)
        };
        
        var stack = new StackPanel();
        
        stack.Children.Add(new TextBlock
        {
            Text = "معلومات المستأجر والعقار",
            FontSize = 16,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush(Color.FromRgb(33, 150, 243)),
            Margin = new Thickness(0, 0, 0, 15)
        });
        
        // Get customer and property info
        string customerName = "";
        string propertyAddress = "";
        string referenceNumber = "";

        if (_receipt.Payment != null)
        {
            customerName = _receipt.Payment.Customer.FullName;
            propertyAddress = _receipt.Payment.Property.Address;
            referenceNumber = $"دفعة رقم: {_receipt.Payment.Id}";
        }
        else if (_receipt.Contract != null)
        {
            customerName = _receipt.Contract.Customer.FullName;
            propertyAddress = _receipt.Contract.Property.Address;
            referenceNumber = $"عقد رقم: {_receipt.Contract.Id}";
        }

        // Tenant name
        var tenantPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 0, 10) };
        tenantPanel.Children.Add(new TextBlock { Text = "اسم المستأجر: ", FontWeight = FontWeights.Bold, Width = 120 });
        tenantPanel.Children.Add(new TextBlock { Text = customerName });
        stack.Children.Add(tenantPanel);

        // Property address
        var propertyPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 0, 10) };
        propertyPanel.Children.Add(new TextBlock { Text = "عنوان العقار: ", FontWeight = FontWeights.Bold, Width = 120 });
        propertyPanel.Children.Add(new TextBlock { Text = propertyAddress, TextWrapping = TextWrapping.Wrap });
        stack.Children.Add(propertyPanel);

        // Reference number
        var referencePanel = new StackPanel { Orientation = Orientation.Horizontal };
        referencePanel.Children.Add(new TextBlock { Text = "المرجع: ", FontWeight = FontWeights.Bold, Width = 120 });
        referencePanel.Children.Add(new TextBlock { Text = referenceNumber });
        stack.Children.Add(referencePanel);
        
        border.Child = stack;
        return border;
    }

    private FrameworkElement CreatePaymentDetailsSection(bool isA6Size = false)
    {
        var border = new Border
        {
            Background = new SolidColorBrush(Color.FromRgb(232, 245, 232)),
            BorderBrush = new SolidColorBrush(Color.FromRgb(76, 175, 80)),
            BorderThickness = new Thickness(isA6Size ? 1 : 2),
            Padding = new Thickness(isA6Size ? 10 : 20),
            Margin = new Thickness(0, 0, 0, isA6Size ? 15 : 30)
        };

        var stack = new StackPanel();

        stack.Children.Add(new TextBlock
        {
            Text = "تفاصيل الدفع",
            FontSize = 16,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush(Color.FromRgb(46, 125, 50)),
            Margin = new Thickness(0, 0, 0, 15)
        });

        // Payment period
        var periodPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 0, 10) };
        periodPanel.Children.Add(new TextBlock { Text = "الفترة المؤداة: ", FontWeight = FontWeights.Bold, Width = 120 });
        periodPanel.Children.Add(new TextBlock { Text = _receipt.PaymentPeriodDisplay });
        stack.Children.Add(periodPanel);

        // Payment date
        var datePanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 0, 10) };
        datePanel.Children.Add(new TextBlock { Text = "تاريخ الأداء: ", FontWeight = FontWeights.Bold, Width = 120 });
        datePanel.Children.Add(new TextBlock { Text = _receipt.PaymentDateDisplay });
        stack.Children.Add(datePanel);

        // Payment method
        var methodPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 0, 10) };
        methodPanel.Children.Add(new TextBlock { Text = "طريقة الدفع: ", FontWeight = FontWeights.Bold, Width = 120 });
        methodPanel.Children.Add(new TextBlock { Text = _receipt.PaymentMethod });
        stack.Children.Add(methodPanel);

        // Amount
        var amountPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 0, 15) };
        amountPanel.Children.Add(new TextBlock { Text = "المبلغ المؤدى: ", FontWeight = FontWeights.Bold, Width = 120 });
        amountPanel.Children.Add(new TextBlock
        {
            Text = _receipt.AmountDisplay,
            FontSize = 18,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush(Color.FromRgb(46, 125, 50))
        });
        stack.Children.Add(amountPanel);

        // Amount in words
        var wordsSection = new Border
        {
            Background = Brushes.White,
            BorderBrush = new SolidColorBrush(Color.FromRgb(76, 175, 80)),
            BorderThickness = new Thickness(1),
            Padding = new Thickness(15)
        };

        var wordsStack = new StackPanel();
        wordsStack.Children.Add(new TextBlock
        {
            Text = "المبلغ بالحروف:",
            FontWeight = FontWeights.Bold,
            Margin = new Thickness(0, 0, 0, 5)
        });
        wordsStack.Children.Add(new TextBlock
        {
            Text = _receipt.AmountInWords,
            FontStyle = FontStyles.Italic,
            TextWrapping = TextWrapping.Wrap
        });

        wordsSection.Child = wordsStack;
        stack.Children.Add(wordsSection);

        border.Child = stack;
        return border;
    }

    private FrameworkElement CreateNotesSection(bool isA6Size = false)
    {
        var border = new Border
        {
            Background = new SolidColorBrush(Color.FromRgb(255, 243, 224)),
            BorderBrush = new SolidColorBrush(Color.FromRgb(255, 152, 0)),
            BorderThickness = new Thickness(1),
            Padding = new Thickness(isA6Size ? 10 : 20),
            Margin = new Thickness(0, 0, 0, isA6Size ? 15 : 30)
        };

        var stack = new StackPanel();
        stack.Children.Add(new TextBlock
        {
            Text = "ملاحظات:",
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush(Color.FromRgb(230, 81, 0)),
            Margin = new Thickness(0, 0, 0, 10)
        });
        stack.Children.Add(new TextBlock
        {
            Text = _receipt.Notes,
            TextWrapping = TextWrapping.Wrap
        });

        border.Child = stack;
        return border;
    }

    private FrameworkElement CreateFooterSection(bool isA6Size = false)
    {
        var border = new Border
        {
            Background = new SolidColorBrush(Color.FromRgb(245, 245, 245)),
            Padding = new Thickness(isA6Size ? 10 : 20),
            Margin = new Thickness(0, isA6Size ? 15 : 30, 0, 0)
        };

        var grid = new Grid();
        grid.ColumnDefinitions.Add(new ColumnDefinition());
        grid.ColumnDefinitions.Add(new ColumnDefinition());

        var leftStack = new StackPanel();
        leftStack.Children.Add(new TextBlock
        {
            Text = "توقيع المستأجر:",
            FontWeight = FontWeights.Bold,
            Margin = new Thickness(0, 0, 0, 30)
        });
        leftStack.Children.Add(new Border
        {
            BorderBrush = Brushes.Black,
            BorderThickness = new Thickness(0, 0, 0, 1),
            Width = 150,
            Height = 1,
            HorizontalAlignment = HorizontalAlignment.Left
        });
        Grid.SetColumn(leftStack, 0);
        grid.Children.Add(leftStack);

        var rightStack = new StackPanel { HorizontalAlignment = HorizontalAlignment.Left };
        rightStack.Children.Add(new TextBlock
        {
            Text = "ختم وتوقيع المؤجر:",
            FontWeight = FontWeights.Bold,
            Margin = new Thickness(0, 0, 0, 30)
        });
        rightStack.Children.Add(new Border
        {
            BorderBrush = Brushes.Black,
            BorderThickness = new Thickness(0, 0, 0, 1),
            Width = 150,
            Height = 1,
            HorizontalAlignment = HorizontalAlignment.Left
        });
        Grid.SetColumn(rightStack, 1);
        grid.Children.Add(rightStack);

        border.Child = grid;
        return border;
    }

    private FrameworkElement CreateLegalNoticesSection(bool isA6Size = false)
    {
        var border = new Border
        {
            Background = new SolidColorBrush(Color.FromRgb(255, 248, 225)),
            BorderBrush = new SolidColorBrush(Color.FromRgb(255, 193, 7)),
            BorderThickness = new Thickness(1),
            Padding = new Thickness(isA6Size ? 8 : 15),
            Margin = new Thickness(0, isA6Size ? 8 : 15, 0, isA6Size ? 8 : 15)
        };

        var stack = new StackPanel();

        var legalText = new TextBlock
        {
            Text = LegalNotices.ArabicLegalText,
            FontSize = isA6Size ? 6 : 10,
            TextWrapping = TextWrapping.Wrap,
            LineHeight = isA6Size ? 8 : 12,
            Foreground = new SolidColorBrush(Color.FromRgb(102, 102, 102))
        };

        stack.Children.Add(legalText);
        border.Child = stack;
        return border;
    }


    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }
}
