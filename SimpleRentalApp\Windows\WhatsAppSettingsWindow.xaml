<Window x:Class="SimpleRentalApp.Windows.WhatsAppSettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="⚙️ إعدادات الواتساب - تدبير الكراء"
        Height="500" Width="700"
        WindowStartupLocation="CenterOwner"
        Background="#F5F5F5"
        FontFamily="Segoe UI"
        ResizeMode="CanResize">

    <Window.Resources>
        <!-- Color Resources -->
        <SolidColorBrush x:Key="PrimaryColor" Color="#25D366"/>
        <SolidColorBrush x:Key="SecondaryColor" Color="#128C7E"/>
        <SolidColorBrush x:Key="AccentColor" Color="#34B7F1"/>
        <SolidColorBrush x:Key="SurfaceColor" Color="White"/>
        <SolidColorBrush x:Key="TextColor" Color="#2C3E50"/>
        <SolidColorBrush x:Key="BorderColor" Color="#E0E0E0"/>

        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryColor}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource SecondaryColor}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#6C757D"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource SurfaceColor}" CornerRadius="15" Padding="25" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="3" BlurRadius="15"/>
            </Border.Effect>
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="⚙️" FontSize="32" VerticalAlignment="Center" Margin="0,0,15,0"/>
                <StackPanel>
                    <TextBlock Text="إعدادات الواتساب" 
                               FontSize="24" 
                               FontWeight="Bold" 
                               Foreground="{StaticResource TextColor}"/>
                    <TextBlock Text="تخصيص إعدادات إرسال الرسائل عبر الواتساب" 
                               FontSize="14" 
                               Foreground="#7F8C8D" 
                               Margin="0,5,0,0"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Settings Content -->
        <Border Grid.Row="1" Background="{StaticResource SurfaceColor}" CornerRadius="12" Padding="25">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="10"/>
            </Border.Effect>
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!-- Sending Method -->
                    <GroupBox Header="📱 طريقة الإرسال" FontWeight="Bold" Padding="15" Margin="0,0,0,20">
                        <StackPanel>
                            <RadioButton Name="WebRadioButton" Content="استخدام WhatsApp Web (المتصفح)" 
                                         IsChecked="True" Margin="0,0,0,10"/>
                            <RadioButton Name="AppRadioButton" Content="استخدام تطبيق الواتساب المثبت" 
                                         Margin="0,0,0,10"/>
                            <TextBlock Text="💡 WhatsApp Web يعمل في جميع الأوقات، بينما التطبيق قد لا يكون متاحاً دائماً." 
                                       FontSize="12" 
                                       Foreground="#7F8C8D" 
                                       TextWrapping="Wrap"/>
                        </StackPanel>
                    </GroupBox>

                    <!-- Country Code -->
                    <GroupBox Header="🌍 رمز الدولة" FontWeight="Bold" Padding="15" Margin="0,0,0,20">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="رمز الدولة الافتراضي:" VerticalAlignment="Center" Margin="0,0,15,0"/>
                            <ComboBox Grid.Column="1" Name="CountryCodeComboBox" SelectedIndex="0">
                                <ComboBoxItem Content="المغرب (+212)" Tag="212"/>
                                <ComboBoxItem Content="السعودية (+966)" Tag="966"/>
                                <ComboBoxItem Content="الإمارات (+971)" Tag="971"/>
                                <ComboBoxItem Content="مصر (+20)" Tag="20"/>
                                <ComboBoxItem Content="الجزائر (+213)" Tag="213"/>
                                <ComboBoxItem Content="تونس (+216)" Tag="216"/>
                                <ComboBoxItem Content="ليبيا (+218)" Tag="218"/>
                                <ComboBoxItem Content="الأردن (+962)" Tag="962"/>
                                <ComboBoxItem Content="لبنان (+961)" Tag="961"/>
                                <ComboBoxItem Content="سوريا (+963)" Tag="963"/>
                            </ComboBox>
                        </Grid>
                    </GroupBox>

                    <!-- Message Settings -->
                    <GroupBox Header="💬 إعدادات الرسائل" FontWeight="Bold" Padding="15" Margin="0,0,0,20">
                        <StackPanel>
                            <CheckBox Name="ShowConfirmationCheckBox" Content="عرض رسالة تأكيد بعد فتح الواتساب" 
                                      IsChecked="True" Margin="0,0,0,10"/>
                            <CheckBox Name="ShowPreviewCheckBox" Content="عرض معاينة الرسالة قبل الإرسال" 
                                      IsChecked="True" Margin="0,0,0,10"/>
                            <CheckBox Name="AutoCloseCheckBox" Content="إغلاق النافذة تلقائياً بعد الإرسال" 
                                      IsChecked="False" Margin="0,0,0,10"/>
                        </StackPanel>
                    </GroupBox>

                    <!-- Bulk Sending -->
                    <GroupBox Header="📤 الإرسال الجماعي" FontWeight="Bold" Padding="15" Margin="0,0,0,20">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="60"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="التأخير بين الرسائل (ثانية):" 
                                       VerticalAlignment="Center" Margin="0,0,15,10"/>
                            <Slider Grid.Row="0" Grid.Column="1" Name="DelaySlider" 
                                    Minimum="1" Maximum="10" Value="2" 
                                    TickFrequency="1" IsSnapToTickEnabled="True" 
                                    Margin="0,0,10,10"/>
                            <TextBlock Grid.Row="0" Grid.Column="2" Text="القيمة:" 
                                       VerticalAlignment="Center" Margin="0,0,5,10"/>
                            <TextBlock Grid.Row="0" Grid.Column="3" Text="{Binding ElementName=DelaySlider, Path=Value}" 
                                       VerticalAlignment="Center" Margin="0,0,0,10"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="4" 
                                       Text="💡 التأخير يساعد في تجنب حظر الرسائل المتتالية" 
                                       FontSize="12" 
                                       Foreground="#7F8C8D"/>
                        </Grid>
                    </GroupBox>

                    <!-- Test Section -->
                    <GroupBox Header="🧪 اختبار الإعدادات" FontWeight="Bold" Padding="15">
                        <StackPanel>
                            <Grid Margin="0,0,0,15">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Column="0" Text="رقم الاختبار:" VerticalAlignment="Center" Margin="0,0,15,0"/>
                                <TextBox Grid.Column="1" Name="TestPhoneTextBox" Text="0612345678"/>
                            </Grid>
                            
                            <Button Content="📱 اختبار الإرسال" Style="{StaticResource ModernButtonStyle}"
                                    Click="TestSendBtn_Click" HorizontalAlignment="Left"/>
                        </StackPanel>
                    </GroupBox>
                </StackPanel>
            </ScrollViewer>
        </Border>

        <!-- Action Buttons -->
        <Border Grid.Row="2" Background="{StaticResource SurfaceColor}" CornerRadius="12" Padding="20" Margin="0,20,0,0">
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="10"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="💾" FontSize="16" Margin="0,0,10,0"/>
                    <TextBlock Text="احفظ الإعدادات لتطبيقها على جميع الإشعارات" 
                               FontSize="12" 
                               Foreground="#7F8C8D"/>
                </StackPanel>

                <Button Grid.Column="1" Content="💾 حفظ الإعدادات" 
                        Style="{StaticResource ModernButtonStyle}"
                        Margin="0,0,10,0"
                        Click="SaveBtn_Click"/>

                <Button Grid.Column="2" Content="❌ إلغاء" 
                        Style="{StaticResource SecondaryButtonStyle}"
                        Click="CancelBtn_Click"/>
            </Grid>
        </Border>
    </Grid>
</Window>
