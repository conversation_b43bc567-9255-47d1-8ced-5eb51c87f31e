using System.Windows;
using System.Windows.Controls;
using SimpleRentalApp.Models;
using SimpleRentalApp.Services;

namespace SimpleRentalApp.Windows;

public partial class CustomerDialog : Window
{
    public Customer? Customer { get; private set; }
    private readonly Customer? _existingCustomer;

    public CustomerDialog(Customer? customer = null)
    {
        InitializeComponent();
        _existingCustomer = customer;
        
        if (customer != null)
        {
            Title = "تعديل بيانات الكاري";
            LoadCustomerData(customer);
        }
        else
        {
            Title = "إضافة كاري جديد";
        }
    }

    private void LoadCustomerData(Customer customer)
    {
        FirstNameTextBox.Text = customer.FirstName;
        LastNameTextBox.Text = customer.LastName;
        PhoneTextBox.Text = customer.Phone;
        EmailTextBox.Text = customer.Email;
        NationalIdTextBox.Text = customer.NationalId;
        AddressTextBox.Text = customer.Address;
    }

    private async void SaveButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // Validation
            if (string.IsNullOrWhiteSpace(FirstNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال الاسم الأول", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                FirstNameTextBox.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(LastNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم العائلة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                LastNameTextBox.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(PhoneTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الهاتف", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                PhoneTextBox.Focus();
                return;
            }

            // Create or update customer
            var customer = _existingCustomer ?? new Customer();
            customer.FirstName = FirstNameTextBox.Text.Trim();
            customer.LastName = LastNameTextBox.Text.Trim();
            customer.Phone = PhoneTextBox.Text.Trim();
            customer.Email = EmailTextBox.Text.Trim();
            customer.NationalId = NationalIdTextBox.Text.Trim();
            customer.Address = AddressTextBox.Text.Trim();

            if (_existingCustomer == null)
            {
                customer.CreatedDate = DateTime.Now;
            }

            // Save to database
            Customer = await DatabaseService.Instance.SaveCustomerAsync(customer);

            MessageBox.Show("تم حفظ بيانات الكاري بنجاح! ✅", "نجح الحفظ",
                MessageBoxButton.OK, MessageBoxImage.Information);

            DialogResult = true;
            Close();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }



    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        DialogResult = false;
        Close();
    }
}
