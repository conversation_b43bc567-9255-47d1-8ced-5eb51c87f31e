using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using SimpleRentalApp.Models;
using SimpleRentalApp.Services;

namespace SimpleRentalApp.Windows;

public partial class CleaningTaxReceiptSelectionDialog : Window
{
    private readonly Property _property;
    private readonly List<Payment> _payments;
    
    public Payment? SelectedPayment { get; private set; }
    
    public CleaningTaxReceiptSelectionDialog(Property property, List<Payment> payments)
    {
        InitializeComponent();
        _property = property;
        _payments = payments;
        
        LoadData();
    }
    
    private void LoadData()
    {
        // Load property info
        PropertyNameText.Text = _property.Name;
        PropertyAddressText.Text = _property.Address;
        
        // Load payments
        PaymentsDataGrid.ItemsSource = _payments.OrderByDescending(p => p.PaymentDate);
    }
    
    private void PaymentsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        var isSelected = PaymentsDataGrid.SelectedItem != null;
        PrintButton.IsEnabled = isSelected;
        PreviewButton.IsEnabled = isSelected;

        if (isSelected)
        {
            SelectedPayment = (Payment)PaymentsDataGrid.SelectedItem;
        }
    }
    
    private async void PrintButton_Click(object sender, RoutedEventArgs e)
    {
        if (SelectedPayment == null) return;

        try
        {
            var customer = await DatabaseService.Instance.GetCustomerAsync(_property.CustomerId ?? 0);

            // Create CleaningTaxReceipt object
            var receipt = new CleaningTaxReceipt
            {
                ReceiptNumber = $"CT{SelectedPayment.PaymentDate?.Year}-{SelectedPayment.Id:D4}",
                PropertyId = _property.Id,
                Property = _property,
                PaymentId = SelectedPayment.Id,
                Payment = SelectedPayment,
                Amount = SelectedPayment.CleaningTaxAmount,
                PaymentDate = SelectedPayment.PaymentDate ?? DateTime.Now,
                TaxYear = SelectedPayment.PaymentDate?.Year ?? DateTime.Now.Year,
                PaymentMethod = SelectedPayment.PaymentMethod ?? "نقداً",
                Notes = SelectedPayment.Notes ?? "",
                CreatedDate = DateTime.Now,
                CreatedBy = "النظام"
            };

            // Use new progressive calculation service for printing
            var printService = CleaningTaxPrintService.Instance;
            var success = await printService.PrintReceiptFromPaymentAsync(SelectedPayment);

            if (success)
            {
                MessageBox.Show("تم طباعة الوصل بنجاح مع الحساب التدريجي الصحيح", "نجح",
                    MessageBoxButton.OK, MessageBoxImage.Information);
                DialogResult = true;
                Close();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في طباعة الوصل: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
    
    private async void PreviewButton_Click(object sender, RoutedEventArgs e)
    {
        if (SelectedPayment == null) return;

        try
        {
            // إنشاء نافذة المعاينة
            var previewWindow = new CleaningTaxReceiptPreviewWindow(SelectedPayment);
            previewWindow.Owner = this;
            previewWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في معاينة الوصل: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        DialogResult = false;
        Close();
    }
}
