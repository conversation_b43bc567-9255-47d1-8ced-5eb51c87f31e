; تطبيق تدبير الكراء - سكريبت المثبت
; المطور: حفيظ عبدو

!define APP_NAME "تدبير الكراء"
!define APP_VERSION "1.0.0"
!define APP_PUBLISHER "حفيظ عبدو"
!define APP_URL ""
!define APP_DESCRIPTION "نظام إدارة الكراء والعقارات"

; تضمين المكتبات المطلوبة
!include "MUI2.nsh"
!include "FileFunc.nsh"

; إعدادات عامة
Name "${APP_NAME}"
OutFile "تدبير_الكراء_مثبت_v${APP_VERSION}.exe"
InstallDir "$PROGRAMFILES64\${APP_NAME}"
InstallDirRegKey HKLM "Software\${APP_NAME}" "InstallDir"
RequestExecutionLevel admin

; إعدادات واجهة المستخدم الحديثة
!define MUI_ABORTWARNING
!define MUI_ICON "app_icon.ico"
!define MUI_UNICON "app_icon.ico"

; صفحات المثبت
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE.txt"
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

; صفحات إلغاء التثبيت
!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

; اللغات
!insertmacro MUI_LANGUAGE "Arabic"
!insertmacro MUI_LANGUAGE "English"

; معلومات الإصدار
VIProductVersion "${APP_VERSION}.0"
VIAddVersionKey /LANG=${LANG_ARABIC} "ProductName" "${APP_NAME}"
VIAddVersionKey /LANG=${LANG_ARABIC} "Comments" "${APP_DESCRIPTION}"
VIAddVersionKey /LANG=${LANG_ARABIC} "CompanyName" "${APP_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_ARABIC} "LegalCopyright" "© 2024 ${APP_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_ARABIC} "FileDescription" "${APP_DESCRIPTION}"
VIAddVersionKey /LANG=${LANG_ARABIC} "FileVersion" "${APP_VERSION}"
VIAddVersionKey /LANG=${LANG_ARABIC} "ProductVersion" "${APP_VERSION}"

; قسم التثبيت الرئيسي
Section "التطبيق الرئيسي" SecMain
  SectionIn RO
  
  ; تعيين مجلد الإخراج
  SetOutPath "$INSTDIR"
  
  ; نسخ الملفات
  File "dist\تدبير_الكراء.exe"
  File "dist\README.txt"
  
  ; إنشاء اختصار في قائمة البداية
  CreateDirectory "$SMPROGRAMS\${APP_NAME}"
  CreateShortCut "$SMPROGRAMS\${APP_NAME}\${APP_NAME}.lnk" "$INSTDIR\تدبير_الكراء.exe"
  CreateShortCut "$SMPROGRAMS\${APP_NAME}\إلغاء التثبيت.lnk" "$INSTDIR\Uninstall.exe"
  
  ; إنشاء اختصار على سطح المكتب
  CreateShortCut "$DESKTOP\${APP_NAME}.lnk" "$INSTDIR\تدبير_الكراء.exe"
  
  ; كتابة معلومات إلغاء التثبيت في السجل
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "DisplayName" "${APP_NAME}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "UninstallString" "$INSTDIR\Uninstall.exe"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "InstallLocation" "$INSTDIR"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "DisplayIcon" "$INSTDIR\تدبير_الكراء.exe"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "Publisher" "${APP_PUBLISHER}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "DisplayVersion" "${APP_VERSION}"
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "NoModify" 1
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "NoRepair" 1
  
  ; حساب حجم التثبيت
  ${GetSize} "$INSTDIR" "/S=0K" $0 $1 $2
  IntFmt $0 "0x%08X" $0
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "EstimatedSize" "$0"
  
  ; إنشاء ملف إلغاء التثبيت
  WriteUninstaller "$INSTDIR\Uninstall.exe"
  
  ; حفظ مجلد التثبيت
  WriteRegStr HKLM "Software\${APP_NAME}" "InstallDir" "$INSTDIR"
SectionEnd

; وصف الأقسام
LangString DESC_SecMain ${LANG_ARABIC} "الملفات الأساسية للتطبيق"
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
  !insertmacro MUI_DESCRIPTION_TEXT ${SecMain} $(DESC_SecMain)
!insertmacro MUI_FUNCTION_DESCRIPTION_END

; قسم إلغاء التثبيت
Section "Uninstall"
  ; حذف الملفات
  Delete "$INSTDIR\تدبير_الكراء.exe"
  Delete "$INSTDIR\README.txt"
  Delete "$INSTDIR\Uninstall.exe"
  
  ; حذف الاختصارات
  Delete "$SMPROGRAMS\${APP_NAME}\${APP_NAME}.lnk"
  Delete "$SMPROGRAMS\${APP_NAME}\إلغاء التثبيت.lnk"
  Delete "$DESKTOP\${APP_NAME}.lnk"
  
  ; حذف المجلدات
  RMDir "$SMPROGRAMS\${APP_NAME}"
  RMDir "$INSTDIR"
  
  ; حذف مفاتيح السجل
  DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}"
  DeleteRegKey HKLM "Software\${APP_NAME}"
SectionEnd
