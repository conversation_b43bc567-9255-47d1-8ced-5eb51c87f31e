&lt;Project Sdk="Microsoft.NET.Sdk"&gt;

  &lt;PropertyGroup&gt;
    &lt;TargetFramework&gt;net8.0-windows&lt;/TargetFramework&gt;
    &lt;UseWPF&gt;true&lt;/UseWPF&gt;
    &lt;ImplicitUsings&gt;enable&lt;/ImplicitUsings&gt;
    &lt;Nullable&gt;enable&lt;/Nullable&gt;
    &lt;IsPackable&gt;false&lt;/IsPackable&gt;
    &lt;IsTestProject&gt;true&lt;/IsTestProject&gt;
  &lt;/PropertyGroup&gt;

  &lt;ItemGroup&gt;
    &lt;PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" /&gt;
    &lt;PackageReference Include="xunit" Version="2.6.2" /&gt;
    &lt;PackageReference Include="xunit.runner.visualstudio" Version="2.5.3"&gt;
      &lt;IncludeAssets&gt;runtime; build; native; contentfiles; analyzers; buildtransitive&lt;/IncludeAssets&gt;
      &lt;PrivateAssets&gt;all&lt;/PrivateAssets&gt;
    &lt;/PackageReference&gt;
    &lt;PackageReference Include="coverlet.collector" Version="6.0.0"&gt;
      &lt;IncludeAssets&gt;runtime; build; native; contentfiles; analyzers; buildtransitive&lt;/IncludeAssets&gt;
      &lt;PrivateAssets&gt;all&lt;/PrivateAssets&gt;
    &lt;/PackageReference&gt;
    &lt;PackageReference Include="Moq" Version="4.20.69" /&gt;
    &lt;PackageReference Include="FluentAssertions" Version="6.12.0" /&gt;
    &lt;PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.6" /&gt;
  &lt;/ItemGroup&gt;

  &lt;ItemGroup&gt;
    &lt;ProjectReference Include="..\SimpleRentalApp\SimpleRentalApp.csproj" /&gt;
  &lt;/ItemGroup&gt;

  &lt;ItemGroup&gt;
    &lt;Folder Include="CloudSync\" /&gt;
    &lt;Folder Include="Data\" /&gt;
    &lt;Folder Include="Services\" /&gt;
    &lt;Folder Include="Models\" /&gt;
    &lt;Folder Include="Views\" /&gt;
  &lt;/ItemGroup&gt;

&lt;/Project&gt;
