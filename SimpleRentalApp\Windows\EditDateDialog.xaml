<Window x:Class="SimpleRentalApp.Windows.EditDateDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تعديل تاريخ الاستحقاق"
        Height="300" Width="450"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="{StaticResource BackgroundColor}"
        FontFamily="Segoe UI">

    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryColor}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource PrimaryDarkColor}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Cancel Button Style -->
        <Style x:Key="CancelButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="{StaticResource SecondaryColor}"/>
            <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
        </Style>
    </Window.Resources>

    <Border Background="{StaticResource SurfaceColor}" 
            CornerRadius="15" 
            Margin="20" 
            Padding="30">
        <Border.Effect>
            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="3" BlurRadius="15"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
                <TextBlock Text="📅" FontSize="24" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock Text="تعديل تاريخ الاستحقاق" 
                           FontSize="20" 
                           FontWeight="Bold" 
                           Foreground="{StaticResource TextPrimaryColor}"
                           VerticalAlignment="Center"/>
            </StackPanel>

            <!-- Current Date Display -->
            <Border Grid.Row="1" 
                    Background="{StaticResource AccentColor}" 
                    CornerRadius="8" 
                    Padding="15,10" 
                    Margin="0,0,0,20">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="التاريخ الحالي:" 
                               Foreground="White" 
                               FontWeight="Medium" 
                               Margin="0,0,10,0"/>
                    <TextBlock Name="CurrentDateTextBlock" 
                               Foreground="White" 
                               FontWeight="Bold"/>
                </StackPanel>
            </Border>

            <!-- New Date Selection -->
            <StackPanel Grid.Row="2" Margin="0,0,0,20">
                <TextBlock Text="التاريخ الجديد:" 
                           FontSize="14" 
                           FontWeight="Medium" 
                           Foreground="{StaticResource TextPrimaryColor}" 
                           Margin="0,0,0,10"/>
                
                <DatePicker Name="NewDatePicker" 
                            FontSize="14" 
                            Padding="10"
                            Background="White"
                            BorderBrush="{StaticResource AccentColor}"
                            BorderThickness="2"/>
            </StackPanel>

            <!-- Instructions -->
            <Border Grid.Row="3" 
                    Background="#FFF3CD" 
                    BorderBrush="#FFEAA7" 
                    BorderThickness="1" 
                    CornerRadius="6" 
                    Padding="15,10" 
                    Margin="0,0,0,20">
                <StackPanel>
                    <TextBlock Text="💡 ملاحظة:" 
                               FontWeight="Bold" 
                               Foreground="#856404" 
                               Margin="0,0,0,5"/>
                    <TextBlock Text="• سيتم تحديث تاريخ الاستحقاق للإشعار المحدد فقط" 
                               Foreground="#856404" 
                               TextWrapping="Wrap"/>
                    <TextBlock Text="• يمكنك اختيار أي تاريخ في المستقبل أو الماضي" 
                               Foreground="#856404" 
                               TextWrapping="Wrap"/>
                </StackPanel>
            </Border>

            <!-- Buttons -->
            <StackPanel Grid.Row="5" 
                        Orientation="Horizontal" 
                        HorizontalAlignment="Center" 
                        Margin="0,20,0,0">
                <Button Name="SaveButton" 
                        Content="💾 حفظ التغييرات" 
                        Style="{StaticResource ModernButtonStyle}"
                        Margin="0,0,15,0"
                        Click="SaveButton_Click"/>
                
                <Button Name="CancelButton" 
                        Content="❌ إلغاء" 
                        Style="{StaticResource CancelButtonStyle}"
                        Click="CancelButton_Click"/>
            </StackPanel>
        </Grid>
    </Border>
</Window>
