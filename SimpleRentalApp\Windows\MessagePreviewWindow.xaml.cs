using Microsoft.EntityFrameworkCore;
using SimpleRentalApp.Data;
using SimpleRentalApp.Models;
using SimpleRentalApp.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;

namespace SimpleRentalApp.Windows
{
    public partial class MessagePreviewWindow : Window
    {
        private readonly RentalDbContext _context;
        private readonly WhatsAppNotificationService _notificationService;
        private readonly NotificationDisplayInfo _notification;
        private List<MessageTemplate> _templates;

        public MessagePreviewWindow(NotificationDisplayInfo notification)
        {
            InitializeComponent();
            _context = new RentalDbContext();
            _notificationService = new WhatsAppNotificationService();
            _notification = notification;
            _templates = new List<MessageTemplate>();
            
            LoadNotificationInfo();
            LoadTemplatesAsync();
        }

        private void LoadNotificationInfo()
        {
            try
            {
                CustomerNameText.Text = _notification.CustomerName;
                PropertyNameText.Text = _notification.PropertyName;
                NotificationTypeText.Text = _notification.TypeDisplay;
                AmountText.Text = _notification.AmountDisplay;
                
                ContactNameText.Text = _notification.CustomerName;
                PhoneNumberText.Text = _notification.PhoneNumber ?? "غير محدد";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل معلومات الإشعار: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void LoadTemplatesAsync()
        {
            try
            {
                _templates = await _context.MessageTemplates
                    .Where(t => t.Type == _notification.Type)
                    .ToListAsync();

                TemplateComboBox.Items.Clear();
                
                if (_templates.Any())
                {
                    foreach (var template in _templates)
                    {
                        TemplateComboBox.Items.Add(new ComboBoxItem 
                        { 
                            Content = template.Name, 
                            Tag = template 
                        });
                    }
                    
                    TemplateComboBox.SelectedIndex = 0;
                }
                else
                {
                    // إنشاء قالب افتراضي
                    var defaultTemplate = CreateDefaultTemplate();
                    TemplateComboBox.Items.Add(new ComboBoxItem 
                    { 
                        Content = "القالب الافتراضي", 
                        Tag = defaultTemplate 
                    });
                    TemplateComboBox.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل القوالب: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private MessageTemplate CreateDefaultTemplate()
        {
            return _notification.Type switch
            {
                NotificationType.MonthlyRent => new MessageTemplate
                {
                    Type = NotificationType.MonthlyRent,
                    Name = "إشعار الإيجار الشهري",
                    Template = "السلام عليكم {CustomerName}\n\nنذكركم بأن إيجار {PropertyName} مستحق الدفع.\n\nالمبلغ: {Amount} درهم\nتاريخ الاستحقاق: {DueDate}\n\nيرجى التواصل معنا لتسوية المبلغ.\n\nشكراً لكم"
                },
                NotificationType.CleaningTax => new MessageTemplate
                {
                    Type = NotificationType.CleaningTax,
                    Name = "إشعار ضريبة النظافة",
                    Template = "السلام عليكم {CustomerName}\n\nنذكركم بأن ضريبة النظافة لـ {PropertyName} مستحقة الدفع.\n\nالمبلغ: {Amount} درهم\nالسنة: {Year}\n\nيرجى التواصل معنا لتسوية المبلغ.\n\nشكراً لكم"
                },
                NotificationType.RentOverdue => new MessageTemplate
                {
                    Type = NotificationType.RentOverdue,
                    Name = "تذكير بتأخير الإيجار",
                    Template = "السلام عليكم {CustomerName}\n\n⚠️ تذكير هام: إيجار {PropertyName} متأخر عن موعد الاستحقاق.\n\nالمبلغ: {Amount} درهم\nتاريخ الاستحقاق: {DueDate}\nعدد الأيام المتأخرة: {OverdueDays}\n\nيرجى المبادرة بالدفع لتجنب أي إجراءات إضافية.\n\nشكراً لكم"
                },
                NotificationType.PaymentReminder => new MessageTemplate
                {
                    Type = NotificationType.PaymentReminder,
                    Name = "تذكير عام بالدفع",
                    Template = "السلام عليكم {CustomerName}\n\n📌 تذكير ودي بالدفع المستحق لـ {PropertyName}.\n\nالمبلغ: {Amount} درهم\n\nنقدر تفهمكم وتعاونكم.\n\nشكراً لكم"
                },
                _ => new MessageTemplate
                {
                    Type = _notification.Type,
                    Name = "قالب عام",
                    Template = "السلام عليكم {CustomerName}\n\nلديكم إشعار متعلق بـ {PropertyName}.\n\nيرجى التواصل معنا.\n\nشكراً لكم"
                }
            };
        }

        private void TemplateComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (TemplateComboBox.SelectedItem is ComboBoxItem item && item.Tag is MessageTemplate template)
                {
                    var processedMessage = ProcessTemplate(template.Template);
                    MessageContentText.Text = processedMessage;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معالجة القالب: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string ProcessTemplate(string template)
        {
            try
            {
                var processed = template
                    .Replace("{CustomerName}", _notification.CustomerName)
                    .Replace("{PropertyName}", _notification.PropertyName)
                    .Replace("{Amount}", _notification.Amount.ToString("N0"))
                    .Replace("{DueDate}", _notification.DueDate.ToString("dd/MM/yyyy"))
                    .Replace("{Year}", _notification.DueDate.Year.ToString())
                    .Replace("{OverdueDays}", _notification.IsOverdue ? 
                        (DateTime.Now - _notification.DueDate).Days.ToString() : "0");

                return processed;
            }
            catch (Exception)
            {
                return template; // إرجاع القالب الأصلي في حالة الخطأ
            }
        }

        private void EditTemplateBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var templateWindow = new MessageTemplatesWindow();
                templateWindow.Owner = this;
                if (templateWindow.ShowDialog() == true)
                {
                    LoadTemplatesAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة تعديل القوالب: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SendNowBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    $"هل تريد إرسال الرسالة الآن للمكتري {_notification.CustomerName}؟",
                    "تأكيد الإرسال",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    SendNowBtn.Content = "جاري الإرسال...";
                    SendNowBtn.IsEnabled = false;
                    
                    // محاكاة الإرسال
                    await Task.Delay(2000);
                    
                    MessageBox.Show("تم إرسال الرسالة بنجاح!", "تم الإرسال", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    DialogResult = true;
                    Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إرسال الرسالة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                SendNowBtn.Content = "📤 إرسال الآن";
                SendNowBtn.IsEnabled = true;
            }
        }

        private void CopyTextBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!string.IsNullOrEmpty(MessageContentText.Text))
                {
                    Clipboard.SetText(MessageContentText.Text);
                    MessageBox.Show("تم نسخ النص إلى الحافظة!", "تم النسخ", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في نسخ النص: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseBtn_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            base.OnClosed(e);
        }
    }
}
