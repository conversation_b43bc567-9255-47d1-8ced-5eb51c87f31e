using System.Windows;
using RentalManagement.ViewModels;

namespace RentalManagement.Views
{
    /// <summary>
    /// Interaction logic for PaymentDialog.xaml
    /// </summary>
    public partial class PaymentDialog : Window
    {
        public PaymentDialog(PaymentDialogViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
            
            // Subscribe to dialog closed event
            viewModel.DialogClosed += OnDialogClosed;
        }

        private void OnDialogClosed(object? sender, bool result)
        {
            DialogResult = result;
            Close();
        }
    }
}
