using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;
using System.Windows.Media;
using Microsoft.EntityFrameworkCore;
using Microsoft.Win32;
using SimpleRentalApp.Data;
using SimpleRentalApp.Models;
using SimpleRentalApp.Services;
using SimpleRentalApp.Utils;
using System.IO;
using System.Text.Json;

namespace SimpleRentalApp.Windows
{
    public partial class PropertyDetailsWindow : Window
    {
        #region Fields and Configuration
        private readonly Property _property;
        private readonly CleaningTaxCalculationService _calculationService;
        private readonly RentalDbContext _context;
        private ObservableCollection<PaymentDisplayInfo> _allPayments;
        private CollectionViewSource _paymentsViewSource;
        private ICollectionView _paymentsView;
        private int _selectedYear;
        private bool _isDarkMode = false;
        private bool _showStatistics = true;
        private bool _showOnlyCurrentYear = false;

        // Year Configuration - يمكن تخصيص هذه القيم حسب الحاجة
        private const int DEFAULT_START_YEAR = 2020;           // سنة البداية الافتراضية
        private const int DEFAULT_FUTURE_YEARS = 5;            // عدد السنوات المستقبلية
        private const bool DEFAULT_DESCENDING_ORDER = true;    // ترتيب تنازلي (الأحدث أولاً)

        // يمكن جعل هذه القيم قابلة للتخصيص من إعدادات التطبيق لاحقاً
        private static int StartYear => DEFAULT_START_YEAR;
        private static int FutureYears => DEFAULT_FUTURE_YEARS;
        private static bool DescendingOrder => DEFAULT_DESCENDING_ORDER;
        #endregion

        public PropertyDetailsWindow(Property property) : this(property, DateTime.Now.Year)
        {
        }

        public PropertyDetailsWindow(Property property, int selectedYear)
        {
            if (property == null)
            {
                throw new ArgumentNullException(nameof(property), "Property cannot be null");
            }

            _property = property;
            _calculationService = new CleaningTaxCalculationService();
            _context = new RentalDbContext();
            _selectedYear = selectedYear;

            try
            {
                InitializeComponent();
                InitializeDataStructures();
                InitializeWindow();
                LoadPropertyData();
                LoadYears();

                // Load data automatically after window is fully loaded
                this.Loaded += async (s, e) =>
                {
                    try
                    {
                        await LoadPaymentsAsync();
                        await LoadTaxSummaryAsync();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error loading initial data: {ex.Message}");
                        if (StatusTextBlock != null)
                            StatusTextBlock.Text = "خطأ في تحميل البيانات الأولية";
                    }
                };
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة نافذة التفاصيل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InitializeDataStructures()
        {
            // Initialize ObservableCollection and CollectionViewSource
            _allPayments = new ObservableCollection<PaymentDisplayInfo>();
            _paymentsViewSource = new CollectionViewSource();

            // Set source after creating the CollectionViewSource
            _paymentsViewSource.Source = _allPayments;
            _paymentsView = _paymentsViewSource.View;

            // Set up DataGrid binding
            if (PaymentsDataGrid != null)
            {
                PaymentsDataGrid.ItemsSource = _paymentsView;
            }
        }

        private void InitializeWindow()
        {
            Title = $"تفاصيل المحل - {_property.Name}";
            PropertyNameTitle.Text = $"تفاصيل المحل: {_property.Name}";
            PropertyAddressSubtitle.Text = _property.Address;
        }

        private async void LoadPropertyData()
        {
            try
            {
                if (_property == null)
                {
                    MessageBox.Show("بيانات المحل غير متوفرة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // Load property basic info with null checks
                if (PropertyNameText != null)
                    PropertyNameText.Text = _property.Name ?? "غير محدد";

                if (PropertyAddressText != null)
                    PropertyAddressText.Text = _property.Address ?? "غير محدد";

                if (PropertyTypeText != null)
                    PropertyTypeText.Text = "محل تجاري"; // نوع ثابت

                if (MonthlyRentText != null)
                    MonthlyRentText.Text = $"{_property.MonthlyRent:N0} درهم";

                if (TaxPaymentMethodText != null)
                    TaxPaymentMethodText.Text = GetPaymentMethodText(_property.CleaningTaxPaymentOption);

                // Load customer info from active contract
                if (CustomerNameText != null)
                {
                    try
                    {
                        // جلب المكتري من أحدث عقد للمحل (نشط أو أحدث عقد)
                        Customer? customer = null;

                        // أولاً: البحث عن عقد نشط
                        var activeContract = await _context.Contracts
                            .Include(c => c.Customer)
                            .Where(c => c.PropertyId == _property.Id &&
                                   c.StartDate <= DateTime.Now &&
                                   c.EndDate >= DateTime.Now)
                            .OrderByDescending(c => c.StartDate)
                            .FirstOrDefaultAsync();

                        // إذا لم يوجد عقد نشط، جلب أحدث عقد
                        if (activeContract == null)
                        {
                            activeContract = await _context.Contracts
                                .Include(c => c.Customer)
                                .Where(c => c.PropertyId == _property.Id)
                                .OrderByDescending(c => c.StartDate)
                                .FirstOrDefaultAsync();
                        }

                        if (activeContract != null)
                        {
                            customer = activeContract.Customer;
                            CustomerNameText.Text = customer?.FullName ?? "غير محدد";
                            System.Diagnostics.Debug.WriteLine($"PropertyDetailsWindow - Property: {_property.Name}, Customer from Contract: {customer?.FullName ?? "null"}");
                        }
                        else
                        {
                            CustomerNameText.Text = "غير محدد";
                            System.Diagnostics.Debug.WriteLine($"PropertyDetailsWindow - Property: {_property.Name}, No contract found");
                        }
                    }
                    catch (Exception customerEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error loading customer from contract: {customerEx.Message}");
                        CustomerNameText.Text = "خطأ في تحميل بيانات المكتري";
                    }
                }

                // Load tax summary
                await LoadTaxSummary();
            }
            catch (Exception ex)
            {
                var errorMessage = $"خطأ في تحميل بيانات المحل:\n\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $"\n\nتفاصيل إضافية: {ex.InnerException.Message}";
                }

                MessageBox.Show(errorMessage, "خطأ تفصيلي",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadYears()
        {
            try
            {
                // Generate dynamic years list using configuration
                var years = GenerateYearsList(StartYear, FutureYears, DescendingOrder);

                // Set ItemsSource
                YearComboBox.ItemsSource = years;

                // Set default selection to current year if not already set
                if (_selectedYear == 0)
                    _selectedYear = DateTime.Now.Year;

                // Ensure selected year is in the list
                if (years.Contains(_selectedYear))
                {
                    YearComboBox.SelectedItem = _selectedYear;
                }
                else
                {
                    // If selected year is not in list, default to current year
                    _selectedYear = DateTime.Now.Year;
                    YearComboBox.SelectedItem = _selectedYear;
                }

                // Log the generated years for debugging
                System.Diagnostics.Debug.WriteLine($"Generated years: {string.Join(", ", years)} (Total: {years.Count})");
                System.Diagnostics.Debug.WriteLine($"Selected year: {_selectedYear}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in LoadYears: {ex.Message}");
                // Fallback to simple year list
                var currentYear = DateTime.Now.Year;
                var fallbackYears = new List<int> { currentYear - 2, currentYear - 1, currentYear, currentYear + 1 };
                YearComboBox.ItemsSource = fallbackYears;
                YearComboBox.SelectedItem = currentYear;
                _selectedYear = currentYear;
            }
        }

        /// <summary>
        /// Generates a dynamic list of years from start year to current year + future years
        /// This method creates a future-proof year list that automatically adapts to the current year
        /// </summary>
        /// <param name="startYear">Starting year (e.g., 2020 - when the system started)</param>
        /// <param name="futureYears">Number of future years to include (e.g., 5 years ahead)</param>
        /// <param name="descending">Whether to sort in descending order (true = newest first, better UX)</param>
        /// <returns>List of years ready for ComboBox binding</returns>
        private List<int> GenerateYearsList(int startYear = 2020, int futureYears = 5, bool descending = true)
        {
            try
            {
                var currentYear = DateTime.Now.Year;
                var endYear = currentYear + futureYears;
                var years = new List<int>();

                // Validate input parameters
                if (startYear > currentYear + futureYears)
                {
                    System.Diagnostics.Debug.WriteLine($"Warning: Start year {startYear} is beyond end year {endYear}");
                    startYear = currentYear - 2; // Fallback to reasonable start
                }

                if (futureYears < 0)
                {
                    System.Diagnostics.Debug.WriteLine($"Warning: Future years {futureYears} is negative, using 0");
                    futureYears = 0;
                }

                // Generate years from start to end
                for (int year = startYear; year <= endYear; year++)
                {
                    years.Add(year);
                }

                // Sort based on preference (descending by default for better UX)
                if (descending)
                {
                    years.Sort((x, y) => y.CompareTo(x)); // Descending order (2029, 2028, 2027, ...)
                }
                else
                {
                    years.Sort(); // Ascending order (2020, 2021, 2022, ...)
                }

                // Log generation info for debugging
                System.Diagnostics.Debug.WriteLine($"Generated years from {startYear} to {endYear}, " +
                    $"Current: {currentYear}, Total: {years.Count}, Order: {(descending ? "Descending" : "Ascending")}");

                return years;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error generating years list: {ex.Message}");
                // Return minimal fallback list centered around current year
                var currentYear = DateTime.Now.Year;
                var fallbackYears = new List<int> { currentYear + 1, currentYear, currentYear - 1, currentYear - 2 };
                System.Diagnostics.Debug.WriteLine($"Using fallback years: {string.Join(", ", fallbackYears)}");
                return fallbackYears;
            }
        }

        /// <summary>
        /// Refreshes the years list - useful when the application runs across year boundaries
        /// or when year configuration changes
        /// </summary>
        public void RefreshYearsList()
        {
            try
            {
                var currentSelectedYear = _selectedYear;
                LoadYears();

                // Try to maintain the previously selected year if it's still valid
                if (YearComboBox.ItemsSource is List<int> years && years.Contains(currentSelectedYear))
                {
                    YearComboBox.SelectedItem = currentSelectedYear;
                    _selectedYear = currentSelectedYear;
                }

                System.Diagnostics.Debug.WriteLine($"Years list refreshed. Selected year: {_selectedYear}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error refreshing years list: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets information about the current year configuration
        /// </summary>
        /// <returns>String with year range information</returns>
        public string GetYearRangeInfo()
        {
            try
            {
                var currentYear = DateTime.Now.Year;
                var startYear = StartYear;
                var endYear = currentYear + FutureYears;
                var totalYears = endYear - startYear + 1;

                return $"نطاق السنوات: من {startYear} إلى {endYear} (المجموع: {totalYears} سنة) - " +
                       $"السنة الحالية: {currentYear}";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting year range info: {ex.Message}");
                return "معلومات النطاق غير متاحة";
            }
        }

        private async Task LoadTaxSummary()
        {
            try
            {
                // Show loading status
                if (StatusTextBlock != null)
                    StatusTextBlock.Text = "جاري تحديث الإحصائيات...";

                var status = await _calculationService.GetPaymentStatusAsync(_property.Id, _selectedYear);

                // Update tax summary with null checks
                if (TotalTaxText != null)
                    TotalTaxText.Text = $"{status.AnnualTax:N0} درهم";

                if (PaidAmountText != null)
                    PaidAmountText.Text = $"{status.TotalPaid:N0} درهم";

                if (RemainingAmountText != null)
                    RemainingAmountText.Text = $"{status.RemainingAmount:N0} درهم";

                // Update progress bar with null checks
                var progressPercentage = status.AnnualTax > 0 ? (status.TotalPaid / status.AnnualTax) * 100 : 0;

                if (PaymentProgressBar != null)
                    PaymentProgressBar.Value = Math.Min(100, Math.Max(0, (double)progressPercentage));

                if (ProgressPercentageText != null)
                    ProgressPercentageText.Text = $"{progressPercentage:F1}%";

                // Update colors based on payment status
                if (status.IsFullyPaid)
                {
                    if (RemainingAmountText != null)
                        RemainingAmountText.Foreground = new SolidColorBrush(Color.FromRgb(76, 175, 80)); // Green
                    if (PaymentProgressBar != null)
                        PaymentProgressBar.Foreground = new SolidColorBrush(Color.FromRgb(76, 175, 80));
                }
                else
                {
                    if (RemainingAmountText != null)
                        RemainingAmountText.Foreground = new SolidColorBrush(Color.FromRgb(244, 67, 54)); // Red
                    if (PaymentProgressBar != null)
                        PaymentProgressBar.Foreground = new SolidColorBrush(Color.FromRgb(255, 152, 0)); // Orange
                }

                // Load advanced statistics
                await LoadAdvancedStatistics();

                // Update status
                if (StatusTextBlock != null)
                    StatusTextBlock.Text = $"تم تحديث الإحصائيات للسنة {_selectedYear}";
            }
            catch (Exception ex)
            {
                if (StatusTextBlock != null)
                    StatusTextBlock.Text = "خطأ في تحديث الإحصائيات";

                MessageBox.Show($"خطأ في تحميل ملخص الضريبة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadAdvancedStatistics()
        {
            try
            {
                // Check if required objects are initialized
                if (_allPayments == null || StatisticsCard == null)
                {
                    System.Diagnostics.Debug.WriteLine("LoadAdvancedStatistics: Required objects not initialized");
                    return;
                }

                if (!_showStatistics)
                {
                    StatisticsCard.Visibility = Visibility.Collapsed;
                    return;
                }

                StatisticsCard.Visibility = Visibility.Visible;

                var yearPayments = _allPayments.Where(p =>
                    (p.Payment.TaxYear.HasValue && p.Payment.TaxYear.Value == _selectedYear) ||
                    (!p.Payment.TaxYear.HasValue && p.Payment.PaymentDate?.Year == _selectedYear)).ToList();

                if (yearPayments.Any())
                {
                    // حساب الإحصائيات الشاملة
                    var totalPaid = yearPayments.Sum(p => p.Payment.CleaningTaxAmount);
                    var annualTax = _property.MonthlyRent * 12 * 0.105m;
                    var remainingAmount = annualTax - totalPaid;
                    var completionPercentage = (totalPaid / annualTax) * 100;

                    // عدد الدفعات
                    PaymentCountText.Text = $"{yearPayments.Count} دفعة";

                    // إجمالي المبلغ المدفوع (استخدام العنصر الموجود)
                    // TotalPaidAmountText غير موجود، سنستخدم عنصر آخر أو نتجاهله

                    // متوسط الدفعة
                    var averagePayment = yearPayments.Average(p => p.Payment.CleaningTaxAmount);
                    AveragePaymentText.Text = $"{averagePayment:N2} درهم";

                    // أكبر دفعة
                    var largestPayment = yearPayments.Max(p => p.Payment.CleaningTaxAmount);
                    LargestPaymentText.Text = $"{largestPayment:N2} درهم";

                    // أصغر دفعة
                    var smallestPayment = yearPayments.Min(p => p.Payment.CleaningTaxAmount);
                    SmallestPaymentText.Text = $"{smallestPayment:N2} درهم";

                    // آخر دفعة مع التاريخ والمبلغ
                    var lastPayment = yearPayments.OrderByDescending(p => p.Payment.PaymentDate).FirstOrDefault();
                    if (lastPayment != null)
                    {
                        LastPaymentDateText.Text = $"{lastPayment.PaymentDateDisplay} - {lastPayment.Payment.CleaningTaxAmount:N2} درهم";
                    }
                    else
                    {
                        LastPaymentDateText.Text = "لا توجد دفعات";
                    }

                    // نسبة الإنجاز والمبلغ المتبقي (العناصر غير موجودة، سنتجاهلها)
                    // CompletionPercentageText و RemainingAmountText غير موجودين

                    // الدفعة القادمة (تقدير بناءً على نمط الدفع)
                    var nextDueDate = CalculateNextPaymentDue();
                    NextPaymentDueText.Text = nextDueDate?.ToString("dd/MM/yyyy") ?? "غير محدد";

                    // مؤشر الحالة المحدث
                    UpdateStatusIndicator(yearPayments, (double)completionPercentage);
                }
                else
                {
                    // لا توجد دفعات - إعادة تعيين القيم الموجودة فقط
                    PaymentCountText.Text = "0 دفعة";
                    AveragePaymentText.Text = "0.00 درهم";
                    LargestPaymentText.Text = "0.00 درهم";
                    SmallestPaymentText.Text = "0.00 درهم";
                    LastPaymentDateText.Text = "لا توجد دفعات";

                    NextPaymentDueText.Text = "31/05/" + _selectedYear;

                    // مؤشر الحالة - غير مدفوعة
                    if (StatusIndicatorBorder != null)
                        StatusIndicatorBorder.Background = new SolidColorBrush(Color.FromRgb(244, 67, 54));
                    if (StatusIndicatorText != null)
                        StatusIndicatorText.Text = "لم يتم الدفع";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإحصائيات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private DateTime? CalculateNextPaymentDue()
        {
            try
            {
                // بناءً على طريقة دفع الضريبة
                var paymentOption = _property.CleaningTaxPaymentOption;
                var currentDate = DateTime.Now;

                return paymentOption switch
                {
                    1 => new DateTime(_selectedYear, 5, 31), // شهرياً - الموعد النهائي
                    2 => new DateTime(_selectedYear, 5, 31), // نصف سنوي
                    3 => new DateTime(_selectedYear, 5, 31), // ثلث سنوي
                    4 => new DateTime(_selectedYear, 5, 31), // ربع سنوي
                    12 => new DateTime(_selectedYear, 5, 31), // سنوياً
                    _ => new DateTime(_selectedYear, 5, 31)
                };
            }
            catch
            {
                return null;
            }
        }

        private void UpdateStatusIndicator(List<PaymentDisplayInfo> payments)
        {
            try
            {
                var totalPaid = payments.Sum(p => p.Payment.CleaningTaxAmount);
                var annualTax = _property.MonthlyRent * 12 * 0.105m;
                var remainingAmount = annualTax - totalPaid;

                if (remainingAmount <= 0)
                {
                    StatusIndicatorBorder.Background = new SolidColorBrush(Color.FromRgb(76, 175, 80));
                    StatusIndicatorText.Text = "✅ مكتمل";
                }
                else if (DateTime.Now > new DateTime(_selectedYear, 5, 31))
                {
                    StatusIndicatorBorder.Background = new SolidColorBrush(Color.FromRgb(244, 67, 54));
                    StatusIndicatorText.Text = "⚠️ متأخر";
                }
                else if (DateTime.Now > new DateTime(_selectedYear, 4, 1))
                {
                    StatusIndicatorBorder.Background = new SolidColorBrush(Color.FromRgb(255, 152, 0));
                    StatusIndicatorText.Text = "⏰ قريب الانتهاء";
                }
                else
                {
                    StatusIndicatorBorder.Background = new SolidColorBrush(Color.FromRgb(33, 150, 243));
                    StatusIndicatorText.Text = "📅 في الوقت المحدد";
                }
            }
            catch
            {
                StatusIndicatorBorder.Background = new SolidColorBrush(Color.FromRgb(158, 158, 158));
                StatusIndicatorText.Text = "غير محدد";
            }
        }

        private void UpdateStatusIndicator(List<PaymentDisplayInfo> payments, double completionPercentage)
        {
            try
            {
                if (StatusIndicatorBorder != null && StatusIndicatorText != null)
                {
                    if (completionPercentage >= 100)
                    {
                        StatusIndicatorBorder.Background = new SolidColorBrush(Color.FromRgb(76, 175, 80)); // أخضر
                        StatusIndicatorText.Text = "✅ مدفوعة بالكامل";
                    }
                    else if (completionPercentage >= 75)
                    {
                        StatusIndicatorBorder.Background = new SolidColorBrush(Color.FromRgb(139, 195, 74)); // أخضر فاتح
                        StatusIndicatorText.Text = $"🟢 مدفوعة تقريباً ({completionPercentage:F1}%)";
                    }
                    else if (completionPercentage >= 50)
                    {
                        StatusIndicatorBorder.Background = new SolidColorBrush(Color.FromRgb(255, 193, 7)); // أصفر
                        StatusIndicatorText.Text = $"🟡 مدفوعة جزئياً ({completionPercentage:F1}%)";
                    }
                    else if (completionPercentage > 0)
                    {
                        StatusIndicatorBorder.Background = new SolidColorBrush(Color.FromRgb(255, 152, 0)); // برتقالي
                        StatusIndicatorText.Text = $"🟠 بداية الدفع ({completionPercentage:F1}%)";
                    }
                    else
                    {
                        StatusIndicatorBorder.Background = new SolidColorBrush(Color.FromRgb(244, 67, 54)); // أحمر
                        StatusIndicatorText.Text = "🔴 لم يتم الدفع";
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating status indicator: {ex.Message}");
            }
        }

        private async Task LoadPayments()
        {
            try
            {
                // Show loading status
                if (StatusTextBlock != null)
                    StatusTextBlock.Text = "جاري تحميل الدفعات...";

                var allPayments = await DatabaseService.Instance.GetPaymentsAsync();

                // Debug: Log all payments for this property
                var allPropertyPayments = allPayments.Where(p => p.PropertyId == _property.Id).ToList();
                System.Diagnostics.Debug.WriteLine($"PropertyDetailsWindow - Total payments for property {_property.Id}: {allPropertyPayments.Count}");
                System.Diagnostics.Debug.WriteLine($"PropertyDetailsWindow - Selected year: {_selectedYear}");

                foreach (var p in allPropertyPayments)
                {
                    System.Diagnostics.Debug.WriteLine($"Payment ID: {p.Id}, TaxYear: {p.TaxYear}, PaymentDate: {p.PaymentDate}, CleaningTax: {p.CleaningTaxAmount}");
                }

                var propertyPayments = allPayments
                    .Where(p => p.PropertyId == _property.Id &&
                               ((p.TaxYear.HasValue && p.TaxYear.Value == _selectedYear) ||
                                (!p.TaxYear.HasValue && p.PaymentDate?.Year == _selectedYear)) &&
                               p.CleaningTaxAmount > 0 &&
                               p.Status == PaymentStatus.Paid)
                    .OrderByDescending(p => p.PaymentDate)
                    .ToList();

                System.Diagnostics.Debug.WriteLine($"PropertyDetailsWindow - Filtered payments for year {_selectedYear}: {propertyPayments.Count}");

                // Clear existing data to prevent duplication
                _allPayments.Clear();

                foreach (var payment in propertyPayments)
                {
                    // حساب المبلغ المتبقي قبل وبعد هذه الدفعة
                    var remainingBefore = await _calculationService.CalculateRemainingAmountBeforePaymentAsync(
                        _property.Id, _selectedYear, payment.PaymentDate ?? DateTime.Now, payment.Id);

                    var remainingAfter = await _calculationService.CalculateRemainingAmountAfterPaymentAsync(
                        _property.Id, _selectedYear, payment.PaymentDate ?? DateTime.Now,
                        payment.CleaningTaxAmount, payment.Id);

                    var displayInfo = new PaymentDisplayInfo
                    {
                        Payment = payment,
                        PaymentDateDisplay = payment.PaymentDate?.ToString("dd/MM/yyyy") ?? "غير محدد",
                        CleaningTaxAmountDisplay = $"{payment.CleaningTaxAmount:N0} درهم",
                        AmountInWords = NumberToArabicWords.ConvertCurrencyToWords(payment.CleaningTaxAmount),
                        PaymentMethod = payment.PaymentMethod ?? "نقداً",
                        StatusDisplay = GetStatusDisplay(payment.Status),
                        Notes = payment.Notes ?? "",
                        ReceiptNumber = payment.ReceiptNumber ?? "", // رقم التوصيل الجديد
                        RemainingBeforePayment = remainingBefore,
                        RemainingAfterPayment = remainingAfter
                        // Note: RemainingBeforeDisplay, RemainingAfterDisplay, and PaymentStatusText are computed properties
                    };

                    _allPayments.Add(displayInfo);
                }

                // Apply filters and sorting to new data
                ApplyFiltersAndSort();

                // Debug: Check final state
                System.Diagnostics.Debug.WriteLine($"PropertyDetailsWindow - Final _allPayments count: {_allPayments.Count}");
                System.Diagnostics.Debug.WriteLine($"PropertyDetailsWindow - PaymentsDataGrid ItemsSource: {PaymentsDataGrid?.ItemsSource}");
                System.Diagnostics.Debug.WriteLine($"PropertyDetailsWindow - _paymentsView count: {_paymentsView?.Cast<object>().Count()}");

                // Update status
                if (StatusTextBlock != null)
                    StatusTextBlock.Text = $"تم تحميل {_allPayments.Count} دفعة للسنة {_selectedYear}";
            }
            catch (Exception ex)
            {
                if (StatusTextBlock != null)
                    StatusTextBlock.Text = "خطأ في تحميل الدفعات";

                MessageBox.Show($"خطأ في تحميل الدفعات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);

                // Initialize empty collection to prevent further errors
                _allPayments = new ObservableCollection<PaymentDisplayInfo>();
                ApplyFiltersAndSort();
            }
        }

        private void ApplyFiltersAndSort()
        {
            try
            {
                // Initialize if null
                if (_allPayments == null || _paymentsView == null)
                {
                    UpdateDataGridAndStatistics();
                    return;
                }

                // Clear existing filters
                _paymentsView.Filter = null;

                // Apply combined filter
                _paymentsView.Filter = item =>
                {
                    if (item is not PaymentDisplayInfo payment) return false;

                    // Apply year filter - filter by selected year from ComboBox
                    if (YearComboBox?.SelectedItem != null)
                    {
                        var selectedYear = (int)YearComboBox.SelectedItem;
                        // Use TaxYear if available, otherwise fall back to PaymentDate.Year
                        var paymentYear = payment.Payment.TaxYear ?? payment.Payment.PaymentDate?.Year;
                        if (paymentYear != selectedYear)
                            return false;
                    }

                    // Apply current year filter if enabled
                    if (_showOnlyCurrentYear)
                    {
                        var currentYear = DateTime.Now.Year;
                        // Use TaxYear if available, otherwise fall back to PaymentDate.Year
                        var paymentYear = payment.Payment.TaxYear ?? payment.Payment.PaymentDate?.Year;
                        if (paymentYear != currentYear)
                            return false;
                    }

                    // Apply status filter - check if control exists first
                    if (StatusFilterComboBox != null)
                    {
                        var statusFilter = StatusFilterComboBox.SelectedItem as ComboBoxItem;
                        if (statusFilter?.Tag != null)
                        {
                            var filterTag = statusFilter.Tag.ToString();
                            var statusMatch = filterTag switch
                            {
                                "Complete" => payment.RemainingAfterPayment == 0,
                                "Partial" => payment.RemainingAfterPayment > 0,
                                "Paid" => payment.Payment.Status == PaymentStatus.Paid,
                                "Unpaid" => payment.Payment.Status == PaymentStatus.Unpaid,
                                _ => true
                            };
                            if (!statusMatch) return false;
                        }
                    }

                    // Apply period filter - check if control exists first
                    if (PeriodFilterComboBox != null)
                    {
                        var periodFilter = PeriodFilterComboBox.SelectedItem as ComboBoxItem;
                        if (periodFilter?.Content.ToString() != "كل الفترات")
                        {
                            var now = DateTime.Now;
                            var periodMatch = payment.Payment.PaymentDate.HasValue && periodFilter.Content.ToString() switch
                            {
                                "آخر 30 يوم" => payment.Payment.PaymentDate.Value >= now.AddDays(-30),
                                "آخر 3 أشهر" => payment.Payment.PaymentDate.Value >= now.AddMonths(-3),
                                "آخر 6 أشهر" => payment.Payment.PaymentDate.Value >= now.AddMonths(-6),
                                "السنة الحالية" => payment.Payment.PaymentDate.Value.Year == now.Year,
                                _ => true
                            };
                            if (!periodMatch) return false;
                        }
                    }

                    // Apply search filter - check if control exists first
                    if (SearchTextBox != null)
                    {
                        var searchText = SearchTextBox.Text?.Trim().ToLower();
                        if (!string.IsNullOrEmpty(searchText))
                        {
                            var searchMatch = payment.PaymentDateDisplay.ToLower().Contains(searchText) ||
                                payment.CleaningTaxAmountDisplay.ToLower().Contains(searchText) ||
                                payment.ReceiptNumber.ToLower().Contains(searchText) ||
                                payment.PaymentMethod.ToLower().Contains(searchText) ||
                                payment.Notes.ToLower().Contains(searchText) ||
                                payment.PaymentStatusText.ToLower().Contains(searchText);
                            if (!searchMatch) return false;
                        }
                    }

                    return true;
                };

                // Apply sorting with improved logic
                ApplySorting();

                // Update UI and statistics
                UpdateDataGridAndStatistics();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in ApplyFiltersAndSort: {ex.Message}");
                UpdateDataGridAndStatistics();
            }
        }

        private void ApplySorting()
        {
            try
            {
                if (SortComboBox?.SelectedIndex >= 0 && _paymentsView != null)
                {
                    // Clear existing sort descriptions
                    _paymentsView.SortDescriptions.Clear();

                    var selectedSort = SortComboBox.SelectedIndex;
                    switch (selectedSort)
                    {
                        case 0: // التاريخ (الأحدث أولاً)
                            _paymentsView.SortDescriptions.Add(new SortDescription("Payment.PaymentDate", ListSortDirection.Descending));
                            break;
                        case 1: // التاريخ (الأقدم أولاً)
                            _paymentsView.SortDescriptions.Add(new SortDescription("Payment.PaymentDate", ListSortDirection.Ascending));
                            break;
                        case 2: // المبلغ (الأكبر أولاً)
                            _paymentsView.SortDescriptions.Add(new SortDescription("Payment.CleaningTaxAmount", ListSortDirection.Descending));
                            break;
                        case 3: // المبلغ (الأصغر أولاً)
                            _paymentsView.SortDescriptions.Add(new SortDescription("Payment.CleaningTaxAmount", ListSortDirection.Ascending));
                            break;
                        case 4: // رقم التوصيل
                            _paymentsView.SortDescriptions.Add(new SortDescription("ReceiptNumber", ListSortDirection.Ascending));
                            break;
                        case 5: // حالة الدفع
                            _paymentsView.SortDescriptions.Add(new SortDescription("PaymentStatusText", ListSortDirection.Ascending));
                            break;
                        default: // Default: التاريخ (الأحدث أولاً)
                            _paymentsView.SortDescriptions.Add(new SortDescription("Payment.PaymentDate", ListSortDirection.Descending));
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in ApplySorting: {ex.Message}");
            }
        }

        private void UpdateDataGridAndStatistics()
        {
            try
            {
                // Update DataGrid with CollectionView
                if (PaymentsDataGrid != null && _paymentsView != null)
                {
                    // Clear current selection to prevent issues
                    PaymentsDataGrid.SelectedItem = null;

                    // Set ItemsSource to the CollectionView
                    PaymentsDataGrid.ItemsSource = _paymentsView;

                    // Refresh the view
                    _paymentsView.Refresh();
                }

                // Update all statistics
                UpdateFilteredStatistics();
                UpdateRecordCount();

                // Update button states
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in UpdateDataGridAndStatistics: {ex.Message}");
            }
        }

        private void UpdateButtonStates()
        {
            try
            {
                var hasData = _paymentsView?.Cast<PaymentDisplayInfo>().Any() == true;
                var hasSelection = PaymentsDataGrid?.SelectedItem != null;

                // Update button states based on data and selection
                if (EditPaymentButton != null)
                    EditPaymentButton.IsEnabled = hasSelection;

                if (DeletePaymentButton != null)
                    DeletePaymentButton.IsEnabled = hasSelection;

                if (PrintReceiptButton != null)
                    PrintReceiptButton.IsEnabled = hasSelection;

                if (PreviewReceiptButton != null)
                    PreviewReceiptButton.IsEnabled = hasSelection;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in UpdateButtonStates: {ex.Message}");
            }
        }

        private void UpdateRecordCount()
        {
            try
            {
                if (RecordCountText != null && _paymentsView != null)
                {
                    var filteredItems = _paymentsView.Cast<PaymentDisplayInfo>().ToList();
                    var count = filteredItems.Count;
                    var totalCount = _allPayments?.Count ?? 0;

                    if (count == totalCount)
                    {
                        RecordCountText.Text = $"{count} دفعة";
                    }
                    else
                    {
                        RecordCountText.Text = $"{count} من {totalCount} دفعة";
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating record count: {ex.Message}");
            }
        }

        private void UpdateFilteredStatistics()
        {
            try
            {
                var filteredPayments = _paymentsView?.Cast<PaymentDisplayInfo>().ToList();
                if (filteredPayments != null && filteredPayments.Any())
                {
                    var totalFiltered = filteredPayments.Sum(p => p.Payment.CleaningTaxAmount);
                    var countFiltered = filteredPayments.Count;
                    var averageAmount = totalFiltered / countFiltered;
                    var lastPayment = filteredPayments.OrderByDescending(p => p.Payment.PaymentDate).FirstOrDefault();

                    // Update footer statistics
                    if (TotalPaymentsCountText != null)
                        TotalPaymentsCountText.Text = countFiltered.ToString();

                    if (TotalAmountText != null)
                        TotalAmountText.Text = $"{totalFiltered:N0} درهم";

                    if (AverageAmountText != null)
                        AverageAmountText.Text = $"{averageAmount:N0} درهم";

                    if (LastPaymentText != null)
                        LastPaymentText.Text = lastPayment?.PaymentDateDisplay ?? "لا توجد";
                }
                else
                {
                    // Reset statistics when no data
                    if (TotalPaymentsCountText != null)
                        TotalPaymentsCountText.Text = "0";

                    if (TotalAmountText != null)
                        TotalAmountText.Text = "0 درهم";

                    if (AverageAmountText != null)
                        AverageAmountText.Text = "0 درهم";

                    if (LastPaymentText != null)
                        LastPaymentText.Text = "لا توجد";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating statistics: {ex.Message}");
            }
        }

        private string GetPaymentMethodText(decimal option)
        {
            return option switch
            {
                1 => "شهرياً مع الإيجار",
                2 => "نصف سنوي (مرتين في السنة)",
                3 => "ثلث سنوي (3 مرات في السنة)",
                4 => "ربع سنوي (4 مرات في السنة)",
                12 => "دفعة واحدة سنوياً",
                _ => "غير محدد"
            };
        }

        private string GetStatusDisplay(PaymentStatus status)
        {
            return status switch
            {
                PaymentStatus.Paid => "مدفوع",
                PaymentStatus.Unpaid => "غير مدفوع",
                PaymentStatus.Overdue => "متأخر",
                _ => "غير محدد"
            };
        }

        private string GetPaymentStatusText(PaymentStatus status)
        {
            return status switch
            {
                PaymentStatus.Paid => "✅ مدفوع",
                PaymentStatus.Unpaid => "❌ غير مدفوع",
                PaymentStatus.Overdue => "⏰ متأخر",
                _ => "❓ غير محدد"
            };
        }

        /// <summary>
        /// Refresh all data for the selected year
        /// </summary>
        private async Task RefreshDataAsync()
        {
            try
            {
                // Show loading indicator
                if (StatusTextBlock != null)
                    StatusTextBlock.Text = "جاري تحميل البيانات...";

                // Load payments for the selected year
                await LoadPaymentsAsync();

                // Load tax summary and statistics
                await LoadTaxSummaryAsync();

                // Load advanced statistics
                LoadAdvancedStatistics();

                // Update data grid and all statistics
                UpdateDataGridAndStatistics();

                // Update property information
                LoadPropertyData();

                // Update status
                if (StatusTextBlock != null)
                    StatusTextBlock.Text = $"تم تحديث جميع البيانات للسنة {_selectedYear}";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in RefreshDataAsync: {ex.Message}");
                if (StatusTextBlock != null)
                    StatusTextBlock.Text = "خطأ في تحميل البيانات";

                MessageBox.Show($"خطأ في تحديث البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Load payments for the selected year (simplified version)
        /// </summary>
        private async Task LoadPaymentsAsync()
        {
            try
            {
                // Check if required objects are initialized
                if (_allPayments == null)
                {
                    System.Diagnostics.Debug.WriteLine("LoadPaymentsAsync: _allPayments not initialized");
                    return;
                }

                // Clear existing data
                _allPayments.Clear();

                // Get all payments for this property and selected year
                var allPayments = await DatabaseService.Instance.GetPaymentsAsync();
                var filteredPayments = allPayments
                    .Where(p => p.PropertyId == _property.Id &&
                               ((p.TaxYear.HasValue && p.TaxYear.Value == _selectedYear) ||
                                (!p.TaxYear.HasValue && p.PaymentDate?.Year == _selectedYear)) &&
                               p.CleaningTaxAmount > 0 &&
                               p.Status == PaymentStatus.Paid)
                    .OrderByDescending(p => p.PaymentDate)
                    .ToList();

                System.Diagnostics.Debug.WriteLine($"LoadPaymentsAsync - Found {filteredPayments.Count} payments for year {_selectedYear}");

                // Add to ObservableCollection
                foreach (var payment in filteredPayments)
                {
                    // حساب المبلغ المتبقي قبل وبعد هذه الدفعة
                    var remainingBefore = await _calculationService.CalculateRemainingAmountBeforePaymentAsync(
                        _property.Id, _selectedYear, payment.PaymentDate ?? DateTime.Now, payment.Id);

                    var remainingAfter = await _calculationService.CalculateRemainingAmountAfterPaymentAsync(
                        _property.Id, _selectedYear, payment.PaymentDate ?? DateTime.Now,
                        payment.CleaningTaxAmount, payment.Id);

                    var displayInfo = new PaymentDisplayInfo
                    {
                        Payment = payment,
                        PaymentDateDisplay = payment.PaymentDate?.ToString("dd/MM/yyyy") ?? "غير محدد",
                        CleaningTaxAmountDisplay = $"{payment.CleaningTaxAmount:N0} درهم",
                        AmountInWords = NumberToArabicWords.ConvertCurrencyToWords(payment.CleaningTaxAmount),
                        PaymentMethod = payment.PaymentMethod ?? "نقداً",
                        StatusDisplay = GetStatusDisplay(payment.Status),
                        Notes = payment.Notes ?? "",
                        ReceiptNumber = payment.ReceiptNumber ?? "",
                        RemainingBeforePayment = remainingBefore,
                        RemainingAfterPayment = remainingAfter
                    };
                    _allPayments.Add(displayInfo);
                }

                System.Diagnostics.Debug.WriteLine($"LoadPaymentsAsync - Added {_allPayments.Count} items to collection");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in LoadPaymentsAsync: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Load tax summary for the selected year (comprehensive version)
        /// </summary>
        private async Task LoadTaxSummaryAsync()
        {
            try
            {
                // Calculate comprehensive tax summary
                var annualTax = _property.MonthlyRent * 12 * 0.105m;
                var totalPaid = _allPayments.Sum(p => p.Payment.CleaningTaxAmount);
                var remainingAmount = annualTax - totalPaid;
                var paymentCount = _allPayments.Count;
                var averagePayment = paymentCount > 0 ? totalPaid / paymentCount : 0;

                // Get last payment info
                var lastPayment = _allPayments.OrderByDescending(p => p.Payment.PaymentDate).FirstOrDefault();
                var lastPaymentDate = lastPayment?.Payment.PaymentDate?.ToString("dd/MM/yyyy") ?? "لا توجد دفعات";
                var lastPaymentAmount = lastPayment?.Payment.CleaningTaxAmount ?? 0;

                // Update UI elements (using existing elements only)
                // العناصر المطلوبة غير موجودة في XAML، سنستخدم العناصر الموجودة فقط

                // Update payment status in status text
                string paymentStatus;
                if (remainingAmount <= 0)
                    paymentStatus = "✅ مدفوعة بالكامل";
                else if (totalPaid > 0)
                    paymentStatus = $"⚠️ مدفوعة جزئياً ({(totalPaid / annualTax * 100):F1}%)";
                else
                    paymentStatus = "❌ غير مدفوعة";

                if (StatusTextBlock != null)
                    StatusTextBlock.Text = $"الحالة: {paymentStatus} - المدفوع: {totalPaid:N2} درهم من أصل {annualTax:N2} درهم";

                System.Diagnostics.Debug.WriteLine($"LoadTaxSummaryAsync - Tax: {annualTax}, Paid: {totalPaid}, Remaining: {remainingAmount}, Count: {paymentCount}, Average: {averagePayment}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in LoadTaxSummaryAsync: {ex.Message}");
                throw;
            }
        }

        // Event Handlers
        private async void YearComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (YearComboBox.SelectedItem != null && IsLoaded)
                {
                    var newYear = (int)YearComboBox.SelectedItem;

                    // Only update if year actually changed
                    if (newYear != _selectedYear)
                    {
                        _selectedYear = newYear;

                        // Show loading indicator
                        if (StatusTextBlock != null)
                            StatusTextBlock.Text = "جاري تحديث البيانات...";

                        // Clear existing data first to prevent mixing
                        _allPayments.Clear();

                        // Clear any existing filters
                        if (_paymentsView != null)
                        {
                            _paymentsView.Filter = null;
                            _paymentsView.SortDescriptions.Clear();
                        }

                        // Update data using simplified method
                        await RefreshDataAsync();

                        // Update status
                        if (StatusTextBlock != null)
                            StatusTextBlock.Text = $"تم تحديث البيانات للسنة {_selectedYear}";
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in YearComboBox_SelectionChanged: {ex.Message}");
                if (StatusTextBlock != null)
                    StatusTextBlock.Text = "خطأ في تحديث البيانات";

                MessageBox.Show($"خطأ في تحديث البيانات للسنة المحددة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SortComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                // Only apply filters if the window is fully loaded and we have data
                if (IsLoaded && _allPayments != null && SortComboBox?.SelectedIndex >= 0)
                {
                    // Show sorting status
                    if (StatusTextBlock != null)
                        StatusTextBlock.Text = "جاري ترتيب البيانات...";

                    // Apply filters and sorting
                    ApplyFiltersAndSort();

                    // Update status with sort info
                    if (StatusTextBlock != null && SortComboBox.SelectedItem != null)
                    {
                        var sortOption = ((ComboBoxItem)SortComboBox.SelectedItem).Content.ToString();
                        StatusTextBlock.Text = $"تم ترتيب البيانات حسب: {sortOption}";
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in SortComboBox_SelectionChanged: {ex.Message}");
                if (StatusTextBlock != null)
                    StatusTextBlock.Text = "خطأ في ترتيب البيانات";
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFiltersAndSort();
        }

        private void StatusFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFiltersAndSort();
        }

        private void PeriodFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFiltersAndSort();
        }

        private void ResetFiltersButton_Click(object sender, RoutedEventArgs e)
        {
            // إعادة تعيين جميع المرشحات
            SortComboBox.SelectedIndex = 0;
            StatusFilterComboBox.SelectedIndex = 0;
            PeriodFilterComboBox.SelectedIndex = 0;
            SearchTextBox.Text = "";
            ShowOnlyCurrentYearCheckBox.IsChecked = false;
            ShowStatisticsCheckBox.IsChecked = true;

            ApplyFiltersAndSort();
        }

        private void ShowOnlyCurrentYearCheckBox_Changed(object sender, RoutedEventArgs e)
        {
            _showOnlyCurrentYear = ShowOnlyCurrentYearCheckBox.IsChecked == true;
            ApplyFiltersAndSort();
        }

        private async void ShowStatisticsCheckBox_Changed(object sender, RoutedEventArgs e)
        {
            _showStatistics = ShowStatisticsCheckBox.IsChecked == true;
            await LoadAdvancedStatistics();
        }

        private void PaymentsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                var isSelected = PaymentsDataGrid.SelectedItem != null;
                var selectedPayment = PaymentsDataGrid.SelectedItem as PaymentDisplayInfo;

                // تفعيل/إلغاء تفعيل الأزرار بناءً على التحديد
                if (EditPaymentButton != null)
                    EditPaymentButton.IsEnabled = isSelected;

                if (DeletePaymentButton != null)
                    DeletePaymentButton.IsEnabled = isSelected;

                if (PrintReceiptButton != null)
                    PrintReceiptButton.IsEnabled = isSelected;

                if (PreviewReceiptButton != null)
                    PreviewReceiptButton.IsEnabled = isSelected;

                // تحديث معلومات إضافية عند التحديد
                if (isSelected && selectedPayment != null)
                {
                    // يمكن إضافة معلومات إضافية هنا مثل عرض تفاصيل الدفعة في status bar
                    UpdateStatusForSelectedPayment(selectedPayment);
                }
                else
                {
                    // إعادة تعيين status bar عند عدم التحديد
                    if (StatusTextBlock != null)
                        StatusTextBlock.Text = "جاهز";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in PaymentsDataGrid_SelectionChanged: {ex.Message}");
            }
        }

        private void UpdateStatusForSelectedPayment(PaymentDisplayInfo selectedPayment)
        {
            try
            {
                if (StatusTextBlock != null && selectedPayment != null)
                {
                    var statusMessage = $"محدد: {selectedPayment.CleaningTaxAmountDisplay} - {selectedPayment.PaymentDateDisplay} - {selectedPayment.ReceiptNumber}";
                    StatusTextBlock.Text = statusMessage;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating status: {ex.Message}");
            }
        }

        private void ThemeToggleButton_Click(object sender, RoutedEventArgs e)
        {
            _isDarkMode = !_isDarkMode;
            ApplyTheme();
        }

        private void ApplyTheme()
        {
            if (_isDarkMode)
            {
                // Apply dark theme
                Background = new SolidColorBrush(Color.FromRgb(33, 33, 33));
                ThemeToggleButton.Content = "☀️ الوضع النهاري";
                // يمكن إضافة المزيد من تخصيصات الوضع الليلي هنا
            }
            else
            {
                // Apply light theme
                Background = new SolidColorBrush(Color.FromRgb(248, 249, 250));
                ThemeToggleButton.Content = "🌙 الوضع الليلي";
            }
        }



        private async void AddPaymentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إنشاء دفعة جديدة
                var newPayment = new Payment
                {
                    PropertyId = _property.Id,
                    PaymentDate = DateTime.Now,
                    Status = PaymentStatus.Paid
                };

                // فتح نافذة إضافة الدفعة
                var currentYear = YearComboBox.SelectedItem != null ? (int)YearComboBox.SelectedItem : DateTime.Now.Year;
                var paymentDialog = new SimpleCleaningTaxPaymentDialog(_property, currentYear);
                paymentDialog.Owner = this;

                if (paymentDialog.ShowDialog() == true)
                {
                    // تحديث البيانات
                    await RefreshDataAsync();

                    // عرض رسالة نجاح مع خيارات إضافية
                    var result = MessageBox.Show(
                        "تم إضافة الدفعة بنجاح!\n\nهل تريد طباعة الوصل الآن؟",
                        "تم بنجاح",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Information);

                    if (result == MessageBoxResult.Yes)
                    {
                        // البحث عن الدفعة المضافة حديثاً وطباعة الوصل
                        var latestPayment = _allPayments.OrderByDescending(p => p.Payment.Id).FirstOrDefault();
                        if (latestPayment != null)
                        {
                            await PrintCleaningTaxReceipt(latestPayment.Payment);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الدفعة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void RefreshDataButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Show loading indicator
                if (RefreshDataButton != null)
                {
                    RefreshDataButton.IsEnabled = false;
                    RefreshDataButton.Content = "⏳ جاري التحديث...";
                }

                // Refresh all data
                await RefreshDataAsync();

                // Show success message briefly
                if (RefreshDataButton != null)
                {
                    RefreshDataButton.Content = "✅ تم التحديث";
                    await Task.Delay(1000);
                    RefreshDataButton.Content = "🔄 تحديث المعلومات";
                    RefreshDataButton.IsEnabled = true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);

                // Reset button state
                if (RefreshDataButton != null)
                {
                    RefreshDataButton.Content = "🔄 تحديث المعلومات";
                    RefreshDataButton.IsEnabled = true;
                }
            }
        }

        private async void EditPaymentButton_Click(object sender, RoutedEventArgs e)
        {
            if (PaymentsDataGrid.SelectedItem is PaymentDisplayInfo selectedPayment)
            {
                try
                {
                    // التأكد من صحة البيانات
                    if (selectedPayment.Payment == null)
                    {
                        MessageBox.Show("خطأ في بيانات الدفعة المحددة", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }

                    // فتح نافذة التعديل
                    var editDialog = new EditPaymentDialog(selectedPayment.Payment, _property);
                    editDialog.Owner = this;

                    if (editDialog.ShowDialog() == true && editDialog.IsDataChanged)
                    {
                        // تحديث البيانات بعد التعديل
                        await RefreshDataAsync();

                        // عرض رسالة نجاح مع خيارات إضافية
                        var result = MessageBox.Show(
                            "تم تحديث الدفعة بنجاح!\n\nهل تريد طباعة الوصل المحدث؟",
                            "تم التحديث بنجاح",
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Information);

                        if (result == MessageBoxResult.Yes)
                        {
                            await PrintCleaningTaxReceipt(selectedPayment.Payment);
                        }
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تعديل الدفعة: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                MessageBox.Show("يرجى تحديد دفعة للتعديل", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async void DeletePaymentButton_Click(object sender, RoutedEventArgs e)
        {
            if (PaymentsDataGrid.SelectedItem is PaymentDisplayInfo selectedPayment)
            {
                try
                {
                    // التأكد من صحة البيانات
                    if (selectedPayment.Payment == null)
                    {
                        MessageBox.Show("خطأ في بيانات الدفعة المحددة", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }

                    // عرض تفاصيل أكثر في رسالة التأكيد
                    var confirmMessage = $"⚠️ تحذير: هل أنت متأكد من حذف هذه الدفعة؟\n\n" +
                                       $"📋 تفاصيل الدفعة:\n" +
                                       $"💰 المبلغ: {selectedPayment.CleaningTaxAmountDisplay}\n" +
                                       $"📅 التاريخ: {selectedPayment.PaymentDateDisplay}\n" +
                                       $"🧾 رقم التوصيل: {selectedPayment.ReceiptNumber}\n" +
                                       $"💳 طريقة الدفع: {selectedPayment.PaymentMethod}\n\n" +
                                       $"⚠️ تنبيه: لا يمكن التراجع عن هذا الإجراء!";

                    var result = MessageBox.Show(
                        confirmMessage,
                        "تأكيد حذف الدفعة",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        // إظهار مؤشر التحميل
                        Mouse.OverrideCursor = Cursors.Wait;

                        try
                        {
                            await DatabaseService.Instance.DeletePaymentAsync(selectedPayment.Payment.Id);

                            // تحديث البيانات
                            await RefreshDataAsync();

                            // إظهار رسالة نجاح
                            MessageBox.Show(
                                $"✅ تم حذف الدفعة بنجاح!\n\n" +
                                $"المبلغ المحذوف: {selectedPayment.CleaningTaxAmountDisplay}\n" +
                                $"رقم التوصيل: {selectedPayment.ReceiptNumber}",
                                "تم الحذف بنجاح",
                                MessageBoxButton.OK,
                                MessageBoxImage.Information);
                        }
                        finally
                        {
                            Mouse.OverrideCursor = null;
                        }
                    }
                }
                catch (Exception ex)
                {
                    Mouse.OverrideCursor = null;
                    MessageBox.Show($"❌ خطأ في حذف الدفعة:\n\n{ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                MessageBox.Show("يرجى تحديد دفعة للحذف", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async void PrintReceiptButton_Click(object sender, RoutedEventArgs e)
        {
            if (PaymentsDataGrid.SelectedItem is PaymentDisplayInfo selectedPayment)
            {
                try
                {
                    // التأكد من صحة البيانات
                    if (selectedPayment.Payment == null)
                    {
                        MessageBox.Show("خطأ في بيانات الدفعة المحددة", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }

                    // إظهار مؤشر التحميل
                    Mouse.OverrideCursor = Cursors.Wait;

                    try
                    {
                        await PrintCleaningTaxReceipt(selectedPayment.Payment);
                    }
                    finally
                    {
                        Mouse.OverrideCursor = null;
                    }
                }
                catch (Exception ex)
                {
                    Mouse.OverrideCursor = null;
                    MessageBox.Show($"❌ خطأ في طباعة الوصل:\n\n{ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                MessageBox.Show("يرجى تحديد دفعة لطباعة الوصل", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async void PreviewReceiptButton_Click(object sender, RoutedEventArgs e)
        {
            if (PaymentsDataGrid.SelectedItem is PaymentDisplayInfo selectedPayment)
            {
                try
                {
                    // التأكد من صحة البيانات
                    if (selectedPayment.Payment == null)
                    {
                        MessageBox.Show("خطأ في بيانات الدفعة المحددة", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }

                    // إظهار مؤشر التحميل
                    Mouse.OverrideCursor = Cursors.Wait;

                    try
                    {
                        // فتح نافذة معاينة الوصل (سيتم تنفيذها لاحقاً)
                        MessageBox.Show("ميزة معاينة الوصل ستكون متاحة قريباً", "قيد التطوير",
                            MessageBoxButton.OK, MessageBoxImage.Information);

                        // TODO: إضافة نافذة معاينة الوصل
                        // var previewDialog = new ReceiptPreviewDialog(selectedPayment.Payment, _property, isCleaningTax: true);
                        // previewDialog.Owner = this;
                        // previewDialog.ShowDialog();
                    }
                    finally
                    {
                        Mouse.OverrideCursor = null;
                    }
                }
                catch (Exception ex)
                {
                    Mouse.OverrideCursor = null;
                    MessageBox.Show($"❌ خطأ في معاينة الوصل:\n\n{ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                MessageBox.Show("يرجى تحديد دفعة لمعاينة الوصل", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async Task PrintCleaningTaxReceipt(Payment payment)
        {
            try
            {
                var printService = CleaningTaxPrintService.Instance;
                var success = await printService.PrintReceiptFromPaymentAsync(payment);

                if (success)
                {
                    MessageBox.Show(
                        $"✅ تم طباعة الوصل بنجاح!\n\n" +
                        $"رقم التوصيل: {payment.ReceiptNumber}\n" +
                        $"المبلغ: {payment.CleaningTaxAmount:N0} درهم\n" +
                        $"التاريخ: {payment.PaymentDate:dd/MM/yyyy}",
                        "تم الطباعة بنجاح",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("فشل في طباعة الوصل. يرجى المحاولة مرة أخرى.", "خطأ في الطباعة",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في خدمة الطباعة: {ex.Message}");
            }
        }

        private void AdvancedSearchButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var allPaymentsList = _allPayments.ToList();
                var advancedSearchDialog = new AdvancedSearchDialog(allPaymentsList);
                advancedSearchDialog.Owner = this;

                if (advancedSearchDialog.ShowDialog() == true && advancedSearchDialog.HasSearchCriteria)
                {
                    // Clear existing collection and add filtered results
                    _allPayments.Clear();
                    foreach (var payment in advancedSearchDialog.FilteredPayments)
                    {
                        _allPayments.Add(payment);
                    }

                    // Apply filters and sorting
                    ApplyFiltersAndSort();

                    // Show search results summary
                    var filteredItems = _paymentsView?.Cast<PaymentDisplayInfo>().ToList() ?? new List<PaymentDisplayInfo>();
                    var count = filteredItems.Count;
                    var totalAmount = filteredItems.Sum(p => p.Payment.CleaningTaxAmount);

                    MessageBox.Show(
                        $"نتائج البحث المتقدم:\n\n" +
                        $"عدد الدفعات: {count}\n" +
                        $"إجمالي المبلغ: {totalAmount:N0} درهم\n\n" +
                        $"تم تطبيق المرشحات على الجدول",
                        "نتائج البحث المتقدم",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث المتقدم: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void GenerateDetailedReportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var reportDialog = new DetailedReportDialog(_property, _allPayments.ToList(), _selectedYear);
                reportDialog.Owner = this;
                reportDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "JSON files (*.json)|*.json|All files (*.*)|*.*",
                    FileName = $"تفاصيل_المحل_{_property.Name}_{_selectedYear}.json"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    var exportData = new
                    {
                        Property = _property,
                        Year = _selectedYear,
                        Payments = _allPayments.ToList(),
                        ExportDate = DateTime.Now
                    };

                    var json = JsonSerializer.Serialize(exportData, new JsonSerializerOptions 
                    { 
                        WriteIndented = true,
                        Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                    });
                    
                    File.WriteAllText(saveDialog.FileName, json);
                    MessageBox.Show("تم تصدير البيانات بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            base.OnClosed(e);
        }
    }

    // PaymentDisplayInfo class is already defined in CleaningTaxDetailsWindow.xaml.cs
}
