using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace SimpleRentalApp.Services.CloudSync.Security
{
    /// <summary>
    /// خدمة التشفير والأمان
    /// </summary>
    public class EncryptionService
    {
        private readonly string _encryptionKey;
        private readonly byte[] _salt;

        public EncryptionService(string encryptionKey)
        {
            _encryptionKey = encryptionKey ?? throw new ArgumentNullException(nameof(encryptionKey));
            _salt = Encoding.UTF8.GetBytes("RentalAppSalt2024"); // يجب أن يكون فريد لكل تطبيق
        }

        /// <summary>
        /// تشفير النص
        /// </summary>
        /// <param name="plainText">النص المراد تشفيره</param>
        /// <returns>النص المشفر</returns>
        public async Task<string> EncryptAsync(string plainText)
        {
            if (string.IsNullOrEmpty(plainText))
                return string.Empty;

            try
            {
                using var aes = Aes.Create();
                var key = DeriveKeyFromPassword(_encryptionKey, _salt);
                aes.Key = key;
                aes.GenerateIV();

                using var encryptor = aes.CreateEncryptor();
                using var msEncrypt = new MemoryStream();
                
                // كتابة IV في بداية البيانات المشفرة
                await msEncrypt.WriteAsync(aes.IV, 0, aes.IV.Length);
                
                using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                using (var swEncrypt = new StreamWriter(csEncrypt))
                {
                    await swEncrypt.WriteAsync(plainText);
                }

                return Convert.ToBase64String(msEncrypt.ToArray());
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"فشل في تشفير البيانات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// فك تشفير النص
        /// </summary>
        /// <param name="cipherText">النص المشفر</param>
        /// <returns>النص الأصلي</returns>
        public async Task<string> DecryptAsync(string cipherText)
        {
            if (string.IsNullOrEmpty(cipherText))
                return string.Empty;

            try
            {
                var cipherBytes = Convert.FromBase64String(cipherText);
                
                using var aes = Aes.Create();
                var key = DeriveKeyFromPassword(_encryptionKey, _salt);
                aes.Key = key;

                using var msDecrypt = new MemoryStream(cipherBytes);
                
                // قراءة IV من بداية البيانات المشفرة
                var iv = new byte[aes.IV.Length];
                await msDecrypt.ReadAsync(iv, 0, iv.Length);
                aes.IV = iv;

                using var decryptor = aes.CreateDecryptor();
                using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
                using var srDecrypt = new StreamReader(csDecrypt);
                
                return await srDecrypt.ReadToEndAsync();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"فشل في فك تشفير البيانات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إنشاء hash للبيانات
        /// </summary>
        /// <param name="data">البيانات</param>
        /// <returns>Hash</returns>
        public string CreateHash(string data)
        {
            if (string.IsNullOrEmpty(data))
                return string.Empty;

            using var sha256 = SHA256.Create();
            var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(data));
            return Convert.ToBase64String(hashBytes);
        }

        /// <summary>
        /// التحقق من صحة Hash
        /// </summary>
        /// <param name="data">البيانات الأصلية</param>
        /// <param name="hash">Hash المراد التحقق منه</param>
        /// <returns>صحيح إذا كان Hash صحيح</returns>
        public bool VerifyHash(string data, string hash)
        {
            var computedHash = CreateHash(data);
            return string.Equals(computedHash, hash, StringComparison.Ordinal);
        }

        /// <summary>
        /// إنشاء مفتاح تشفير عشوائي
        /// </summary>
        /// <returns>مفتاح التشفير</returns>
        public static string GenerateEncryptionKey()
        {
            using var rng = RandomNumberGenerator.Create();
            var keyBytes = new byte[32]; // 256-bit key
            rng.GetBytes(keyBytes);
            return Convert.ToBase64String(keyBytes);
        }

        /// <summary>
        /// إنشاء معرف جهاز فريد
        /// </summary>
        /// <returns>معرف الجهاز</returns>
        public static string GenerateDeviceId()
        {
            var machineName = Environment.MachineName;
            var userName = Environment.UserName;
            var osVersion = Environment.OSVersion.ToString();
            
            var deviceInfo = $"{machineName}-{userName}-{osVersion}";
            
            using var sha256 = SHA256.Create();
            var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(deviceInfo));
            return Convert.ToBase64String(hashBytes)[..16]; // أخذ أول 16 حرف
        }

        /// <summary>
        /// اشتقاق مفتاح من كلمة المرور
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <param name="salt">الملح</param>
        /// <returns>المفتاح المشتق</returns>
        private static byte[] DeriveKeyFromPassword(string password, byte[] salt)
        {
            using var pbkdf2 = new Rfc2898DeriveBytes(password, salt, 10000, HashAlgorithmName.SHA256);
            return pbkdf2.GetBytes(32); // 256-bit key
        }

        /// <summary>
        /// تشفير كلمة المرور
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <returns>كلمة المرور المشفرة</returns>
        public static string HashPassword(string password)
        {
            if (string.IsNullOrEmpty(password))
                return string.Empty;

            using var sha256 = SHA256.Create();
            var saltedPassword = password + "RentalAppSalt2024";
            var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(saltedPassword));
            return Convert.ToBase64String(hashBytes);
        }

        /// <summary>
        /// التحقق من كلمة المرور
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <param name="hashedPassword">كلمة المرور المشفرة</param>
        /// <returns>صحيح إذا كانت كلمة المرور صحيحة</returns>
        public static bool VerifyPassword(string password, string hashedPassword)
        {
            var computedHash = HashPassword(password);
            return string.Equals(computedHash, hashedPassword, StringComparison.Ordinal);
        }

        /// <summary>
        /// إنشاء توقيع رقمي للبيانات
        /// </summary>
        /// <param name="data">البيانات</param>
        /// <param name="privateKey">المفتاح الخاص</param>
        /// <returns>التوقيع الرقمي</returns>
        public string CreateDigitalSignature(string data, string privateKey)
        {
            var dataBytes = Encoding.UTF8.GetBytes(data);
            var keyBytes = Convert.FromBase64String(privateKey);
            
            using var hmac = new HMACSHA256(keyBytes);
            var signatureBytes = hmac.ComputeHash(dataBytes);
            return Convert.ToBase64String(signatureBytes);
        }

        /// <summary>
        /// التحقق من التوقيع الرقمي
        /// </summary>
        /// <param name="data">البيانات الأصلية</param>
        /// <param name="signature">التوقيع</param>
        /// <param name="privateKey">المفتاح الخاص</param>
        /// <returns>صحيح إذا كان التوقيع صحيح</returns>
        public bool VerifyDigitalSignature(string data, string signature, string privateKey)
        {
            var computedSignature = CreateDigitalSignature(data, privateKey);
            return string.Equals(computedSignature, signature, StringComparison.Ordinal);
        }
    }
}
