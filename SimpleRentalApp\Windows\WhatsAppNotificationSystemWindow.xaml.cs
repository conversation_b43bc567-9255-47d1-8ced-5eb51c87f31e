using Microsoft.EntityFrameworkCore;
using SimpleRentalApp.Data;
using SimpleRentalApp.Models;
using SimpleRentalApp.Services;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;

namespace SimpleRentalApp.Windows
{
    /// <summary>
    /// نافذة نظام إشعارات الواتساب الحقيقي
    /// </summary>
    public partial class WhatsAppNotificationSystemWindow : Window
    {
        private readonly RentalDbContext _context;
        private readonly RealWhatsAppNotificationService _notificationService;
        private readonly NotificationTemplateService _templateService;
        private readonly WhatsAppSenderService _whatsAppSender;
        
        private ObservableCollection<WhatsAppNotificationDisplayInfo> _allNotifications;
        private ObservableCollection<WhatsAppNotificationDisplayInfo> _filteredNotifications;
        private CollectionViewSource _notificationsViewSource;
        private ICollectionView _notificationsView;

        public WhatsAppNotificationSystemWindow()
        {
            try
            {
                InitializeComponent();
                
                _context = new RentalDbContext();
                _notificationService = new RealWhatsAppNotificationService(_context);
                _templateService = new NotificationTemplateService(_context);
                _whatsAppSender = new WhatsAppSenderService();
                
                _allNotifications = new ObservableCollection<WhatsAppNotificationDisplayInfo>();
                _filteredNotifications = new ObservableCollection<WhatsAppNotificationDisplayInfo>();
                
                InitializeFilters();
                LoadNotificationsAsync();
                UpdateStatistics();
                UpdateLastUpdateTime();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة نظام إشعارات الواتساب:\n\n{ex.Message}", 
                    "خطأ في التهيئة", MessageBoxButton.OK, MessageBoxImage.Error);
                
                try { this.Close(); } catch { }
            }
        }

        /// <summary>
        /// تهيئة الفلاتر
        /// </summary>
        private void InitializeFilters()
        {
            try
            {
                // Type Filter
                TypeFilterComboBox.Items.Add(new ComboBoxItem { Content = "جميع الأنواع", Tag = null });
                TypeFilterComboBox.Items.Add(new ComboBoxItem { Content = "🏠 إيجار شهري", Tag = WhatsAppNotificationType.MonthlyRent });
                TypeFilterComboBox.Items.Add(new ComboBoxItem { Content = "🧹 ضريبة النظافة", Tag = WhatsAppNotificationType.CleaningTax });
                TypeFilterComboBox.Items.Add(new ComboBoxItem { Content = "⚠️ تأخير إيجار", Tag = WhatsAppNotificationType.RentOverdue });
                TypeFilterComboBox.Items.Add(new ComboBoxItem { Content = "🚨 تأخير ضريبة", Tag = WhatsAppNotificationType.CleaningTaxOverdue });
                TypeFilterComboBox.Items.Add(new ComboBoxItem { Content = "📋 انتهاء عقد", Tag = WhatsAppNotificationType.ContractExpiry });
                TypeFilterComboBox.SelectedIndex = 0;

                // Status Filter
                StatusFilterComboBox.Items.Add(new ComboBoxItem { Content = "جميع الحالات", Tag = null });
                StatusFilterComboBox.Items.Add(new ComboBoxItem { Content = "⏳ بانتظار الإرسال", Tag = WhatsAppNotificationStatus.Pending });
                StatusFilterComboBox.Items.Add(new ComboBoxItem { Content = "✅ تم الإرسال", Tag = WhatsAppNotificationStatus.Sent });
                StatusFilterComboBox.Items.Add(new ComboBoxItem { Content = "📨 تم التسليم", Tag = WhatsAppNotificationStatus.Delivered });
                StatusFilterComboBox.Items.Add(new ComboBoxItem { Content = "👁️ تم القراءة", Tag = WhatsAppNotificationStatus.Read });
                StatusFilterComboBox.Items.Add(new ComboBoxItem { Content = "❌ فشل الإرسال", Tag = WhatsAppNotificationStatus.Failed });
                StatusFilterComboBox.Items.Add(new ComboBoxItem { Content = "🚫 ملغي", Tag = WhatsAppNotificationStatus.Cancelled });
                StatusFilterComboBox.Items.Add(new ComboBoxItem { Content = "📅 مجدول", Tag = WhatsAppNotificationStatus.Scheduled });
                StatusFilterComboBox.SelectedIndex = 0;

                // Priority Filter
                PriorityFilterComboBox.Items.Add(new ComboBoxItem { Content = "جميع الأولويات", Tag = null });
                PriorityFilterComboBox.Items.Add(new ComboBoxItem { Content = "🔴 عاجل", Tag = WhatsAppNotificationPriority.Urgent });
                PriorityFilterComboBox.Items.Add(new ComboBoxItem { Content = "🟡 عالي", Tag = WhatsAppNotificationPriority.High });
                PriorityFilterComboBox.Items.Add(new ComboBoxItem { Content = "🟢 عادي", Tag = WhatsAppNotificationPriority.Normal });
                PriorityFilterComboBox.Items.Add(new ComboBoxItem { Content = "🔵 منخفض", Tag = WhatsAppNotificationPriority.Low });
                PriorityFilterComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تهيئة الفلاتر: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل الإشعارات
        /// </summary>
        private async void LoadNotificationsAsync()
        {
            try
            {
                StatusTextBlock.Text = "جاري تحميل الإشعارات...";

                var notifications = await _context.WhatsAppNotifications
                    .Include(n => n.Customer)
                    .Include(n => n.Property)
                    .Include(n => n.Contract)
                    .Include(n => n.Template)
                    .OrderByDescending(n => n.CreatedDate)
                    .ToListAsync();

                _allNotifications.Clear();
                
                foreach (var notification in notifications)
                {
                    _allNotifications.Add(new WhatsAppNotificationDisplayInfo(notification));
                }

                ApplyFilters();
                UpdateStatistics();
                StatusTextBlock.Text = $"تم تحميل {_allNotifications.Count} إشعار";
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "فشل في تحميل الإشعارات";
                MessageBox.Show($"خطأ في تحميل الإشعارات:\n\n{ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تطبيق الفلاتر
        /// </summary>
        private void ApplyFilters()
        {
            try
            {
                var filteredList = _allNotifications.AsEnumerable();

                // Type Filter
                if (TypeFilterComboBox.SelectedItem is ComboBoxItem typeItem && typeItem.Tag != null)
                {
                    var selectedType = (WhatsAppNotificationType)typeItem.Tag;
                    filteredList = filteredList.Where(n => n.Type == selectedType);
                }

                // Status Filter
                if (StatusFilterComboBox.SelectedItem is ComboBoxItem statusItem && statusItem.Tag != null)
                {
                    var selectedStatus = (WhatsAppNotificationStatus)statusItem.Tag;
                    filteredList = filteredList.Where(n => n.Status == selectedStatus);
                }

                // Priority Filter
                if (PriorityFilterComboBox.SelectedItem is ComboBoxItem priorityItem && priorityItem.Tag != null)
                {
                    var selectedPriority = (WhatsAppNotificationPriority)priorityItem.Tag;
                    filteredList = filteredList.Where(n => n.Priority == selectedPriority);
                }

                // Search Filter
                if (!string.IsNullOrWhiteSpace(SearchTextBox.Text))
                {
                    var searchTerm = SearchTextBox.Text.ToLower();
                    filteredList = filteredList.Where(n => 
                        n.CustomerName.ToLower().Contains(searchTerm) ||
                        n.PropertyName.ToLower().Contains(searchTerm) ||
                        n.MessageContent.ToLower().Contains(searchTerm) ||
                        n.PhoneNumber.Contains(searchTerm));
                }

                _filteredNotifications.Clear();
                foreach (var item in filteredList)
                {
                    _filteredNotifications.Add(item);
                }

                NotificationsDataGrid.ItemsSource = _filteredNotifications;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق الفلاتر: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث الإحصائيات
        /// </summary>
        private void UpdateStatistics()
        {
            try
            {
                var total = _allNotifications.Count;
                var pending = _allNotifications.Count(n => n.Status == WhatsAppNotificationStatus.Pending);
                var sent = _allNotifications.Count(n => n.Status == WhatsAppNotificationStatus.Sent);
                var failed = _allNotifications.Count(n => n.Status == WhatsAppNotificationStatus.Failed);

                StatisticsTextBlock.Text = $"الإجمالي: {total} | معلق: {pending} | تم الإرسال: {sent} | فشل: {failed}";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الإحصائيات: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث وقت آخر تحديث
        /// </summary>
        private void UpdateLastUpdateTime()
        {
            LastUpdateTextBlock.Text = $"آخر تحديث: {DateTime.Now:HH:mm:ss}";
        }

        // Event Handlers
        private void RefreshBtn_Click(object sender, RoutedEventArgs e)
        {
            LoadNotificationsAsync();
            UpdateLastUpdateTime();
        }

        private void SettingsBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var settingsWindow = new WhatsAppSettingsWindow();
                settingsWindow.Owner = this;
                settingsWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة الإعدادات:\n\n{ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void GenerateMonthlyRentBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                StatusTextBlock.Text = "جاري توليد إشعارات الإيجار الشهري...";

                var currentMonth = DateTime.Now.Month;
                var currentYear = DateTime.Now.Year;

                var result = MessageBox.Show(
                    $"هل تريد توليد إشعارات الإيجار الشهري لشهر {currentMonth}/{currentYear}؟\n\n" +
                    "سيتم إنشاء إشعارات لجميع العقود النشطة.",
                    "توليد إشعارات الإيجار الشهري",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    var notifications = await _notificationService.GenerateMonthlyRentNotificationsAsync(currentMonth, currentYear);
                    
                    MessageBox.Show(
                        $"تم توليد {notifications.Count} إشعار إيجار شهري بنجاح!",
                        "تم التوليد",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);

                    LoadNotificationsAsync();
                }

                StatusTextBlock.Text = "تم الانتهاء من توليد إشعارات الإيجار الشهري";
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "فشل في توليد إشعارات الإيجار الشهري";
                MessageBox.Show($"خطأ في توليد إشعارات الإيجار الشهري:\n\n{ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void GenerateCleaningTaxBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                StatusTextBlock.Text = "جاري توليد إشعارات ضريبة النظافة...";

                var currentYear = DateTime.Now.Year;

                var result = MessageBox.Show(
                    $"هل تريد توليد إشعارات ضريبة النظافة لسنة {currentYear}؟\n\n" +
                    "سيتم إنشاء إشعارات لجميع العقود النشطة.",
                    "توليد إشعارات ضريبة النظافة",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    var notifications = await _notificationService.GenerateCleaningTaxNotificationsAsync(currentYear);
                    
                    MessageBox.Show(
                        $"تم توليد {notifications.Count} إشعار ضريبة نظافة بنجاح!",
                        "تم التوليد",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);

                    LoadNotificationsAsync();
                }

                StatusTextBlock.Text = "تم الانتهاء من توليد إشعارات ضريبة النظافة";
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "فشل في توليد إشعارات ضريبة النظافة";
                MessageBox.Show($"خطأ في توليد إشعارات ضريبة النظافة:\n\n{ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void GenerateOverdueBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                StatusTextBlock.Text = "جاري توليد إشعارات التأخير...";

                var result = MessageBox.Show(
                    "هل تريد توليد إشعارات التأخير للدفعات المستحقة؟\n\n" +
                    "سيتم البحث عن جميع الإشعارات المتأخرة وإنشاء تذكيرات لها.",
                    "توليد إشعارات التأخير",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    var notifications = await _notificationService.GenerateOverdueNotificationsAsync();
                    
                    MessageBox.Show(
                        $"تم توليد {notifications.Count} إشعار تأخير بنجاح!",
                        "تم التوليد",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);

                    LoadNotificationsAsync();
                }

                StatusTextBlock.Text = "تم الانتهاء من توليد إشعارات التأخير";
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "فشل في توليد إشعارات التأخير";
                MessageBox.Show($"خطأ في توليد إشعارات التأخير:\n\n{ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void MessageTemplatesBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var templatesWindow = new MessageTemplatesWindow();
                templatesWindow.Owner = this;
                templatesWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة قوالب الرسائل:\n\n{ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // Filter Event Handlers
        private void TypeFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void StatusFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void PriorityFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFilters();
        }

        // Action Button Event Handlers
        private async void PreviewBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is WhatsAppNotificationDisplayInfo notification)
                {
                    var previewDialog = new MessagePreviewDialog(notification, notification.MessageContent, _templateService);
                    previewDialog.Owner = this;
                    previewDialog.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معاينة الرسالة:\n\n{ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SendBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is WhatsAppNotificationDisplayInfo notification)
                {
                    StatusTextBlock.Text = "جاري إرسال الإشعار...";
                    button.IsEnabled = false;

                    bool success = await _notificationService.SendNotificationAsync(notification.Id);

                    if (success)
                    {
                        StatusTextBlock.Text = "تم إرسال الإشعار بنجاح! ✅";
                        LoadNotificationsAsync();
                    }
                    else
                    {
                        StatusTextBlock.Text = "فشل في إرسال الإشعار ❌";
                    }

                    button.IsEnabled = true;
                }
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = "فشل في إرسال الإشعار ❌";
                MessageBox.Show($"خطأ في إرسال الإشعار:\n\n{ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);

                if (sender is Button btn)
                    btn.IsEnabled = true;
            }
        }

        private void EditBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is WhatsAppNotificationDisplayInfo notification)
                {
                    // TODO: إنشاء نافذة تعديل الإشعار
                    MessageBox.Show("ميزة التعديل قيد التطوير", "معلومات",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل الإشعار:\n\n{ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void DeleteBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is WhatsAppNotificationDisplayInfo notification)
                {
                    var result = MessageBox.Show(
                        $"هل تريد حذف الإشعار للمكتري {notification.CustomerName}؟\n\n" +
                        "لا يمكن التراجع عن هذا الإجراء.",
                        "تأكيد الحذف",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        var notificationEntity = await _context.WhatsAppNotifications
                            .FirstOrDefaultAsync(n => n.Id == notification.Id);

                        if (notificationEntity != null)
                        {
                            _context.WhatsAppNotifications.Remove(notificationEntity);
                            await _context.SaveChangesAsync();

                            LoadNotificationsAsync();
                            StatusTextBlock.Text = "تم حذف الإشعار بنجاح";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الإشعار:\n\n{ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BulkSendBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var selectedNotifications = _filteredNotifications.Where(n => n.IsSelected).ToList();

                if (!selectedNotifications.Any())
                {
                    MessageBox.Show("يرجى تحديد إشعار واحد على الأقل للإرسال", "لا توجد إشعارات محددة",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var result = MessageBox.Show(
                    $"هل تريد إرسال {selectedNotifications.Count} إشعار محدد؟\n\n" +
                    "سيتم إرسال جميع الإشعارات المحددة دفعة واحدة.",
                    "تأكيد الإرسال الجماعي",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    StatusTextBlock.Text = $"جاري إرسال {selectedNotifications.Count} إشعار...";

                    var notificationIds = selectedNotifications.Select(n => n.Id).ToList();
                    var (successCount, failedCount) = await _notificationService.SendMultipleNotificationsAsync(notificationIds);

                    MessageBox.Show(
                        $"تم الإرسال الجماعي!\n\n" +
                        $"✅ نجح: {successCount}\n" +
                        $"❌ فشل: {failedCount}\n" +
                        $"📊 الإجمالي: {selectedNotifications.Count}",
                        "نتائج الإرسال الجماعي",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);

                    LoadNotificationsAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الإرسال الجماعي:\n\n{ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            _notificationService?.Dispose();
            _templateService?.Dispose();
            base.OnClosed(e);
        }
    }

    /// <summary>
    /// نموذج عرض بيانات إشعار الواتساب
    /// </summary>
    public class WhatsAppNotificationDisplayInfo : INotifyPropertyChanged
    {
        private bool _isSelected;

        public WhatsAppNotificationDisplayInfo(WhatsAppNotification notification)
        {
            Id = notification.Id;
            Type = notification.Type;
            CustomerId = notification.CustomerId;
            PropertyId = notification.PropertyId;
            ContractId = notification.ContractId;
            PhoneNumber = notification.PhoneNumber;
            MessageContent = notification.MessageContent;
            Amount = notification.Amount;
            DueDate = notification.DueDate;
            Status = notification.Status;
            Priority = notification.Priority;
            CreatedDate = notification.CreatedDate;
            SentDate = notification.SentDate;
            AttemptCount = notification.AttemptCount;
            ErrorMessage = notification.ErrorMessage;

            CustomerName = notification.Customer?.FullName ?? "غير محدد";
            PropertyName = notification.Property?.Name ?? "غير محدد";
        }

        public int Id { get; set; }
        public WhatsAppNotificationType Type { get; set; }
        public int CustomerId { get; set; }
        public int PropertyId { get; set; }
        public int? ContractId { get; set; }
        public string PhoneNumber { get; set; } = string.Empty;
        public string MessageContent { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public DateTime? DueDate { get; set; }
        public WhatsAppNotificationStatus Status { get; set; }
        public WhatsAppNotificationPriority Priority { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? SentDate { get; set; }
        public int AttemptCount { get; set; }
        public string? ErrorMessage { get; set; }

        public string CustomerName { get; set; }
        public string PropertyName { get; set; }

        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                _isSelected = value;
                OnPropertyChanged(nameof(IsSelected));
            }
        }

        // Display Properties
        public string TypeDisplay => Type switch
        {
            WhatsAppNotificationType.MonthlyRent => "🏠 إيجار شهري",
            WhatsAppNotificationType.CleaningTax => "🧹 ضريبة النظافة",
            WhatsAppNotificationType.RentOverdue => "⚠️ تأخير إيجار",
            WhatsAppNotificationType.CleaningTaxOverdue => "🚨 تأخير ضريبة",
            WhatsAppNotificationType.ContractExpiry => "📋 انتهاء عقد",
            WhatsAppNotificationType.RentIncrease => "📈 زيادة إيجار",
            WhatsAppNotificationType.GeneralReminder => "📌 تذكير عام",
            _ => "غير محدد"
        };

        public string StatusDisplay => Status switch
        {
            WhatsAppNotificationStatus.Pending => "⏳ معلق",
            WhatsAppNotificationStatus.Sent => "✅ تم الإرسال",
            WhatsAppNotificationStatus.Delivered => "📨 تم التسليم",
            WhatsAppNotificationStatus.Read => "👁️ تم القراءة",
            WhatsAppNotificationStatus.Failed => "❌ فشل",
            WhatsAppNotificationStatus.Cancelled => "🚫 ملغي",
            WhatsAppNotificationStatus.Scheduled => "📅 مجدول",
            WhatsAppNotificationStatus.Expired => "⏰ منتهي الصلاحية",
            _ => "غير محدد"
        };

        public string PriorityDisplay => Priority switch
        {
            WhatsAppNotificationPriority.Urgent => "🔴 عاجل",
            WhatsAppNotificationPriority.High => "🟡 عالي",
            WhatsAppNotificationPriority.Normal => "🟢 عادي",
            WhatsAppNotificationPriority.Low => "🔵 منخفض",
            _ => "غير محدد"
        };

        public string AmountDisplay => Amount > 0 ? $"{Amount:N0} درهم" : "-";
        public string DueDateDisplay => DueDate?.ToString("dd/MM/yyyy") ?? "-";
        public string CreatedDateDisplay => CreatedDate.ToString("dd/MM/yyyy HH:mm");
        public string SentDateDisplay => SentDate?.ToString("dd/MM/yyyy HH:mm") ?? "-";

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
