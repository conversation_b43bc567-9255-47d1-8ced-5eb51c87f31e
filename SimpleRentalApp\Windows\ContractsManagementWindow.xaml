<Window x:Class="SimpleRentalApp.Windows.ContractsManagementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة العقود"
        Height="900" Width="1600"
        MinHeight="600" MinWidth="1000"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        Background="#F5F7FA"
        WindowStyle="SingleBorderWindow"
        ResizeMode="CanResize"
        WindowState="Maximized">

    <Grid Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header Section -->
            <Border Grid.Row="0" 
                    Background="#1976D2" 
                    CornerRadius="10" 
                    Padding="25,20"
                    Margin="0,0,0,20">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- Title Section -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="📋" FontSize="28" Foreground="White" Margin="0,0,15,0"/>
                        <StackPanel VerticalAlignment="Center">
                            <TextBlock Text="إدارة العقود" 
                                       FontSize="24" 
                                       FontWeight="Bold"
                                       Foreground="White"/>
                            <TextBlock Name="SubtitleText" 
                                       Text="إدارة شاملة لجميع العقود مع الإحصائيات والتصنيف"
                                       FontSize="14" 
                                       Foreground="#E3F2FD"
                                       Margin="0,5,0,0"/>
                        </StackPanel>
                    </StackPanel>
                    
                    <!-- Action Buttons -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                        <Button Name="AddContractButton"
                                Content="➕ إضافة عقد جديد"
                                Background="#4CAF50"
                                Foreground="White"
                                Padding="15,10"
                                Margin="10,0"
                                BorderThickness="0"
                                FontWeight="SemiBold"
                                Click="AddContractButton_Click"/>

                        <Button Name="CloseButton"
                                Content="❌"
                                Background="#F44336"
                                Foreground="White"
                                Padding="12,10"
                                Margin="5,0"
                                BorderThickness="0"
                                FontWeight="SemiBold"
                                Click="CloseButton_Click"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Search and Filters -->
            <Border Grid.Row="1" 
                    Background="White" 
                    CornerRadius="10" 
                    Padding="20,15"
                    Margin="0,0,0,20"
                    BorderBrush="#E0E0E0"
                    BorderThickness="1">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- Search Box -->
                    <TextBox Name="SearchTextBox" 
                             Grid.Column="0"
                             Text=""
                             FontSize="14"
                             Padding="10"
                             Margin="0,0,15,0"
                             TextChanged="SearchTextBox_TextChanged">
                        <TextBox.Style>
                            <Style TargetType="TextBox">
                                <Style.Triggers>
                                    <Trigger Property="Text" Value="">
                                        <Setter Property="Background">
                                            <Setter.Value>
                                                <VisualBrush Stretch="None">
                                                    <VisualBrush.Visual>
                                                        <TextBlock Text="🔍 البحث في العقود..." Foreground="#999" FontSize="14" Margin="10,0,0,0"/>
                                                    </VisualBrush.Visual>
                                                </VisualBrush>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </TextBox.Style>
                    </TextBox>
                    
                    <!-- Status Filter -->
                    <ComboBox Name="StatusFilterComboBox"
                              Grid.Column="1"
                              Width="180"
                              Margin="0,0,15,0"
                              Padding="10"
                              FontSize="14"
                              SelectionChanged="StatusFilterComboBox_SelectionChanged">
                        <ComboBoxItem Content="📋 جميع العقود" IsSelected="True"/>
                        <ComboBoxItem Content="✅ العقود النشطة"/>
                        <ComboBoxItem Content="⚠️ قريبة الانتهاء"/>
                        <ComboBoxItem Content="❌ منتهية الصلاحية"/>
                    </ComboBox>
                    
                    <!-- Sort Options -->
                    <ComboBox Name="SortComboBox"
                              Grid.Column="2"
                              Width="150"
                              Margin="0,0,15,0"
                              Padding="10"
                              FontSize="14"
                              SelectionChanged="SortComboBox_SelectionChanged">
                        <ComboBoxItem Content="📅 تاريخ البداية" IsSelected="True"/>
                        <ComboBoxItem Content="📅 تاريخ الانتهاء"/>
                        <ComboBoxItem Content="💰 قيمة الإيجار"/>
                        <ComboBoxItem Content="🏠 اسم المحل"/>
                        <ComboBoxItem Content="👤 اسم المكتري"/>
                    </ComboBox>
                    
                    <!-- Export Button -->
                    <Button Name="ExportButton"
                            Grid.Column="3"
                            Content="📊 تصدير التقرير"
                            Background="#9C27B0"
                            Foreground="White"
                            Padding="15,10"
                            BorderThickness="0"
                            FontWeight="SemiBold"
                            Click="ExportButton_Click"/>
                </Grid>
            </Border>

            <!-- Statistics Dashboard -->
            <Border Grid.Row="2" 
                    Background="White" 
                    CornerRadius="10" 
                    Padding="20"
                    Margin="0,0,0,20"
                    BorderBrush="#E0E0E0"
                    BorderThickness="1">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Dashboard Title -->
                    <TextBlock Grid.Row="0" 
                               Text="📊 لوحة الإحصائيات والعقود"
                               FontSize="18"
                               FontWeight="Bold"
                               Foreground="#1976D2"
                               Margin="0,0,0,15"/>
                    
                    <!-- Statistics Cards -->
                    <Grid Grid.Row="1" Margin="0,0,0,20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- Total Contracts Card -->
                        <Border Grid.Column="0" Background="#E3F2FD" CornerRadius="8" Padding="15" Margin="5">
                            <StackPanel HorizontalAlignment="Center">
                                <TextBlock Text="📋" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <TextBlock Name="TotalContractsText" Text="0" FontSize="20" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#1976D2"/>
                                <TextBlock Text="إجمالي العقود" FontSize="12" HorizontalAlignment="Center" Foreground="#666"/>
                            </StackPanel>
                        </Border>
                        
                        <!-- Active Contracts Card -->
                        <Border Grid.Column="1" Background="#E8F5E8" CornerRadius="8" Padding="15" Margin="5">
                            <StackPanel HorizontalAlignment="Center">
                                <TextBlock Text="✅" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <TextBlock Name="ActiveContractsText" Text="0" FontSize="20" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#4CAF50"/>
                                <TextBlock Text="العقود النشطة" FontSize="12" HorizontalAlignment="Center" Foreground="#666"/>
                            </StackPanel>
                        </Border>
                        
                        <!-- Expiring Soon Card -->
                        <Border Grid.Column="2" Background="#FFF3E0" CornerRadius="8" Padding="15" Margin="5">
                            <StackPanel HorizontalAlignment="Center">
                                <TextBlock Text="⚠️" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <TextBlock Name="ExpiringSoonText" Text="0" FontSize="20" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#FF9800"/>
                                <TextBlock Text="قريبة الانتهاء" FontSize="12" HorizontalAlignment="Center" Foreground="#666"/>
                            </StackPanel>
                        </Border>
                        
                        <!-- Expired Contracts Card -->
                        <Border Grid.Column="3" Background="#FFEBEE" CornerRadius="8" Padding="15" Margin="5">
                            <StackPanel HorizontalAlignment="Center">
                                <TextBlock Text="❌" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <TextBlock Name="ExpiredContractsText" Text="0" FontSize="20" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#F44336"/>
                                <TextBlock Text="العقود المنتهية" FontSize="12" HorizontalAlignment="Center" Foreground="#666"/>
                            </StackPanel>
                        </Border>
                        
                        <!-- Average Duration Card -->
                        <Border Grid.Column="4" Background="#F3E5F5" CornerRadius="8" Padding="15" Margin="5">
                            <StackPanel HorizontalAlignment="Center">
                                <TextBlock Text="📅" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <TextBlock Name="AverageDurationText" Text="0 شهر" FontSize="20" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#9C27B0"/>
                                <TextBlock Text="متوسط المدة" FontSize="12" HorizontalAlignment="Center" Foreground="#666"/>
                            </StackPanel>
                        </Border>
                    </Grid>
                    
                    <!-- Contracts List -->
                    <ScrollViewer Grid.Row="2" 
                                  VerticalScrollBarVisibility="Auto"
                                  HorizontalScrollBarVisibility="Disabled">
                        <DataGrid Name="ContractsDataGrid"
                                  AutoGenerateColumns="False"
                                  CanUserAddRows="False"
                                  CanUserDeleteRows="False"
                                  IsReadOnly="True"
                                  GridLinesVisibility="None"
                                  HeadersVisibility="Column"
                                  Background="White"
                                  RowBackground="White"
                                  AlternatingRowBackground="#F9F9F9"
                                  BorderThickness="0"
                                  FontSize="14">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="رقم العقد" Binding="{Binding Id}" Width="80"/>
                                <DataGridTextColumn Header="المحل" Binding="{Binding PropertyName}" Width="150"/>
                                <DataGridTextColumn Header="المكتري" Binding="{Binding CustomerName}" Width="150"/>
                                <DataGridTextColumn Header="تاريخ البداية" Binding="{Binding StartDateDisplay}" Width="120"/>
                                <DataGridTextColumn Header="تاريخ الانتهاء" Binding="{Binding EndDateDisplay}" Width="120"/>
                                <DataGridTextColumn Header="الإيجار الحالي" Binding="{Binding CurrentRentDisplay}" Width="120"/>
                                <DataGridTextColumn Header="الحالة" Binding="{Binding StatusDisplay}" Width="100"/>
                                <DataGridTextColumn Header="الأيام المتبقية" Binding="{Binding DaysRemainingDisplay}" Width="120"/>
                                <DataGridTemplateColumn Header="الإجراءات" Width="250">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                <Button Content="📝" ToolTip="تعديل" Background="#FF9800" Foreground="White"
                                                        BorderThickness="0" Padding="6" Margin="1" FontSize="12"
                                                        Click="EditContractButton_Click" Tag="{Binding Id}"/>
                                                <Button Content="📈" ToolTip="زيادة الإيجار" Background="#9C27B0" Foreground="White"
                                                        BorderThickness="0" Padding="6" Margin="1" FontSize="12"
                                                        Click="AddRentIncreaseButton_Click" Tag="{Binding Id}"/>
                                                <Button Content="📎" ToolTip="المرفقات" Background="#4CAF50" Foreground="White"
                                                        BorderThickness="0" Padding="6" Margin="1" FontSize="12"
                                                        Click="ManageAttachmentsButton_Click" Tag="{Binding Id}"/>
                                                <Button Content="🗑️" ToolTip="حذف" Background="#F44336" Foreground="White"
                                                        BorderThickness="0" Padding="6" Margin="1" FontSize="12"
                                                        Click="DeleteContractButton_Click" Tag="{Binding Id}"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </ScrollViewer>
                </Grid>
            </Border>

            <!-- Status Bar -->
            <Border Grid.Row="3" 
                    Background="#F8F9FA" 
                    CornerRadius="8" 
                    Padding="15,10"
                    BorderBrush="#E0E0E0"
                    BorderThickness="1">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Name="ResultsCountText" 
                               Grid.Column="0"
                               Text="0 عقد معروض"
                               FontSize="14"
                               Foreground="#666"
                               VerticalAlignment="Center"/>
                    
                    <TextBlock Grid.Column="1"
                               Text="تم التحديث: الآن"
                               FontSize="12"
                               Foreground="#999"
                               VerticalAlignment="Center"/>
                </Grid>
            </Border>
    </Grid>
</Window>
