-- إنشاء جداول نظام إدارة الإيجارات في Supabase
-- تشغيل هذا الملف في SQL Editor في Supabase

-- 1. جدول المستخدمين
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    role INTEGER NOT NULL DEFAULT 3, -- 1=Admin, 2=Manager, 3=Employee
    is_active BOOLEAN DEFAULT true,
    created_date TIMESTAMP DEFAULT NOW(),
    updated_date TIMESTAMP DEFAULT NOW()
);

-- 2. جدول العملاء
CREATE TABLE customers (
    id SERIAL PRIMARY KEY,
    first_name VARCHAR(100) NOT NULL,
    last_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    phone VARCHAR(15) NOT NULL,
    email VARCHAR(100),
    national_id VARCHAR(20),
    address VARCHAR(200),
    created_date TIMESTAMP DEFAULT NOW(),
    updated_date TIMESTAMP DEFAULT NOW()
);

-- 3. جدول العقارات/المحلات
CREATE TABLE properties (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    address VARCHAR(200) NOT NULL,
    type INTEGER NOT NULL, -- 1=Apartment, 2=Shop, 3=Office, 4=Villa
    status INTEGER DEFAULT 1, -- 1=Available, 2=Rented, 3=Maintenance
    monthly_rent DECIMAL(10,2) NOT NULL,
    cleaning_tax_payment_option DECIMAL(3,1) DEFAULT 1, -- 1=شهري، 2=نصف سنوي، 3=ثلث سنوي، 4=ربع سنوي، 12=سنوي
    rooms INTEGER,
    bathrooms INTEGER,
    area DECIMAL(8,2),
    description VARCHAR(500),
    created_date TIMESTAMP DEFAULT NOW(),
    updated_date TIMESTAMP DEFAULT NOW(),
    customer_id INTEGER REFERENCES customers(id)
);

-- 4. جدول العقود
CREATE TABLE contracts (
    id SERIAL PRIMARY KEY,
    customer_id INTEGER NOT NULL REFERENCES customers(id),
    property_id INTEGER NOT NULL REFERENCES properties(id),
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    initial_rent_amount DECIMAL(10,2) NOT NULL,
    rent_increase_count INTEGER DEFAULT 0,
    rent_increase_percentage DECIMAL(5,2) DEFAULT 0,
    notes VARCHAR(500),
    created_date TIMESTAMP DEFAULT NOW(),
    updated_date TIMESTAMP DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true
);

-- 5. جدول الأداءات
CREATE TABLE payments (
    id SERIAL PRIMARY KEY,
    property_id INTEGER NOT NULL REFERENCES properties(id),
    customer_id INTEGER NOT NULL REFERENCES customers(id),
    type INTEGER DEFAULT 3, -- 1=Rent, 2=CleaningTax, 3=Combined, 4=Deposit, 5=Other
    rent_amount DECIMAL(10,2) NOT NULL,
    cleaning_tax_amount DECIMAL(10,2) DEFAULT 0,
    due_date DATE NOT NULL,
    payment_date DATE,
    status INTEGER DEFAULT 1, -- 1=Unpaid, 2=Paid, 3=Overdue, 4=Partial
    notes VARCHAR(200),
    payment_method VARCHAR(100) DEFAULT 'نقداً',
    created_date TIMESTAMP DEFAULT NOW(),
    updated_date TIMESTAMP DEFAULT NOW()
);

-- 6. جدول إيصالات الإيجار
CREATE TABLE rent_receipts (
    id SERIAL PRIMARY KEY,
    receipt_number VARCHAR(50) NOT NULL UNIQUE,
    contract_id INTEGER NOT NULL REFERENCES contracts(id),
    payment_id INTEGER REFERENCES payments(id),
    amount DECIMAL(10,2) NOT NULL,
    payment_date DATE NOT NULL,
    period_start_date DATE,
    period_end_date DATE,
    payment_period VARCHAR(100),
    payment_method VARCHAR(50) DEFAULT 'نقداً',
    notes VARCHAR(500),
    created_date TIMESTAMP DEFAULT NOW()
);

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX idx_customers_phone ON customers(phone);
CREATE INDEX idx_customers_national_id ON customers(national_id);
CREATE INDEX idx_properties_status ON properties(status);
CREATE INDEX idx_properties_customer_id ON properties(customer_id);
CREATE INDEX idx_contracts_customer_id ON contracts(customer_id);
CREATE INDEX idx_contracts_property_id ON contracts(property_id);
CREATE INDEX idx_contracts_active ON contracts(is_active);
CREATE INDEX idx_payments_property_id ON payments(property_id);
CREATE INDEX idx_payments_customer_id ON payments(customer_id);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_payments_due_date ON payments(due_date);
CREATE INDEX idx_receipts_contract_id ON rent_receipts(contract_id);
CREATE INDEX idx_receipts_payment_id ON rent_receipts(payment_id);

-- تفعيل Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE properties ENABLE ROW LEVEL SECURITY;
ALTER TABLE contracts ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE rent_receipts ENABLE ROW LEVEL SECURITY;

-- إنشاء سياسات الأمان (تسمح بجميع العمليات للمستخدمين المصرح لهم)
CREATE POLICY "Enable all operations for authenticated users" ON users FOR ALL USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON customers FOR ALL USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON properties FOR ALL USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON contracts FOR ALL USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON payments FOR ALL USING (true);
CREATE POLICY "Enable all operations for authenticated users" ON rent_receipts FOR ALL USING (true);

-- إنشاء دوال مساعدة لحساب ضريبة النظافة
CREATE OR REPLACE FUNCTION calculate_cleaning_tax(
    monthly_rent DECIMAL,
    payment_option DECIMAL
) RETURNS DECIMAL AS $$
BEGIN
    -- ضريبة النظافة = 10.5% من الإيجار السنوي مقسمة حسب خيار الدفع
    RETURN (monthly_rent * 12 * 0.105) / payment_option;
END;
$$ LANGUAGE plpgsql;

-- إنشاء دالة لتحديث updated_date تلقائياً
CREATE OR REPLACE FUNCTION update_updated_date_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_date = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء المحفزات لتحديث updated_date
CREATE TRIGGER update_customers_updated_date BEFORE UPDATE ON customers
    FOR EACH ROW EXECUTE FUNCTION update_updated_date_column();

CREATE TRIGGER update_properties_updated_date BEFORE UPDATE ON properties
    FOR EACH ROW EXECUTE FUNCTION update_updated_date_column();

CREATE TRIGGER update_contracts_updated_date BEFORE UPDATE ON contracts
    FOR EACH ROW EXECUTE FUNCTION update_updated_date_column();

CREATE TRIGGER update_payments_updated_date BEFORE UPDATE ON payments
    FOR EACH ROW EXECUTE FUNCTION update_updated_date_column();

-- إدراج بيانات المستخدم الافتراضي
INSERT INTO users (username, full_name, password_hash, email, role, is_active) VALUES
('admin', 'مدير النظام', '8c6976e5b5410415bde908bd4dee15dfb167a9c873fc4bb8a81f6f2ab448a918', '<EMAIL>', 1, true);

-- رسالة تأكيد
SELECT 'تم إنشاء جميع الجداول والفهارس والسياسات بنجاح!' as message;
