using System;
using System.Diagnostics;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Windows;

namespace SimpleRentalApp.Services
{
    /// <summary>
    /// خدمة إرسال الرسائل عبر الواتساب
    /// </summary>
    public class WhatsAppSenderService
    {
        /// <summary>
        /// إرسال رسالة عبر الواتساب باستخدام WhatsApp Web
        /// </summary>
        /// <param name="phoneNumber">رقم الهاتف (مع رمز الدولة)</param>
        /// <param name="message">نص الرسالة</param>
        /// <returns>true إذا تم فتح الواتساب بنجاح</returns>
        public async Task<bool> SendMessageAsync(string phoneNumber, string message)
        {
            try
            {
                // تنظيف رقم الهاتف
                string cleanPhoneNumber = CleanPhoneNumber(phoneNumber);
                
                if (string.IsNullOrEmpty(cleanPhoneNumber))
                {
                    throw new ArgumentException("رقم الهاتف غير صحيح");
                }

                // تشفير الرسالة للـ URL
                string encodedMessage = HttpUtility.UrlEncode(message);
                
                // إنشاء رابط الواتساب
                string whatsappUrl = $"https://wa.me/{cleanPhoneNumber}?text={encodedMessage}";
                
                // فتح الرابط في المتصفح الافتراضي
                Process.Start(new ProcessStartInfo
                {
                    FileName = whatsappUrl,
                    UseShellExecute = true
                });

                // انتظار قصير للتأكد من فتح المتصفح
                await Task.Delay(500);
                
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إرسال رسالة الواتساب: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إرسال رسالة عبر تطبيق الواتساب المثبت على الجهاز
        /// </summary>
        /// <param name="phoneNumber">رقم الهاتف</param>
        /// <param name="message">نص الرسالة</param>
        /// <returns>true إذا تم فتح التطبيق بنجاح</returns>
        public async Task<bool> SendMessageViaAppAsync(string phoneNumber, string message)
        {
            try
            {
                string cleanPhoneNumber = CleanPhoneNumber(phoneNumber);
                
                if (string.IsNullOrEmpty(cleanPhoneNumber))
                {
                    throw new ArgumentException("رقم الهاتف غير صحيح");
                }

                string encodedMessage = HttpUtility.UrlEncode(message);
                
                // محاولة فتح تطبيق الواتساب مباشرة
                string whatsappAppUrl = $"whatsapp://send?phone={cleanPhoneNumber}&text={encodedMessage}";
                
                try
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = whatsappAppUrl,
                        UseShellExecute = true
                    });
                    
                    await Task.Delay(500);
                    return true;
                }
                catch
                {
                    // إذا فشل فتح التطبيق، استخدم WhatsApp Web
                    return await SendMessageAsync(phoneNumber, message);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إرسال رسالة الواتساب عبر التطبيق: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تنظيف رقم الهاتف وإضافة رمز الدولة إذا لزم الأمر
        /// </summary>
        /// <param name="phoneNumber">رقم الهاتف الأصلي</param>
        /// <returns>رقم الهاتف المنظف</returns>
        private string CleanPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return string.Empty;

            // إزالة جميع الرموز غير الرقمية
            StringBuilder cleanNumber = new StringBuilder();
            foreach (char c in phoneNumber)
            {
                if (char.IsDigit(c))
                    cleanNumber.Append(c);
            }

            string result = cleanNumber.ToString();

            // إذا كان الرقم يبدأ بـ 0، استبدله بـ 212 (رمز المغرب)
            if (result.StartsWith("0"))
            {
                result = "212" + result.Substring(1);
            }
            // إذا لم يبدأ بـ 212، أضف رمز المغرب
            else if (!result.StartsWith("212"))
            {
                result = "212" + result;
            }

            return result;
        }

        /// <summary>
        /// التحقق من صحة رقم الهاتف
        /// </summary>
        /// <param name="phoneNumber">رقم الهاتف</param>
        /// <returns>true إذا كان الرقم صحيحاً</returns>
        public bool IsValidPhoneNumber(string phoneNumber)
        {
            string cleanNumber = CleanPhoneNumber(phoneNumber);
            
            // التحقق من أن الرقم يحتوي على عدد صحيح من الأرقام
            return !string.IsNullOrEmpty(cleanNumber) && 
                   cleanNumber.Length >= 10 && 
                   cleanNumber.Length <= 15;
        }

        /// <summary>
        /// إرسال رسالة مع خيارات متقدمة
        /// </summary>
        /// <param name="phoneNumber">رقم الهاتف</param>
        /// <param name="message">نص الرسالة</param>
        /// <param name="useApp">استخدام التطبيق بدلاً من المتصفح</param>
        /// <param name="showConfirmation">عرض رسالة تأكيد</param>
        /// <returns>true إذا تم الإرسال بنجاح</returns>
        public async Task<bool> SendMessageWithOptionsAsync(string phoneNumber, string message, bool useApp = false, bool showConfirmation = true)
        {
            try
            {
                // التحقق من صحة رقم الهاتف
                if (!IsValidPhoneNumber(phoneNumber))
                {
                    if (showConfirmation)
                    {
                        MessageBox.Show("رقم الهاتف غير صحيح. يرجى التحقق من الرقم والمحاولة مرة أخرى.", 
                            "رقم هاتف غير صحيح", 
                            MessageBoxButton.OK, 
                            MessageBoxImage.Warning);
                    }
                    return false;
                }

                // التحقق من وجود نص الرسالة
                if (string.IsNullOrWhiteSpace(message))
                {
                    if (showConfirmation)
                    {
                        MessageBox.Show("لا يمكن إرسال رسالة فارغة.", 
                            "رسالة فارغة", 
                            MessageBoxButton.OK, 
                            MessageBoxImage.Warning);
                    }
                    return false;
                }

                bool success;
                
                if (useApp)
                {
                    success = await SendMessageViaAppAsync(phoneNumber, message);
                }
                else
                {
                    success = await SendMessageAsync(phoneNumber, message);
                }

                if (success && showConfirmation)
                {
                    MessageBox.Show("تم فتح الواتساب بنجاح!\n\nيرجى التحقق من الرسالة وإرسالها يدوياً.", 
                        "تم فتح الواتساب", 
                        MessageBoxButton.OK, 
                        MessageBoxImage.Information);
                }
                else if (!success && showConfirmation)
                {
                    MessageBox.Show("فشل في فتح الواتساب. يرجى التحقق من تثبيت الواتساب أو المتصفح.", 
                        "فشل في الإرسال", 
                        MessageBoxButton.OK, 
                        MessageBoxImage.Error);
                }

                return success;
            }
            catch (Exception ex)
            {
                if (showConfirmation)
                {
                    MessageBox.Show($"خطأ في إرسال الرسالة: {ex.Message}", 
                        "خطأ", 
                        MessageBoxButton.OK, 
                        MessageBoxImage.Error);
                }
                return false;
            }
        }

        /// <summary>
        /// إنشاء رابط واتساب للنسخ
        /// </summary>
        /// <param name="phoneNumber">رقم الهاتف</param>
        /// <param name="message">نص الرسالة</param>
        /// <returns>رابط الواتساب</returns>
        public string GenerateWhatsAppLink(string phoneNumber, string message)
        {
            try
            {
                string cleanPhoneNumber = CleanPhoneNumber(phoneNumber);
                string encodedMessage = HttpUtility.UrlEncode(message);
                return $"https://wa.me/{cleanPhoneNumber}?text={encodedMessage}";
            }
            catch
            {
                return string.Empty;
            }
        }
    }
}
