<?xml version="1.0" encoding="utf-8"?>
<Window x:Class="SimpleRentalApp.Windows.NotificationPreviewDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="معاينة رسالة الواتساب"
        Height="500" Width="400"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,15">
            <TextBlock Text="📱 معاينة رسالة الواتساب" 
                       FontSize="18" 
                       FontWeight="Bold" 
                       Foreground="#2C3E50"
                       HorizontalAlignment="Center"/>
        </StackPanel>

        <!-- Phone Number -->
        <StackPanel Grid.Row="1" Margin="0,0,0,15">
            <TextBlock Text="رقم الهاتف:" FontWeight="Bold" Margin="0,0,0,5"/>
            <TextBox Name="PhoneNumberTextBox"
                     IsReadOnly="True"
                     Background="#F8F9FA"
                     Padding="10"
                     FontSize="14"
                     BorderBrush="#BDC3C7"/>
        </StackPanel>

        <!-- Message -->
        <StackPanel Grid.Row="2">
            <TextBlock Text="نص الرسالة:" FontWeight="Bold" Margin="0,0,0,5"/>
            <Border BorderBrush="#BDC3C7" BorderThickness="2" Background="White">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <TextBox Name="MessageTextBox"
                             TextWrapping="Wrap"
                             IsReadOnly="True"
                             Background="Transparent"
                             BorderThickness="0"
                             Padding="15"
                             FontSize="14"/>
                </ScrollViewer>
            </Border>
        </StackPanel>

        <!-- Buttons -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Name="SendButton"
                    Content="📱 إرسال عبر الواتساب"
                    Padding="15,10"
                    Margin="10,0"
                    Background="#25D366"
                    Foreground="White"
                    BorderThickness="0"
                    FontSize="14"
                    FontWeight="Bold"
                    Click="SendButton_Click"/>
            
            <Button Name="CloseButton"
                    Content="❌ إغلاق"
                    Padding="15,10"
                    Margin="10,0"
                    Background="#95A5A6"
                    Foreground="White"
                    BorderThickness="0"
                    FontSize="14"
                    FontWeight="Bold"
                    Click="CloseButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
