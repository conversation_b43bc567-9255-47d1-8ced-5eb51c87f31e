using iTextSharp.text;
using iTextSharp.text.pdf;
using RentalManagement.Models;
using System.IO;

namespace RentalManagement.Services
{
    public class ReceiptService
    {
        private static ReceiptService? _instance;
        private static readonly object _lock = new object();

        public static ReceiptService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                            _instance = new ReceiptService();
                    }
                }
                return _instance;
            }
        }

        private ReceiptService() { }

        public async Task<string> GenerateReceiptAsync(Payment payment)
        {
            if (payment.Customer == null || payment.Property == null)
                throw new ArgumentException("بيانات الأداء غير مكتملة");

            try
            {
                // Create receipts directory if it doesn't exist
                var receiptsDir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), 
                                             "RentalManagement", "Receipts");
                if (!Directory.Exists(receiptsDir))
                {
                    Directory.CreateDirectory(receiptsDir);
                }

                // Generate filename
                var fileName = $"Receipt_{payment.Customer.FullName}_{payment.PaymentDate:yyyyMMdd}_{payment.Id}.pdf";
                var filePath = Path.Combine(receiptsDir, fileName);

                await Task.Run(() => CreatePdfReceipt(payment, filePath));

                return filePath;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء الإيصال: {ex.Message}", ex);
            }
        }

        private void CreatePdfReceipt(Payment payment, string filePath)
        {
            // Create document
            var document = new Document(PageSize.A4, 50, 50, 50, 50);
            var writer = PdfWriter.GetInstance(document, new FileStream(filePath, FileMode.Create));
            
            document.Open();

            try
            {
                // Add header
                AddHeader(document);
                
                // Add receipt details
                AddReceiptDetails(document, payment);
                
                // Add customer details
                AddCustomerDetails(document, payment.Customer);
                
                // Add property details
                AddPropertyDetails(document, payment.Property);
                
                // Add payment details
                AddPaymentDetails(document, payment);
                
                // Add footer
                AddFooter(document);
            }
            finally
            {
                document.Close();
            }
        }

        private void AddHeader(Document document)
        {
            // Company name
            var titleFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 18, BaseColor.DARK_GRAY);
            var title = new Paragraph("نظام إدارة العقارات", titleFont)
            {
                Alignment = Element.ALIGN_CENTER,
                SpacingAfter = 10
            };
            document.Add(title);

            // Receipt title
            var receiptTitleFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 16, BaseColor.BLACK);
            var receiptTitle = new Paragraph("إيصال كراء", receiptTitleFont)
            {
                Alignment = Element.ALIGN_CENTER,
                SpacingAfter = 20
            };
            document.Add(receiptTitle);

            // Add line separator
            var line = new Paragraph("_".PadRight(80, '_'))
            {
                Alignment = Element.ALIGN_CENTER,
                SpacingAfter = 20
            };
            document.Add(line);
        }

        private void AddReceiptDetails(Document document, Payment payment)
        {
            var font = FontFactory.GetFont(FontFactory.HELVETICA, 12, BaseColor.BLACK);
            
            var table = new PdfPTable(2) { WidthPercentage = 100 };
            table.SetWidths(new float[] { 1, 1 });

            // Receipt number
            table.AddCell(new PdfPCell(new Phrase($"رقم الإيصال: {payment.Id}", font)) 
            { 
                Border = Rectangle.NO_BORDER,
                HorizontalAlignment = Element.ALIGN_RIGHT
            });

            // Date
            table.AddCell(new PdfPCell(new Phrase($"التاريخ: {DateTime.Now:dd/MM/yyyy}", font)) 
            { 
                Border = Rectangle.NO_BORDER,
                HorizontalAlignment = Element.ALIGN_LEFT
            });

            document.Add(table);
            document.Add(new Paragraph(" ") { SpacingAfter = 10 });
        }

        private void AddCustomerDetails(Document document, Customer customer)
        {
            var headerFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 14, BaseColor.DARK_GRAY);
            var font = FontFactory.GetFont(FontFactory.HELVETICA, 12, BaseColor.BLACK);

            // Customer section header
            var header = new Paragraph("بيانات المستأجر:", headerFont)
            {
                SpacingAfter = 10
            };
            document.Add(header);

            // Customer details
            document.Add(new Paragraph($"الاسم الكامل: {customer.FullName}", font));
            document.Add(new Paragraph($"رقم البطاقة الوطنية: {customer.NationalId}", font));
            document.Add(new Paragraph($"رقم الهاتف: {customer.PhoneNumber}", font));
            document.Add(new Paragraph($"العنوان: {customer.Address}", font));
            
            document.Add(new Paragraph(" ") { SpacingAfter = 10 });
        }

        private void AddPropertyDetails(Document document, Property property)
        {
            var headerFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 14, BaseColor.DARK_GRAY);
            var font = FontFactory.GetFont(FontFactory.HELVETICA, 12, BaseColor.BLACK);

            // Property section header
            var header = new Paragraph("بيانات العقار:", headerFont)
            {
                SpacingAfter = 10
            };
            document.Add(header);

            // Property details
            var propertyType = property.Type == PropertyType.Apartment ? "شقة" : "محل";
            document.Add(new Paragraph($"نوع العقار: {propertyType}", font));
            document.Add(new Paragraph($"الموقع: {property.Location}", font));
            document.Add(new Paragraph($"مبلغ الكراء الشهري: {property.RentAmount:C}", font));
            
            if (!string.IsNullOrEmpty(property.Description))
            {
                document.Add(new Paragraph($"الوصف: {property.Description}", font));
            }
            
            document.Add(new Paragraph(" ") { SpacingAfter = 10 });
        }

        private void AddPaymentDetails(Document document, Payment payment)
        {
            var headerFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 14, BaseColor.DARK_GRAY);
            var font = FontFactory.GetFont(FontFactory.HELVETICA, 12, BaseColor.BLACK);
            var amountFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 14, BaseColor.DARK_GRAY);

            // Payment section header
            var header = new Paragraph("تفاصيل الأداء:", headerFont)
            {
                SpacingAfter = 10
            };
            document.Add(header);

            // Payment details
            document.Add(new Paragraph($"تاريخ الاستحقاق: {payment.DueDate:dd/MM/yyyy}", font));
            document.Add(new Paragraph($"تاريخ الدفع: {payment.PaymentDate:dd/MM/yyyy}", font));
            document.Add(new Paragraph($"المبلغ المدفوع: {payment.Amount:C}", amountFont) { SpacingAfter = 10 });
            
            if (!string.IsNullOrEmpty(payment.Notes))
            {
                document.Add(new Paragraph($"ملاحظات: {payment.Notes}", font));
            }
            
            document.Add(new Paragraph(" ") { SpacingAfter = 20 });
        }

        private void AddFooter(Document document)
        {
            var font = FontFactory.GetFont(FontFactory.HELVETICA, 10, BaseColor.GRAY);
            
            // Signature section
            var signatureTable = new PdfPTable(2) { WidthPercentage = 100 };
            signatureTable.SetWidths(new float[] { 1, 1 });

            signatureTable.AddCell(new PdfPCell(new Phrase("توقيع المستأجر: _______________", font)) 
            { 
                Border = Rectangle.NO_BORDER,
                HorizontalAlignment = Element.ALIGN_RIGHT,
                PaddingTop = 30
            });

            signatureTable.AddCell(new PdfPCell(new Phrase("توقيع المؤجر: _______________", font)) 
            { 
                Border = Rectangle.NO_BORDER,
                HorizontalAlignment = Element.ALIGN_LEFT,
                PaddingTop = 30
            });

            document.Add(signatureTable);

            // Footer note
            var footerNote = new Paragraph("هذا الإيصال مُولد تلقائياً من نظام إدارة العقارات", font)
            {
                Alignment = Element.ALIGN_CENTER,
                SpacingBefore = 30
            };
            document.Add(footerNote);
        }

        public async Task<bool> PrintReceiptAsync(string filePath)
        {
            try
            {
                await Task.Run(() =>
                {
                    var startInfo = new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = filePath,
                        UseShellExecute = true,
                        Verb = "print"
                    };
                    System.Diagnostics.Process.Start(startInfo);
                });
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> OpenReceiptAsync(string filePath)
        {
            try
            {
                await Task.Run(() =>
                {
                    var startInfo = new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = filePath,
                        UseShellExecute = true
                    };
                    System.Diagnostics.Process.Start(startInfo);
                });
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
