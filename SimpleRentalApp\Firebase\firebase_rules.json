{"rules": {"sync_data": {"$entity_type": {"$entity_id": {".read": "auth != null && (data.child('user_id').val() == auth.uid || root.child('users').child(auth.uid).child('role').val() == 'admin')", ".write": "auth != null && (data.child('user_id').val() == auth.uid || newData.child('user_id').val() == auth.uid || root.child('users').child(auth.uid).child('role').val() == 'admin')", ".validate": "newData.hasChildren(['entity_type', 'entity_id', 'json_data', 'operation', 'timestamp'])", "entity_type": {".validate": "newData.isString() && newData.val().length > 0"}, "entity_id": {".validate": "newData.isString() && newData.val().length > 0"}, "json_data": {".validate": "newData.isString() && newData.val().length > 0"}, "operation": {".validate": "newData.isString() && (newData.val() == 'Create' || newData.val() == 'Update' || newData.val() == 'Delete')"}, "timestamp": {".validate": "newData.isNumber() && newData.val() <= now"}, "user_id": {".validate": "newData.isString()"}, "device_id": {".validate": "newData.isString()"}, "hash": {".validate": "newData.isString()"}, "metadata": {".validate": "newData.isString()"}}}}, "backups": {"$backup_id": {".read": "auth != null && (data.child('user_id').val() == auth.uid || root.child('users').child(auth.uid).child('role').val() == 'admin')", ".write": "auth != null && (data.child('user_id').val() == auth.uid || newData.child('user_id').val() == auth.uid || root.child('users').child(auth.uid).child('role').val() == 'admin')", ".validate": "newData.hasChildren(['name', 'created_at', 'size', 'data', 'type', 'version'])", "name": {".validate": "newData.isString() && newData.val().length > 0"}, "created_at": {".validate": "newData.isNumber() && newData.val() <= now"}, "size": {".validate": "newData.isNumber() && newData.val() >= 0"}, "data": {".validate": "newData.isString() && newData.val().length > 0"}, "type": {".validate": "newData.isString() && (newData.val() == 'Full' || newData.val() == 'Incremental')"}, "version": {".validate": "newData.isString() && newData.val().length > 0"}, "description": {".validate": "newData.isString()"}, "user_id": {".validate": "newData.isString() && newData.val() == auth.uid"}, "device_id": {".validate": "newData.isString()"}}}, "sync_log": {"$log_id": {".read": "auth != null && (data.child('user_id').val() == auth.uid || root.child('users').child(auth.uid).child('role').val() == 'admin')", ".write": "auth != null && newData.child('user_id').val() == auth.uid", ".validate": "newData.hasChildren(['timestamp', 'operation', 'status'])", "timestamp": {".validate": "newData.isNumber() && newData.val() <= now"}, "operation": {".validate": "newData.isString() && newData.val().length > 0"}, "entity_type": {".validate": "newData.isString()"}, "entity_id": {".validate": "newData.isString()"}, "status": {".validate": "newData.isString() && (newData.val() == 'Success' || newData.val() == 'Failed' || newData.val() == 'Conflict')"}, "message": {".validate": "newData.isString()"}, "user_id": {".validate": "newData.isString() && newData.val() == auth.uid"}, "device_id": {".validate": "newData.isString()"}, "duration": {".validate": "newData.isNumber() && newData.val() >= 0"}, "error_details": {".validate": "newData.isString()"}}}, "sync_conflicts": {"$conflict_id": {".read": "auth != null && (data.child('user_id').val() == auth.uid || root.child('users').child(auth.uid).child('role').val() == 'admin')", ".write": "auth != null && (data.child('user_id').val() == auth.uid || newData.child('user_id').val() == auth.uid || root.child('users').child(auth.uid).child('role').val() == 'admin')", ".validate": "newData.hasChildren(['entity_type', 'entity_id', 'local_data', 'remote_data', 'conflict_type', 'detected_at'])", "entity_type": {".validate": "newData.isString() && newData.val().length > 0"}, "entity_id": {".validate": "newData.isString() && newData.val().length > 0"}, "local_data": {".validate": "newData.isString() && newData.val().length > 0"}, "remote_data": {".validate": "newData.isString() && newData.val().length > 0"}, "conflict_type": {".validate": "newData.isString() && newData.val().length > 0"}, "detected_at": {".validate": "newData.isNumber() && newData.val() <= now"}, "resolved_at": {".validate": "newData.isNumber()"}, "resolution": {".validate": "newData.isString() && (newData.val() == 'ServerWins' || newData.val() == 'ClientWins' || newData.val() == 'Manual' || newData.val() == 'Merged')"}, "resolved_by": {".validate": "newData.isString()"}, "is_resolved": {".validate": "newData.isBoolean()"}, "resolution_notes": {".validate": "newData.isString()"}, "user_id": {".validate": "newData.isString()"}}}, "sync_settings": {"$user_id": {"$device_id": {".read": "auth != null && ($user_id == auth.uid || root.child('users').child(auth.uid).child('role').val() == 'admin')", ".write": "auth != null && $user_id == auth.uid", ".validate": "newData.hasChildren(['service_type', 'auto_sync_enabled', 'sync_interval_minutes', 'enable_encryption', 'conflict_resolution'])", "service_type": {".validate": "newData.isString() && (newData.val() == 'supabase' || newData.val() == 'firebase' || newData.val() == 'azuresql')"}, "auto_sync_enabled": {".validate": "newData.isBoolean()"}, "sync_interval_minutes": {".validate": "newData.isNumber() && newData.val() >= 5 && newData.val() <= 1440"}, "enable_encryption": {".validate": "newData.isBoolean()"}, "conflict_resolution": {".validate": "newData.isString() && (newData.val() == 'ServerWins' || newData.val() == 'ClientWins' || newData.val() == 'Manual' || newData.val() == 'MergeChanges')"}, "enable_real_time_sync": {".validate": "newData.isBoolean()"}, "last_sync_time": {".validate": "newData.isNumber()"}, "created_at": {".validate": "newData.isNumber() && newData.val() <= now"}, "updated_at": {".validate": "newData.isNumber() && newData.val() <= now"}}}}, "users": {"$uid": {".read": "auth != null && auth.uid == $uid", ".write": "auth != null && auth.uid == $uid", "email": {".validate": "newData.isString() && newData.val().matches(/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,}$/i)"}, "name": {".validate": "newData.isString() && newData.val().length > 0"}, "role": {".validate": "newData.isString() && (newData.val() == 'user' || newData.val() == 'admin')"}, "created_at": {".validate": "newData.isNumber() && newData.val() <= now"}, "last_login": {".validate": "newData.isNumber()"}, "devices": {"$device_id": {".validate": "newData.hasChildren(['name', 'last_seen'])", "name": {".validate": "newData.isString() && newData.val().length > 0"}, "last_seen": {".validate": "newData.isNumber() && newData.val() <= now"}, "platform": {".validate": "newData.isString()"}, "version": {".validate": "newData.isString()"}}}}}, ".read": false, ".write": false}}