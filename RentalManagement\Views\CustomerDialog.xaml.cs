using System.Windows;
using RentalManagement.ViewModels;

namespace RentalManagement.Views
{
    /// <summary>
    /// Interaction logic for CustomerDialog.xaml
    /// </summary>
    public partial class CustomerDialog : Window
    {
        public CustomerDialog(CustomerDialogViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
            
            // Subscribe to dialog closed event
            viewModel.DialogClosed += OnDialogClosed;
        }

        private void OnDialogClosed(object? sender, bool result)
        {
            DialogResult = result;
            Close();
        }
    }
}
