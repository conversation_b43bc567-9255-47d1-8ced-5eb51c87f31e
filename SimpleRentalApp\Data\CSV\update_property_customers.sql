-- ربط العقارات بالعملاء بعد استيراد البيانات
-- تشغيل هذا الملف بعد استيراد customers و properties

-- ربط العقارات المؤجرة بالعملاء (بناءً على الترتيب)
UPDATE properties SET customer_id = 1 WHERE id = 1;  -- شقة الملك فهد 101 → أحمد محمد
UPDATE properties SET customer_id = 2 WHERE id = 2;  -- محل تجاري النخيل → فاطمة علي
UPDATE properties SET customer_id = 3 WHERE id = 3;  -- م<PERSON><PERSON><PERSON> العليا 205 → محمد السعيد
UPDATE properties SET customer_id = 4 WHERE id = 4;  -- شقة الصفا 302 → نورا الأحمد
UPDATE properties SET customer_id = 5 WHERE id = 5;  -- فيلا الأمير سلطان → خالد المطيري
UPDATE properties SET customer_id = 6 WHERE id = 6;  -- شقة الملز 150 → عائشة القحطاني
UPDATE properties SET customer_id = 7 WHERE id = 7;  -- محل التحلية → عبدالله الزهراني
UPDATE properties SET customer_id = 8 WHERE id = 8;  -- شقة الفيصلية 401 → مريم الشهري
UPDATE properties SET customer_id = 9 WHERE id = 9;  -- مكتب الملك عبدالعزيز → سعد العتيبي
UPDATE properties SET customer_id = 10 WHERE id = 10; -- شقة الروضة 201 → هند الدوسري
UPDATE properties SET customer_id = 11 WHERE id = 11; -- فيلا الأمير محمد → يوسف الغامدي
UPDATE properties SET customer_id = 12 WHERE id = 12; -- شقة السليمانية 105 → زينب الحربي
UPDATE properties SET customer_id = 13 WHERE id = 13; -- محل الملك فيصل → عمر الشمري
UPDATE properties SET customer_id = 14 WHERE id = 14; -- شقة الخزامى 301 → ليلى البقمي
UPDATE properties SET customer_id = 15 WHERE id = 15; -- مكتب العروبة 150 → إبراهيم الراشد

-- العقارات 16، 17، 18 تبقى بدون عملاء (متاحة للإيجار)

-- التحقق من النتائج
SELECT 
    p.id,
    p.name as property_name,
    CASE 
        WHEN c.id IS NOT NULL THEN c.first_name || ' ' || c.last_name 
        ELSE 'متاح للإيجار' 
    END as customer_name,
    CASE p.status 
        WHEN 1 THEN 'متاح'
        WHEN 2 THEN 'مؤجر'
        WHEN 3 THEN 'صيانة'
    END as status
FROM properties p
LEFT JOIN customers c ON p.customer_id = c.id
ORDER BY p.id;
