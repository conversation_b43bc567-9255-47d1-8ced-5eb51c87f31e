# ملفات CSV لنظام إدارة الإيجارات

## 📋 **نظرة عامة**
هذا المجلد يحتوي على ملفات CSV تحتوي على بيانات تجريبية لجميع جداول قاعدة البيانات في نظام إدارة الإيجارات.

## 📁 **الملفات المتاحة**

### 1. **customers.csv** - جدول العملاء
- **الحقول**: Id, FirstName, LastName, Phone, Email, NationalId, Address, CreatedDate, UpdatedDate
- **عدد السجلات**: 15 عميل
- **الوصف**: بيانات العملاء المكترين

### 2. **properties.csv** - جدول المحلات/العقارات
- **الحقول**: Id, Name, Address, Type, Status, MonthlyRent, CleaningTaxPaymentOption, Rooms, Bathrooms, Area, Description, CreatedDate, UpdatedDate, CustomerId
- **عدد السجلات**: 18 عقار (15 مؤجر + 3 متاح)
- **الوصف**: بيانات العقارات والمحلات
- **أنواع العقارات**:
  - 1 = شقة (Apartment)
  - 2 = محل تجاري (Shop)
  - 3 = مكتب (Office)
  - 4 = فيلا (Villa)
- **حالة العقار**:
  - 1 = متاح (Available)
  - 2 = مؤجر (Rented)
  - 3 = صيانة (Maintenance)

### 3. **contracts.csv** - جدول العقود
- **الحقول**: Id, CustomerId, PropertyId, StartDate, EndDate, InitialRentAmount, RentIncreaseCount, RentIncreasePercentage, Notes, CreatedDate, UpdatedDate, IsActive
- **عدد السجلات**: 15 عقد
- **الوصف**: عقود الإيجار بين العملاء والعقارات

### 4. **payments.csv** - جدول الأداءات
- **الحقول**: Id, PropertyId, CustomerId, Type, RentAmount, CleaningTaxAmount, DueDate, PaymentDate, Status, Notes, PaymentMethod, CreatedDate, UpdatedDate
- **عدد السجلات**: 21 أداء (15 مدفوع + 6 مستحق)
- **الوصف**: أداءات الإيجار وضريبة النظافة
- **أنواع الأداء**:
  - 1 = إيجار فقط (Rent)
  - 2 = ضريبة نظافة فقط (CleaningTax)
  - 3 = مجمع (Combined)
  - 4 = تأمين (Deposit)
  - 5 = أخرى (Other)
- **حالة الأداء**:
  - 1 = غير مدفوع (Unpaid)
  - 2 = مدفوع (Paid)
  - 3 = متأخر (Overdue)
  - 4 = جزئي (Partial)

### 5. **rent_receipts.csv** - جدول إيصالات الإيجار
- **الحقول**: Id, ReceiptNumber, ContractId, PaymentId, Amount, PaymentDate, PeriodStartDate, PeriodEndDate, PaymentPeriod, PaymentMethod, Notes, CreatedDate
- **عدد السجلات**: 15 إيصال
- **الوصف**: إيصالات الأداءات المدفوعة

### 6. **users.csv** - جدول المستخدمين
- **الحقول**: Id, Username, FullName, PasswordHash, Email, Role, IsActive, CreatedDate, UpdatedDate
- **عدد السجلات**: 4 مستخدمين
- **الوصف**: مستخدمي النظام
- **الأدوار**:
  - 1 = مدير (Admin)
  - 2 = مدير فرع (Manager)
  - 3 = موظف (Employee)

## 🔐 **كلمات المرور للمستخدمين**
- **admin**: admin123
- **manager1**: manager123
- **employee1**: employee123
- **employee2**: employee123

## 📊 **إحصائيات البيانات**
- **إجمالي العملاء**: 15
- **إجمالي العقارات**: 18 (15 مؤجر، 3 متاح)
- **إجمالي العقود النشطة**: 15
- **إجمالي الأداءات**: 21 (15 مدفوع، 6 مستحق)
- **إجمالي الإيصالات**: 15

## 💰 **البيانات المالية**
- **إجمالي الإيجارات الشهرية**: 49,400 ريال
- **إجمالي ضريبة النظافة المحصلة**: 13,671.25 ريال
- **متوسط الإيجار الشهري**: 3,293 ريال

## 🗓️ **الفترة الزمنية للبيانات**
- **تاريخ البداية**: 15 يناير 2024
- **تاريخ النهاية**: 1 مارس 2024
- **فترة العقود**: سنة واحدة (بعضها سنتان)

## 🔄 **كيفية استيراد البيانات**

### إلى Supabase:
1. اذهب إلى Table Editor في Supabase
2. أنشئ الجداول باستخدام SQL المرفق
3. استخدم خاصية "Import data" لكل جدول
4. اختر الملف CSV المناسب

### إلى قاعدة بيانات أخرى:
1. أنشئ الجداول بنفس البنية
2. استخدم أدوات الاستيراد المناسبة
3. تأكد من تطابق أنواع البيانات

## ⚠️ **ملاحظات مهمة**
- جميع التواريخ بصيغة ISO 8601
- الأسعار بالريال السعودي
- النصوص باللغة العربية مع دعم UTF-8
- الهواتف بصيغة سعودية (05xxxxxxxx)
- الهويات الوطنية تجريبية

## 🛠️ **التخصيص**
يمكنك تعديل البيانات حسب احتياجاتك:
- إضافة المزيد من العملاء والعقارات
- تغيير الأسعار والتواريخ
- إضافة بيانات جديدة للاختبار

## 📞 **الدعم**
في حالة وجود مشاكل في استيراد البيانات، تأكد من:
- ترميز الملف UTF-8
- تطابق أسماء الأعمدة
- صحة أنواع البيانات
- وجود المراجع الخارجية (Foreign Keys)
