using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using SimpleRentalApp.Data;
using SimpleRentalApp.Models;
using Newtonsoft.Json;

namespace SimpleRentalApp.Services.CloudSync
{
    /// <summary>
    /// خدمة تكامل المزامنة السحابية مع التطبيق
    /// </summary>
    public class CloudSyncIntegrationService : IDisposable
    {
        private readonly DatabaseService _databaseService;
        private SyncManager? _syncManager;
        private SyncConfiguration? _config;
        private readonly string _configFilePath;
        private bool _isDisposed;

        public bool IsInitialized => _syncManager != null;
        public bool IsConnected => _syncManager?.IsConnected ?? false;
        public bool AutoSyncEnabled => _syncManager?.AutoSyncEnabled ?? false;

        // الأحداث
        public event EventHandler<SyncCompletedEventArgs>? SyncCompleted;
        public event EventHandler<SyncErrorEventArgs>? SyncError;
        public event EventHandler<ConnectionStatusChangedEventArgs>? ConnectionStatusChanged;

        public CloudSyncIntegrationService(DatabaseService databaseService)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            _configFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                "SimpleRentalApp", "sync_config.json");
        }

        /// <summary>
        /// تهيئة الخدمة عند بدء التطبيق
        /// </summary>
        public async Task InitializeAsync()
        {
            try
            {
                await LoadConfigurationAsync();
                
                if (_config != null)
                {
                    await InitializeSyncManagerAsync(_config);
                }
            }
            catch (Exception ex)
            {
                OnSyncError(new SyncErrorEventArgs
                {
                    Exception = ex,
                    ErrorMessage = $"فشل في تهيئة المزامنة السحابية: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// تحميل الإعدادات
        /// </summary>
        private async Task LoadConfigurationAsync()
        {
            try
            {
                if (File.Exists(_configFilePath))
                {
                    var json = await File.ReadAllTextAsync(_configFilePath);
                    _config = JsonConvert.DeserializeObject<SyncConfiguration>(json);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تحميل إعدادات المزامنة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تهيئة مدير المزامنة
        /// </summary>
        private async Task InitializeSyncManagerAsync(SyncConfiguration config)
        {
            try
            {
                if (_syncManager != null)
                {
                    await _syncManager.DisconnectAsync();
                    _syncManager.Dispose();
                }

                _syncManager = new SyncManager(_databaseService);
                
                // ربط الأحداث
                _syncManager.SyncCompleted += OnSyncCompleted;
                _syncManager.SyncError += OnSyncError;
                _syncManager.ConnectionStatusChanged += OnConnectionStatusChanged;

                // تهيئة الاتصال
                await _syncManager.InitializeAsync(config);
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تهيئة مدير المزامنة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// مزامنة كيان عند الإضافة
        /// </summary>
        public async Task SyncEntityAddedAsync<T>(T entity) where T : class
        {
            if (_syncManager == null || !IsConnected) return;

            try
            {
                await _syncManager.SyncEntityAsync(entity, SyncOperation.Create);
            }
            catch (Exception ex)
            {
                OnSyncError(new SyncErrorEventArgs
                {
                    Exception = ex,
                    ErrorMessage = $"فشل في مزامنة الكيان المضاف: {ex.Message}",
                    EntityType = typeof(T).Name
                });
            }
        }

        /// <summary>
        /// مزامنة كيان عند التحديث
        /// </summary>
        public async Task SyncEntityUpdatedAsync<T>(T entity) where T : class
        {
            if (_syncManager == null || !IsConnected) return;

            try
            {
                await _syncManager.SyncEntityAsync(entity, SyncOperation.Update);
            }
            catch (Exception ex)
            {
                OnSyncError(new SyncErrorEventArgs
                {
                    Exception = ex,
                    ErrorMessage = $"فشل في مزامنة الكيان المحدث: {ex.Message}",
                    EntityType = typeof(T).Name
                });
            }
        }

        /// <summary>
        /// مزامنة كيان عند الحذف
        /// </summary>
        public async Task SyncEntityDeletedAsync<T>(T entity) where T : class
        {
            if (_syncManager == null || !IsConnected) return;

            try
            {
                await _syncManager.SyncEntityAsync(entity, SyncOperation.Delete);
            }
            catch (Exception ex)
            {
                OnSyncError(new SyncErrorEventArgs
                {
                    Exception = ex,
                    ErrorMessage = $"فشل في مزامنة الكيان المحذوف: {ex.Message}",
                    EntityType = typeof(T).Name
                });
            }
        }

        /// <summary>
        /// مزامنة يدوية
        /// </summary>
        public async Task<SyncResult?> SyncNowAsync()
        {
            try
            {
                // التحقق من تهيئة مدير المزامنة
                if (_syncManager == null)
                {
                    // محاولة إعادة التهيئة
                    await InitializeAsync();

                    if (_syncManager == null)
                    {
                        return new SyncResult
                        {
                            Success = false,
                            Message = "مدير المزامنة غير مهيأ - تحقق من إعدادات المزامنة"
                        };
                    }
                }

                // التحقق من الاتصال
                if (!IsConnected)
                {
                    return new SyncResult
                    {
                        Success = false,
                        Message = "غير متصل بالخدمة السحابية - تحقق من إعدادات الاتصال"
                    };
                }

                return await _syncManager.SyncNowAsync();
            }
            catch (Exception ex)
            {
                return new SyncResult
                {
                    Success = false,
                    Message = $"فشل في المزامنة: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية
        /// </summary>
        public async Task<BackupResult?> CreateBackupAsync()
        {
            if (_syncManager == null)
            {
                throw new InvalidOperationException("مدير المزامنة غير مهيأ");
            }

            return await _syncManager.CreateBackupAsync();
        }

        /// <summary>
        /// اختبار الاتصال
        /// </summary>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                if (_syncManager == null)
                {
                    await InitializeAsync();
                }

                if (_syncManager == null) return false;

                return await _syncManager.TestConnectionAsync();
            }
            catch (Exception ex)
            {
                OnSyncError(new SyncErrorEventArgs
                {
                    Exception = ex,
                    ErrorMessage = $"فشل في اختبار الاتصال: {ex.Message}"
                });
                return false;
            }
        }

        /// <summary>
        /// تفعيل/إلغاء تفعيل المزامنة التلقائية
        /// </summary>
        public void SetAutoSync(bool enabled)
        {
            if (_syncManager == null) return;

            if (enabled)
            {
                _syncManager.StartAutoSync();
            }
            else
            {
                _syncManager.StopAutoSync();
            }
        }

        /// <summary>
        /// إعادة تحميل الإعدادات
        /// </summary>
        public async Task ReloadConfigurationAsync()
        {
            await LoadConfigurationAsync();
            
            if (_config != null)
            {
                await InitializeSyncManagerAsync(_config);
            }
        }

        /// <summary>
        /// الحصول على حالة المزامنة
        /// </summary>
        public SyncStatus GetSyncStatus()
        {
            return new SyncStatus
            {
                IsInitialized = IsInitialized,
                IsConnected = IsConnected,
                AutoSyncEnabled = AutoSyncEnabled,
                LastSyncTime = _syncManager?.LastSyncTime,
                ServiceName = _syncManager?.CurrentServiceName
            };
        }

        #region Event Handlers

        private void OnSyncCompleted(object? sender, SyncCompletedEventArgs e)
        {
            SyncCompleted?.Invoke(this, e);
        }

        private void OnSyncError(object? sender, SyncErrorEventArgs e)
        {
            SyncError?.Invoke(this, e);
        }

        private void OnSyncError(SyncErrorEventArgs e)
        {
            SyncError?.Invoke(this, e);
        }

        private void OnConnectionStatusChanged(object? sender, ConnectionStatusChangedEventArgs e)
        {
            ConnectionStatusChanged?.Invoke(this, e);
        }

        #endregion

        #region IDisposable

        public void Dispose()
        {
            if (_isDisposed) return;

            _syncManager?.Dispose();
            _syncManager = null;
            _isDisposed = true;
        }

        #endregion
    }

    /// <summary>
    /// حالة المزامنة
    /// </summary>
    public class SyncStatus
    {
        public bool IsInitialized { get; set; }
        public bool IsConnected { get; set; }
        public bool AutoSyncEnabled { get; set; }
        public DateTime? LastSyncTime { get; set; }
        public string? ServiceName { get; set; }
    }
}
