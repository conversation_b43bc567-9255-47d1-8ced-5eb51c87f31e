using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using SimpleRentalApp.Models;

namespace SimpleRentalApp.Services.CloudSync
{
    /// <summary>
    /// أداة تشخيص شاملة لمزامنة Supabase
    /// </summary>
    public class SupabaseDiagnostics : IDisposable
    {
        private readonly string _databaseUrl;
        private readonly string _apiKey;
        private readonly HttpClient _httpClient;

        public SupabaseDiagnostics(string databaseUrl, string apiKey)
        {
            _databaseUrl = databaseUrl;
            _apiKey = apiKey;
            _httpClient = new HttpClient();

            // إعداد Headers الصحيحة
            _httpClient.DefaultRequestHeaders.Add("apikey", _apiKey);
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_apiKey}");
            // Content-Type يجب أن يكون مع HttpContent وليس مع HttpClient
        }

        /// <summary>
        /// تشخيص شامل للمزامنة
        /// </summary>
        public async Task<DiagnosticResult> RunFullDiagnosticsAsync()
        {
            var result = new DiagnosticResult();
            
            try
            {
                // 1. اختبار الاتصال الأساسي
                result.ConnectionTest = await TestBasicConnectionAsync();
                
                // 2. اختبار وجود الجداول
                result.TablesTest = await TestTablesExistAsync();
                
                // 3. اختبار سياسات RLS
                result.RlsTest = await TestRlsPoliciesAsync();
                
                // 4. اختبار إدراج البيانات
                result.InsertTest = await TestDataInsertionAsync();
                
                // 5. اختبار قراءة البيانات
                result.ReadTest = await TestDataReadingAsync();
                
                // 6. اختبار تحديث البيانات
                result.UpdateTest = await TestDataUpdateAsync();
                
                // 7. اختبار حذف البيانات
                result.DeleteTest = await TestDataDeletionAsync();
                
                result.OverallSuccess = result.ConnectionTest.Success && 
                                      result.TablesTest.Success && 
                                      result.InsertTest.Success && 
                                      result.ReadTest.Success;
                
                result.Summary = GenerateSummary(result);
            }
            catch (Exception ex)
            {
                result.OverallSuccess = false;
                result.Summary = $"فشل التشخيص: {ex.Message}";
            }
            
            return result;
        }

        /// <summary>
        /// اختبار الاتصال الأساسي
        /// </summary>
        private async Task<TestResult> TestBasicConnectionAsync()
        {
            try
            {
                // اختبار الاتصال الأساسي مع Supabase REST API
                var response = await _httpClient.GetAsync($"{_databaseUrl}/rest/v1/");

                if (response.IsSuccessStatusCode)
                {
                    return new TestResult { Success = true, Message = $"الاتصال يعمل بنجاح ✅ - Status: {response.StatusCode}" };
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
                {
                    return new TestResult { Success = false, Message = "❌ خطأ 401: API Key غير صحيح أو منتهي الصلاحية" };
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.Forbidden)
                {
                    return new TestResult { Success = false, Message = "❌ خطأ 403: ممنوع - تحقق من الصلاحيات" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new TestResult { Success = false, Message = $"❌ فشل الاتصال: {response.StatusCode} - {errorContent}" };
                }
            }
            catch (HttpRequestException httpEx)
            {
                return new TestResult { Success = false, Message = $"❌ خطأ في الشبكة: {httpEx.Message}" };
            }
            catch (TaskCanceledException timeoutEx)
            {
                return new TestResult { Success = false, Message = "❌ انتهت مهلة الاتصال - تحقق من الإنترنت" };
            }
            catch (Exception ex)
            {
                return new TestResult { Success = false, Message = $"❌ خطأ غير متوقع: {ex.Message}" };
            }
        }

        /// <summary>
        /// اختبار وجود الجداول
        /// </summary>
        private async Task<TestResult> TestTablesExistAsync()
        {
            try
            {
                // اختبار جدول sync_data
                var syncDataResponse = await _httpClient.GetAsync($"{_databaseUrl}/rest/v1/sync_data?select=id&limit=1");

                if (syncDataResponse.IsSuccessStatusCode)
                {
                    var content = await syncDataResponse.Content.ReadAsStringAsync();
                    return new TestResult { Success = true, Message = "✅ جدول sync_data موجود ويمكن الوصول إليه" };
                }
                else if (syncDataResponse.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return new TestResult { Success = false, Message = "❌ جدول sync_data غير موجود - يجب تشغيل Supabase_Complete_Setup.sql" };
                }
                else if (syncDataResponse.StatusCode == System.Net.HttpStatusCode.Unauthorized)
                {
                    return new TestResult { Success = false, Message = "❌ غير مصرح بالوصول للجدول - تحقق من API Key" };
                }
                else if (syncDataResponse.StatusCode == System.Net.HttpStatusCode.Forbidden)
                {
                    return new TestResult { Success = false, Message = "❌ ممنوع الوصول للجدول - تحقق من سياسات RLS" };
                }
                else
                {
                    var errorContent = await syncDataResponse.Content.ReadAsStringAsync();
                    return new TestResult { Success = false, Message = $"❌ خطأ في الوصول للجدول: {syncDataResponse.StatusCode} - {errorContent}" };
                }
            }
            catch (Exception ex)
            {
                return new TestResult { Success = false, Message = $"❌ خطأ في اختبار الجداول: {ex.Message}" };
            }
        }

        /// <summary>
        /// اختبار سياسات RLS
        /// </summary>
        private async Task<TestResult> TestRlsPoliciesAsync()
        {
            try
            {
                // محاولة قراءة بسيطة لاختبار سياسات القراءة
                var readResponse = await _httpClient.GetAsync($"{_databaseUrl}/rest/v1/sync_data?select=id&limit=1");
                
                if (readResponse.IsSuccessStatusCode)
                {
                    return new TestResult { Success = true, Message = "سياسات RLS تسمح بالقراءة ✅" };
                }
                else if (readResponse.StatusCode == System.Net.HttpStatusCode.Forbidden)
                {
                    return new TestResult { Success = false, Message = "سياسات RLS تمنع القراءة ❌" };
                }
                else
                {
                    return new TestResult { Success = false, Message = $"خطأ في اختبار RLS: {readResponse.StatusCode}" };
                }
            }
            catch (Exception ex)
            {
                return new TestResult { Success = false, Message = $"خطأ في اختبار RLS: {ex.Message}" };
            }
        }

        /// <summary>
        /// اختبار إدراج البيانات
        /// </summary>
        private async Task<TestResult> TestDataInsertionAsync()
        {
            try
            {
                var testData = new
                {
                    entity_type = "DiagnosticTest",
                    entity_id = Guid.NewGuid().ToString(),
                    json_data = JsonConvert.SerializeObject(new { test = "data", timestamp = DateTime.UtcNow }),
                    operation = "Create",
                    user_id = "diagnostic",
                    device_id = "test-device"
                };

                var json = JsonConvert.SerializeObject(testData);

                // إنشاء HttpContent مع Content-Type الصحيح
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_databaseUrl}/rest/v1/sync_data", content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    return new TestResult { Success = true, Message = $"إدراج البيانات يعمل ✅ - تم إدراج السجل بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new TestResult { Success = false, Message = $"فشل الإدراج: {response.StatusCode} - {errorContent}" };
                }
            }
            catch (Exception ex)
            {
                return new TestResult { Success = false, Message = $"خطأ في اختبار الإدراج: {ex.Message}" };
            }
        }

        /// <summary>
        /// اختبار قراءة البيانات
        /// </summary>
        private async Task<TestResult> TestDataReadingAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync($"{_databaseUrl}/rest/v1/sync_data?entity_type=eq.DiagnosticTest&limit=5");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return new TestResult { Success = true, Message = $"قراءة البيانات تعمل ✅ - تم جلب البيانات" };
                }
                else
                {
                    return new TestResult { Success = false, Message = $"فشل في قراءة البيانات: {response.StatusCode}" };
                }
            }
            catch (Exception ex)
            {
                return new TestResult { Success = false, Message = $"خطأ في اختبار القراءة: {ex.Message}" };
            }
        }

        /// <summary>
        /// اختبار تحديث البيانات
        /// </summary>
        private async Task<TestResult> TestDataUpdateAsync()
        {
            try
            {
                // محاولة تحديث بيانات الاختبار
                var updateData = new
                {
                    json_data = JsonConvert.SerializeObject(new { test = "updated_data", timestamp = DateTime.UtcNow })
                };

                var json = JsonConvert.SerializeObject(updateData);

                // إنشاء HttpContent مع Content-Type الصحيح
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PatchAsync($"{_databaseUrl}/rest/v1/sync_data?entity_type=eq.DiagnosticTest", content);

                if (response.IsSuccessStatusCode)
                {
                    return new TestResult { Success = true, Message = "تحديث البيانات يعمل ✅" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new TestResult { Success = false, Message = $"فشل في التحديث: {response.StatusCode} - {errorContent}" };
                }
            }
            catch (Exception ex)
            {
                return new TestResult { Success = false, Message = $"خطأ في اختبار التحديث: {ex.Message}" };
            }
        }

        /// <summary>
        /// اختبار حذف البيانات
        /// </summary>
        private async Task<TestResult> TestDataDeletionAsync()
        {
            try
            {
                var response = await _httpClient.DeleteAsync($"{_databaseUrl}/rest/v1/sync_data?entity_type=eq.DiagnosticTest");

                if (response.IsSuccessStatusCode)
                {
                    return new TestResult { Success = true, Message = "حذف البيانات يعمل ✅" };
                }
                else
                {
                    return new TestResult { Success = false, Message = $"فشل في الحذف: {response.StatusCode}" };
                }
            }
            catch (Exception ex)
            {
                return new TestResult { Success = false, Message = $"خطأ في اختبار الحذف: {ex.Message}" };
            }
        }

        /// <summary>
        /// إنشاء ملخص التشخيص
        /// </summary>
        private string GenerateSummary(DiagnosticResult result)
        {
            var summary = new StringBuilder();
            summary.AppendLine("📊 ملخص التشخيص:");
            summary.AppendLine($"🔗 الاتصال: {(result.ConnectionTest.Success ? "✅" : "❌")}");
            summary.AppendLine($"🗃️ الجداول: {(result.TablesTest.Success ? "✅" : "❌")}");
            summary.AppendLine($"🔒 سياسات RLS: {(result.RlsTest.Success ? "✅" : "❌")}");
            summary.AppendLine($"➕ الإدراج: {(result.InsertTest.Success ? "✅" : "❌")}");
            summary.AppendLine($"📖 القراءة: {(result.ReadTest.Success ? "✅" : "❌")}");
            summary.AppendLine($"✏️ التحديث: {(result.UpdateTest.Success ? "✅" : "❌")}");
            summary.AppendLine($"🗑️ الحذف: {(result.DeleteTest.Success ? "✅" : "❌")}");
            
            if (result.OverallSuccess)
            {
                summary.AppendLine("\n🎉 جميع الاختبارات نجحت! المزامنة جاهزة للعمل.");
            }
            else
            {
                summary.AppendLine("\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة التفاصيل أعلاه.");
            }
            
            return summary.ToString();
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    /// <summary>
    /// نتيجة التشخيص الشامل
    /// </summary>
    public class DiagnosticResult
    {
        public TestResult ConnectionTest { get; set; } = new();
        public TestResult TablesTest { get; set; } = new();
        public TestResult RlsTest { get; set; } = new();
        public TestResult InsertTest { get; set; } = new();
        public TestResult ReadTest { get; set; } = new();
        public TestResult UpdateTest { get; set; } = new();
        public TestResult DeleteTest { get; set; } = new();
        public bool OverallSuccess { get; set; }
        public string Summary { get; set; } = "";
    }

    /// <summary>
    /// نتيجة اختبار واحد
    /// </summary>
    public class TestResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = "";
    }
}
