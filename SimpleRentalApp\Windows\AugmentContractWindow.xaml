<Window x:Class="SimpleRentalApp.Windows.AugmentContractWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة زيادة للعقد" 
        Height="650" Width="600"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        Background="#F8F9FA"
        WindowStyle="None"
        AllowsTransparency="True"
        ResizeMode="NoResize">
    
    <Border Background="White" CornerRadius="16" Margin="20">
        <Border.Effect>
            <DropShadowEffect Color="#424242" 
                            Direction="270" 
                            ShadowDepth="12" 
                            BlurRadius="24" 
                            Opacity="0.3"/>
        </Border.Effect>
        
        <Grid Margin="30">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Modern Header -->
            <Border Grid.Row="0" 
                    Background="#FF9800" 
                    CornerRadius="12" 
                    Padding="25,20"
                    Margin="0,0,0,25">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <TextBlock Text="➕" FontSize="28" Margin="0,0,15,0"/>
                        <TextBlock Text="إضافة زيادة للعقد" 
                                   FontSize="22" 
                                   FontWeight="Bold"
                                   Foreground="White"/>
                    </StackPanel>
                    <TextBlock Name="ContractInfoTextBlock"
                               Text="عقد رقم: --" 
                               FontSize="16" 
                               Foreground="#FFF3E0"
                               HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>

            <!-- Form Content -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    
                    <!-- Augment Type -->
                    <Border Background="#FFF3E0" 
                            BorderBrush="#FF9800" 
                            BorderThickness="2" 
                            CornerRadius="12" 
                            Padding="20"
                            Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="📋 نوع الزيادة *" 
                                       FontWeight="Bold" 
                                       FontSize="16"
                                       Foreground="#E65100"
                                       Margin="0,0,0,15"/>
                            <ComboBox Name="AugmentTypeComboBox"
                                      Height="45"
                                      FontSize="14"
                                      Padding="15,10"
                                      BorderBrush="#FF9800"
                                      BorderThickness="2"
                                      Background="White"
                                      DisplayMemberPath="Value"
                                      SelectedValuePath="Key"
                                      SelectionChanged="AugmentTypeComboBox_SelectionChanged"/>
                        </StackPanel>
                    </Border>

                    <!-- Base Rent (Read-only) -->
                    <Border Background="#F0F8FF"
                            BorderBrush="#4169E1"
                            BorderThickness="1"
                            CornerRadius="12"
                            Padding="20"
                            Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="💰 ثمن الإيجار الأساسي"
                                       FontWeight="Bold"
                                       FontSize="16"
                                       Foreground="#4169E1"
                                       Margin="0,0,0,15"/>
                            <TextBox Name="BaseRentTextBox"
                                     Height="45"
                                     FontSize="16"
                                     FontWeight="Bold"
                                     Padding="15,10"
                                     BorderBrush="#4169E1"
                                     BorderThickness="1"
                                     Background="#F8F9FA"
                                     Foreground="#4169E1"
                                     HorizontalContentAlignment="Center"
                                     IsReadOnly="True"/>
                        </StackPanel>
                    </Border>

                    <!-- Percentage -->
                    <Border Background="#E8F5E8"
                            BorderBrush="#4CAF50"
                            BorderThickness="2"
                            CornerRadius="12"
                            Padding="20"
                            Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="📊 نسبة الزيادة (%) *"
                                       FontWeight="Bold"
                                       FontSize="16"
                                       Foreground="#2E7D32"
                                       Margin="0,0,0,15"/>
                            <TextBox Name="PercentageTextBox"
                                     Height="50"
                                     FontSize="18"
                                     FontWeight="Bold"
                                     Padding="15,10"
                                     BorderBrush="#4CAF50"
                                     BorderThickness="2"
                                     Background="White"
                                     Foreground="#2E7D32"
                                     HorizontalContentAlignment="Center"
                                     TextChanged="PercentageTextBox_TextChanged"/>
                        </StackPanel>
                    </Border>

                    <!-- Calculated Amount (Read-only) -->
                    <Border Background="#FFF3E0"
                            BorderBrush="#FF9800"
                            BorderThickness="2"
                            CornerRadius="12"
                            Padding="20"
                            Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="💵 القيمة المحسوبة تلقائياً"
                                       FontWeight="Bold"
                                       FontSize="16"
                                       Foreground="#E65100"
                                       Margin="0,0,0,15"/>
                            <TextBox Name="CalculatedAmountTextBox"
                                     Height="50"
                                     FontSize="18"
                                     FontWeight="Bold"
                                     Padding="15,10"
                                     BorderBrush="#FF9800"
                                     BorderThickness="2"
                                     Background="#FFF8E1"
                                     Foreground="#E65100"
                                     HorizontalContentAlignment="Center"
                                     IsReadOnly="True"/>
                        </StackPanel>
                    </Border>

                    <!-- Application Date -->
                    <Border Background="#E3F2FD" 
                            BorderBrush="#2196F3" 
                            BorderThickness="2" 
                            CornerRadius="12" 
                            Padding="20"
                            Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="📅 تاريخ تطبيق الزيادة *" 
                                       FontWeight="Bold" 
                                       FontSize="16"
                                       Foreground="#1976D2"
                                       Margin="0,0,0,15"/>
                            <DatePicker Name="ApplicationDatePicker" 
                                        Height="45"
                                        FontSize="14"
                                        BorderBrush="#2196F3"
                                        BorderThickness="2"/>
                        </StackPanel>
                    </Border>

                    <!-- Notes -->
                    <Border Background="#F8F9FA"
                            BorderBrush="#DEE2E6"
                            BorderThickness="1"
                            CornerRadius="12"
                            Padding="20"
                            Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="📝 ملاحظات/سبب الزيادة"
                                       FontWeight="Bold"
                                       FontSize="16"
                                       Foreground="#495057"
                                       Margin="0,0,0,15"/>
                            <TextBox Name="NotesTextBox"
                                     Height="80"
                                     FontSize="14"
                                     Padding="15,10"
                                     BorderBrush="#CED4DA"
                                     BorderThickness="1"
                                     Background="White"
                                     TextWrapping="Wrap"
                                     AcceptsReturn="True"
                                     VerticalScrollBarVisibility="Auto"/>
                        </StackPanel>
                    </Border>

                </StackPanel>
            </ScrollViewer>

            <!-- Action Buttons -->
            <Border Grid.Row="2" 
                    Background="#F8F9FA" 
                    CornerRadius="12" 
                    Padding="20"
                    Margin="0,25,0,0">
                <StackPanel Orientation="Horizontal" 
                            HorizontalAlignment="Center">
                    <Button Name="SaveButton"
                            Content="💾 حفظ الزيادة"
                            Width="150"
                            Height="50"
                            FontSize="16"
                            FontWeight="Bold"
                            Background="#4CAF50"
                            Foreground="White"
                            BorderThickness="0"
                            Margin="0,0,15,0"
                            Click="SaveButton_Click"/>
                    
                    <Button Name="CancelButton"
                            Content="❌ إلغاء"
                            Width="120"
                            Height="50"
                            FontSize="16"
                            Background="#6C757D"
                            Foreground="White"
                            BorderThickness="0"
                            Click="CancelButton_Click"/>
                </StackPanel>
            </Border>
        </Grid>
    </Border>
</Window>
