<Window x:Class="SimpleRentalApp.Windows.AdvancedSearchDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="البحث المتقدم" 
        Height="600" 
        Width="700"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        Background="#F8F9FA"
        ResizeMode="NoResize">

    <Window.Resources>
        <!-- Modern Card Style -->
        <Style x:Key="ModernCard" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="4" BlurRadius="12" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern Button Style -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="8"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Success Button Style -->
        <Style x:Key="SuccessButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#4CAF50"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#388E3C"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Cancel Button Style -->
        <Style x:Key="CancelButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#757575"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#616161"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Modern TextBox Style -->
        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Padding" Value="12,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                         Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#2196F3"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Label Style -->
        <Style x:Key="ModernLabel" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="#424242"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>
    </Window.Resources>

    <Grid>
        <!-- Header -->
        <Border CornerRadius="0"
                Height="80"
                VerticalAlignment="Top">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#2196F3" Offset="0"/>
                    <GradientStop Color="#1976D2" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <StackPanel Orientation="Horizontal" 
                       HorizontalAlignment="Center" 
                       VerticalAlignment="Center">
                <TextBlock Text="🔍" FontSize="24" Margin="0,0,15,0"/>
                <TextBlock Text="البحث المتقدم في دفعات ضريبة النظافة" 
                          FontSize="20" 
                          FontWeight="Bold" 
                          Foreground="White"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <Border Style="{StaticResource ModernCard}" 
                Margin="20,100,20,20">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!-- Search Criteria -->
                    <TextBlock Text="معايير البحث" 
                              FontSize="18" 
                              FontWeight="Bold" 
                              Foreground="#2196F3" 
                              Margin="0,0,0,20"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Left Column -->
                        <StackPanel Grid.Column="0" Margin="0,0,15,0">
                            <TextBlock Text="🧾 رقم التوصيل:" Style="{StaticResource ModernLabel}"/>
                            <TextBox Name="ReceiptNumberTextBox" Style="{StaticResource ModernTextBox}"/>

                            <TextBlock Text="💰 المبلغ من:" Style="{StaticResource ModernLabel}"/>
                            <TextBox Name="AmountFromTextBox" Style="{StaticResource ModernTextBox}"/>

                            <TextBlock Text="💰 المبلغ إلى:" Style="{StaticResource ModernLabel}"/>
                            <TextBox Name="AmountToTextBox" Style="{StaticResource ModernTextBox}"/>

                            <TextBlock Text="📅 تاريخ الدفع من:" Style="{StaticResource ModernLabel}"/>
                            <DatePicker Name="DateFromPicker" Padding="12,10" FontSize="14"/>

                            <TextBlock Text="📅 تاريخ الدفع إلى:" Style="{StaticResource ModernLabel}"/>
                            <DatePicker Name="DateToPicker" Padding="12,10" FontSize="14"/>
                        </StackPanel>

                        <!-- Right Column -->
                        <StackPanel Grid.Column="1" Margin="15,0,0,0">
                            <TextBlock Text="💳 طريقة الدفع:" Style="{StaticResource ModernLabel}"/>
                            <ComboBox Name="PaymentMethodComboBox" Padding="12,10" FontSize="14">
                                <ComboBoxItem Content="جميع الطرق" IsSelected="True"/>
                                <ComboBoxItem Content="نقداً"/>
                                <ComboBoxItem Content="شيك"/>
                                <ComboBoxItem Content="تحويل بنكي"/>
                                <ComboBoxItem Content="بطاقة ائتمان"/>
                            </ComboBox>

                            <TextBlock Text="📊 حالة الدفع:" Style="{StaticResource ModernLabel}"/>
                            <ComboBox Name="PaymentStatusComboBox" Padding="12,10" FontSize="14">
                                <ComboBoxItem Content="جميع الحالات" IsSelected="True"/>
                                <ComboBoxItem Content="مكتملة" Tag="Complete"/>
                                <ComboBoxItem Content="جزئية" Tag="Partial"/>
                                <ComboBoxItem Content="مدفوعة" Tag="Paid"/>
                                <ComboBoxItem Content="غير مدفوعة" Tag="Unpaid"/>
                            </ComboBox>

                            <TextBlock Text="📅 السنة:" Style="{StaticResource ModernLabel}"/>
                            <ComboBox Name="YearComboBox" Padding="12,10" FontSize="14"/>

                            <TextBlock Text="📝 الملاحظات تحتوي على:" Style="{StaticResource ModernLabel}"/>
                            <TextBox Name="NotesTextBox" Style="{StaticResource ModernTextBox}"/>

                            <CheckBox Name="ExactMatchCheckBox" 
                                     Content="مطابقة تامة للنص" 
                                     Margin="0,15,0,0"/>
                        </StackPanel>
                    </Grid>

                    <!-- Quick Filters -->
                    <TextBlock Text="مرشحات سريعة" 
                              FontSize="16" 
                              FontWeight="Bold" 
                              Foreground="#2196F3" 
                              Margin="0,30,0,15"/>

                    <WrapPanel Orientation="Horizontal">
                        <CheckBox Name="LastMonthCheckBox" Content="آخر شهر" Margin="0,0,20,10"/>
                        <CheckBox Name="Last3MonthsCheckBox" Content="آخر 3 أشهر" Margin="0,0,20,10"/>
                        <CheckBox Name="CurrentYearCheckBox" Content="السنة الحالية" Margin="0,0,20,10"/>
                        <CheckBox Name="OverdueCheckBox" Content="متأخرة عن الموعد" Margin="0,0,20,10"/>
                        <CheckBox Name="LargeAmountsCheckBox" Content="مبالغ كبيرة (أكثر من 1000)" Margin="0,0,20,10"/>
                        <CheckBox Name="SmallAmountsCheckBox" Content="مبالغ صغيرة (أقل من 500)" Margin="0,0,20,10"/>
                    </WrapPanel>

                    <!-- Results Preview -->
                    <TextBlock Text="معاينة النتائج" 
                              FontSize="16" 
                              FontWeight="Bold" 
                              Foreground="#2196F3" 
                              Margin="0,30,0,15"/>

                    <Border Background="#F5F5F5" 
                           BorderBrush="#E0E0E0" 
                           BorderThickness="1" 
                           CornerRadius="6" 
                           Padding="15">
                        <StackPanel>
                            <TextBlock Name="ResultsPreviewText" 
                                      Text="اضغط 'معاينة' لرؤية عدد النتائج المتوقعة"
                                      FontStyle="Italic"
                                      Foreground="#666"/>
                            
                            <Button Name="PreviewButton" 
                                   Style="{StaticResource ModernButton}"
                                   Content="🔍 معاينة النتائج"
                                   HorizontalAlignment="Left"
                                   Margin="0,10,0,0"
                                   Click="PreviewButton_Click"/>
                        </StackPanel>
                    </Border>

                    <!-- Action Buttons -->
                    <StackPanel Orientation="Horizontal" 
                               HorizontalAlignment="Center" 
                               Margin="0,30,0,0">
                        <Button Name="SearchButton" 
                               Style="{StaticResource SuccessButton}"
                               Content="🔍 تطبيق البحث"
                               Click="SearchButton_Click"/>
                        <Button Name="ResetButton" 
                               Style="{StaticResource ModernButton}"
                               Content="🔄 إعادة تعيين"
                               Click="ResetButton_Click"/>
                        <Button Name="CancelButton" 
                               Style="{StaticResource CancelButton}"
                               Content="❌ إلغاء"
                               Click="CancelButton_Click"/>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
        </Border>
    </Grid>
</Window>
