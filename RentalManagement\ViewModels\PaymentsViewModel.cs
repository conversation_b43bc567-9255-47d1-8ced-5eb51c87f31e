using Microsoft.EntityFrameworkCore;
using RentalManagement.Data;
using RentalManagement.Helpers;
using RentalManagement.Models;
using RentalManagement.Services;
using System.Collections.ObjectModel;
using System.Windows.Input;

namespace RentalManagement.ViewModels
{
    public class PaymentsViewModel : BaseViewModel
    {
        private Payment? _selectedPayment;
        private string _searchText = string.Empty;
        private PaymentStatus? _statusFilter;
        private DateTime? _fromDate;
        private DateTime? _toDate;

        public PaymentsViewModel()
        {
            Title = "إدارة الأداءات";
            Payments = new ObservableCollection<Payment>();
            OverduePayments = new ObservableCollection<Payment>();

            // Initialize commands
            AddPaymentCommand = new RelayCommand(AddPayment);
            EditPaymentCommand = new RelayCommand<Payment>(EditPayment);
            DeletePaymentCommand = new RelayCommand<Payment>(DeletePayment);
            MarkAsPaidCommand = new RelayCommand<Payment>(MarkAsPaid);
            ViewPaymentDetailsCommand = new RelayCommand<Payment>(ViewPaymentDetails);
            SearchCommand = new RelayCommand(Search);
            RefreshCommand = new RelayCommand(LoadPayments);
            ClearFiltersCommand = new RelayCommand(ClearFilters);
            GenerateReceiptCommand = new RelayCommand<Payment>(GenerateReceipt);

            LoadPayments();
            LoadOverduePayments();
        }

        public ObservableCollection<Payment> Payments { get; }
        public ObservableCollection<Payment> OverduePayments { get; }

        public Payment? SelectedPayment
        {
            get => _selectedPayment;
            set => SetProperty(ref _selectedPayment, value);
        }

        public string SearchText
        {
            get => _searchText;
            set => SetProperty(ref _searchText, value);
        }

        public PaymentStatus? StatusFilter
        {
            get => _statusFilter;
            set
            {
                if (SetProperty(ref _statusFilter, value))
                {
                    Search();
                }
            }
        }

        public DateTime? FromDate
        {
            get => _fromDate;
            set
            {
                if (SetProperty(ref _fromDate, value))
                {
                    Search();
                }
            }
        }

        public DateTime? ToDate
        {
            get => _toDate;
            set
            {
                if (SetProperty(ref _toDate, value))
                {
                    Search();
                }
            }
        }

        // Commands
        public ICommand AddPaymentCommand { get; }
        public ICommand EditPaymentCommand { get; }
        public ICommand DeletePaymentCommand { get; }
        public ICommand MarkAsPaidCommand { get; }
        public ICommand ViewPaymentDetailsCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand RefreshCommand { get; }
        public ICommand ClearFiltersCommand { get; }
        public ICommand GenerateReceiptCommand { get; }

        private async void LoadPayments()
        {
            try
            {
                IsBusy = true;
                ClearError();

                using var context = DatabaseService.Instance.CreateContext();
                var payments = await context.Payments
                    .Include(p => p.Customer)
                    .Include(p => p.Property)
                    .OrderByDescending(p => p.DueDate)
                    .ToListAsync();

                Payments.Clear();
                foreach (var payment in payments)
                {
                    Payments.Add(payment);
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في تحميل الأداءات: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async void LoadOverduePayments()
        {
            try
            {
                using var context = DatabaseService.Instance.CreateContext();
                var overduePayments = await context.Payments
                    .Include(p => p.Customer)
                    .Include(p => p.Property)
                    .Where(p => p.Status == PaymentStatus.Unpaid && p.DueDate < DateTime.Now)
                    .OrderBy(p => p.DueDate)
                    .ToListAsync();

                OverduePayments.Clear();
                foreach (var payment in overduePayments)
                {
                    // Update status to overdue
                    payment.Status = PaymentStatus.Overdue;
                    OverduePayments.Add(payment);
                }

                // Update database with overdue status
                if (overduePayments.Any())
                {
                    context.UpdateRange(overduePayments);
                    await context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في تحميل الأداءات المتأخرة: {ex.Message}");
            }
        }

        private async void AddPayment()
        {
            var dialog = new PaymentDialogViewModel();
            var result = await ShowPaymentDialog(dialog);

            if (result == true && dialog.Payment != null)
            {
                try
                {
                    IsBusy = true;
                    ClearError();

                    using var context = DatabaseService.Instance.CreateContext();
                    context.Payments.Add(dialog.Payment);
                    await context.SaveChangesAsync();

                    Payments.Insert(0, dialog.Payment);
                }
                catch (Exception ex)
                {
                    SetError($"خطأ في إضافة الأداء: {ex.Message}");
                }
                finally
                {
                    IsBusy = false;
                }
            }
        }

        private async void EditPayment(Payment? payment)
        {
            if (payment == null) return;

            var dialog = new PaymentDialogViewModel(payment);
            var result = await ShowPaymentDialog(dialog);

            if (result == true && dialog.Payment != null)
            {
                try
                {
                    IsBusy = true;
                    ClearError();

                    using var context = DatabaseService.Instance.CreateContext();
                    context.Payments.Update(dialog.Payment);
                    await context.SaveChangesAsync();

                    // Update the payment in the collection
                    var index = Payments.IndexOf(payment);
                    if (index >= 0)
                    {
                        Payments[index] = dialog.Payment;
                        SelectedPayment = dialog.Payment;
                    }
                }
                catch (Exception ex)
                {
                    SetError($"خطأ في تعديل الأداء: {ex.Message}");
                }
                finally
                {
                    IsBusy = false;
                }
            }
        }

        private async void DeletePayment(Payment? payment)
        {
            if (payment == null) return;

            // Show confirmation dialog
            var result = System.Windows.MessageBox.Show(
                $"هل أنت متأكد من حذف هذا الأداء؟",
                "تأكيد الحذف",
                System.Windows.MessageBoxButton.YesNo,
                System.Windows.MessageBoxImage.Warning);

            if (result != System.Windows.MessageBoxResult.Yes) return;

            try
            {
                IsBusy = true;
                ClearError();

                using var context = DatabaseService.Instance.CreateContext();
                var paymentToDelete = await context.Payments.FindAsync(payment.Id);
                if (paymentToDelete != null)
                {
                    context.Payments.Remove(paymentToDelete);
                    await context.SaveChangesAsync();
                    Payments.Remove(payment);

                    if (SelectedPayment == payment)
                        SelectedPayment = null;
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في حذف الأداء: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async void MarkAsPaid(Payment? payment)
        {
            if (payment == null) return;

            try
            {
                IsBusy = true;
                ClearError();

                using var context = DatabaseService.Instance.CreateContext();
                var paymentToUpdate = await context.Payments.FindAsync(payment.Id);
                if (paymentToUpdate != null)
                {
                    paymentToUpdate.Status = PaymentStatus.Paid;
                    paymentToUpdate.PaymentDate = DateTime.Now;

                    await context.SaveChangesAsync();

                    // Update in collection
                    payment.Status = PaymentStatus.Paid;
                    payment.PaymentDate = DateTime.Now;

                    // Remove from overdue if it was there
                    if (OverduePayments.Contains(payment))
                    {
                        OverduePayments.Remove(payment);
                    }
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في تحديث حالة الأداء: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void ViewPaymentDetails(Payment? payment)
        {
            if (payment == null) return;

            // This would show a detailed view of the payment
            // For now, just select the payment
            SelectedPayment = payment;
        }

        private async void GenerateReceipt(Payment? payment)
        {
            if (payment == null || payment.Status != PaymentStatus.Paid) return;

            try
            {
                IsBusy = true;
                ClearError();

                // Load full payment details with related entities
                using var context = DatabaseService.Instance.CreateContext();
                var fullPayment = await context.Payments
                    .Include(p => p.Customer)
                    .Include(p => p.Property)
                    .FirstOrDefaultAsync(p => p.Id == payment.Id);

                if (fullPayment == null)
                {
                    SetError("لم يتم العثور على بيانات الأداء");
                    return;
                }

                // Generate receipt
                var receiptPath = await ReceiptService.Instance.GenerateReceiptAsync(fullPayment);

                // Ask user what to do with the receipt
                var result = System.Windows.MessageBox.Show(
                    $"تم إنشاء الإيصال بنجاح.\nهل تريد فتح الإيصال؟",
                    "إنشاء الإيصال",
                    System.Windows.MessageBoxButton.YesNo,
                    System.Windows.MessageBoxImage.Question);

                if (result == System.Windows.MessageBoxResult.Yes)
                {
                    await ReceiptService.Instance.OpenReceiptAsync(receiptPath);
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في إنشاء الإيصال: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async void Search()
        {
            try
            {
                IsBusy = true;
                ClearError();

                using var context = DatabaseService.Instance.CreateContext();
                var query = context.Payments
                    .Include(p => p.Customer)
                    .Include(p => p.Property)
                    .AsQueryable();

                if (!string.IsNullOrWhiteSpace(SearchText))
                {
                    query = query.Where(p => p.Customer.FullName.Contains(SearchText) ||
                                           p.Property.Location.Contains(SearchText) ||
                                           p.Notes.Contains(SearchText));
                }

                if (StatusFilter.HasValue)
                {
                    query = query.Where(p => p.Status == StatusFilter.Value);
                }

                if (FromDate.HasValue)
                {
                    query = query.Where(p => p.DueDate >= FromDate.Value);
                }

                if (ToDate.HasValue)
                {
                    query = query.Where(p => p.DueDate <= ToDate.Value);
                }

                var payments = await query.OrderByDescending(p => p.DueDate).ToListAsync();

                Payments.Clear();
                foreach (var payment in payments)
                {
                    Payments.Add(payment);
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في البحث: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void ClearFilters()
        {
            SearchText = string.Empty;
            StatusFilter = null;
            FromDate = null;
            ToDate = null;
            LoadPayments();
        }

        private async Task<bool?> ShowPaymentDialog(PaymentDialogViewModel dialog)
        {
            return await Task.Run(() =>
            {
                bool? result = null;

                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    var dialogWindow = new Views.PaymentDialog(dialog);
                    dialogWindow.Owner = System.Windows.Application.Current.MainWindow;
                    result = dialogWindow.ShowDialog();
                });

                return result;
            });
        }
    }
}
