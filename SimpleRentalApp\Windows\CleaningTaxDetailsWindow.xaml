<Window x:Class="SimpleRentalApp.Windows.CleaningTaxDetailsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تفاصيل دفعات ضريبة النظافة" 
        Height="700" 
        Width="900"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        Background="#F5F5F5"
        ResizeMode="CanResize">

    <Window.Resources>
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="EditButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#FF9800"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#F57C00"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="DeleteButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#F44336"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#D32F2F"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="AddButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#4CAF50"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#388E3C"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="CloseButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#757575"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#616161"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="ModernDataGrid" TargetType="DataGrid">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HorizontalGridLinesBrush" Value="#F0F0F0"/>
            <Setter Property="RowBackground" Value="White"/>
            <Setter Property="AlternatingRowBackground" Value="#FAFAFA"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
            <Setter Property="AutoGenerateColumns" Value="False"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="CanUserDeleteRows" Value="False"/>
            <Setter Property="IsReadOnly" Value="True"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="FontSize" Value="12"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" 
                Background="#4CAF50" 
                CornerRadius="8,8,0,0" 
                Padding="20,15"
                Margin="0,0,0,20">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📊" FontSize="24" Margin="0,0,10,0"/>
                <TextBlock Text="تفاصيل دفعات ضريبة النظافة" 
                          FontSize="18" 
                          FontWeight="Bold" 
                          Foreground="White"/>
            </StackPanel>
        </Border>

        <!-- Property Information -->
        <Border Grid.Row="1" 
                Background="White" 
                BorderBrush="#E0E0E0" 
                BorderThickness="1" 
                CornerRadius="6" 
                Padding="15"
                Margin="0,0,0,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock Text="معلومات المحل" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                    <TextBlock Name="PropertyNameText" FontSize="12" Margin="0,0,0,5"/>
                    <TextBlock Name="CustomerNameText" FontSize="12" Foreground="#666"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1">
                    <TextBlock Name="YearText" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                    <TextBlock Name="AnnualTaxText" FontSize="12" Margin="0,0,0,5"/>
                    <TextBlock Name="PaymentOptionText" FontSize="12" Foreground="#666"/>
                </StackPanel>
                
                <StackPanel Grid.Column="2">
                    <TextBlock Text="إحصائيات الدفع" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                    <TextBlock Name="PaidAmountText" FontSize="12" Margin="0,0,0,5" Foreground="#4CAF50"/>
                    <TextBlock Name="RemainingAmountText" FontSize="12" Foreground="#F57C00"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="2" 
                   Orientation="Horizontal" 
                   HorizontalAlignment="Left" 
                   Margin="0,0,0,15">
            <Button Name="AddPaymentButton" 
                   Content="➕ إضافة دفعة جديدة" 
                   Style="{StaticResource AddButton}" 
                   Click="AddPaymentButton_Click"/>
            <Button Name="EditPaymentButton" 
                   Content="✏️ تعديل الدفعة" 
                   Style="{StaticResource EditButton}" 
                   Click="EditPaymentButton_Click"
                   IsEnabled="False"/>
            <Button Name="DeletePaymentButton" 
                   Content="🗑️ حذف الدفعة" 
                   Style="{StaticResource DeleteButton}" 
                   Click="DeletePaymentButton_Click"
                   IsEnabled="False"/>
            <Button Name="RefreshButton" 
                   Content="🔄 تحديث" 
                   Style="{StaticResource ModernButton}" 
                   Click="RefreshButton_Click"/>
        </StackPanel>

        <!-- Payments DataGrid -->
        <Border Grid.Row="3" 
                Background="White" 
                BorderBrush="#E0E0E0" 
                BorderThickness="1" 
                CornerRadius="6">
            <DataGrid Name="PaymentsDataGrid" 
                     Style="{StaticResource ModernDataGrid}"
                     SelectionChanged="PaymentsDataGrid_SelectionChanged">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="التاريخ"
                                       Binding="{Binding PaymentDateDisplay}"
                                       Width="100"/>
                    <DataGridTextColumn Header="المبلغ"
                                       Binding="{Binding CleaningTaxAmountDisplay}"
                                       Width="100"/>
                    <DataGridTextColumn Header="المتبقي قبل"
                                       Binding="{Binding RemainingBeforeDisplay}"
                                       Width="100"/>
                    <DataGridTextColumn Header="المتبقي بعد"
                                       Binding="{Binding RemainingAfterDisplay}"
                                       Width="100"/>
                    <DataGridTextColumn Header="حالة الدفع"
                                       Binding="{Binding PaymentStatusText}"
                                       Width="80"/>
                    <DataGridTextColumn Header="طريقة الدفع"
                                       Binding="{Binding PaymentMethod}"
                                       Width="100"/>
                    <DataGridTextColumn Header="الملاحظات"
                                       Binding="{Binding Notes}"
                                       Width="*"/>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- Status and Close Button -->
        <Grid Grid.Row="4" Margin="0,15,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <TextBlock Name="StatusTextBlock" 
                      Grid.Column="0"
                      Text="جاهز" 
                      FontSize="12" 
                      Foreground="#666" 
                      VerticalAlignment="Center"/>
            
            <Button Name="CloseButton" 
                   Grid.Column="1"
                   Content="❌ إغلاق" 
                   Style="{StaticResource CloseButton}" 
                   Click="CloseButton_Click"
                   MinWidth="100"/>
        </Grid>
    </Grid>
</Window>
