using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using SimpleRentalApp.Models;
using SimpleRentalApp.Services;

namespace SimpleRentalApp.Windows
{
    public partial class PropertySelectionDialog : Window
    {
        private List<PropertyDisplayInfo> _allProperties = new();
        private List<PropertyDisplayInfo> _filteredProperties = new();

        public Property? SelectedProperty { get; private set; }
        public new bool DialogResult { get; private set; }

        public PropertySelectionDialog(List<Property> properties, string title = "اختيار المحل")
        {
            InitializeComponent();
            TitleText.Text = title;
            InitializeProperties(properties);
        }

        private async void InitializeProperties(List<Property> properties)
        {
            try
            {
                _allProperties.Clear();
                
                foreach (var property in properties)
                {
                    Customer? customer = null;
                    if (property.CustomerId.HasValue)
                    {
                        customer = await DatabaseService.Instance.GetCustomerByIdAsync(property.CustomerId.Value);
                    }

                    var displayInfo = new PropertyDisplayInfo
                    {
                        Property = property,
                        Name = property.Name,
                        CustomerDisplay = customer != null ? $"المكتري: {customer.FullName}" : "لا يوجد مكتري",
                        AddressDisplay = !string.IsNullOrEmpty(property.Address) ? $"العنوان: {property.Address}" : "لا يوجد عنوان",
                        MonthlyRentDisplay = $"{property.MonthlyRent:N0} درهم"
                    };

                    _allProperties.Add(displayInfo);
                }

                _filteredProperties = new List<PropertyDisplayInfo>(_allProperties);
                PropertiesListBox.ItemsSource = _filteredProperties;

                // Setup selection changed event
                PropertiesListBox.SelectionChanged += (s, e) =>
                {
                    SelectButton.IsEnabled = PropertiesListBox.SelectedItem != null;
                };
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المحلات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            var searchText = SearchTextBox.Text?.ToLower() ?? "";
            
            if (string.IsNullOrWhiteSpace(searchText))
            {
                _filteredProperties = new List<PropertyDisplayInfo>(_allProperties);
            }
            else
            {
                _filteredProperties = _allProperties.Where(p =>
                    p.Name.ToLower().Contains(searchText) ||
                    p.CustomerDisplay.ToLower().Contains(searchText) ||
                    p.AddressDisplay.ToLower().Contains(searchText)
                ).ToList();
            }

            PropertiesListBox.ItemsSource = _filteredProperties;
        }

        private void PropertiesListBox_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (PropertiesListBox.SelectedItem is PropertyDisplayInfo selectedInfo)
            {
                SelectedProperty = selectedInfo.Property;
                DialogResult = true;
                Close();
            }
        }

        private void SelectButton_Click(object sender, RoutedEventArgs e)
        {
            if (PropertiesListBox.SelectedItem is PropertyDisplayInfo selectedInfo)
            {
                SelectedProperty = selectedInfo.Property;
                DialogResult = true;
                Close();
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }

    public class PropertyDisplayInfo
    {
        public Property Property { get; set; } = new();
        public string Name { get; set; } = string.Empty;
        public string CustomerDisplay { get; set; } = string.Empty;
        public string AddressDisplay { get; set; } = string.Empty;
        public string MonthlyRentDisplay { get; set; } = string.Empty;
    }
}
