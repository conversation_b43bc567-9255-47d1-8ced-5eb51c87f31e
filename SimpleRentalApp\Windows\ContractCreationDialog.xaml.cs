using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using SimpleRentalApp.Models;
using SimpleRentalApp.Services;

namespace SimpleRentalApp.Windows;

public partial class ContractCreationDialog : Window
{
    private List<Customer> _customers = new();
    private List<Property> _properties = new();
    private Contract? _contract;
    private bool _isUpdating = false;
    
    public ContractCreationDialog(Contract? contract = null)
    {
        InitializeComponent();
        _contract = contract;
        
        if (_contract != null)
        {
            HeaderTextBlock.Text = "تعديل العقد";
        }
        
        SetDefaultValues();

        // Load data asynchronously after window is loaded
        Loaded += async (s, e) => await LoadDataAsync();
    }

    private async Task LoadDataAsync()
    {
        try
        {
            _isUpdating = true;

            _customers = await DatabaseService.Instance.GetCustomersAsync();
            _properties = await DatabaseService.Instance.GetPropertiesAsync();

            // Set ItemsSource only once to avoid clearing
            if (CustomerComboBox.ItemsSource == null)
                CustomerComboBox.ItemsSource = _customers;

            if (PropertyComboBox.ItemsSource == null)
                PropertyComboBox.ItemsSource = _properties;

            _isUpdating = false;

            // If editing existing contract, load its data
            if (_contract != null)
            {
                LoadContractData();
            }
        }
        catch (Exception ex)
        {
            _isUpdating = false;
            MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void SetDefaultValues()
    {
        StartDatePicker.SelectedDate = DateTime.Today;
        EndDatePicker.SelectedDate = DateTime.Today.AddYears(1);
        InitialRentAmountTextBox.Text = "0";
        PaymentMethodComboBox.SelectedIndex = 0;
    }

    private void LoadContractData()
    {
        if (_contract == null) return;

        try
        {
            _isUpdating = true;

            CustomerComboBox.SelectedValue = _contract.CustomerId;
            PropertyComboBox.SelectedValue = _contract.PropertyId;
            StartDatePicker.SelectedDate = _contract.StartDate;
            EndDatePicker.SelectedDate = _contract.EndDate;
            InitialRentAmountTextBox.Text = _contract.InitialRentAmount.ToString("F0");
            NotesTextBox.Text = _contract.Notes ?? string.Empty;

            // Set payment method if available (for future use)
            PaymentMethodComboBox.SelectedIndex = 0;

            _isUpdating = false;

            // Update calculations
            UpdateDurationDisplay();
        }
        catch (Exception ex)
        {
            _isUpdating = false;
            MessageBox.Show($"خطأ في تحميل بيانات العقد: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void CustomerComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        if (_isUpdating) return;

        // Don't filter properties - show all properties
        // The user should be able to select any property for any customer
        UpdateRentAmountFromProperty();
    }

    private void PropertyComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        if (_isUpdating) return;

        UpdateRentAmountFromProperty();
    }

    private void UpdateRentAmountFromProperty()
    {
        if (_isUpdating) return;

        if (PropertyComboBox.SelectedItem is Property property)
        {
            InitialRentAmountTextBox.Text = property.MonthlyRent.ToString("F0");
        }
    }

    private void DatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
    {
        UpdateDurationDisplay();
    }

    private void UpdateDurationDisplay()
    {
        try
        {
            if (StartDatePicker == null || EndDatePicker == null ||
                DurationTextBlock == null || DurationBorder == null)
                return;

            if (StartDatePicker.SelectedDate.HasValue && EndDatePicker.SelectedDate.HasValue)
            {
                var startDate = StartDatePicker.SelectedDate.Value;
                var endDate = EndDatePicker.SelectedDate.Value;

                if (endDate > startDate)
                {
                    var duration = endDate - startDate;
                    var years = (int)(duration.TotalDays / 365.25);
                    var months = (int)((duration.TotalDays % 365.25) / 30.44);
                    var days = (int)(duration.TotalDays % 30.44);

                    string durationText;
                    if (years > 0)
                        durationText = $"{years} سنة و {months} شهر";
                    else if (months > 0)
                        durationText = $"{months} شهر و {days} يوم";
                    else
                        durationText = $"{(int)duration.TotalDays} يوم";

                    DurationTextBlock.Text =
                        $"📅 مدة العقد: {durationText}\n" +
                        $"📊 إجمالي الأيام: {(int)duration.TotalDays} يوم\n" +
                        $"📈 إجمالي الأشهر: {(int)(duration.TotalDays / 30.44)} شهر";

                    DurationBorder.Visibility = Visibility.Visible;
                }
                else
                {
                    DurationBorder.Visibility = Visibility.Collapsed;
                }
            }
            else
            {
                DurationBorder.Visibility = Visibility.Collapsed;
            }
        }
        catch
        {
            if (DurationBorder != null)
                DurationBorder.Visibility = Visibility.Collapsed;
        }
    }

    private async void SaveButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // Check for null controls first
            if (CustomerComboBox == null || PropertyComboBox == null ||
                StartDatePicker == null || EndDatePicker == null ||
                InitialRentAmountTextBox == null || NotesTextBox == null ||
                PaymentMethodComboBox == null)
            {
                MessageBox.Show("خطأ في تحميل النافذة. يرجى إعادة المحاولة.", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            // Validation
            if (CustomerComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار المكتري", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (PropertyComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار العقار", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (!StartDatePicker.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى اختيار تاريخ بداية العقد", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (!EndDatePicker.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى اختيار تاريخ نهاية العقد", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (EndDatePicker.SelectedDate.Value <= StartDatePicker.SelectedDate.Value)
            {
                MessageBox.Show("تاريخ نهاية العقد يجب أن يكون بعد تاريخ البداية", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (!decimal.TryParse(InitialRentAmountTextBox.Text, out decimal initialRentAmount) || initialRentAmount <= 0)
            {
                MessageBox.Show("يرجى إدخال مبلغ صحيح للإيجار الأولي", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // Create or update contract
            var contract = _contract ?? new Contract();
            
            contract.CustomerId = (int)CustomerComboBox.SelectedValue;
            contract.PropertyId = (int)PropertyComboBox.SelectedValue;
            contract.StartDate = StartDatePicker.SelectedDate.Value;
            contract.EndDate = EndDatePicker.SelectedDate.Value;
            contract.InitialRentAmount = initialRentAmount;
            contract.RentIncreaseCount = _contract?.RentIncreaseCount ?? 0;
            contract.RentIncreasePercentage = _contract?.RentIncreasePercentage ?? 0;
            contract.Notes = NotesTextBox.Text.Trim();

            if (_contract == null)
            {
                contract.CreatedDate = DateTime.Now;
            }
            contract.UpdatedDate = DateTime.Now;

            // Save to database
            await DatabaseService.Instance.SaveContractAsync(contract);

            // تحديث المحل لربطه بالمكتري الجديد
            var property = await DatabaseService.Instance.GetPropertyByIdAsync(contract.PropertyId);
            if (property != null)
            {
                property.CustomerId = contract.CustomerId;
                property.Status = PropertyStatus.Rented;
                property.UpdatedDate = DateTime.Now;
                await DatabaseService.Instance.SavePropertyAsync(property);

                System.Diagnostics.Debug.WriteLine($"Updated property {property.Name} with CustomerId: {contract.CustomerId}");
            }

            MessageBox.Show("تم حفظ العقد بنجاح! ✅", "نجح الحفظ",
                MessageBoxButton.OK, MessageBoxImage.Information);

            DialogResult = true;
            Close();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في حفظ العقد: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        DialogResult = false;
        Close();
    }
}
