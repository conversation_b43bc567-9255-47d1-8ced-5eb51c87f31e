using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using SimpleRentalApp.Models;

namespace SimpleRentalApp.Windows;

public partial class ContractDetailsDialog : Window
{
    private readonly Contract _contract;
    public bool ContractUpdated { get; private set; } = false;
    
    public ContractDetailsDialog(Contract contract)
    {
        InitializeComponent();
        _contract = contract;
        LoadContractDetails();
    }

    private void LoadContractDetails()
    {
        try
        {
            // Header information
            ContractNumberTextBlock.Text = _contract.Id > 0 ? $"عقد رقم: {_contract.Id}" : "عقد جديد";
            StatusTextBlock.Text = _contract.StatusDisplay ?? "غير محدد";
            StatusBorder.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(_contract.StatusColor ?? "#9E9E9E"));

        // Customer information
        if (_contract.Customer != null)
        {
            CustomerNameTextBlock.Text = $"الاسم: {_contract.Customer.FullName ?? "غير محدد"}";
            CustomerPhoneTextBlock.Text = $"الهاتف: {_contract.Customer.Phone ?? "غير محدد"}";
            CustomerEmailTextBlock.Text = $"البريد: {(!string.IsNullOrEmpty(_contract.Customer.Email) ? _contract.Customer.Email : "غير محدد")}";
        }
        else
        {
            CustomerNameTextBlock.Text = "الاسم: غير محدد";
            CustomerPhoneTextBlock.Text = "الهاتف: غير محدد";
            CustomerEmailTextBlock.Text = "البريد: غير محدد";
        }

        // Property information
        if (_contract.Property != null)
        {
            PropertyNameTextBlock.Text = $"اسم العقار: {_contract.Property.Name ?? "غير محدد"}";
            PropertyAddressTextBlock.Text = $"العنوان: {_contract.Property.Address ?? "غير محدد"}";
            PropertyTypeTextBlock.Text = $"النوع: {_contract.Property.Type.ToString() ?? "غير محدد"}";
        }
        else
        {
            PropertyNameTextBlock.Text = "اسم العقار: غير محدد";
            PropertyAddressTextBlock.Text = "العنوان: غير محدد";
            PropertyTypeTextBlock.Text = "النوع: غير محدد";
        }

        // Contract dates
        StartDateTextBlock.Text = _contract.StartDate.ToString("dd/MM/yyyy");
        EndDateTextBlock.Text = _contract.EndDate.ToString("dd/MM/yyyy");
        DurationTextBlock.Text = _contract.DurationDisplay;

        // Rent information
        InitialRentTextBlock.Text = $"{_contract.InitialRentAmount:N0} درهم";
        CurrentRentTextBlock.Text = $"{_contract.CurrentRentAmount:N0} درهم";
        TotalIncreaseTextBlock.Text = $"{_contract.TotalRentIncrease:N0} درهم";

        // Rent increase details
        IncreaseCountTextBlock.Text = $"عدد الزيادات: {_contract.RentIncreaseCount} مرة";
        IncreasePercentageTextBlock.Text = $"نسبة الزيادة: {_contract.RentIncreasePercentage:F1}% لكل مرة";
        RemainingTimeTextBlock.Text = _contract.DaysRemainingDisplay;

        // Set remaining time color based on status
        RemainingTimeTextBlock.Foreground = _contract.Status switch
        {
            ContractStatus.Active => new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4CAF50")),
            ContractStatus.ExpiringSoon => new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FF9800")),
            ContractStatus.Expired => new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F44336")),
            _ => Brushes.Gray
        };

        // Notes
        NotesTextBlock.Text = !string.IsNullOrEmpty(_contract.Notes) ? _contract.Notes : "لا توجد ملاحظات";

        // Load rent increases
        LoadRentIncreases();

        // Load contract augments
        LoadContractAugments();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل تفاصيل العقد: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void LoadRentIncreases()
    {
        RentIncreasesPanel.Children.Clear();

        if (_contract.RentIncreases?.Any() == true)
        {
            RentIncreasesSection.Visibility = Visibility.Visible;

            foreach (var increase in _contract.RentIncreases.OrderBy(r => r.IncreaseDate))
            {
                var increaseCard = CreateRentIncreaseCard(increase);
                RentIncreasesPanel.Children.Add(increaseCard);
            }
        }
        else
        {
            RentIncreasesSection.Visibility = Visibility.Collapsed;
        }
    }

    private System.Windows.Controls.Border CreateRentIncreaseCard(RentIncrease increase)
    {
        var border = new System.Windows.Controls.Border
        {
            Background = Brushes.White,
            BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FFC107")),
            BorderThickness = new Thickness(1),
            CornerRadius = new CornerRadius(8),
            Margin = new Thickness(0, 5, 0, 5),
            Padding = new Thickness(15)
        };

        var grid = new Grid();
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

        // Increase number/icon
        var iconPanel = new StackPanel { VerticalAlignment = VerticalAlignment.Center };
        iconPanel.Children.Add(new TextBlock
        {
            Text = "📈",
            FontSize = 20,
            HorizontalAlignment = HorizontalAlignment.Center
        });
        Grid.SetColumn(iconPanel, 0);
        grid.Children.Add(iconPanel);

        // Increase details
        var detailsPanel = new StackPanel
        {
            Margin = new Thickness(15, 0, 15, 0),
            VerticalAlignment = VerticalAlignment.Center
        };

        detailsPanel.Children.Add(new TextBlock
        {
            Text = $"تاريخ الزيادة: {increase.IncreaseDate:dd/MM/yyyy}",
            FontSize = 12,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E65100")),
            Margin = new Thickness(0, 0, 0, 5)
        });

        detailsPanel.Children.Add(new TextBlock
        {
            Text = increase.IncreaseDetails,
            FontSize = 14,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#F57C00")),
            Margin = new Thickness(0, 0, 0, 5)
        });

        if (!string.IsNullOrEmpty(increase.Reason))
        {
            detailsPanel.Children.Add(new TextBlock
            {
                Text = $"السبب: {increase.Reason}",
                FontSize = 12,
                Foreground = Brushes.Gray,
                FontStyle = FontStyles.Italic
            });
        }

        Grid.SetColumn(detailsPanel, 1);
        grid.Children.Add(detailsPanel);

        // Increase amount highlight
        var amountPanel = new StackPanel
        {
            VerticalAlignment = VerticalAlignment.Center,
            HorizontalAlignment = HorizontalAlignment.Center
        };

        amountPanel.Children.Add(new TextBlock
        {
            Text = increase.IncreaseAmountDisplay,
            FontSize = 16,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4CAF50")),
            HorizontalAlignment = HorizontalAlignment.Center
        });

        amountPanel.Children.Add(new TextBlock
        {
            Text = increase.IncreasePercentageDisplay,
            FontSize = 12,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FF9800")),
            HorizontalAlignment = HorizontalAlignment.Center
        });

        Grid.SetColumn(amountPanel, 2);
        grid.Children.Add(amountPanel);

        border.Child = grid;
        return border;
    }

    private void LoadContractAugments()
    {
        AugmentsPanel.Children.Clear();

        if (_contract.Augments?.Any() == true)
        {
            AugmentsSection.Visibility = Visibility.Visible;

            foreach (var augment in _contract.Augments.Where(a => a.IsActive).OrderByDescending(a => a.CreatedDate))
            {
                var augmentCard = CreateAugmentCard(augment);
                AugmentsPanel.Children.Add(augmentCard);
            }
        }
        else
        {
            AugmentsSection.Visibility = Visibility.Collapsed;
        }
    }

    private System.Windows.Controls.Border CreateAugmentCard(ContractAugment augment)
    {
        var border = new System.Windows.Controls.Border
        {
            Background = Brushes.White,
            BorderBrush = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FF9800")),
            BorderThickness = new Thickness(1),
            CornerRadius = new CornerRadius(8),
            Margin = new Thickness(0, 5, 0, 5),
            Padding = new Thickness(15)
        };

        var grid = new Grid();
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

        // Augment icon
        var iconPanel = new StackPanel { VerticalAlignment = VerticalAlignment.Center };
        iconPanel.Children.Add(new TextBlock
        {
            Text = augment.AugmentIcon,
            FontSize = 20,
            HorizontalAlignment = HorizontalAlignment.Center
        });
        Grid.SetColumn(iconPanel, 0);
        grid.Children.Add(iconPanel);

        // Augment details
        var detailsPanel = new StackPanel
        {
            Margin = new Thickness(15, 0, 15, 0),
            VerticalAlignment = VerticalAlignment.Center
        };

        detailsPanel.Children.Add(new TextBlock
        {
            Text = $"{augment.AugmentTypeDisplay}",
            FontSize = 14,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E65100")),
            Margin = new Thickness(0, 0, 0, 5)
        });

        detailsPanel.Children.Add(new TextBlock
        {
            Text = $"تاريخ التطبيق: {augment.ApplicationDateDisplay}",
            FontSize = 12,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FF9800")),
            Margin = new Thickness(0, 0, 0, 5)
        });

        if (!string.IsNullOrEmpty(augment.Notes))
        {
            detailsPanel.Children.Add(new TextBlock
            {
                Text = $"ملاحظات: {augment.Notes}",
                FontSize = 12,
                Foreground = Brushes.Gray,
                FontStyle = FontStyles.Italic
            });
        }

        Grid.SetColumn(detailsPanel, 1);
        grid.Children.Add(detailsPanel);

        // Amount highlight
        var amountPanel = new StackPanel
        {
            VerticalAlignment = VerticalAlignment.Center,
            HorizontalAlignment = HorizontalAlignment.Center
        };

        amountPanel.Children.Add(new TextBlock
        {
            Text = augment.CalculatedAmountDisplay,
            FontSize = 16,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4CAF50")),
            HorizontalAlignment = HorizontalAlignment.Center
        });

        amountPanel.Children.Add(new TextBlock
        {
            Text = augment.PercentageDisplay,
            FontSize = 12,
            Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FF9800")),
            HorizontalAlignment = HorizontalAlignment.Center
        });

        Grid.SetColumn(amountPanel, 2);
        grid.Children.Add(amountPanel);

        border.Child = grid;
        return border;
    }

    private async void EditButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var editDialog = new ContractDetailsDialog(_contract);

            // Set owner safely
            try
            {
                if (this.IsLoaded)
                {
                    editDialog.Owner = this;
                }
                else
                {
                    editDialog.WindowStartupLocation = WindowStartupLocation.CenterScreen;
                }
            }
            catch
            {
                editDialog.WindowStartupLocation = WindowStartupLocation.CenterScreen;
            }

            if (editDialog.ShowDialog() == true)
            {
                // Reload contract details after edit
                var updatedContract = await SimpleRentalApp.Services.DatabaseService.Instance.GetContractByIdAsync(_contract.Id);
                if (updatedContract != null)
                {
                    // Update the current contract reference
                    _contract.InitialRentAmount = updatedContract.InitialRentAmount;
                    _contract.StartDate = updatedContract.StartDate;
                    _contract.EndDate = updatedContract.EndDate;
                    _contract.RentIncreaseCount = updatedContract.RentIncreaseCount;
                    _contract.RentIncreasePercentage = updatedContract.RentIncreasePercentage;
                    _contract.Notes = updatedContract.Notes;
                    _contract.RentIncreases = updatedContract.RentIncreases;

                    // Reload the display
                    LoadContractDetails();

                    ContractUpdated = true;
                    MessageBox.Show("تم تحديث العقد بنجاح! ✅", "نجح التحديث",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحديث العقد: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void AttachmentsButton_Click(object sender, RoutedEventArgs e)
    {
        var attachmentsDialog = new AttachmentsDialog(_contract);

        // Set owner safely
        try
        {
            if (this.IsLoaded)
            {
                attachmentsDialog.Owner = this;
            }
            else
            {
                attachmentsDialog.WindowStartupLocation = WindowStartupLocation.CenterScreen;
            }
        }
        catch
        {
            attachmentsDialog.WindowStartupLocation = WindowStartupLocation.CenterScreen;
        }

        attachmentsDialog.ShowDialog();
    }

    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }
}
