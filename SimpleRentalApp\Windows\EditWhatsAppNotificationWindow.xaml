<Window x:Class="SimpleRentalApp.Windows.EditWhatsAppNotificationWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="✏️ تعديل إشعار الواتساب"
        Height="700" Width="900"
        WindowStartupLocation="CenterOwner"
        Background="#F5F5F5"
        FontFamily="Segoe UI"
        FlowDirection="RightToLeft"
        KeyDown="Window_KeyDown">

    <Window.Resources>
        <!-- Color Resources -->
        <SolidColorBrush x:Key="PrimaryColor" Color="#25D366"/>
        <SolidColorBrush x:Key="PrimaryDarkColor" Color="#128C7E"/>
        <SolidColorBrush x:Key="SecondaryColor" Color="#34495E"/>
        <SolidColorBrush x:Key="AccentColor" Color="#3498DB"/>
        <SolidColorBrush x:Key="SuccessColor" Color="#27AE60"/>
        <SolidColorBrush x:Key="WarningColor" Color="#F39C12"/>
        <SolidColorBrush x:Key="DangerColor" Color="#E74C3C"/>
        <SolidColorBrush x:Key="InfoColor" Color="#3498DB"/>
        <SolidColorBrush x:Key="BackgroundColor" Color="#F8F9FA"/>
        <SolidColorBrush x:Key="SurfaceColor" Color="White"/>
        <SolidColorBrush x:Key="BorderColor" Color="#E9ECEF"/>
        <SolidColorBrush x:Key="TextColor" Color="#2C3E50"/>

        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryColor}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource PrimaryDarkColor}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Success Button Style -->
        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="{StaticResource SuccessColor}"/>
        </Style>

        <!-- Danger Button Style -->
        <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="{StaticResource DangerColor}"/>
        </Style>

        <!-- Warning Button Style -->
        <Style x:Key="WarningButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="{StaticResource WarningColor}"/>
        </Style>

        <!-- Info Button Style -->
        <Style x:Key="InfoButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="{StaticResource InfoColor}"/>
        </Style>

        <!-- Card Style -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Header Style -->
        <Style x:Key="HeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="{StaticResource TextColor}"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <!-- Input Style -->
        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                          Margin="{TemplateBinding Padding}"
                                          VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="{StaticResource PrimaryColor}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="10,10,10,5">
            <StackPanel>
                <TextBlock Text="✏️ تعديل إشعار الواتساب" Style="{StaticResource HeaderStyle}" FontSize="24"/>
                <TextBlock Text="قم بتعديل بيانات الإشعار ومعاينة النص قبل الحفظ" 
                           FontSize="14" Foreground="#7F8C8D"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- Basic Information -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="📋 المعلومات الأساسية" Style="{StaticResource HeaderStyle}"/>
                        
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Customer Name (Read-only) -->
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم المكتري:" 
                                       VerticalAlignment="Center" Margin="0,0,10,10"/>
                            <TextBox Grid.Row="0" Grid.Column="1" Name="CustomerNameTextBox" 
                                     Style="{StaticResource ModernTextBoxStyle}" 
                                     IsReadOnly="True" Background="#F8F9FA" Margin="0,0,0,10"/>

                            <!-- Property Name (Read-only) -->
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="اسم المحل:" 
                                       VerticalAlignment="Center" Margin="0,0,10,10"/>
                            <TextBox Grid.Row="1" Grid.Column="1" Name="PropertyNameTextBox" 
                                     Style="{StaticResource ModernTextBoxStyle}" 
                                     IsReadOnly="True" Background="#F8F9FA" Margin="0,0,0,10"/>

                            <!-- Phone Number -->
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="رقم الهاتف:" 
                                       VerticalAlignment="Center" Margin="0,0,10,10"/>
                            <TextBox Grid.Row="2" Grid.Column="1" Name="PhoneNumberTextBox" 
                                     Style="{StaticResource ModernTextBoxStyle}" 
                                     TextChanged="PhoneNumberTextBox_TextChanged" Margin="0,0,0,10"/>

                            <!-- Amount -->
                            <TextBlock Grid.Row="3" Grid.Column="0" Text="المبلغ (درهم):" 
                                       VerticalAlignment="Center" Margin="0,0,10,10"/>
                            <TextBox Grid.Row="3" Grid.Column="1" Name="AmountTextBox" 
                                     Style="{StaticResource ModernTextBoxStyle}" 
                                     TextChanged="AmountTextBox_TextChanged" Margin="0,0,0,10"/>

                            <!-- Due Date -->
                            <TextBlock Grid.Row="4" Grid.Column="0" Text="تاريخ الاستحقاق:" 
                                       VerticalAlignment="Center" Margin="0,0,10,10"/>
                            <DatePicker Grid.Row="4" Grid.Column="1" Name="DueDatePicker" 
                                        FontSize="14" Margin="0,0,0,10"
                                        SelectedDateChanged="DueDatePicker_SelectedDateChanged"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Message Template -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="📝 قالب الرسالة" Style="{StaticResource HeaderStyle}"/>
                        
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Template Selection -->
                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                                <TextBlock Text="اختيار القالب:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <ComboBox Name="TemplateComboBox" Width="300" FontSize="14"
                                          SelectionChanged="TemplateComboBox_SelectionChanged"/>
                                <Button Content="🔄 تحديث القوالب" Style="{StaticResource InfoButtonStyle}"
                                        Margin="10,0,0,0" Click="RefreshTemplatesBtn_Click"/>
                            </StackPanel>

                            <!-- Manual Message -->
                            <TextBlock Grid.Row="1" Text="أو اكتب الرسالة يدوياً:" Margin="0,0,0,10"/>
                            <TextBox Grid.Row="2" Name="MessageTextBox" 
                                     Style="{StaticResource ModernTextBoxStyle}"
                                     Height="120" TextWrapping="Wrap" AcceptsReturn="True"
                                     VerticalScrollBarVisibility="Auto"
                                     TextChanged="MessageTextBox_TextChanged"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Message Preview -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="👁️ معاينة الرسالة" Style="{StaticResource HeaderStyle}"/>
                        
                        <Border Background="#F8F9FA" CornerRadius="8" 
                                BorderBrush="{StaticResource BorderColor}" BorderThickness="1"
                                Padding="15" MinHeight="100">
                            <ScrollViewer VerticalScrollBarVisibility="Auto">
                                <TextBlock Name="PreviewTextBlock" 
                                           Text="معاينة الرسالة ستظهر هنا..."
                                           FontSize="14" LineHeight="22"
                                           TextWrapping="Wrap"
                                           Foreground="{StaticResource TextColor}"/>
                            </ScrollViewer>
                        </Border>

                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,10,0,0">
                            <TextBlock Text="📊 عدد الأحرف: " FontSize="12" Foreground="#7F8C8D"/>
                            <TextBlock Name="CharacterCountTextBlock" Text="0" FontSize="12" Foreground="#7F8C8D"/>
                            <TextBlock Text=" | طول الرسالة: " FontSize="12" Foreground="#7F8C8D" Margin="10,0,0,0"/>
                            <TextBlock Name="MessageLengthTextBlock" Text="قصيرة" FontSize="12" Foreground="#7F8C8D"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>

        <!-- Footer Buttons -->
        <Border Grid.Row="2" Background="{StaticResource SurfaceColor}" 
                BorderBrush="{StaticResource BorderColor}" BorderThickness="0,1,0,0"
                Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="💡 تلميح: استخدم Enter للحفظ أو Esc للإلغاء" 
                               FontSize="12" Foreground="#7F8C8D" VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="💾 حفظ التعديلات" Style="{StaticResource SuccessButtonStyle}"
                            Margin="0,0,10,0" Click="SaveBtn_Click"
                            ToolTip="حفظ التعديلات (Enter)"/>
                    
                    <Button Content="❌ إلغاء" Style="{StaticResource DangerButtonStyle}"
                            Click="CancelBtn_Click"
                            ToolTip="إلغاء التعديل (Esc)"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
