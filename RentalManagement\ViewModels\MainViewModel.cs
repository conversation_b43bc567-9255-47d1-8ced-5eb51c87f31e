using RentalManagement.Helpers;
using RentalManagement.Services;
using System.Windows.Input;

namespace RentalManagement.ViewModels
{
    public class MainViewModel : BaseViewModel
    {
        private BaseViewModel? _currentViewModel;
        private bool _isDarkMode;

        public MainViewModel()
        {
            Title = "نظام إدارة العقارات";

            // Initialize commands
            NavigateToDashboardCommand = new RelayCommand(NavigateToDashboard);
            NavigateToCustomersCommand = new RelayCommand(NavigateToCustomers);
            NavigateToPropertiesCommand = new RelayCommand(NavigateToProperties);
            NavigateToPaymentsCommand = new RelayCommand(NavigateToPayments);
            NavigateToReportsCommand = new RelayCommand(NavigateToReports);
            ToggleThemeCommand = new RelayCommand(ToggleTheme);
            ExitCommand = new RelayCommand(Exit);

            // Initialize theme
            InitializeTheme();

            // Initialize database
            InitializeDatabase();

            // Set default view
            NavigateToDashboard();
        }

        public BaseViewModel? CurrentViewModel
        {
            get => _currentViewModel;
            set => SetProperty(ref _currentViewModel, value);
        }

        public bool IsDarkMode
        {
            get => _isDarkMode;
            set => SetProperty(ref _isDarkMode, value);
        }

        // Commands
        public ICommand NavigateToDashboardCommand { get; }
        public ICommand NavigateToCustomersCommand { get; }
        public ICommand NavigateToPropertiesCommand { get; }
        public ICommand NavigateToPaymentsCommand { get; }
        public ICommand NavigateToReportsCommand { get; }
        public ICommand ToggleThemeCommand { get; }
        public ICommand ExitCommand { get; }

        private void NavigateToDashboard()
        {
            CurrentViewModel = new DashboardViewModel();
        }

        private void NavigateToCustomers()
        {
            CurrentViewModel = new CustomersViewModel();
        }

        private void NavigateToProperties()
        {
            CurrentViewModel = new PropertiesViewModel();
        }

        private void NavigateToPayments()
        {
            CurrentViewModel = new PaymentsViewModel();
        }

        private void NavigateToReports()
        {
            CurrentViewModel = new ReportsViewModel();
        }

        private void ToggleTheme()
        {
            ThemeService.Instance.ToggleTheme();
            IsDarkMode = ThemeService.Instance.CurrentTheme == Services.AppTheme.Dark;
        }

        private void Exit()
        {
            System.Windows.Application.Current.Shutdown();
        }

        private void InitializeTheme()
        {
            try
            {
                ThemeService.Instance.InitializeTheme();
                IsDarkMode = ThemeService.Instance.CurrentTheme == Services.AppTheme.Dark;

                // Subscribe to theme changes
                ThemeService.Instance.ThemeChanged += OnThemeChanged;
            }
            catch (Exception ex)
            {
                SetError($"خطأ في تهيئة الثيم: {ex.Message}");
            }
        }

        private void OnThemeChanged(object? sender, Services.AppTheme theme)
        {
            IsDarkMode = theme == Services.AppTheme.Dark;
        }

        private async void InitializeDatabase()
        {
            try
            {
                IsBusy = true;
                ClearError();

                await Task.Run(() => DatabaseService.Instance.InitializeDatabase());

                // Test connection
                var isConnected = await DatabaseService.Instance.TestConnectionAsync();
                if (!isConnected)
                {
                    SetError("فشل في الاتصال بقاعدة البيانات");
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في تهيئة قاعدة البيانات: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }
    }
}
