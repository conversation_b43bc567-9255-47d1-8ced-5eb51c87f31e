﻿#pragma checksum "..\..\..\..\Windows\PropertyDetailsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "2FFE34CECBF0C12577E33B238E0A3EF11E816747"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleRentalApp.Windows {
    
    
    /// <summary>
    /// PropertyDetailsWindow
    /// </summary>
    public partial class PropertyDetailsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 221 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PropertyNameTitle;
        
        #line default
        #line hidden
        
        
        #line 226 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PropertyAddressSubtitle;
        
        #line default
        #line hidden
        
        
        #line 234 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton ThemeToggleButton;
        
        #line default
        #line hidden
        
        
        #line 266 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PropertyNameText;
        
        #line default
        #line hidden
        
        
        #line 272 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PropertyAddressText;
        
        #line default
        #line hidden
        
        
        #line 278 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PropertyTypeText;
        
        #line default
        #line hidden
        
        
        #line 285 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MonthlyRentText;
        
        #line default
        #line hidden
        
        
        #line 292 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TaxPaymentMethodText;
        
        #line default
        #line hidden
        
        
        #line 297 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CustomerNameText;
        
        #line default
        #line hidden
        
        
        #line 311 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox YearComboBox;
        
        #line default
        #line hidden
        
        
        #line 339 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalTaxText;
        
        #line default
        #line hidden
        
        
        #line 346 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaidAmountText;
        
        #line default
        #line hidden
        
        
        #line 353 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RemainingAmountText;
        
        #line default
        #line hidden
        
        
        #line 361 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar PaymentProgressBar;
        
        #line default
        #line hidden
        
        
        #line 367 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProgressPercentageText;
        
        #line default
        #line hidden
        
        
        #line 377 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border StatisticsCard;
        
        #line default
        #line hidden
        
        
        #line 382 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PaymentCountText;
        
        #line default
        #line hidden
        
        
        #line 385 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AveragePaymentText;
        
        #line default
        #line hidden
        
        
        #line 388 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LargestPaymentText;
        
        #line default
        #line hidden
        
        
        #line 391 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SmallestPaymentText;
        
        #line default
        #line hidden
        
        
        #line 394 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastPaymentDateText;
        
        #line default
        #line hidden
        
        
        #line 397 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NextPaymentDueText;
        
        #line default
        #line hidden
        
        
        #line 400 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border StatusIndicatorBorder;
        
        #line default
        #line hidden
        
        
        #line 405 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusIndicatorText;
        
        #line default
        #line hidden
        
        
        #line 437 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddPaymentButton;
        
        #line default
        #line hidden
        
        
        #line 445 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshDataButton;
        
        #line default
        #line hidden
        
        
        #line 457 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditPaymentButton;
        
        #line default
        #line hidden
        
        
        #line 464 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeletePaymentButton;
        
        #line default
        #line hidden
        
        
        #line 478 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintReceiptButton;
        
        #line default
        #line hidden
        
        
        #line 485 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PreviewReceiptButton;
        
        #line default
        #line hidden
        
        
        #line 521 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SortComboBox;
        
        #line default
        #line hidden
        
        
        #line 531 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 540 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PeriodFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 567 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 573 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowOnlyCurrentYearCheckBox;
        
        #line default
        #line hidden
        
        
        #line 579 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowStatisticsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 603 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid PaymentsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 658 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalPaymentsCountText;
        
        #line default
        #line hidden
        
        
        #line 663 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalAmountText;
        
        #line default
        #line hidden
        
        
        #line 668 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AverageAmountText;
        
        #line default
        #line hidden
        
        
        #line 673 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastPaymentText;
        
        #line default
        #line hidden
        
        
        #line 692 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 697 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        
        #line 719 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 726 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RecordCountText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleRentalApp;V1.0.0.0;component/windows/propertydetailswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.PropertyNameTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.PropertyAddressSubtitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.ThemeToggleButton = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 237 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
            this.ThemeToggleButton.Click += new System.Windows.RoutedEventHandler(this.ThemeToggleButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.PropertyNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.PropertyAddressText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.PropertyTypeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.MonthlyRentText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.TaxPaymentMethodText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.CustomerNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.YearComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 312 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
            this.YearComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.YearComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.TotalTaxText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.PaidAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.RemainingAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.PaymentProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 15:
            this.ProgressPercentageText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.StatisticsCard = ((System.Windows.Controls.Border)(target));
            return;
            case 17:
            this.PaymentCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.AveragePaymentText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.LargestPaymentText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.SmallestPaymentText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.LastPaymentDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.NextPaymentDueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.StatusIndicatorBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 24:
            this.StatusIndicatorText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            this.AddPaymentButton = ((System.Windows.Controls.Button)(target));
            
            #line 440 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
            this.AddPaymentButton.Click += new System.Windows.RoutedEventHandler(this.AddPaymentButton_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.RefreshDataButton = ((System.Windows.Controls.Button)(target));
            
            #line 448 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
            this.RefreshDataButton.Click += new System.Windows.RoutedEventHandler(this.RefreshDataButton_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.EditPaymentButton = ((System.Windows.Controls.Button)(target));
            
            #line 461 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
            this.EditPaymentButton.Click += new System.Windows.RoutedEventHandler(this.EditPaymentButton_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            this.DeletePaymentButton = ((System.Windows.Controls.Button)(target));
            
            #line 468 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
            this.DeletePaymentButton.Click += new System.Windows.RoutedEventHandler(this.DeletePaymentButton_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.PrintReceiptButton = ((System.Windows.Controls.Button)(target));
            
            #line 482 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
            this.PrintReceiptButton.Click += new System.Windows.RoutedEventHandler(this.PrintReceiptButton_Click);
            
            #line default
            #line hidden
            return;
            case 30:
            this.PreviewReceiptButton = ((System.Windows.Controls.Button)(target));
            
            #line 489 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
            this.PreviewReceiptButton.Click += new System.Windows.RoutedEventHandler(this.PreviewReceiptButton_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            this.SortComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 521 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
            this.SortComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SortComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 32:
            this.StatusFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 531 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
            this.StatusFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.StatusFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 33:
            this.PeriodFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 540 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
            this.PeriodFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PeriodFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 34:
            
            #line 551 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ResetFiltersButton_Click);
            
            #line default
            #line hidden
            return;
            case 35:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 568 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 36:
            this.ShowOnlyCurrentYearCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 576 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
            this.ShowOnlyCurrentYearCheckBox.Checked += new System.Windows.RoutedEventHandler(this.ShowOnlyCurrentYearCheckBox_Changed);
            
            #line default
            #line hidden
            
            #line 577 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
            this.ShowOnlyCurrentYearCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.ShowOnlyCurrentYearCheckBox_Changed);
            
            #line default
            #line hidden
            return;
            case 37:
            this.ShowStatisticsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 583 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
            this.ShowStatisticsCheckBox.Checked += new System.Windows.RoutedEventHandler(this.ShowStatisticsCheckBox_Changed);
            
            #line default
            #line hidden
            
            #line 584 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
            this.ShowStatisticsCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.ShowStatisticsCheckBox_Changed);
            
            #line default
            #line hidden
            return;
            case 38:
            
            #line 589 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AdvancedSearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 39:
            
            #line 596 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.GenerateDetailedReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 40:
            this.PaymentsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 605 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
            this.PaymentsDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PaymentsDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 41:
            this.TotalPaymentsCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 42:
            this.TotalAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 43:
            this.AverageAmountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 44:
            this.LastPaymentText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 45:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 695 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 46:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 701 "..\..\..\..\Windows\PropertyDetailsWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 47:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 48:
            this.RecordCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

