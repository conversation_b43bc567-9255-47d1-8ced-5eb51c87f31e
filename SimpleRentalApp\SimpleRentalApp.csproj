﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>

    <AssemblyTitle>تدبير الكراء</AssemblyTitle>
    <AssemblyDescription>نظام إدارة الكراء والعقارات</AssemblyDescription>
    <AssemblyCompany>حفيظ عبدو</AssemblyCompany>
    <AssemblyProduct>تدبير الكراء</AssemblyProduct>
    <AssemblyCopyright>© 2024 حفيظ عبدو</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <ApplicationIcon>app_icon.ico</ApplicationIcon>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="app_icon.ico">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.6">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.6" />
    <PackageReference Include="QuestPDF" Version="2025.7.0" />
    <PackageReference Include="System.Text.Json" Version="9.0.6" />
  </ItemGroup>

</Project>
